package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleDelFormClientDTO", desc = "null")
@Data
@ApiModel("规则删除（修改状态）请求参数对象")
public class RuleDelFormClientDTO {

    /**
     * 操作人账号
     */
    @Field(name = "account", desc = "")
    @JsonIgnore
    @ApiModelProperty("操作人账号")
    private String account;

    /**
     * 规则ID
     */
    @Field(name = "规则ID", desc = "规则ID")
    @ApiModelProperty("规则ID")
    private Long id;

    /**
     * 是否启用（0禁用，1启用，默认1）
     */
    @Field(name = "是否启用（0禁用，1启用，默认1）", desc = "是否启用（0禁用，1启用，默认1）")
    @ApiModelProperty("是否启用（0禁用，1启用，默认1）")
    private Integer enable;
}
