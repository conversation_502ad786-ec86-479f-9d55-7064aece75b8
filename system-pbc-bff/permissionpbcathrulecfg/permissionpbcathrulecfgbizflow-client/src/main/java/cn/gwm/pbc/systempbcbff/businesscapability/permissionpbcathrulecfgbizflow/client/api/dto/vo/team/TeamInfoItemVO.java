package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.team;

import com.alibaba.bizworks.core.specification.*;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
/**
 * <AUTHOR>
 */
@StructureObject(name = "TeamInfoItemVO", desc = "")
@Data
public class TeamInfoItemVO implements Serializable {
    /**
     * id
     */
    @Field(name = "id", desc = "")
    private String id;
    /**
     * name
     */
    @Field(name = "name", desc = "")
    @JSONField(name = "team_name")
    private String name;
    /**
     * parentId
     */
    @Field(name = "parentId", desc = "")
    @JSONField(name = "parent_id")
    private String parentId;
    /**
     * showOrder
     */
    @Field(name = "showOrder", desc = "")
    @JSONField(name = "show_order")
    private Integer showOrder;
    /**
     * children
     */
    @Field(name = "children", desc = "")
    private List<TeamInfoItemVO> children;
    /**
     * setUp
     */
    @Field(name = "setUp", desc = "")
    @JSONField(name = "set_up")
    private Boolean setUp;

}
