package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("规则字段数据请求参数对象")
public class RuleFieldDataForm {
    /**
     * 字段名称
     */
    @ApiModelProperty("字段名称")
    @JsonProperty(value = "field_name")
    private String fieldName;

    /**
     * 字段编码
     */
    @ApiModelProperty("字段编码")
    @JsonProperty(value = "field_code")
    private String fieldCode;

    /**
     * 字段类型
     */
    @ApiModelProperty("字段类型")
    @JsonProperty(value = "field_type")
    private Integer fieldType;
}
