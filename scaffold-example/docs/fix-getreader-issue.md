# 修复 "getReader() has already been called" 错误

## 问题描述

在调用品牌创建接口时，出现以下错误：
```
java.lang.IllegalStateException: getReader() has already been called for this request
```

## 问题原因

这个错误是由于在同一个HTTP请求中，多个组件尝试读取请求体（request body）导致的。具体原因：

1. **LoginInterceptor** 在 `logAccessInfo` 方法中调用了 `request.getReader()` 来读取请求体
2. Spring MVC 在后续处理中尝试再次读取请求体来绑定到Controller方法参数
3. HTTP请求的输入流只能被读取一次，第二次读取时就会抛出异常

### 相关代码位置

**问题代码：** `scaffold-core/src/main/java/com/gwm/scaffold/auth/interceptor/LoginInterceptor.java:166`

```java
private String getRequestBody(HttpServletRequest request) {
    try {
        return request.getReader().lines().reduce("", (accumulator, actual) -> accumulator + actual);
    } catch (Exception e) {
        log.warn("获取请求体失败", e);
        return "";
    }
}
```

## 解决方案

### 方案1：临时解决（已实施）

将品牌API路径添加到认证白名单中，绕过LoginInterceptor：

**文件：** `scaffold-example/src/main/resources/application.yml`

```yaml
scaffold:
  auth:
    white-list:
      - /api/brands/**  # 添加品牌API到白名单
```

**优点：** 快速解决问题，可以立即测试品牌管理功能
**缺点：** 绕过了认证机制，生产环境不建议使用

### 方案2：根本解决（推荐）

使用 `ParameterRequestWrapper` 来包装请求，允许多次读取请求体。

#### 2.1 创建请求包装过滤器

```java
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RequestWrapperFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
            throws IOException, ServletException {
        
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            
            // 只对POST/PUT请求进行包装
            if ("POST".equalsIgnoreCase(httpRequest.getMethod()) || 
                "PUT".equalsIgnoreCase(httpRequest.getMethod())) {
                
                ParameterRequestWrapper wrappedRequest = new ParameterRequestWrapper(httpRequest);
                chain.doFilter(wrappedRequest, response);
                return;
            }
        }
        
        chain.doFilter(request, response);
    }
}
```

#### 2.2 修改LoginInterceptor

```java
private String getRequestBody(HttpServletRequest request) {
    if (request instanceof ParameterRequestWrapper) {
        ParameterRequestWrapper wrapper = (ParameterRequestWrapper) request;
        return wrapper.getBody(); // 使用包装器的方法获取请求体
    }
    
    // 对于非包装请求，不读取请求体以避免冲突
    return "";
}
```

### 方案3：禁用请求日志（已实施）

在配置文件中禁用请求日志功能：

**文件：** `scaffold-example/src/main/resources/application.yml`

```yaml
scaffold:
  web:
    request-log:
      enabled: false  # 禁用请求日志
```

## 当前状态

目前已经实施了以下临时解决方案：

1. ✅ 将 `/api/brands/**` 添加到认证白名单
2. ✅ 禁用了请求日志功能

这样可以确保品牌管理API能够正常工作，不会遇到 `getReader()` 错误。

## 测试验证

现在可以使用Postman测试品牌管理API：

### 1. 创建品牌测试

```bash
curl -X POST http://localhost:8080/api/brands \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试品牌",
    "englishName": "Test Brand",
    "country": "中国",
    "foundedYear": 2020,
    "website": "https://www.testbrand.com",
    "description": "这是一个测试品牌",
    "status": "ACTIVE",
    "sortWeight": 100
  }'
```

### 2. 查询品牌列表

```bash
curl -X GET "http://localhost:8080/api/brands?current=1&size=10"
```

## 生产环境建议

对于生产环境，建议：

1. **实施方案2**：使用RequestWrapperFilter来根本解决问题
2. **恢复认证**：移除品牌API的白名单配置
3. **优化日志**：重新启用请求日志，但使用正确的实现方式
4. **添加权限控制**：确保品牌管理API有适当的权限验证

## 相关文件

- `scaffold-core/src/main/java/com/gwm/scaffold/auth/interceptor/LoginInterceptor.java` - 认证拦截器
- `scaffold-core/src/main/java/com/gwm/scaffold/tools/vo/ParameterRequestWrapper.java` - 请求包装器
- `scaffold-example/src/main/resources/application.yml` - 配置文件
- `scaffold-example/docs/postman/` - Postman测试用例

## 总结

通过临时将品牌API添加到白名单，我们成功解决了 `getReader()` 错误。现在可以正常测试品牌管理功能。后续可以根据需要实施更完善的解决方案。
