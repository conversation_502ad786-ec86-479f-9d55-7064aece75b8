package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcMenuSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.menu.MenuInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcMenuSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuDeleteDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuSaveDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.lang.Override;
import java.util.List;
import java.util.Map;

import com.gwm.framework.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限PBC-应用菜单管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.15
 */
@RestController("PermissionPBCAthRuleCfgBizFlowSystemPbcMenuSvcCON")
public class SystemPbcMenuSvcController implements SystemPbcMenuSvcI {

    @Autowired
    private SystemPbcMenuSvc systemPbcMenuSvc;

    @Override
    public Response<Void> save(@RequestBody(required = true) MenuSaveDTO request)  throws BusinessException {
    	systemPbcMenuSvc.save(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<Void> delete(@RequestBody(required = true) MenuDeleteDTO request) {
    	systemPbcMenuSvc.delete(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<MenuInfoItemVO> detail(@RequestBody(required = true) MenuDetailDTO request) {
    	MenuInfoItemVO menuInfoItemVOReturn = systemPbcMenuSvc.detail(request);
		return Response.buildSuccess(menuInfoItemVOReturn);
	}

    @Override
    public Response<Map<String, Object>> treeUser() {
    	Map mapReturn = systemPbcMenuSvc.treeUser();
		return Response.buildSuccess(mapReturn);
	}

    @Override
    public Response<List<MenuInfoItemVO>> treeAll() {
    	List<MenuInfoItemVO> listReturn = systemPbcMenuSvc.treeAll();
		return Response.buildSuccess(listReturn);
	}
}
