package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleBindGroupFormClientDTO", desc = "null")
@Data
@ApiModel("角色绑定组织参数对象")
public class RoleBindGroupFormClientDTO implements Serializable {

    /**
     * account
     */
    @Field(name = "account", desc = "")
    @JsonIgnore
    private String account;

	@Field(name = "platformCode", desc = "")
    @ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 角色ID
     */
    @Field(name = "id", desc = "")
    @ApiModelProperty("角色ID")
    private String id;

    /**
     * 组织集合
     */
    @Field(name = "groupIds", desc = "")
    @ApiModelProperty("组织集合")
    @JsonProperty(value = "group_ids")
    private List<Integer> groupIds;
}