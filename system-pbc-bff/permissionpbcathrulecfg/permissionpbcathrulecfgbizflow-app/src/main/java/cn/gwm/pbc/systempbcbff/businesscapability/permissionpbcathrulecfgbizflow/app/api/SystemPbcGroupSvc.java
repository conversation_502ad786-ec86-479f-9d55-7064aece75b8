package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.group.SearchGroupClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.SearchAdminResourceOfRoleFormClientDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;

import java.util.Map;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-角色组织服务")
public interface SystemPbcGroupSvc {
    /**
     * 权限中心-通过账号获取树形结构
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-通过账号获取树形结构")
    @Method(desc = "权限中心-通过账号获取树形结构", name = "权限中心-通过账号获取树形结构 v2-/manage/admin/group/tree")
    Map<String, Object> groupTree();
    /**
     * 权限中心-获取完整树形结构（管理组织）
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-获取完整树形结构（管理组织）")
    @Method(desc = "权限中心-获取完整树形结构（管理组织）", name = "权限中心-获取完整树形结构（管理组织） v2-/manage/admin/group/tree/all")
    Map<String, Object> groupTreeAll();
    /**
     * 权限中心-获取完整树形结构（HR组织）
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-获取完整树形结构（HR组织）")
    @Method(desc = "权限中心-获取完整树形结构（HR组织）", name = "权限中心-获取完整树形结构（HR组织） v2-/manage/admin/group/tree/hr/all")
    Map<String, Object> hrGroupTreeAll();
    /**
     * 权限中心-查询组织管理员
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-查询组织管理员")
    @Method(desc = "权限中心-查询组织管理员", name = "权限中心-查询组织管理员 v2-/manage/admin/group/info")
    Map<String, Object> groupInfo(@Parameter(name = "request", required = true) SearchGroupClientDTO request);

}
