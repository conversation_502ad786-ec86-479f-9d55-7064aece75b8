# 异常处理规范（精简版）

## 异常分类
- **业务异常（BusinessException）**：参数校验、业务规则、权限等可预期错误，需带业务错误码。
- **系统异常（ServiceException）**：数据库、IO、网络等不可预期错误，或未知异常兜底。

## Controller层异常处理
- controller层catch到BusinessException和ServiceException时，仅日志输出后直接抛出，由全局异常处理器统一处理。
- 仅对Exception兜底，日志后抛出ServiceException（包含原始异常信息）。
- 不在controller层自定义错误响应，所有错误响应由全局异常处理器统一生成。

## 全局异常处理器职责
- 捕获所有未处理的异常，统一封装为AjaxResult响应。
- 业务异常返回业务错误码和消息，系统异常返回5000和友好提示。

## 典型controller异常处理范例
```java
try {
    // ...
} catch (BusinessException e) {
    log.warn("业务异常: {}", e.getMessage());
    throw e;
} catch (ServiceException e) {
    log.error("系统异常", e);
    throw e;
} catch (Exception e) {
    log.error("未知异常", e);
    throw new ServiceException("未知异常", e);
}
```
