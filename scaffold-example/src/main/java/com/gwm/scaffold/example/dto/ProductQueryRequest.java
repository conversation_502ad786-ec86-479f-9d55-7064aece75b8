package com.gwm.scaffold.example.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import com.gwm.scaffold.web.cmd.AbstractPageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品信息表查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "产品信息表查询请求")
public class ProductQueryRequest extends AbstractPageRequest {

    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private Long id;

    /**
     * 产品编码
     */
    @Schema(description = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String productName;

    /**
     * 产品英文名称
     */
    @Schema(description = "产品英文名称")
    private String productNameEn;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private Long categoryId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String categoryName;

    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID")
    private Long brandId;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;

    /**
     * 产品类型 1:实物商品 2:虚拟商品 3:服务商品
     */
    @Schema(description = "产品类型 1:实物商品 2:虚拟商品 3:服务商品")
    private Integer productType;

    /**
     * 计量单位
     */
    @Schema(description = "计量单位")
    private String unit;

    /**
     * 重量(kg)
     */
    @Schema(description = "重量(kg)")
    private BigDecimal weight;

    /**
     * 体积(立方米)
     */
    @Schema(description = "体积(立方米)")
    private BigDecimal volume;

    /**
     * 长度(cm)
     */
    @Schema(description = "长度(cm)")
    private BigDecimal length;

    /**
     * 宽度(cm)
     */
    @Schema(description = "宽度(cm)")
    private BigDecimal width;

    /**
     * 高度(cm)
     */
    @Schema(description = "高度(cm)")
    private BigDecimal height;

    /**
     * 成本价
     */
    @Schema(description = "成本价")
    private BigDecimal costPrice;

    /**
     * 销售价
     */
    @Schema(description = "销售价")
    private BigDecimal salePrice;

    /**
     * 市场价
     */
    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    /**
     * 最小库存
     */
    @Schema(description = "最小库存")
    private Integer minStock;

    /**
     * 最大库存
     */
    @Schema(description = "最大库存")
    private Integer maxStock;

    /**
     * 当前库存
     */
    @Schema(description = "当前库存")
    private Integer currentStock;

    /**
     * 销售数量
     */
    @Schema(description = "销售数量")
    private Integer salesCount;

    /**
     * 浏览次数
     */
    @Schema(description = "浏览次数")
    private Integer viewCount;

    /**
     * 状态 1:上架 2:下架 3:停产
     */
    @Schema(description = "状态 1:上架 2:下架 3:停产")
    private Integer status;

    /**
     * 是否热销 0:否 1:是
     */
    @Schema(description = "是否热销 0:否 1:是")
    private Integer isHot;

    /**
     * 是否新品 0:否 1:是
     */
    @Schema(description = "是否新品 0:否 1:是")
    private Integer isNew;

    /**
     * 是否推荐 0:否 1:是
     */
    @Schema(description = "是否推荐 0:否 1:是")
    private Integer isRecommend;

    /**
     * 排序号
     */
    @Schema(description = "排序号")
    private Integer sortOrder;

    /**
     * 主图片URL
     */
    @Schema(description = "主图片URL")
    private String mainImage;

    /**
     * 图片列表JSON
     */
    @Schema(description = "图片列表JSON")
    private String imageList;

    /**
     * 视频URL
     */
    @Schema(description = "视频URL")
    private String videoUrl;

    /**
     * 产品描述
     */
    @Schema(description = "产品描述")
    private String description;

    /**
     * 规格参数JSON
     */
    @Schema(description = "规格参数JSON")
    private String specification;

    /**
     * 产品特色
     */
    @Schema(description = "产品特色")
    private String features;

    /**
     * 售后服务信息
     */
    @Schema(description = "售后服务信息")
    private String serviceInfo;

    /**
     * 关键词
     */
    @Schema(description = "关键词")
    private String keywords;

    /**
     * SEO标题
     */
    @Schema(description = "SEO标题")
    private String metaTitle;

    /**
     * SEO描述
     */
    @Schema(description = "SEO描述")
    private String metaDescription;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 保修期(月)
     */
    @Schema(description = "保修期(月)")
    private Integer warrantyPeriod;

    /**
     * 保质期(天)
     */
    @Schema(description = "保质期(天)")
    private Integer shelfLife;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    private Date productionDate;

    /**
     * 生产日期开始时间
     */
    @Schema(description = "生产日期开始时间")
    private Date productionDateStart;

    /**
     * 生产日期结束时间
     */
    @Schema(description = "生产日期结束时间")
    private Date productionDateEnd;

    /**
     * 过期日期
     */
    @Schema(description = "过期日期")
    private Date expiryDate;

    /**
     * 过期日期开始时间
     */
    @Schema(description = "过期日期开始时间")
    private Date expiryDateStart;

    /**
     * 过期日期结束时间
     */
    @Schema(description = "过期日期结束时间")
    private Date expiryDateEnd;

    /**
     * 条形码
     */
    @Schema(description = "条形码")
    private String barcode;

    /**
     * 二维码
     */
    @Schema(description = "二维码")
    private String qrCode;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

}
