package com.alibaba.bizworks.demo.mybatisplus;

import com.alibaba.bizworks.core.runtime.common.MultiResponse;
import com.alibaba.bizworks.core.runtime.common.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * demo controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.05
 */
@RestController
@RequestMapping("/user")
public class DemoUserController {

    @Autowired
    private DemoUserMapper demoUserMapper;

    @PostMapping(value = "/insert")
    public SingleResponse insert(@RequestBody DemoUserDO demoUserDO) {
        int i = demoUserMapper.insert(demoUserDO);
        if (i < 1) {
            return SingleResponse.buildFailure("Error", "Error");
        }
        return SingleResponse.buildSuccess();
    }

    @GetMapping(value = "/delete")
    public SingleResponse delete(@RequestParam Long id) {
        int i = demoUserMapper.deleteById(id);
        if (i < 1) {
            return SingleResponse.buildFailure("Error", "Error");
        }
        return SingleResponse.buildSuccess();
    }

    @GetMapping(value = "/select")
    public MultiResponse<DemoUserDO> select(@RequestParam String name) {
        QueryWrapper<DemoUserDO> qw = new QueryWrapper<>();
        IPage<DemoUserDO> page = new Page<>(1, 2);
        qw.eq("name", "lisi");
        IPage<DemoUserDO> demoUserIPage = demoUserMapper.selectPage(page, qw);
        return MultiResponse.buildSuccess(demoUserIPage.getRecords());
    }

    @PostMapping(value = "/update")
    private SingleResponse update(@RequestBody DemoUserDO demoUserDO) {
        DemoUserDO user = demoUserMapper.selectById(demoUserDO.getId());
        UpdateWrapper<DemoUserDO> uw = new UpdateWrapper<>();
        uw.eq("name", user.getName());
        user.setName(demoUserDO.getName());
        user.setAge(demoUserDO.getAge());
        user.setPhone(demoUserDO.getPhone());
        int i = demoUserMapper.update(user, uw);
        if (i < 1) {
            return SingleResponse.buildFailure("Error", "Error");
        }
        return SingleResponse.buildSuccess();
    }
}