package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRoleSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteRoleService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.role.RoleInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleDeleteDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleSaveDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import java.lang.Override;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
/**
 * <AUTHOR>
 */
@Component("PermissionPBCAthRuleCfgBizFlowSystemPbcRoleSvcBS")
public class SystemPbcRoleSvcImpl implements SystemPbcRoleSvc {

    @Resource
    private RemoteRoleService remoteRoleService;

    @Override
    public void save(RoleSaveDTO request) {
    	if (StringUtils.isBlank(request.getId())) {
            RemoteRoleCreateDTO remoteDTO = new RemoteRoleCreateDTO();
            BeanUtil.copyProperties(request, remoteDTO);
            this.create(remoteDTO);
        } else {
            RemoteRoleEditDTO remoteDTO = new RemoteRoleEditDTO();
            BeanUtil.copyProperties(request, remoteDTO);
            this.edit(remoteDTO);
        }
	}

    @Override
    public void delete(RoleDeleteDTO request) {
    	RemoteRoleDeleteDTO remoteRoleDeleteDTO = new RemoteRoleDeleteDTO();
        BeanUtil.copyProperties(request, remoteRoleDeleteDTO);
        CommonRlt<JSONArray> jsonObjectCommonRlt = remoteRoleService.roleDelete(remoteRoleDeleteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}

    @Override
    public RoleInfoItemVO detail(RoleDetailDTO request) {
    	RemoteRoleDetailDTO remoteRoleDetailDTO = new RemoteRoleDetailDTO();
        BeanUtil.copyProperties(request, remoteRoleDetailDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteRoleService.roleDetail(remoteRoleDetailDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject result = jsonObjectCommonRlt.getData();
        return result.to(RoleInfoItemVO.class);
	}

    private void create(RemoteRoleCreateDTO remoteRoleCreateDTO) {
    	CommonRlt<JSONObject> jsonObjectCommonRlt = remoteRoleService.roleCreate(remoteRoleCreateDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}

    private void edit(RemoteRoleEditDTO remoteRoleEditDTO) {
    	CommonRlt<JSONObject> jsonObjectCommonRlt = remoteRoleService.roleEdit(remoteRoleEditDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}

    @Override
    public Map<String, Object> list(RoleListDTO request) {
    	RemoteRoleListDTO remoteRoleListDTO = new RemoteRoleListDTO();
        BeanUtil.copyProperties(request, remoteRoleListDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteRoleService.roleList(remoteRoleListDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject result = jsonObjectCommonRlt.getData();
        if (result != null) {
            JSONArray rows = result.getJSONArray("rows");
            List<RoleInfoItemVO> roleInfoItemVOS = rows.toJavaList(RoleInfoItemVO.class);
            result.put("rows", roleInfoItemVOS);
        }
        return BeanUtil.beanToMap(result);
	}
}
