package ${packageName}.vo;

<#list tableInfo.imports as import>
import ${import};
</#list>
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * ${tableInfo.tableComment!entityName}视图对象VO
 * 
 * <AUTHOR>
 * @date ${date}
 */
@Data
@Schema(description = "${tableInfo.tableComment!entityName}信息")
public class ${voName} {

<#list tableInfo.columns as column>
    <#if !column.logicDelete && !column.version>
    /**
     * ${column.columnComment!column.javaField}
     */
    @Schema(description = "${column.columnComment!column.javaField}")
    <#if column.javaType == "Date">
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    </#if>
    private ${column.javaType} ${column.javaField};

    </#if>
</#list>
}
