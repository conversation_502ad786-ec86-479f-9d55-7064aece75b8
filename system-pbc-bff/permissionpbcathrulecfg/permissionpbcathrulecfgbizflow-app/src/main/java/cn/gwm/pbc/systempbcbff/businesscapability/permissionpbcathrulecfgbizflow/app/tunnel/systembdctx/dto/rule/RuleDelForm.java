package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("规则删除（修改状态）请求参数对象")
public class RuleDelForm {

    /**
     * 操作人账号
     */
    @JsonIgnore
    @ApiModelProperty("操作人账号")
    private String account;

    /**
     * 规则ID
     */
    @ApiModelProperty("规则ID")
    private Long id;

    /**
     * 是否启用（0禁用，1启用，默认1）
     */
    @ApiModelProperty("是否启用（0禁用，1启用，默认1）")
    private Integer enable;
}
