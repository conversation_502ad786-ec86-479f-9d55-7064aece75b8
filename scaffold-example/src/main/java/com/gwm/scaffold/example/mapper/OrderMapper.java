package com.gwm.scaffold.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwm.scaffold.example.dto.OrderQueryRequest;
import com.gwm.scaffold.example.entity.Order;
import com.gwm.scaffold.example.vo.OrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单Mapper接口
 * 
 * 提供订单相关的数据访问功能
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 分页查询订单列表
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 订单分页数据
     */
    IPage<OrderVO> selectOrderPage(Page<OrderVO> page, @Param("request") OrderQueryRequest request);

    /**
     * 根据ID查询订单详情
     *
     * @param id 订单ID
     * @return 订单详情
     */
    OrderVO selectOrderById(@Param("id") Long id);

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    Order selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据客户ID查询订单列表
     *
     * @param customerId 客户ID
     * @return 订单列表
     */
    List<OrderVO> selectOrdersByCustomerId(@Param("customerId") Long customerId);

    /**
     * 统计订单数量按状态分组
     *
     * @return 状态统计结果
     */
    List<OrderStatusCount> selectOrderCountByStatus();

    /**
     * 订单状态统计结果
     */
    class OrderStatusCount {
        private Integer orderStatus;
        private Long count;

        public Integer getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(Integer orderStatus) {
            this.orderStatus = orderStatus;
        }

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }
    }
}
