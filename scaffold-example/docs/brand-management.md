# 品牌管理模块文档

## 概述

品牌管理模块是车系车型管理系统的核心模块之一，提供品牌信息的完整CRUD操作、多条件查询、批量操作、状态管理等功能。

## 功能特性

### 核心功能
- ✅ 品牌信息的CRUD操作（创建、查询、更新、删除）
- ✅ 多条件查询和分页显示
- ✅ 批量操作支持
- ✅ 状态管理（启用/禁用）
- ✅ 品牌名称唯一性校验
- ✅ 英文名称唯一性校验
- ✅ 数据完整性校验

### 业务规则
- 品牌名称全局唯一性校验
- 英文名称唯一性校验（如果填写）
- 删除前关联关系检查（TODO：需要与车系模块集成）
- 状态变更级联影响
- 操作权限控制

## 数据模型

### 品牌实体（Brand）

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| id | Long | 是 | 品牌ID | 1 |
| name | String | 是 | 品牌名称 | 奔驰 |
| englishName | String | 否 | 品牌英文名称 | Mercedes-Benz |
| country | String | 是 | 所属国家 | 德国 |
| foundedYear | Integer | 否 | 成立年份 | 1926 |
| website | String | 否 | 官网地址 | https://www.mercedes-benz.com |
| logoUrl | String | 否 | LOGO图片URL | https://example.com/logo/benz.png |
| description | String | 否 | 品牌简介 | 豪华汽车品牌 |
| status | String | 否 | 状态 | ACTIVE/INACTIVE |
| sortWeight | Integer | 否 | 排序权重 | 100 |

### 状态枚举

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| ACTIVE | 启用 | 品牌正常使用状态 |
| INACTIVE | 禁用 | 品牌暂停使用状态 |

## API接口

### 基础CRUD接口

#### 1. 分页查询品牌列表
```http
GET /api/v1/brands?current=1&size=10&name=奔驰&country=德国&status=ACTIVE
```

**查询参数：**
- `current`: 当前页码（默认1）
- `size`: 每页大小（默认10）
- `name`: 品牌名称（模糊查询）
- `englishName`: 英文名称（模糊查询）
- `country`: 所属国家
- `foundedYear`: 成立年份
- `foundedYearStart`: 成立年份范围-开始
- `foundedYearEnd`: 成立年份范围-结束
- `status`: 状态
- `createTimeStart`: 创建时间范围-开始
- `createTimeEnd`: 创建时间范围-结束

#### 2. 获取品牌详情
```http
GET /api/v1/brands/{id}
```

**注意：** 品牌详情查询会返回关联的车系列表，格式如下：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "name": "奔驰",
    // ... 其他品牌字段
    "seriesList": [
      {
        "id": 1,
        "name": "C级",
        "type": "SEDAN",
        "level": "B",
        "status": "ON_SALE"
      }
    ]
  }
}
```

#### 3. 创建品牌
```http
POST /api/v1/brands
Content-Type: application/json

{
  "name": "奔驰",
  "englishName": "Mercedes-Benz",
  "country": "德国",
  "foundedYear": 1926,
  "website": "https://www.mercedes-benz.com",
  "description": "豪华汽车品牌",
  "status": "ACTIVE",
  "sortWeight": 100
}
```

#### 4. 更新品牌
```http
PUT /api/brands/{id}
Content-Type: application/json

{
  "name": "奔驰汽车",
  "description": "德国豪华汽车品牌"
}
```

#### 5. 删除品牌
```http
DELETE /api/brands/{id}
```

#### 6. 批量删除品牌
```http
DELETE /api/brands/batch
Content-Type: application/json

[1, 2, 3]
```

### 扩展功能接口

#### 7. 更新品牌状态
```http
PUT /api/brands/{id}/status?status=INACTIVE
```

#### 8. 获取启用品牌列表
```http
GET /api/brands/active
```

#### 9. 根据国家查询品牌
```http
GET /api/brands/country/{country}
```

#### 10. 检查品牌名称
```http
GET /api/brands/check-name?name=奔驰&excludeId=1
```

## 权限控制

| 操作 | 权限码 | 说明 |
|------|--------|------|
| 查询列表 | brand:list | 查看品牌列表 |
| 查看详情 | brand:read | 查看品牌详情 |
| 创建品牌 | brand:create | 创建新品牌 |
| 更新品牌 | brand:update | 修改品牌信息 |
| 删除品牌 | brand:delete | 删除品牌 |

## 数据校验规则

### 创建品牌校验
- 品牌名称：必填，2-50字符，支持中英文、数字、空格和常用符号
- 英文名称：可选，2-100字符，支持英文、数字、空格和常用符号
- 所属国家：必填，最大50字符
- 成立年份：可选，1800-2030年
- 官网地址：可选，最大255字符，必须以http://或https://开头
- LOGO图片URL：可选，最大500字符
- 品牌简介：可选，最大1000字符
- 状态：可选，只能是ACTIVE或INACTIVE
- 排序权重：可选，0-9999

### 更新品牌校验
- 品牌ID：必填，大于0
- 其他字段：可选，校验规则同创建

## 使用示例

### Java代码示例

```java
@Autowired
private BrandService brandService;

// 创建品牌
BrandCreateRequest createRequest = new BrandCreateRequest();
createRequest.setName("奔驰");
createRequest.setEnglishName("Mercedes-Benz");
createRequest.setCountry("德国");
createRequest.setFoundedYear(1926);
createRequest.setStatus("ACTIVE");
BrandVO brand = brandService.createBrand(createRequest);

// 查询品牌
BrandQueryRequest queryRequest = new BrandQueryRequest();
queryRequest.setCurrent(1L);
queryRequest.setSize(10L);
queryRequest.setStatus("ACTIVE");
IPage<BrandVO> page = brandService.getBrandPage(queryRequest);

// 更新品牌状态
brandService.updateBrandStatus(brand.getId(), "INACTIVE");

// 获取启用品牌列表
List<BrandVO> activeBrands = brandService.getActiveBrandList();
```

## 注意事项

1. **唯一性约束**：品牌名称和英文名称（如果填写）必须全局唯一
2. **关联检查**：删除品牌前需要检查是否有关联的车系（TODO：待实现）
3. **状态管理**：禁用品牌会影响相关车系的显示（TODO：待实现级联逻辑）
4. **权限控制**：所有操作都需要相应的权限
5. **数据完整性**：所有输入数据都会进行严格校验

## 扩展计划

1. **文件上传**：支持LOGO图片上传
2. **数据导入导出**：支持Excel批量导入导出
3. **审计日志**：记录品牌信息变更历史
4. **关联统计**：显示品牌下的车系和车型数量
5. **搜索优化**：支持全文搜索和智能推荐

## 测试

运行单元测试：
```bash
mvn test -Dtest=BrandServiceTest
```

测试覆盖了以下场景：
- 品牌创建（成功/失败）
- 品牌更新（成功/失败）
- 品牌删除（单个/批量）
- 分页查询
- 状态管理
- 唯一性校验
- 业务规则验证
