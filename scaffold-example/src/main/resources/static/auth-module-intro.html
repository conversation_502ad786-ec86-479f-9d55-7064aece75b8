<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GWM Scaffold 认证鉴权模块</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.8);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .feature-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .component-list {
            display: grid;
            gap: 20px;
        }
        
        .component-item {
            background: rgba(102, 126, 234, 0.1);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .component-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }
        
        .component-desc {
            color: #7f8c8d;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .annotation-list {
            display: grid;
            gap: 25px;
        }
        
        .annotation-item {
            background: rgba(255,255,255,0.8);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
        }
        
        .annotation-name {
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .annotation-desc {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            overflow-x: auto;
        }
        
        .config-list {
            display: grid;
            gap: 15px;
        }
        
        .config-item {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: flex;
            flex-direction: column;
        }
        
        .config-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .config-desc {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            text-decoration: none;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        
        .highlight-box h3 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .highlight-box p {
            color: #7f8c8d;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 认证鉴权模块</h1>
            <p>提供完整的用户认证、权限控制和数据权限管理功能</p>
        </div>
        
        <div class="content">
            <!-- 核心特性 -->
            <div class="section">
                <h2 class="section-title">🎯 核心特性</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🔐</span>
                        <div class="feature-title">Token认证</div>
                        <div class="feature-desc">基于Token的用户身份认证机制，支持SSO单点登录集成</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🛡️</span>
                        <div class="feature-title">权限控制</div>
                        <div class="feature-desc">基于注解的细粒度权限控制，支持方法级别的权限验证</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">👥</span>
                        <div class="feature-title">角色管理</div>
                        <div class="feature-desc">支持角色级别的访问控制，灵活的角色权限配置</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📊</span>
                        <div class="feature-title">数据权限</div>
                        <div class="feature-desc">行级数据权限控制，根据用户权限自动过滤数据</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🔄</span>
                        <div class="feature-title">SSO集成</div>
                        <div class="feature-desc">支持单点登录系统集成，统一身份认证</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📝</span>
                        <div class="feature-title">链路追踪</div>
                        <div class="feature-desc">完整的用户操作链路追踪，支持异步场景</div>
                    </div>
                </div>
            </div>
            
            <!-- 核心组件 -->
            <div class="section">
                <h2 class="section-title">🔧 核心组件</h2>
                <div class="component-list">
                    <div class="component-item">
                        <div class="component-name">LoginInterceptor - 登录拦截器</div>
                        <div class="component-desc">拦截HTTP请求，进行Token验证和用户信息设置，支持白名单路径过滤</div>
                    </div>
                    <div class="component-item">
                        <div class="component-name">AuthAspect - 权限切面</div>
                        <div class="component-desc">处理@RequirePermission和@RequireRole注解，实现方法级别的权限控制</div>
                    </div>
                    <div class="component-item">
                        <div class="component-name">LoginUserUtil - 用户工具类</div>
                        <div class="component-desc">基于ThreadLocal的用户信息存储和获取，支持异步场景的用户信息传递</div>
                    </div>
                    <div class="component-item">
                        <div class="component-name">AuthenticateService - 认证服务</div>
                        <div class="component-desc">提供Token验证、权限检查等核心认证功能，支持SSO集成</div>
                    </div>
                    <div class="component-item">
                        <div class="component-name">DataPermissionAspect - 数据权限切面</div>
                        <div class="component-desc">处理@DataPermission注解，实现行级数据权限控制</div>
                    </div>
                </div>
            </div>
            
            <!-- 注解说明 -->
            <div class="section">
                <h2 class="section-title">📝 注解说明</h2>
                <div class="annotation-list">
                    <div class="annotation-item">
                        <div class="annotation-name">@RequirePermission</div>
                        <div class="annotation-desc">用于方法级别的权限控制，支持单个或多个权限验证，支持AND/OR逻辑</div>
                        <div class="code-example">@RequirePermission("user:read")
@RequirePermission(value = {"user:read", "user:write"}, logical = RequirePermission.Logical.AND)</div>
                    </div>
                    <div class="annotation-item">
                        <div class="annotation-name">@RequireRole</div>
                        <div class="annotation-desc">用于方法级别的角色控制，支持单个或多个角色验证，支持AND/OR逻辑</div>
                        <div class="code-example">@RequireRole("admin")
@RequireRole(value = {"admin", "manager"}, logical = RequireRole.Logical.OR)</div>
                    </div>
                    <div class="annotation-item">
                        <div class="annotation-name">@DataPermission</div>
                        <div class="annotation-desc">用于行级数据权限控制，根据用户权限自动过滤数据，支持用户级、部门级等权限类型</div>
                        <div class="code-example">@DataPermission(Type.USER)
@DataPermission(Type.DEPT)</div>
                    </div>
                </div>
            </div>
            
            <!-- 配置说明 -->
            <div class="section">
                <h2 class="section-title">⚙️ 配置说明</h2>
                <div class="config-list">
                    <div class="config-item">
                        <div class="config-name">scaffold.auth.enabled</div>
                        <div class="config-desc">是否启用认证模块（默认：true）</div>
                    </div>
                    <div class="config-item">
                        <div class="config-name">scaffold.auth.white-list</div>
                        <div class="config-desc">白名单路径列表，无需认证即可访问</div>
                    </div>
                    <div class="config-item">
                        <div class="config-name">scaffold.auth.sso.platform-code</div>
                        <div class="config-desc">SSO平台编码，用于单点登录集成</div>
                    </div>
                    <div class="config-item">
                        <div class="config-name">scaffold.data.permission.enabled</div>
                        <div class="config-desc">是否启用数据权限（默认：false）</div>
                    </div>
                </div>
            </div>
            
            <!-- 突出显示框 -->
            <div class="highlight-box">
                <h3>🛡️ 企业级安全保障</h3>
                <p>完整的认证鉴权体系，为您的应用提供全方位的安全保护！</p>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="/test/auth/user-info" class="btn">
                    🔐 测试认证功能
                </a>
                <a href="/api/scaffold/docs" class="btn btn-outline">
                    📚 查看完整文档
                </a>
                <a href="/" class="btn btn-outline">
                    🏠 返回首页
                </a>
            </div>
        </div>
    </div>
</body>
</html>
