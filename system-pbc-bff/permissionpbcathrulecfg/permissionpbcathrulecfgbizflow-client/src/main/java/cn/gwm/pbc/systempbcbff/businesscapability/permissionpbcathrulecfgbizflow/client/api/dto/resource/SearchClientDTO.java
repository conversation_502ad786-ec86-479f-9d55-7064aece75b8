package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource;

import com.alibaba.bizworks.core.specification.*;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * <AUTHOR>
 */
@StructureObject(name = "SearchClientDTO", desc = "")
@Data
public class SearchClientDTO {

    /**
     *  类别（0-菜单管理，1-数据管理）
     *  category
     */
    @Field(name = "类别（0-菜单管理，1-数据管理）", desc = "类别（0-菜单管理，1-数据管理）category")
    private Integer category;

    /**
     *  资源名称
     */
    @Field(name = "资源名称", desc = "资源名称")
    private String name;

}
