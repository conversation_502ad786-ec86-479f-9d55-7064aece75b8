package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("规则列表请求参数对象")
public class RuleListFormDTO implements Serializable {

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer page ;

    /**
     * 每页显示几条
     */
    @ApiModelProperty(value = "每页显示几条")
    private Integer size ;

    /**
     * 角色ID（虚拟角色）
     */
    @ApiModelProperty("角色ID")
    @JsonProperty(value = "role_id")
    private Long roleSetId;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    @JsonProperty(value = "rule_name")
    private String ruleName;

    /**
     * 是否启用（0禁用，1启用，默认1）
     */
    @ApiModelProperty("是否启用（0禁用，1启用，默认1）")
    private Integer enable;
}