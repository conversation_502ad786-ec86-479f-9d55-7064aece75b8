-- GWM Scaffold 示例应用初始化数据

-- 创建用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    mobile VARCHAR(20) COMMENT '手机号',
    gender TINYINT COMMENT '性别 1:男 2:女',
    age INT COMMENT '年龄',
    department_id BIGINT COMMENT '部门ID',
    department_name VARCHAR(100) COMMENT '部门名称',
    position VARCHAR(100) COMMENT '职位',
    status TINYINT DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    avatar VARCHAR(500) COMMENT '头像URL',
    remark VARCHAR(500) COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) DEFAULT 'system' COMMENT '创建人工号',
    create_user_name VARCHAR(50) DEFAULT '系统' COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) DEFAULT 'system' COMMENT '更新人工号',
    update_user_name VARCHAR(50) DEFAULT '系统' COMMENT '更新人姓名',
    version INT DEFAULT 0 COMMENT '版本号',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记 0:未删除 1:已删除'
);

-- 创建索引
CREATE INDEX idx_user_username ON sys_user(username);
CREATE INDEX idx_user_email ON sys_user(email);
CREATE INDEX idx_user_mobile ON sys_user(mobile);
CREATE INDEX idx_user_department ON sys_user(department_id);
CREATE INDEX idx_user_status ON sys_user(status);
CREATE INDEX idx_user_deleted ON sys_user(deleted);

-- 插入示例数据
INSERT INTO sys_user (username, password, real_name, email, mobile, gender, age, department_id, department_name, position, status, remark) VALUES
('admin', '123456_encrypted', '管理员', '<EMAIL>', '13800000001', 1, 30, 1, '管理部', '系统管理员', 1, '系统管理员账号'),
('zhangsan', '123456_encrypted', '张三', '<EMAIL>', '13800000002', 1, 25, 2, '技术部', 'Java开发工程师', 1, 'Java后端开发'),
('lisi', '123456_encrypted', '李四', '<EMAIL>', '13800000003', 1, 28, 2, '技术部', '前端开发工程师', 1, 'Vue前端开发'),
('wangwu', '123456_encrypted', '王五', '<EMAIL>', '13800000004', 1, 32, 2, '技术部', '架构师', 1, '系统架构设计'),
('zhaoliu', '123456_encrypted', '赵六', '<EMAIL>', '13800000005', 2, 26, 3, '产品部', '产品经理', 1, '产品需求管理'),
('sunqi', '123456_encrypted', '孙七', '<EMAIL>', '13800000006', 2, 24, 3, '产品部', 'UI设计师', 1, '界面设计'),
('zhouba', '123456_encrypted', '周八', '<EMAIL>', '13800000007', 1, 29, 4, '测试部', '测试工程师', 1, '软件测试'),
('wujiu', '123456_encrypted', '吴九', '<EMAIL>', '13800000008', 2, 27, 4, '测试部', '自动化测试工程师', 1, '自动化测试'),
('zhengshi', '123456_encrypted', '郑十', '<EMAIL>', '13800000009', 1, 35, 5, '运维部', '运维工程师', 1, '系统运维'),
('liuyi', '123456_encrypted', '刘一', '<EMAIL>', '13800000010', 2, 23, 6, '人事部', 'HR专员', 1, '人力资源管理'),
('chener', '123456_encrypted', '陈二', '<EMAIL>', '13800000011', 1, 31, 7, '财务部', '财务专员', 1, '财务管理'),
('yangsan', '123456_encrypted', '杨三', '<EMAIL>', '13800000012', 2, 28, 8, '市场部', '市场专员', 1, '市场推广'),
('xuesi', '123456_encrypted', '徐四', '<EMAIL>', '13800000013', 1, 26, 8, '市场部', '销售经理', 1, '销售管理'),
('huangwu', '123456_encrypted', '黄五', '<EMAIL>', '13800000014', 2, 25, 9, '客服部', '客服专员', 1, '客户服务'),
('linliu', '123456_encrypted', '林六', '<EMAIL>', '13800000015', 1, 30, 10, '法务部', '法务专员', 1, '法律事务'),
('disabled_user', '123456_encrypted', '禁用用户', '<EMAIL>', '13800000016', 1, 25, 2, '技术部', '开发工程师', 0, '已禁用的测试账号');

-- 插入更多测试数据（用于分页测试）
INSERT INTO sys_user (username, password, real_name, email, mobile, gender, age, department_id, department_name, position, status, remark) VALUES
('test001', '123456_encrypted', '测试用户001', '<EMAIL>', '13900000001', 1, 25, 2, '技术部', '开发工程师', 1, '测试数据'),
('test002', '123456_encrypted', '测试用户002', '<EMAIL>', '13900000002', 2, 26, 2, '技术部', '开发工程师', 1, '测试数据'),
('test003', '123456_encrypted', '测试用户003', '<EMAIL>', '13900000003', 1, 27, 3, '产品部', '产品经理', 1, '测试数据'),
('test004', '123456_encrypted', '测试用户004', '<EMAIL>', '13900000004', 2, 28, 3, '产品部', '产品经理', 1, '测试数据'),
('test005', '123456_encrypted', '测试用户005', '<EMAIL>', '13900000005', 1, 29, 4, '测试部', '测试工程师', 1, '测试数据'),
('test006', '123456_encrypted', '测试用户006', '<EMAIL>', '13900000006', 2, 30, 4, '测试部', '测试工程师', 1, '测试数据'),
('test007', '123456_encrypted', '测试用户007', '<EMAIL>', '13900000007', 1, 31, 5, '运维部', '运维工程师', 1, '测试数据'),
('test008', '123456_encrypted', '测试用户008', '<EMAIL>', '13900000008', 2, 32, 5, '运维部', '运维工程师', 1, '测试数据'),
('test009', '123456_encrypted', '测试用户009', '<EMAIL>', '13900000009', 1, 33, 6, '人事部', 'HR专员', 1, '测试数据'),
('test010', '123456_encrypted', '测试用户010', '<EMAIL>', '13900000010', 2, 34, 6, '人事部', 'HR专员', 1, '测试数据');

-- 创建产品表
CREATE TABLE IF NOT EXISTS sys_product (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    product_name_en VARCHAR(200) COMMENT '产品英文名称',
    category_id BIGINT COMMENT '分类ID',
    category_name VARCHAR(100) COMMENT '分类名称',
    brand_id BIGINT COMMENT '品牌ID',
    brand_name VARCHAR(100) COMMENT '品牌名称',
    product_type TINYINT DEFAULT 1 COMMENT '产品类型 1:实物商品 2:虚拟商品 3:服务商品',
    unit VARCHAR(20) DEFAULT '件' COMMENT '计量单位',
    weight DECIMAL(10,3) COMMENT '重量(kg)',
    volume DECIMAL(10,3) COMMENT '体积(立方米)',
    length DECIMAL(10,2) COMMENT '长度(cm)',
    width DECIMAL(10,2) COMMENT '宽度(cm)',
    height DECIMAL(10,2) COMMENT '高度(cm)',
    cost_price DECIMAL(10,2) COMMENT '成本价',
    sale_price DECIMAL(10,2) NOT NULL COMMENT '销售价',
    market_price DECIMAL(10,2) COMMENT '市场价',
    min_stock INT DEFAULT 0 COMMENT '最小库存',
    max_stock INT DEFAULT 9999 COMMENT '最大库存',
    current_stock INT DEFAULT 0 COMMENT '当前库存',
    sales_count INT DEFAULT 0 COMMENT '销售数量',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    status TINYINT DEFAULT 1 COMMENT '状态 1:上架 2:下架 3:停产',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热销 0:否 1:是',
    is_new TINYINT DEFAULT 0 COMMENT '是否新品 0:否 1:是',
    is_recommend TINYINT DEFAULT 0 COMMENT '是否推荐 0:否 1:是',
    sort_order INT DEFAULT 0 COMMENT '排序号',
    main_image VARCHAR(500) COMMENT '主图片URL',
    image_list TEXT COMMENT '图片列表JSON',
    video_url VARCHAR(500) COMMENT '视频URL',
    description TEXT COMMENT '产品描述',
    specification TEXT COMMENT '规格参数JSON',
    features TEXT COMMENT '产品特色',
    service_info TEXT COMMENT '售后服务信息',
    keywords VARCHAR(500) COMMENT '关键词',
    meta_title VARCHAR(200) COMMENT 'SEO标题',
    meta_description VARCHAR(500) COMMENT 'SEO描述',
    supplier_id BIGINT COMMENT '供应商ID',
    supplier_name VARCHAR(100) COMMENT '供应商名称',
    warranty_period INT DEFAULT 0 COMMENT '保修期(月)',
    shelf_life INT COMMENT '保质期(天)',
    production_date DATE COMMENT '生产日期',
    expiry_date DATE COMMENT '过期日期',
    barcode VARCHAR(50) COMMENT '条形码',
    qr_code VARCHAR(500) COMMENT '二维码',
    remark VARCHAR(500) COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) DEFAULT 'system' COMMENT '创建人工号',
    create_user_name VARCHAR(50) DEFAULT '系统' COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) DEFAULT 'system' COMMENT '更新人工号',
    update_user_name VARCHAR(50) DEFAULT '系统' COMMENT '更新人姓名',
    version INT DEFAULT 0 COMMENT '版本号',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记 0:未删除 1:已删除'
) COMMENT '产品信息表';

-- 创建产品表索引
CREATE INDEX idx_product_code ON sys_product(product_code);
CREATE INDEX idx_product_name ON sys_product(product_name);
CREATE INDEX idx_product_category ON sys_product(category_id);
CREATE INDEX idx_product_brand ON sys_product(brand_id);
CREATE INDEX idx_product_type ON sys_product(product_type);
CREATE INDEX idx_product_status ON sys_product(status);
CREATE INDEX idx_product_price ON sys_product(sale_price);
CREATE INDEX idx_product_stock ON sys_product(current_stock);
CREATE INDEX idx_product_sort ON sys_product(sort_order);
CREATE INDEX idx_product_hot ON sys_product(is_hot);
CREATE INDEX idx_product_new ON sys_product(is_new);
CREATE INDEX idx_product_recommend ON sys_product(is_recommend);
CREATE INDEX idx_product_supplier ON sys_product(supplier_id);
CREATE INDEX idx_product_create_time ON sys_product(create_time);
CREATE INDEX idx_product_deleted ON sys_product(deleted);

-- 插入产品示例数据
INSERT INTO sys_product (product_code, product_name, product_name_en, category_id, category_name, brand_id, brand_name, product_type, unit, weight, volume, length, width, height, cost_price, sale_price, market_price, min_stock, max_stock, current_stock, sales_count, view_count, status, is_hot, is_new, is_recommend, sort_order, main_image, description, specification, features, service_info, keywords, meta_title, meta_description, supplier_id, supplier_name, warranty_period, barcode, remark) VALUES
('PRD001', 'iPhone 15 Pro Max 256GB 深空黑色', 'iPhone 15 Pro Max 256GB Space Black', 1, '智能手机', 1, 'Apple', 1, '台', 0.221, 0.000077, 16.0, 7.7, 0.8, 8500.00, 9999.00, 10999.00, 5, 100, 25, 156, 2580, 1, 1, 1, 1, 100, 'https://example.com/images/iphone15promax.jpg', 'iPhone 15 Pro Max，搭载A17 Pro芯片，钛金属设计，专业级摄像头系统', '{"屏幕":"6.7英寸Super Retina XDR显示屏","芯片":"A17 Pro芯片","存储":"256GB","摄像头":"4800万像素主摄像头"}', '钛金属设计，A17 Pro芯片，专业摄影系统，动作按钮', '1年有限保修，支持AppleCare+', 'iPhone,苹果,智能手机,A17 Pro', 'iPhone 15 Pro Max 256GB 深空黑色 - 官方正品', '苹果iPhone 15 Pro Max，搭载A17 Pro芯片，256GB存储，深空黑色，专业摄影系统', 1, 'Apple官方授权经销商', 12, '194253000000', '苹果旗舰手机'),
('PRD002', 'MacBook Pro 14英寸 M3芯片 512GB 深空灰色', 'MacBook Pro 14-inch M3 512GB Space Gray', 2, '笔记本电脑', 1, 'Apple', 1, '台', 1.55, 0.003, 31.26, 22.12, 1.55, 14000.00, 15999.00, 17999.00, 3, 50, 12, 89, 1250, 1, 1, 0, 1, 95, 'https://example.com/images/macbookpro14.jpg', 'MacBook Pro 14英寸，搭载M3芯片，专业级性能，适合创意工作者', '{"屏幕":"14.2英寸Liquid Retina XDR显示屏","芯片":"M3芯片","内存":"8GB统一内存","存储":"512GB SSD"}', 'M3芯片，Liquid Retina XDR显示屏，专业级性能，长达18小时电池续航', '1年有限保修，支持AppleCare+', 'MacBook,苹果,笔记本,M3芯片', 'MacBook Pro 14英寸 M3芯片 512GB - 专业笔记本', '苹果MacBook Pro 14英寸，M3芯片，512GB存储，专业级性能笔记本电脑', 1, 'Apple官方授权经销商', 12, '194253000001', '专业创意笔记本'),
('PRD003', 'iPad Air 第5代 64GB WiFi版 星光色', 'iPad Air 5th Gen 64GB WiFi Starlight', 3, '平板电脑', 1, 'Apple', 1, '台', 0.461, 0.001, 24.76, 17.85, 0.61, 3800.00, 4399.00, 4999.00, 10, 200, 45, 234, 1890, 1, 0, 1, 1, 90, 'https://example.com/images/ipadair5.jpg', 'iPad Air第5代，搭载M1芯片，轻薄设计，适合学习和娱乐', '{"屏幕":"10.9英寸Liquid Retina显示屏","芯片":"M1芯片","存储":"64GB","摄像头":"1200万像素广角摄像头"}', 'M1芯片，全面屏设计，支持Apple Pencil，Touch ID', '1年有限保修，支持AppleCare+', 'iPad,苹果,平板,M1芯片', 'iPad Air 第5代 64GB WiFi版 - 轻薄平板', '苹果iPad Air第5代，M1芯片，64GB存储，星光色，轻薄便携平板电脑', 1, 'Apple官方授权经销商', 12, '194253000002', '轻薄便携平板'),
('PRD004', 'AirPods Pro 第2代 USB-C充电盒', 'AirPods Pro 2nd Gen USB-C', 4, '耳机音响', 1, 'Apple', 1, '副', 0.0506, 0.000021, 3.05, 2.17, 2.4, 1600.00, 1899.00, 2199.00, 20, 500, 128, 567, 3450, 1, 1, 0, 1, 85, 'https://example.com/images/airpodspro2.jpg', 'AirPods Pro第2代，主动降噪，空间音频，USB-C充电盒', '{"芯片":"H2芯片","降噪":"主动降噪","续航":"最长6小时(开启降噪)","充电":"USB-C充电盒"}', 'H2芯片，主动降噪，空间音频，自适应透明模式', '1年有限保修', 'AirPods,苹果,耳机,降噪', 'AirPods Pro 第2代 USB-C - 主动降噪耳机', '苹果AirPods Pro第2代，H2芯片，主动降噪，USB-C充电盒，无线耳机', 1, 'Apple官方授权经销商', 12, '194253000003', '主动降噪无线耳机'),
('PRD005', 'Apple Watch Series 9 GPS 45mm 午夜色铝金属表壳', 'Apple Watch Series 9 GPS 45mm Midnight', 5, '智能手表', 1, 'Apple', 1, '块', 0.0389, 0.000014, 4.5, 3.8, 1.05, 2800.00, 3199.00, 3599.00, 15, 300, 67, 345, 2100, 1, 0, 1, 1, 80, 'https://example.com/images/applewatch9.jpg', 'Apple Watch Series 9，S9芯片，健康监测，运动追踪', '{"屏幕":"45mm Retina显示屏","芯片":"S9 SiP芯片","传感器":"血氧传感器、心电图、心率传感器","防水":"50米防水"}', 'S9芯片，血氧监测，心电图，摔倒检测，紧急SOS', '1年有限保修，支持AppleCare+', 'Apple Watch,苹果,智能手表,健康', 'Apple Watch Series 9 GPS 45mm - 智能手表', '苹果Apple Watch Series 9，S9芯片，45mm，午夜色，健康监测智能手表', 1, 'Apple官方授权经销商', 12, '194253000004', '健康监测智能手表'),
('PRD006', 'Magic Keyboard 妙控键盘 中文版', 'Magic Keyboard Chinese', 6, '键盘鼠标', 1, 'Apple', 1, '个', 0.231, 0.0004, 27.9, 11.49, 1.09, 900.00, 1099.00, 1299.00, 25, 400, 89, 123, 890, 1, 0, 0, 0, 75, 'https://example.com/images/magickeyboard.jpg', 'Magic Keyboard妙控键盘，无线连接，中文布局，适配Mac', '{"连接":"无线蓝牙","布局":"中文布局","兼容":"Mac系列产品","电池":"内置可充电锂离子电池"}', '无线蓝牙连接，中文布局，剪刀式按键结构，内置电池', '1年有限保修', 'Magic Keyboard,苹果,键盘,无线', 'Magic Keyboard 妙控键盘 中文版 - 无线键盘', '苹果Magic Keyboard妙控键盘，无线蓝牙，中文布局，适配Mac系列', 1, 'Apple官方授权经销商', 12, '194253000005', '无线蓝牙键盘'),
('PRD007', 'Magic Mouse 妙控鼠标 白色', 'Magic Mouse White', 6, '键盘鼠标', 1, 'Apple', 1, '个', 0.099, 0.00011, 11.35, 5.71, 2.16, 550.00, 649.00, 799.00, 30, 600, 156, 234, 1560, 1, 0, 0, 0, 70, 'https://example.com/images/magicmouse.jpg', 'Magic Mouse妙控鼠标，多点触控表面，无线连接', '{"连接":"无线蓝牙","触控":"多点触控表面","兼容":"Mac系列产品","电池":"内置可充电锂离子电池"}', '多点触控表面，无线蓝牙连接，手势操作，内置电池', '1年有限保修', 'Magic Mouse,苹果,鼠标,无线', 'Magic Mouse 妙控鼠标 白色 - 无线鼠标', '苹果Magic Mouse妙控鼠标，多点触控，无线蓝牙，白色，适配Mac系列', 1, 'Apple官方授权经销商', 12, '194253000006', '多点触控无线鼠标'),
('PRD008', 'Studio Display 27英寸 5K Retina显示器', 'Studio Display 27-inch 5K Retina', 7, '显示器', 1, 'Apple', 1, '台', 6.3, 0.047, 62.3, 18.8, 47.4, 10000.00, 11499.00, 12999.00, 2, 30, 8, 45, 567, 1, 1, 1, 1, 65, 'https://example.com/images/studiodisplay.jpg', 'Studio Display 27英寸5K Retina显示器，专业级色彩，适合创意工作', '{"屏幕":"27英寸5K Retina显示屏","分辨率":"5120x2880","亮度":"600尼特","色域":"P3广色域"}', '5K Retina显示屏，P3广色域，600尼特亮度，内置摄像头和扬声器', '1年有限保修', 'Studio Display,苹果,显示器,5K', 'Studio Display 27英寸 5K - 专业显示器', '苹果Studio Display，27英寸5K Retina显示器，专业级色彩，创意工作首选', 1, 'Apple官方授权经销商', 12, '194253000007', '专业级5K显示器'),
('PRD009', 'AirTag 4件装', 'AirTag 4-Pack', 8, '智能配件', 1, 'Apple', 1, '套', 0.044, 0.000003, 3.19, 3.19, 0.8, 750.00, 899.00, 999.00, 50, 1000, 234, 456, 2340, 1, 0, 1, 0, 60, 'https://example.com/images/airtag4pack.jpg', 'AirTag 4件装，精确查找，隐私保护，防丢神器', '{"芯片":"U1芯片","定位":"精确查找","防水":"IP67级防水","电池":"可更换CR2032电池"}', 'U1芯片，精确查找，隐私保护，IP67防水，可更换电池', '1年有限保修', 'AirTag,苹果,防丢,定位', 'AirTag 4件装 - 精确查找防丢器', '苹果AirTag 4件装，U1芯片，精确查找，隐私保护，防丢神器', 1, 'Apple官方授权经销商', 12, '194253000008', '精确查找防丢器'),
('PRD010', 'HomePod mini 深空灰色', 'HomePod mini Space Gray', 4, '耳机音响', 1, 'Apple', 1, '台', 0.345, 0.0008, 8.43, 9.7, 9.7, 650.00, 749.00, 899.00, 40, 800, 167, 289, 1890, 1, 0, 0, 1, 55, 'https://example.com/images/homepodmini.jpg', 'HomePod mini智能音箱，360度音效，Siri语音助手', '{"芯片":"S5芯片","音效":"360度音效","语音":"Siri语音助手","连接":"WiFi、蓝牙"}', 'S5芯片，360度音效，Siri语音助手，智能家居控制', '1年有限保修', 'HomePod,苹果,音箱,智能', 'HomePod mini 深空灰色 - 智能音箱', '苹果HomePod mini，S5芯片，360度音效，深空灰色，智能语音音箱', 1, 'Apple官方授权经销商', 12, '194253000009', '智能语音音箱');

-- 创建订单表
CREATE TABLE IF NOT EXISTS sys_order (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    customer_id BIGINT COMMENT '客户ID',
    customer_name VARCHAR(100) NOT NULL COMMENT '客户姓名',
    customer_phone VARCHAR(20) COMMENT '客户电话',
    customer_email VARCHAR(100) COMMENT '客户邮箱',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    product_sku VARCHAR(100) COMMENT '商品SKU',
    quantity INT NOT NULL DEFAULT 1 COMMENT '商品数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    actual_amount DECIMAL(10,2) NOT NULL COMMENT '实际支付金额',
    order_status TINYINT DEFAULT 1 COMMENT '订单状态 1:待支付 2:已支付 3:已发货 4:已完成 5:已取消',
    payment_method TINYINT COMMENT '支付方式 1:支付宝 2:微信 3:银行卡 4:现金',
    payment_time DATETIME COMMENT '支付时间',
    shipping_address VARCHAR(500) COMMENT '收货地址',
    shipping_phone VARCHAR(20) COMMENT '收货电话',
    shipping_name VARCHAR(100) COMMENT '收货人姓名',
    shipping_time DATETIME COMMENT '发货时间',
    delivery_time DATETIME COMMENT '送达时间',
    remark VARCHAR(500) COMMENT '订单备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) DEFAULT 'system' COMMENT '创建人工号',
    create_user_name VARCHAR(50) DEFAULT '系统' COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) DEFAULT 'system' COMMENT '更新人工号',
    update_user_name VARCHAR(50) DEFAULT '系统' COMMENT '更新人姓名',
    version INT DEFAULT 0 COMMENT '版本号',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记 0:未删除 1:已删除'
);

-- 创建订单表索引
CREATE INDEX idx_order_no ON sys_order(order_no);
CREATE INDEX idx_order_customer_id ON sys_order(customer_id);
CREATE INDEX idx_order_customer_name ON sys_order(customer_name);
CREATE INDEX idx_order_status ON sys_order(order_status);
CREATE INDEX idx_order_create_time ON sys_order(create_time);
CREATE INDEX idx_order_deleted ON sys_order(deleted);

-- 插入订单示例数据
INSERT INTO sys_order (order_no, customer_id, customer_name, customer_phone, customer_email, product_name, product_sku, quantity, unit_price, total_amount, discount_amount, actual_amount, order_status, payment_method, payment_time, shipping_address, shipping_phone, shipping_name, shipping_time, delivery_time, remark) VALUES
('ORD202501150001', 1, '张三', '13800138001', '<EMAIL>', 'iPhone 15 Pro Max 256GB 深空黑色', 'IPHONE15PM256GB-BLACK', 1, 9999.00, 9999.00, 500.00, 9499.00, 4, 1, '2025-01-10 10:30:00', '北京市朝阳区建国门外大街1号国贸大厦A座1001室', '13800138001', '张三', '2025-01-11 09:00:00', '2025-01-12 14:30:00', '请小心轻放'),
('ORD202501150002', 2, '李四', '13800138002', '<EMAIL>', 'MacBook Pro 14英寸 M3芯片 512GB 深空灰色', 'MBP14M3512GB-GRAY', 1, 15999.00, 15999.00, 1000.00, 14999.00, 3, 2, '2025-01-11 14:20:00', '上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼', '13800138002', '李四', '2025-01-12 16:00:00', NULL, '办公用品，请尽快发货'),
('ORD202501150003', 3, '王五', '13800138003', '<EMAIL>', 'iPad Air 第5代 64GB WiFi版 星光色', 'IPADAIR5-64GB-STARLIGHT', 2, 4399.00, 8798.00, 200.00, 8598.00, 2, 3, '2025-01-12 16:45:00', '广州市天河区珠江新城花城大道85号高德置地广场A座3001室', '13800138003', '王五', NULL, NULL, '买两台给孩子学习用'),
('ORD202501150004', 4, '赵六', '13800138004', '<EMAIL>', 'AirPods Pro 第2代 USB-C充电盒', 'AIRPODSPRO2-USBC', 1, 1899.00, 1899.00, 0.00, 1899.00, 1, NULL, NULL, '深圳市南山区科技园南区深南大道9988号', '13800138004', '赵六', NULL, NULL, '白色款'),
('ORD202501150005', 5, '孙七', '13800138005', '<EMAIL>', 'Apple Watch Series 9 GPS 45mm 午夜色铝金属表壳', 'AWSERIES9-45MM-MIDNIGHT', 1, 3199.00, 3199.00, 100.00, 3099.00, 4, 1, '2025-01-09 11:15:00', '杭州市西湖区文三路259号昌地火炬大厦1号楼10楼', '13800138005', '孙七', '2025-01-10 08:30:00', '2025-01-11 10:20:00', '运动表带'),
('ORD202501150006', 6, '周八', '13800138006', '<EMAIL>', 'Magic Keyboard 妙控键盘 中文版', 'MAGICKEYBOARD-CN', 1, 1099.00, 1099.00, 50.00, 1049.00, 3, 2, '2025-01-13 09:30:00', '成都市高新区天府大道中段1388号美年广场A座2501室', '13800138006', '周八', '2025-01-14 10:00:00', NULL, '配合MacBook使用'),
('ORD202501150007', 7, '吴九', '13800138007', '<EMAIL>', 'Magic Mouse 妙控鼠标 白色', 'MAGICMOUSE-WHITE', 2, 649.00, 1298.00, 0.00, 1298.00, 2, 1, '2025-01-14 15:20:00', '武汉市江汉区建设大道568号新世界国贸大厦I座1801室', '13800138007', '吴九', NULL, NULL, '办公室用，买两个'),
('ORD202501150008', 8, '郑十', '13800138008', '<EMAIL>', 'Studio Display 27英寸 5K Retina显示器', 'STUDIODISPLAY-27-5K', 1, 11499.00, 11499.00, 500.00, 10999.00, 1, NULL, NULL, '西安市雁塔区高新四路15号安泰大厦B座1205室', '13800138008', '郑十', NULL, NULL, '设计工作用'),
('ORD202501150009', 9, '刘一', '13800138009', '<EMAIL>', 'AirTag 4件装', 'AIRTAG-4PACK', 1, 899.00, 899.00, 0.00, 899.00, 4, 3, '2025-01-08 13:40:00', '南京市建邺区江东中路359号国睿大厦1号楼15楼', '13800138009', '刘一', '2025-01-09 14:00:00', '2025-01-10 16:30:00', '防丢神器'),
('ORD202501150010', 10, '陈二', '13800138010', '<EMAIL>', 'HomePod mini 深空灰色', 'HOMEPODMINI-GRAY', 2, 749.00, 1498.00, 100.00, 1398.00, 3, 2, '2025-01-13 20:10:00', '天津市和平区南京路189号津汇广场1座2801室', '13800138010', '陈二', '2025-01-14 11:30:00', NULL, '智能音箱，买两个放客厅和卧室'),
('ORD202501150011', 1, '张三', '13800138001', '<EMAIL>', 'iPhone 15 128GB 粉色', 'IPHONE15-128GB-PINK', 1, 5999.00, 5999.00, 300.00, 5699.00, 5, 1, '2025-01-05 16:25:00', '北京市朝阳区建国门外大街1号国贸大厦A座1001室', '13800138001', '张三', '2025-01-06 09:15:00', '2025-01-07 11:45:00', '给女朋友买的，已取消重新下单'),
('ORD202501150012', 2, '李四', '13800138002', '<EMAIL>', 'iPad Pro 11英寸 M4芯片 256GB WiFi版 深空黑色', 'IPADPRO11M4-256GB-BLACK', 1, 7199.00, 7199.00, 400.00, 6799.00, 2, 3, '2025-01-14 12:50:00', '上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼', '13800138002', '李四', NULL, NULL, '工作用平板'),
('ORD202501150013', 11, '杨三', '13800138011', '<EMAIL>', 'Mac Studio M2 Max芯片 512GB SSD', 'MACSTUDIO-M2MAX-512GB', 1, 15999.00, 15999.00, 800.00, 15199.00, 1, NULL, NULL, '重庆市渝北区新牌坊嘉州路15号龙湖时代天街C馆3001室', '13800138011', '杨三', NULL, NULL, '视频剪辑工作站'),
('ORD202501150014', 12, '徐四', '13800138012', '<EMAIL>', 'Apple TV 4K 第3代 128GB', 'APPLETV4K-128GB', 1, 1499.00, 1499.00, 0.00, 1499.00, 4, 2, '2025-01-12 19:30:00', '青岛市市南区香港中路40号数码港大厦1801室', '13800138012', '徐四', '2025-01-13 08:45:00', '2025-01-14 15:20:00', '家庭娱乐中心'),
('ORD202501150015', 13, '黄五', '13800138013', '<EMAIL>', 'Mac mini M2芯片 256GB SSD', 'MACMINI-M2-256GB', 1, 4699.00, 4699.00, 200.00, 4499.00, 3, 1, '2025-01-13 14:15:00', '大连市中山区人民路15号国际金融大厦2501室', '13800138013', '黄五', '2025-01-14 16:20:00', NULL, '小型工作站');


-- 创建品牌表
CREATE TABLE IF NOT EXISTS t_brand (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    english_name VARCHAR(100),
    country VARCHAR(50) NOT NULL,
    founded_year SMALLINT,
    website VARCHAR(255),
    logo_url VARCHAR(500),
    description TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    sort_weight INT DEFAULT 100,
    deleted TINYINT DEFAULT 0,
    deleted_time DATETIME,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_user_code VARCHAR(50) NOT NULL,
    create_user_name VARCHAR(50) NOT NULL,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_user_code VARCHAR(50),
    update_user_name VARCHAR(50)
);

-- 创建品牌表索引
CREATE INDEX IF NOT EXISTS uk_brand_name ON t_brand(name, deleted);
CREATE INDEX IF NOT EXISTS uk_brand_english_name ON t_brand(english_name, deleted);
CREATE INDEX IF NOT EXISTS idx_brand_country ON t_brand(country);
CREATE INDEX IF NOT EXISTS idx_brand_status ON t_brand(status);
CREATE INDEX IF NOT EXISTS idx_brand_create_time ON t_brand(create_time);


-- 创建车系表
CREATE TABLE IF NOT EXISTS t_series (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    english_name VARCHAR(100),
    brand_id BIGINT NOT NULL,
    type VARCHAR(20) NOT NULL,
    level VARCHAR(10) NOT NULL,
    launch_time DATE NOT NULL,
    discontinue_time DATE,
    min_price DECIMAL(8,2),
    max_price DECIMAL(8,2),
    image_url VARCHAR(500),
    description TEXT,
    status VARCHAR(20) DEFAULT 'COMING_SOON',
    sort_weight INT DEFAULT 100,
    deleted TINYINT DEFAULT 0,
    deleted_time DATETIME,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_user_code VARCHAR(50) NOT NULL,
    create_user_name VARCHAR(50) NOT NULL,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_user_code VARCHAR(50),
    update_user_name VARCHAR(50)
);

-- 创建车系表索引
CREATE INDEX IF NOT EXISTS uk_series_brand_name ON t_series(brand_id, name, deleted);
CREATE INDEX IF NOT EXISTS uk_series_english_name ON t_series(english_name, deleted);
CREATE INDEX IF NOT EXISTS idx_series_brand_id ON t_series(brand_id);
CREATE INDEX IF NOT EXISTS idx_series_type ON t_series(type);
CREATE INDEX IF NOT EXISTS idx_series_level ON t_series(level);
CREATE INDEX IF NOT EXISTS idx_series_status ON t_series(status);
CREATE INDEX IF NOT EXISTS idx_series_launch_time ON t_series(launch_time);
CREATE INDEX IF NOT EXISTS idx_series_create_time ON t_series(create_time);

-- 添加外键约束
ALTER TABLE t_series ADD CONSTRAINT IF NOT EXISTS fk_series_brand FOREIGN KEY (brand_id) REFERENCES t_brand(id);


-- 创建车型表
CREATE TABLE IF NOT EXISTS t_model (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    series_id BIGINT NOT NULL,
    year_model SMALLINT NOT NULL,
    displacement DECIMAL(3,1),
    max_power INT,
    max_torque INT,
    transmission VARCHAR(20) NOT NULL,
    gear_count TINYINT,
    drive_type VARCHAR(10) NOT NULL,
    fuel_type VARCHAR(20) NOT NULL,
    body_structure VARCHAR(20) NOT NULL,
    seat_count TINYINT NOT NULL,
    length INT,
    width INT,
    height INT,
    wheelbase INT,
    curb_weight INT,
    tank_capacity INT,
    fuel_consumption DECIMAL(3,1),
    guide_price DECIMAL(8,2) NOT NULL,
    launch_time DATE NOT NULL,
    discontinue_time DATE,
    image_urls CLOB,
    config_details CLOB,
    status VARCHAR(20) DEFAULT 'COMING_SOON',
    sort_weight INT DEFAULT 100,
    deleted TINYINT DEFAULT 0,
    deleted_time DATETIME,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_user_code VARCHAR(50) NOT NULL,
    create_user_name VARCHAR(50) NOT NULL,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_user_code VARCHAR(50),
    update_user_name VARCHAR(50)
);

-- 创建车型表索引
CREATE INDEX IF NOT EXISTS uk_model_series_name ON t_model(series_id, name, deleted);
CREATE INDEX IF NOT EXISTS idx_model_series_id ON t_model(series_id);
CREATE INDEX IF NOT EXISTS idx_model_year ON t_model(year_model);
CREATE INDEX IF NOT EXISTS idx_model_price ON t_model(guide_price);
CREATE INDEX IF NOT EXISTS idx_model_fuel_type ON t_model(fuel_type);
CREATE INDEX IF NOT EXISTS idx_model_drive_type ON t_model(drive_type);
CREATE INDEX IF NOT EXISTS idx_model_status ON t_model(status);
CREATE INDEX IF NOT EXISTS idx_model_launch_time ON t_model(launch_time);
CREATE INDEX IF NOT EXISTS idx_model_create_time ON t_model(create_time);

-- 添加外键约束
ALTER TABLE t_model ADD CONSTRAINT IF NOT EXISTS fk_model_series FOREIGN KEY (series_id) REFERENCES t_series(id);

-- 汽车品牌、车系、车型测试数据
-- 注意：这些数据已经兼容H2数据库语法

-- 插入品牌数据
INSERT INTO t_brand (name, english_name, country, founded_year, website, logo_url, description, status, sort_weight, create_user_code, create_user_name) VALUES
('奔驰', 'Mercedes-Benz', '德国', 1926, 'https://www.mercedes-benz.com', 'https://example.com/logo/mercedes.png', '德国豪华汽车品牌，以高品质、高性能和高科技著称', 'ACTIVE', 100, 'admin', '系统管理员'),
('宝马', 'BMW', '德国', 1916, 'https://www.bmw.com', 'https://example.com/logo/bmw.png', '德国豪华汽车制造商，专注于高性能豪华汽车', 'ACTIVE', 95, 'admin', '系统管理员'),
('奥迪', 'Audi', '德国', 1909, 'https://www.audi.com', 'https://example.com/logo/audi.png', '德国豪华汽车品牌，以quattro四驱技术闻名', 'ACTIVE', 90, 'admin', '系统管理员'),
('丰田', 'Toyota', '日本', 1937, 'https://www.toyota.com', 'https://example.com/logo/toyota.png', '日本汽车制造商，以可靠性和燃油经济性著称', 'ACTIVE', 85, 'admin', '系统管理员'),
('本田', 'Honda', '日本', 1948, 'https://www.honda.com', 'https://example.com/logo/honda.png', '日本汽车制造商，以发动机技术和可靠性闻名', 'ACTIVE', 80, 'admin', '系统管理员'),
('比亚迪', 'BYD', '中国', 1995, 'https://www.byd.com', 'https://example.com/logo/byd.png', '中国新能源汽车领导品牌，专注于电动汽车技术', 'ACTIVE', 75, 'admin', '系统管理员'),
('特斯拉', 'Tesla', '美国', 2003, 'https://www.tesla.com', 'https://example.com/logo/tesla.png', '美国电动汽车制造商，引领电动汽车革命', 'ACTIVE', 70, 'admin', '系统管理员');

-- 插入车系数据
INSERT INTO t_series (name, english_name, brand_id, type, level, launch_time, min_price, max_price, image_url, description, status, sort_weight, create_user_code, create_user_name) VALUES
-- 奔驰车系
('C级', 'C-Class', 1, 'SEDAN', 'C', '2014-01-15', 32.52, 47.48, 'https://example.com/series/benz-c.jpg', '奔驰入门级豪华轿车，兼顾舒适性和运动性', 'ON_SALE', 100, 'admin', '系统管理员'),
('E级', 'E-Class', 1, 'SEDAN', 'D', '2016-07-25', 44.28, 65.68, 'https://example.com/series/benz-e.jpg', '奔驰中大型豪华轿车，商务与舒适并重', 'ON_SALE', 95, 'admin', '系统管理员'),
('GLC', 'GLC-Class', 1, 'SUV', 'C', '2015-11-19', 40.25, 58.78, 'https://example.com/series/benz-glc.jpg', '奔驰中型豪华SUV，城市与越野兼顾', 'ON_SALE', 90, 'admin', '系统管理员'),

-- 宝马车系
('3系', '3 Series', 2, 'SEDAN', 'C', '2019-06-22', 29.39, 40.99, 'https://example.com/series/bmw-3.jpg', '宝马运动型豪华轿车，操控性能出色', 'ON_SALE', 100, 'admin', '系统管理员'),
('5系', '5 Series', 2, 'SEDAN', 'D', '2017-06-23', 42.69, 65.99, 'https://example.com/series/bmw-5.jpg', '宝马中大型豪华轿车，商务舒适首选', 'ON_SALE', 95, 'admin', '系统管理员'),
('X3', 'X3', 2, 'SUV', 'C', '2018-07-09', 38.98, 47.98, 'https://example.com/series/bmw-x3.jpg', '宝马中型豪华SUV，运动与实用并重', 'ON_SALE', 90, 'admin', '系统管理员'),

-- 奥迪车系
('A4L', 'A4L', 3, 'SEDAN', 'C', '2020-04-10', 30.58, 39.68, 'https://example.com/series/audi-a4l.jpg', '奥迪中型豪华轿车，科技配置丰富', 'ON_SALE', 100, 'admin', '系统管理员'),
('A6L', 'A6L', 3, 'SEDAN', 'D', '2018-10-19', 41.98, 65.38, 'https://example.com/series/audi-a6l.jpg', '奥迪中大型豪华轿车，商务精英之选', 'ON_SALE', 95, 'admin', '系统管理员'),
('Q5L', 'Q5L', 3, 'SUV', 'C', '2018-07-06', 39.68, 51.70, 'https://example.com/series/audi-q5l.jpg', '奥迪中型豪华SUV，quattro四驱系统', 'ON_SALE', 90, 'admin', '系统管理员'),

-- 丰田车系
('凯美瑞', 'Camry', 4, 'SEDAN', 'B', '2017-12-20', 17.98, 26.98, 'https://example.com/series/toyota-camry.jpg', '丰田中型轿车，可靠性和燃油经济性出色', 'ON_SALE', 100, 'admin', '系统管理员'),
('汉兰达', 'Highlander', 4, 'SUV', 'C', '2018-03-23', 25.88, 34.88, 'https://example.com/series/toyota-highlander.jpg', '丰田中大型SUV，7座家用首选', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 本田车系
('雅阁', 'Accord', 5, 'SEDAN', 'B', '2018-04-16', 17.98, 25.98, 'https://example.com/series/honda-accord.jpg', '本田中型轿车，运动与舒适平衡', 'ON_SALE', 100, 'admin', '系统管理员'),
('CR-V', 'CR-V', 5, 'SUV', 'B', '2017-07-09', 16.98, 27.68, 'https://example.com/series/honda-crv.jpg', '本田紧凑型SUV，城市代步首选', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 比亚迪车系
('汉', 'Han', 6, 'SEDAN', 'C', '2020-07-12', 21.98, 32.98, 'https://example.com/series/byd-han.jpg', '比亚迪旗舰轿车，刀片电池技术', 'ON_SALE', 100, 'admin', '系统管理员'),
('唐', 'Tang', 6, 'SUV', 'C', '2018-06-26', 23.99, 35.99, 'https://example.com/series/byd-tang.jpg', '比亚迪中大型SUV，插电混动技术', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 特斯拉车系
('Model 3', 'Model 3', 7, 'SEDAN', 'C', '2019-02-22', 26.59, 34.99, 'https://example.com/series/tesla-model3.jpg', '特斯拉入门级电动轿车，智能驾驶技术', 'ON_SALE', 100, 'admin', '系统管理员'),
('Model Y', 'Model Y', 7, 'SUV', 'C', '2021-01-01', 30.99, 39.99, 'https://example.com/series/tesla-modely.jpg', '特斯拉紧凑型电动SUV，超级充电网络', 'ON_SALE', 95, 'admin', '系统管理员');

-- 插入少量车型数据作为示例
INSERT INTO t_model (name, series_id, year_model, displacement, max_power, max_torque, transmission, gear_count, drive_type, fuel_type, body_structure, seat_count, length, width, height, wheelbase, curb_weight, tank_capacity, fuel_consumption, guide_price, launch_time, image_urls, config_details, status, sort_weight, create_user_code, create_user_name) VALUES
-- 奔驰C级车型
('C 200 L 运动版', 1, 2023, 1.5, 150, 280, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 4784, 1810, 1457, 2920, 1555, 66, 6.9, 32.52, '2023-03-15', '["https://example.com/model/benz-c200l-1.jpg"]', '{"安全配置": ["主动刹车", "车道保持"], "舒适配置": ["座椅加热", "自动空调"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('C 260 L 豪华版', 1, 2023, 1.5, 150, 280, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 4784, 1810, 1457, 2920, 1575, 66, 7.1, 37.38, '2023-03-15', '["https://example.com/model/benz-c260l-1.jpg"]', '{"安全配置": ["主动刹车", "车道保持"], "舒适配置": ["座椅加热通风", "自动空调"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 宝马3系车型
('325Li M运动套装', 4, 2023, 2.0, 135, 300, '8挡手自一体', 8, 'RWD', 'GASOLINE', 'SEDAN', 5, 4829, 1827, 1463, 2961, 1555, 59, 6.9, 29.39, '2023-02-15', '["https://example.com/model/bmw-325li-1.jpg"]', '{"安全配置": ["主动刹车"], "舒适配置": ["运动座椅", "自动空调"]}', 'ON_SALE', 100, 'admin', '系统管理员'),

-- 特斯拉Model 3车型
('后轮驱动版', 16, 2023, NULL, 194, 340, '电动车单速变速箱', 1, 'RWD', 'ELECTRIC', 'SEDAN', 5, 4694, 1850, 1443, 2875, 1745, NULL, NULL, 26.59, '2023-01-06', '["https://example.com/model/tesla-model3-rwd-1.jpg"]', '{"安全配置": ["Autopilot自动辅助驾驶"], "舒适配置": ["极简内饰", "15英寸触摸屏"]}', 'ON_SALE', 100, 'admin', '系统管理员');

-- 注意：如需要更多车型数据，请参考 docs/design/h2_test_data.sql 文件
-- 该文件包含完整的H2兼容测试数据

-- 查询验证数据的示例SQL（注释状态）
-- SELECT COUNT(*) as brand_count FROM t_brand WHERE deleted = 0;
-- SELECT COUNT(*) as series_count FROM t_series WHERE deleted = 0;
-- SELECT COUNT(*) as model_count FROM t_model WHERE deleted = 0;
