package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthCenterRequestInterceptor;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteGroupService", url = "EMPTY", configuration = {AuthCenterRequestInterceptor.class})
public interface RemoteGroupService {

    /**
     * 通过账号获取树形结构
     *
     * @return
     */
    @GetMapping(value = "/admin/group/tree")
    CommonRlt groupTree();

    /**
     * 获取完整树形结构（管理组织）
     *
     * @return
     */
    @GetMapping(value = "/admin/group/tree/all")
    CommonRlt groupTreeAll();

    /**
     * 获取完整树形结构（HR组织）
     *
     * @return
     */
    @GetMapping(value = "/admin/group/tree/hr/all")
    CommonRlt hrGroupTreeAll();

    /**
     * 查询组织管理员
     * @param groupId
     * @return CommonRlt
     */
    @GetMapping(value = "/admin/group/info")
    CommonRlt groupInfo(@ApiParam(name = "group_id", value = "组织ID", required = true)
                        @RequestParam(name = "group_id") Integer groupId);
}
