package com.gwm.scaffold.tools.util;

import com.alibaba.fastjson.JSON;
import org.springframework.util.StringUtils;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

import static com.gwm.scaffold.tools.util.Constants.*;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

/**
 * 字符串处理工具
 *
 * <AUTHOR>
 * @date 2025/3/7
 */
public final class StringUtil {

    public static final SimpleDateFormat SDF = new SimpleDateFormat("yyyyMMddHHmmss");
    public static final SimpleDateFormat DATE_SDF = new SimpleDateFormat("yyyyMMdd");

    /**
     * 字符串转化为具体类型（前端使用）
     *
     * @param str 字符串
     * @return 具体类型
     */
    public static Object convert(String str) {
        if (!StringUtils.hasText(str)) {
            return null;
        }
        if (isBoolean(str)) {
            return Boolean.valueOf(str);
        }
        if (isInteger(str)) {
            return Integer.valueOf(str);
        }
        if (isDouble(str)) {
            return Double.valueOf(str);
        }
        if (isArray(str)) {
            return JSON.parseArray(str);
        }
        if (isObject(str)) {
            return JSON.parse(str);
        }
        return str;
    }

    /**
     * 是布尔类型
     *
     * @param str 待判断的字符串
     * @return 是否
     */
    public static boolean isBoolean(String str) {
        if (!StringUtils.hasText(str)) {
            return false;
        }
        return str.trim().equals(TRUE.toString()) || str.trim().equals(FALSE.toString());
    }

    /**
     * 是否是浮点数类型
     *
     * @param str 待判断字符串
     * @return 是否
     */
    public static boolean isDouble(String str) {
        if (!StringUtils.hasText(str)) {
            return false;
        }
        if (str.trim().startsWith(ZERO.toString())) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 是否是数字类型
     *
     * @param str 待判断字符串
     * @return 是否
     */
    public static boolean isInteger(String str) {
        if (!StringUtils.hasText(str)) {
            return false;
        }
        if (str.trim().startsWith(ZERO.toString())) {
            return false;
        }
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean isObject(String str) {
        if (!StringUtils.hasText(str)) {
            return false;
        }
        try {
            JSON.parseObject(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isArray(String str) {
        if (!StringUtils.hasText(str)) {
            return false;
        }
        try {
            JSON.parseArray(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 连接字符串
     *
     * @param strArr 字符串数组
     * @return 拼接后的字符串
     */
    public static String joinString(String... strArr) {
        if (null == strArr) {
            return EMPTY_STR;
        }
        if (strArr.length == 1) {
            return strArr[0];
        }
        StringBuilder builder = new StringBuilder();
        for (String str : strArr) {
            builder.append(str == null ? EMPTY_STR : str);
        }
        return builder.toString();
    }

    /**
     * 拼接字符串
     *
     * @param originalStr 原始字符串
     * @param target      要拼接的字符串
     * @return 结果
     */
    public static String concateString(String originalStr, String target) {
        return concateString(originalStr, target, ",");
    }

    /**
     * 拼接字符串
     *
     * @param originalStr 原始字符串
     * @param target      要拼接的字符串
     * @param delimiter   分隔符
     * @return 结果
     */
    public static String concateString(String originalStr, String target, String delimiter) {
        if (null == originalStr) {
            return null;
        }
        String[] parts = originalStr.split(delimiter);
        // 如果存在目标字符串就原样返回
        for (String part : parts) {
            if (part.equals(target)) {
                return originalStr;
            }
        }
        if (!StringUtils.hasText(originalStr)) {
            return target;
        }
        return originalStr + delimiter + target;
    }

    /**
     * 从指定字符串中删除指定项，默认逗号
     *
     * @param input        输入字符串
     * @param wordToRemove 要删除的项
     * @return 去除指定项后的字符串
     */
    public static String removeWord(String input, String wordToRemove) {
        return removeWord(input, wordToRemove, ",");
    }

    /**
     * 从指定字符串中删除指定项
     *
     * @param input        输入字符串
     * @param wordToRemove 要删除的项
     * @param delimiter    分隔符
     * @return 去除指定项后的字符串
     */
    public static String removeWord(String input, String wordToRemove, String delimiter) {
        if (null == input) {
            return null;
        }
        String[] parts = input.split(delimiter);
        StringJoiner result = new StringJoiner(delimiter);
        for (String part : parts) {
            if (!part.equals(wordToRemove)) {
                result.add(part);
            }
        }
        return result.toString();
    }

    /**
     * 从路径获取文件名
     * 支持url带锚点和参数
     *
     * @param address 地址
     * @return 文件名
     */
    public static String getFileName(String address) {
        if (!StringUtils.hasText(address)) {
            return null;
        }
        int queryIndex = address.indexOf(URL_PARAM);
        int fragmentIndex = address.indexOf(URL_ANCHORS);
        int pathEnd = address.length();
        if (queryIndex < 0) {
            if (fragmentIndex > 0) {
                pathEnd = fragmentIndex;
            }
        } else {
            if (fragmentIndex < 0) {
                pathEnd = queryIndex;
            } else {
                pathEnd = Math.min(queryIndex, fragmentIndex);
            }
        }
        // 提取路径部分
        String path = address.substring(0, pathEnd);
        // 找到最后一个斜杠的位置
        int lastSlashIndex = path.lastIndexOf(URL_DELIMITER);
        // 提取文件名
        if (lastSlashIndex < 0) {
            // 整个路径是文件名
            return path;
        } else if (lastSlashIndex == (path.length() - 1)) {
            return null;
        } else {
            return path.substring(lastSlashIndex + 1);
        }
    }

    /**
     * 判断字符串是否为空
     *
     * @param str 待判断字符串
     * @return 是否为空
     */
    public static boolean isNotBlank(String str) {
        return str != null && !str.isEmpty();
    }

    /**
     * 判断字符串是否为空
     *
     * @param str 待判断字符串
     * @return 是否为空 null、空字符串
     */
    public static boolean isBlank(String str) {
        return !isNotBlank(str);
    }

    /**
     * 获取文件名后缀
     *
     * @param fileName 文件名
     * @return 后缀
     */
    private String getSuffix(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return null;
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return null;
        }
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 获取文件唯一命名
     *
     * @param name 文件名
     * @return 唯一名称
     */
    public static String getUniName(String name) {
        return getUniName(name, DATE_SDF);
    }

    /**
     * 获取文件唯一命名
     *
     * @param name       文件名
     * @param dateFormat 日期格式化
     * @return 唯一名称
     */
    public static String getUniName(String name, DateFormat dateFormat) {
        if (!StringUtils.hasText(name)) {
            return null;
        }
        String nameFill = dateFormat.format(new Date());
        // 找到最后一个斜杠的位置，以此区分路径和文件名
        int lastDotIndex = name.lastIndexOf('.');
        if (lastDotIndex < ZERO) {
            return name + nameFill;
        }
        return name.substring(ZERO, lastDotIndex) + "_" + nameFill + name.substring(lastDotIndex);
    }

    /**
     * 根据数据库保存的部门名称格式化
     * @param CompanyCode 部门名称
     * @param level 从第几级获取
     * @return
     */
    public static String formatCompanyNames(String CompanyCode,int level) {
        // 移除多余的字符，只保留中文公司名
        String cleanedInput = CompanyCode.replaceAll("[\\[\\]\"']", "");
        // 按逗号分隔字符串并去除首尾空格
        List<String> names = Arrays.asList(cleanedInput.split(","));
        // 使用 StringJoiner 构建格式化后的字符串
        StringJoiner joiner = new StringJoiner("/");
        if (names.size() > level){
            for (int i = level; i < names.size(); i++) {
                joiner.add(names.get(i).trim());
            }
        }else{
            names.forEach(name -> joiner.add(name.trim()));
        }

        return joiner.toString();
    }

    private StringUtil() {
    }
}
