package com.gwm.scaffold.monitor.controller;

import com.gwm.scaffold.core.domain.Result;
import com.gwm.scaffold.monitor.domain.AppMetrics;
import com.gwm.scaffold.monitor.domain.BusinessMetrics;
import com.gwm.scaffold.monitor.domain.DatabaseMetrics;
import com.gwm.scaffold.monitor.service.MetricsCollector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 监控控制器
 * 
 * 提供监控面板和监控数据API
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@RestController
@RequestMapping("${scaffold.monitor.dashboard.path:/scaffold/monitor}")
@ConditionalOnProperty(name = "scaffold.monitor.dashboard.enabled", havingValue = "true", matchIfMissing = true)
public class MonitorController {

    @Autowired
    private MetricsCollector metricsCollector;

    /**
     * 监控面板首页
     */
    @GetMapping(value = {"", "/", "/dashboard"}, produces = MediaType.TEXT_HTML_VALUE)
    public void dashboard(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");
        
        try {
            ClassPathResource resource = new ClassPathResource("static/monitor/dashboard.html");
            String html = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            response.getWriter().write(html);
        } catch (Exception e) {
            log.error("加载监控面板失败", e);
            response.getWriter().write(getDefaultDashboard());
        }
    }

    /**
     * 获取所有监控指标
     */
    @GetMapping("/api/metrics")
    public Result<Map<String, Object>> getAllMetrics() {
        try {
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("app", metricsCollector.getAppMetrics());
            metrics.put("business", metricsCollector.getBusinessMetrics());
            metrics.put("database", metricsCollector.getDatabaseMetrics());
            metrics.put("timestamp", System.currentTimeMillis());
            
            return Result.success(metrics);
        } catch (Exception e) {
            log.error("获取监控指标失败", e);
            return Result.error("获取监控指标失败：" + e.getMessage());
        }
    }

    /**
     * 获取应用指标
     */
    @GetMapping("/api/metrics/app")
    public Result<AppMetrics> getAppMetrics() {
        try {
            return Result.success(metricsCollector.getAppMetrics());
        } catch (Exception e) {
            log.error("获取应用指标失败", e);
            return Result.error("获取应用指标失败：" + e.getMessage());
        }
    }

    /**
     * 获取业务指标
     */
    @GetMapping("/api/metrics/business")
    public Result<BusinessMetrics> getBusinessMetrics() {
        try {
            return Result.success(metricsCollector.getBusinessMetrics());
        } catch (Exception e) {
            log.error("获取业务指标失败", e);
            return Result.error("获取业务指标失败：" + e.getMessage());
        }
    }

    /**
     * 获取数据库指标
     */
    @GetMapping("/api/metrics/database")
    public Result<DatabaseMetrics> getDatabaseMetrics() {
        try {
            return Result.success(metricsCollector.getDatabaseMetrics());
        } catch (Exception e) {
            log.error("获取数据库指标失败", e);
            return Result.error("获取数据库指标失败：" + e.getMessage());
        }
    }

    /**
     * 重置监控指标
     */
    @PostMapping("/api/metrics/reset")
    public Result<Void> resetMetrics() {
        try {
            metricsCollector.resetMetrics();
            return Result.success("监控指标重置成功");
        } catch (Exception e) {
            log.error("重置监控指标失败", e);
            return Result.error("重置监控指标失败：" + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/api/health")
    public Result<Map<String, Object>> health() {
        try {
            Map<String, Object> health = new HashMap<>();
            AppMetrics appMetrics = metricsCollector.getAppMetrics();
            DatabaseMetrics dbMetrics = metricsCollector.getDatabaseMetrics();
            
            boolean isHealthy = appMetrics.getMemoryUsagePercent() < 90 
                    && appMetrics.getCpuUsagePercent() < 90 
                    && dbMetrics.isHealthy();
            
            health.put("status", isHealthy ? "UP" : "DOWN");
            health.put("timestamp", System.currentTimeMillis());
            Map<String, String> details = new HashMap<>();
            details.put("memory", appMetrics.getMemoryUsagePercent() < 90 ? "UP" : "DOWN");
            details.put("cpu", appMetrics.getCpuUsagePercent() < 90 ? "UP" : "DOWN");
            details.put("database", dbMetrics.isHealthy() ? "UP" : "DOWN");
            health.put("details", details);
            
            return Result.success(health);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return Result.error("健康检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取默认监控面板HTML
     */
    private String getDefaultDashboard() {
        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <title>应用监控面板</title>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n" +
                "        .dashboard { max-width: 1200px; margin: 0 auto; }\n" +
                "        .header { text-align: center; margin-bottom: 30px; }\n" +
                "        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }\n" +
                "        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }\n" +
                "        .metric-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #333; }\n" +
                "        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }\n" +
                "        .metric-label { font-size: 14px; color: #666; margin-top: 5px; }\n" +
                "        .status-healthy { color: #28a745; }\n" +
                "        .status-unhealthy { color: #dc3545; }\n" +
                "        .refresh-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"dashboard\">\n" +
                "        <div class=\"header\">\n" +
                "            <h1>应用监控面板</h1>\n" +
                "            <button class=\"refresh-btn\" onclick=\"loadMetrics()\">刷新数据</button>\n" +
                "        </div>\n" +
                "        <div class=\"metrics-grid\" id=\"metrics-container\">\n" +
                "            <div class=\"metric-card\">\n" +
                "                <div class=\"metric-title\">加载中...</div>\n" +
                "                <div class=\"metric-value\">请稍候</div>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "    \n" +
                "    <script>\n" +
                "        function loadMetrics() {\n" +
                "            fetch('./api/metrics')\n" +
                "                .then(response => response.json())\n" +
                "                .then(result => {\n" +
                "                    if (result.code === 200) {\n" +
                "                        updateDashboard(result.data);\n" +
                "                    } else {\n" +
                "                        console.error('获取监控数据失败:', result.message);\n" +
                "                    }\n" +
                "                })\n" +
                "                .catch(error => console.error('请求失败:', error));\n" +
                "        }\n" +
                "        \n" +
                "        function updateDashboard(data) {\n" +
                "            const container = document.getElementById('metrics-container');\n" +
                "            container.innerHTML = `\n" +
                "                <div class=\"metric-card\">\n" +
                "                    <div class=\"metric-title\">应用状态</div>\n" +
                "                    <div class=\"metric-value status-healthy\">运行中</div>\n" +
                "                    <div class=\"metric-label\">运行时间: ${data.app.formattedUptime || '未知'}</div>\n" +
                "                </div>\n" +
                "                <div class=\"metric-card\">\n" +
                "                    <div class=\"metric-title\">内存使用</div>\n" +
                "                    <div class=\"metric-value\">${data.app.memoryUsagePercent?.toFixed(1) || 0}%</div>\n" +
                "                    <div class=\"metric-label\">${data.app.formattedMemoryUsage || '未知'}</div>\n" +
                "                </div>\n" +
                "                <div class=\"metric-card\">\n" +
                "                    <div class=\"metric-title\">CPU使用率</div>\n" +
                "                    <div class=\"metric-value\">${data.app.cpuUsagePercent?.toFixed(1) || 0}%</div>\n" +
                "                    <div class=\"metric-label\">处理器: ${data.app.availableProcessors || 0} 核</div>\n" +
                "                </div>\n" +
                "                <div class=\"metric-card\">\n" +
                "                    <div class=\"metric-title\">数据库状态</div>\n" +
                "                    <div class=\"metric-value ${data.database.isHealthy ? 'status-healthy' : 'status-unhealthy'}\">\n" +
                "                        ${data.database.healthStatus || '未知'}\n" +
                "                    </div>\n" +
                "                    <div class=\"metric-label\">连接: ${data.database.formattedConnectionUsage || '未知'}</div>\n" +
                "                </div>\n" +
                "                <div class=\"metric-card\">\n" +
                "                    <div class=\"metric-title\">请求统计</div>\n" +
                "                    <div class=\"metric-value\">${data.business.totalRequests || 0}</div>\n" +
                "                    <div class=\"metric-label\">错误率: ${data.business.errorRate?.toFixed(2) || 0}%</div>\n" +
                "                </div>\n" +
                "                <div class=\"metric-card\">\n" +
                "                    <div class=\"metric-title\">响应性能</div>\n" +
                "                    <div class=\"metric-value\">${data.business.formattedResponseTime || '0ms'}</div>\n" +
                "                    <div class=\"metric-label\">QPS: ${data.business.formattedQps || 0}</div>\n" +
                "                </div>\n" +
                "            `;\n" +
                "        }\n" +
                "        \n" +
                "        // 页面加载时获取数据\n" +
                "        loadMetrics();\n" +
                "        \n" +
                "        // 每5秒自动刷新\n" +
                "        setInterval(loadMetrics, 5000);\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }
}
