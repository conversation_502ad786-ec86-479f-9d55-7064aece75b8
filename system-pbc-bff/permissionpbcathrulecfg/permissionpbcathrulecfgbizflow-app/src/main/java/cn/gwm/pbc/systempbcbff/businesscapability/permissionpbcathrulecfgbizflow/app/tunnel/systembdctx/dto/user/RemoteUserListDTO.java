package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RemoteUserListDTO implements Serializable {

    /**
     * 别名
     */
    private String alias;
    /**
     * 是否启用 0否 1是
     */
    private Integer enable;
    /**
     * 页码
     */
    private Integer page;
    /**
     * 条数（默认返回10条）
     */
    private Integer size;
    /**
     * 用户名称
     */
    private String user_name;
    /**
     * 用户账号
     */
    private String user_code;
    /**
     * 用户类型（1 正式工 2自定义）
     */
    private String user_type;
    /**
     * 用户和平台角色绑定日期（yyyy-MM-dd）
     */
    private String create_time;
    /**
     * 用户信息更新日期（yyyy-MM-dd）
     */
    private String update_time;

}
