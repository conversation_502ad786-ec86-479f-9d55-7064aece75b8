package com.gwm.scaffold.auth.service;

import com.gwm.scaffold.auth.domain.LoginResult;

/**
 * 认证服务接口
 * 
 * 提供用户认证相关的核心功能
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
public interface AuthenticateService {

    /**
     * 验证Token有效性
     *
     * @param token 访问令牌
     * @return 登录结果信息
     */
    LoginResult checkToken(String token);

    /**
     * 刷新Token
     *
     * @param refreshToken 刷新令牌
     * @return 新的登录结果信息
     */
    LoginResult refreshToken(String refreshToken);

    /**
     * 登出
     *
     * @param token 访问令牌
     * @return 是否成功
     */
    boolean logout(String token);

    /**
     * 验证用户权限
     *
     * @param userCode   用户工号
     * @param permission 权限标识
     * @return 是否有权限
     */
    boolean hasPermission(String userCode, String permission);

    /**
     * 验证用户角色
     *
     * @param userCode 用户工号
     * @param roleCode 角色编码
     * @return 是否有角色
     */
    boolean hasRole(String userCode, String roleCode);
}
