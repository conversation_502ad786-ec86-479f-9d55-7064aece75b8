# 🚀 GWM Spring Boot 企业级脚手架

## 📋 项目简介

GWM Spring Boot 脚手架是一个企业级的项目模板，旨在帮助开发团队快速构建高质量的Spring Boot应用。它集成了企业开发中常用的技术栈和最佳实践，提供了完整的基础设施和丰富的工具类库。

### 🎯 设计理念

- **开箱即用**: 引入依赖即可使用，无需复杂配置
- **模块化设计**: 按需引入功能模块，避免冗余
- **企业级标准**: 遵循企业开发规范和最佳实践
- **高度可扩展**: 支持功能的平滑扩展和定制
- **完善的文档**: 提供详细的使用指南和示例

## 🏗️ 技术栈

- **Spring Boot**: 2.7.18
- **Java**: 1.8+
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus 3.5.6
- **连接池**: HikariCP + Druid
- **缓存**: Caffeine
- **文档**: Swagger 2.9.2 + Smart-doc
- **工具库**: Hutool, Apache Commons, Google Guava + 17个自研工具类
- **JSON**: Jackson + FastJSON
- **HTTP客户端**: OkHttp3 + 自研HttpRequestUtil/OkHttpUtil
- **加密**: Jasypt + 自研EncryptUtil/HmacSignUtil
- **测试**: JavaFaker + 自研FakeUtil
- **监控**: Spring Boot Actuator + Arthas

## 🚀 快速开始

### 1. 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+

### 2. 克隆项目
```bash
git clone <repository-url>
cd spring-boot-scaffold
```

### 3. 配置数据库
修改 `application-dev.yml` 中的数据库连接信息：
```yaml
spring:
  datasource:
    url: *****************************************************************************************************************************************************************************
    username: your_username
    password: your_password
```

### 4. 启动项目
```bash
mvn spring-boot:run
```

### 5. 访问应用
- 脚手架介绍: http://localhost:8080
- 脚手架概览: http://localhost:8080/api/scaffold/overview
- 工具类库: http://localhost:8080/api/scaffold/tools
- API文档: http://localhost:8080/swagger-ui.html
- Bootstrap UI: http://localhost:8080/doc.html
- 健康检查: http://localhost:8080/actuator/health

## 📁 项目结构

```
src/main/java/com/company/project/
├── Application.java                    # 启动类
├── config/                            # 配置类
│   ├── AsyncConfig.java               # 异步配置
│   ├── BeanConfig.java                # Bean配置
│   ├── MybatisPlusConfig.java         # MyBatis Plus配置
│   ├── SwaggerConfig.java             # Swagger配置
│   └── WebConfig.java                 # Web配置
├── common/                            # 公共组件
│   ├── entity/                        # 基础实体
│   ├── exception/                     # 异常定义
│   ├── handler/                       # 处理器
│   └── util/                          # 工具类库 (17个实用工具类)
│       ├── CollectionDiffUtil.java    # 集合差异计算
│       ├── DateUtil.java              # 日期时间处理
│       ├── EncryptUtil.java           # 文件加密解密
│       ├── EnumUtil.java              # 枚举处理
│       ├── FakeUtil.java              # 测试数据生成
│       ├── FrontUtil.java             # 前端类型转换
│       ├── HmacSignUtil.java          # HMAC签名认证
│       ├── HttpRequestUtil.java       # HTTP请求封装
│       ├── ImageUtil.java             # 图片处理
│       ├── LoginUserUtil.java         # 登录用户管理
│       ├── ObjectUtil.java            # 对象处理
│       ├── OkHttpUtil.java            # OkHttp封装
│       ├── PageUtil.java              # 分页数据处理
│       ├── ProjectUtil.java           # 项目工具
│       ├── SpringContextUtil.java     # Spring上下文
│       ├── StringUtil.java            # 字符串处理
│       └── UUIDUtil.java              # 唯一标识生成
├── controller/                        # 控制器层
├── service/                           # 服务层
├── mapper/                            # 数据访问层
├── dto/                               # 数据传输对象
├── entity/                            # 实体类
├── enums/                             # 枚举类
└── vo/                                # 视图对象
```

## 🔧 核心特性

### 1. 统一异常处理
- 全局异常捕获
- 统一错误响应格式
- 详细的错误日志记录

### 2. 统一返回结果
- 标准的API响应格式
- 支持分页数据返回
- 灵活的数据转换

### 3. 数据库增强
- MyBatis Plus集成
- 自动填充创建/更新信息
- 逻辑删除支持
- 分页插件
- 乐观锁支持

### 4. 安全特性
- 登录拦截器
- 配置加密支持
- 操作日志记录

### 5. 监控和文档
- Spring Boot Actuator健康检查
- Swagger API文档
- SQL监控和性能分析

## 🛠️ 工具类库

脚手架提供了丰富的工具类库，位于 `com.gwm.yourproject.common.util` 包下，涵盖了企业级开发的各种常用功能。

### 🔐 安全与加密工具

#### EncryptUtil - 文件加密工具
- **文件加密**: `fileEncrypt(File file)` - 加密文件并返回加密内容
- **文件解密**: `fileDecrypt(File file)` - 解密文件
- **压缩包加密**: `packageEncrypt(File file)` - 加密压缩包
- **HMAC签名**: 集成HmacSignUtil进行安全签名

#### HmacSignUtil - HMAC签名工具
- **创建签名头**: `createSignHeader(appKey, appSecret, uri, method)` - 生成API请求签名
- **HTTP头封装**: `getHttpHeaders(appKey, appSecret, url, method)` - 封装带签名的HTTP请求头
- **安全认证**: 支持企业级API安全认证机制

### 🌐 网络请求工具

#### HttpRequestUtil - HTTP请求工具
- **POST请求**: 支持JSON、表单、字符串等多种格式
- **PUT请求**: `put(JSONObject json, String url, Map<String, String> headerMap)`
- **GET请求**: 支持带参数和自定义头的GET请求
- **文件上传**: 支持multipart文件上传

#### OkHttpUtil - OkHttp封装工具
- **链式调用**: 支持流式API调用方式
- **异步请求**: 支持异步HTTP请求处理
- **文件下载**: `file()` - 获取文件输入流
- **连接池管理**: 自动管理HTTP连接池

### 📊 数据处理工具

#### StringUtil - 字符串处理工具
- **类型转换**: `convert(String str)` - 智能转换字符串为具体类型
- **文件命名**: `getUniName(String name)` - 生成唯一文件名
- **部门格式化**: `formatCompanyNames(String companyCode, int level)` - 格式化部门层级名称
- **数据验证**: 提供布尔值、整数、浮点数、数组、对象等类型判断

#### UUIDUtil - 唯一标识生成工具
- **UUID生成**: `getUUID()` - 生成标准UUID
- **唯一编号**: `getUniqueNo()` - 生成时间戳+随机数格式的唯一编号
- **随机字符串**: `getRandomString(int length)` - 生成指定长度随机字符串
- **带前缀编号**: `getUniqueNoWithPrefix(String prefix)` - 生成带前缀的唯一编号

#### CollectionDiffUtil - 集合差异计算工具
- **差异分析**: `diff(List<T> oldList, List<T> newList, Function<T, ID> idExtractor)`
- **增删改识别**: 自动识别需要插入、更新、删除的数据
- **批量操作优化**: 优化批量数据处理性能

### 📅 日期时间工具

#### DateUtil - 日期处理工具
- **天数计算**: `calculateUsageDays(LocalDate startDate, LocalDate endDate)` - 计算两日期间天数
- **类型转换**: `LocalDateToDate(LocalDate localDate)` - LocalDate与Date互转
- **日期操作**: 支持各种日期计算和格式化操作

#### ObjectUtil - 对象处理工具
- **目标日期**: `getTargetDate(Integer amount)` - 获取相对当前日期的目标时间
- **区间检测**: `existCover()` - 检测时间区间是否存在重叠
- **对象转Map**: `objectToMap(Object obj)` - 将对象转换为Map格式

### 🎨 前端支持工具

#### FrontUtil - 前端类型转换工具
- **类型映射**: `convertFrontType(Class<T> clazz)` - 将Java类型转换为前端类型
- **支持类型**: string、number、boolean、array、object
- **前后端协作**: 简化前后端数据类型对接

#### EnumUtil - 枚举处理工具
- **枚举转换**: `convertEnum(String enumName)` - 将枚举转换为前端可用格式
- **反射解析**: 自动解析枚举字段和值
- **动态加载**: 支持通过类名动态加载枚举

### 🖼️ 媒体处理工具

#### ImageUtil - 图片处理工具
- **Base64转换**: `convertImageToBase64(String filePath)` - 图片转Base64编码
- **流处理**: `convertImageToBase64(InputStream inputStream)` - 支持输入流转换
- **异常处理**: 完善的异常处理和日志记录

### 📄 分页工具

#### PageUtil - 分页数据处理工具
- **数据转换**: `convert(IPage<S> source, Function<S, T> converter)` - 分页数据类型转换
- **反射转换**: 支持通过反射调用转换方法
- **分页创建**: `createPage()` - 创建分页对象
- **缓存优化**: 内置方法缓存提升性能

### 🔧 系统工具

#### SpringContextUtil - Spring上下文工具
- **Bean获取**: `getBean(Class<T> beanClass)` - 静态方式获取Spring Bean
- **上下文访问**: 在非Spring管理的类中访问Spring容器
- **依赖注入**: 支持手动依赖注入

#### ProjectUtil - 项目工具
- **类扫描**: `listClasses(String basePackage)` - 扫描指定包下的所有类
- **字段访问**: `getField(Object object, String fieldName)` - 反射获取对象字段值
- **元数据处理**: 支持项目元数据的动态处理

#### LoginUserUtil - 登录用户工具
- **用户信息管理**: 基于ThreadLocal的用户信息存储
- **链路追踪**: 自动生成traceId用于日志追踪
- **MDC集成**: 与SLF4J MDC集成，支持分布式日志追踪
- **异步支持**: 使用TransmittableThreadLocal支持异步场景

### 🧪 测试工具

#### FakeUtil - 测试数据生成工具
- **对象填充**: `createFake(Class<T> clazz)` - 自动生成测试对象
- **深度控制**: 防止循环引用，支持最大递归深度控制
- **类型支持**: 支持基本类型、集合、数组、枚举等各种类型
- **JavaFaker集成**: 基于JavaFaker库生成真实感测试数据

### 使用示例

```java
// 字符串处理
String uniqueFileName = StringUtil.getUniName("document.pdf");
Object convertedValue = StringUtil.convert("123"); // 自动转换为Integer

// UUID生成
String uuid = UUIDUtil.getUUID();
String orderNo = UUIDUtil.getUniqueNoWithPrefix("ORDER_");

// 日期处理
int days = DateUtil.calculateUsageDays(startDate, endDate);
Date targetDate = ObjectUtil.getTargetDate(7); // 7天后

// 图片处理
String base64 = ImageUtil.convertImageToBase64("/path/to/image.jpg");

// 分页转换
IPage<UserDTO> userDTOPage = PageUtil.convert(userPage, userConverter::toDTO);

// Spring Bean获取
UserService userService = SpringContextUtil.getBean(UserService.class);

// 测试数据生成
User fakeUser = FakeUtil.createFake(User.class);
```

## 📖 使用指南

### 1. 创建新的业务模块

#### 1.1 创建实体类
```java
@Data
@TableName("your_table")
public class YourEntity extends BaseSuperEntity implements BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    // 其他字段...
}
```

#### 1.2 创建Mapper接口
```java
@Mapper
public interface YourMapper extends BaseMapper<YourEntity> {
    // 自定义查询方法
}
```

#### 1.3 创建Service
```java
@Service
public class YourServiceImpl extends ServiceImpl<YourMapper, YourEntity> implements YourService {
    // 业务逻辑实现
}
```

#### 1.4 创建Controller
```java
@RestController
@RequestMapping("/your-module")
@Api(tags = "业务模块")
public class YourController {
    // API接口实现
}
```

### 2. 配置管理

#### 2.1 多环境配置
- `application.yml`: 公共配置
- `application-dev.yml`: 开发环境
- `application-test.yml`: 测试环境
- `application-prod.yml`: 生产环境

#### 2.2 敏感信息加密
使用Jasypt加密敏感配置：
```bash
java -cp jasypt-1.9.3.jar org.jasypt.intf.cli.JasyptPBEStringEncryptionCLI input="your_password" password="encryption_key" algorithm=PBEWithMD5AndDES
```

### 3. 开发规范

#### 3.1 命名规范
- 类名：大驼峰命名法
- 方法名：小驼峰命名法
- 常量：全大写，下划线分隔
- 包名：全小写，点分隔

#### 3.2 注释规范
- 类和方法必须添加JavaDoc注释
- 复杂业务逻辑添加行内注释
- API接口使用Swagger注解

#### 3.3 异常处理
- 使用自定义异常类
- 避免捕获Exception
- 记录详细的错误日志

## 🔄 更新日志

### v1.1.0 (2025-07-11)
- 🛠️ **新增工具类库**: 引入17个企业级工具类，覆盖安全加密、网络请求、数据处理等各个方面
- 🔐 **安全增强**: 新增文件加密工具(EncryptUtil)和HMAC签名工具(HmacSignUtil)
- 🌐 **网络工具**: 提供HTTP请求工具(HttpRequestUtil)和OkHttp封装(OkHttpUtil)
- 📊 **数据处理**: 新增字符串处理(StringUtil)、集合差异计算(CollectionDiffUtil)等数据处理工具
- 📅 **日期时间**: 提供日期处理工具(DateUtil)和对象处理工具(ObjectUtil)
- 🎨 **前端支持**: 新增前端类型转换(FrontUtil)和枚举处理(EnumUtil)工具
- 🖼️ **媒体处理**: 提供图片Base64转换工具(ImageUtil)
- 📄 **分页优化**: 新增分页数据处理工具(PageUtil)，支持类型转换和缓存优化
- 🔧 **系统工具**: 提供Spring上下文工具(SpringContextUtil)和项目工具(ProjectUtil)
- 👤 **用户管理**: 新增登录用户工具(LoginUserUtil)，支持链路追踪和异步场景
- 🧪 **测试支持**: 提供测试数据生成工具(FakeUtil)，基于JavaFaker自动生成测试对象
- 📝 **文档完善**: 更新README文档，详细说明各工具类的功能和使用方法

### v1.0.0 (2025-01-10)
- 初始版本发布
- 基础框架搭建
- 核心功能实现

## 🐳 Docker 部署

### 使用 Docker Compose 快速启动

```bash
# 启动所有服务（包括MySQL、Redis、应用）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看应用日志
docker-compose logs -f app

# 停止所有服务
docker-compose down
```

### 单独构建应用镜像

```bash
# 构建镜像
docker build -t spring-boot-scaffold:latest .

# 运行容器
docker run -d \
  --name scaffold-app \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=docker \
  spring-boot-scaffold:latest
```

## 🔧 开发工具

### 代码生成

项目提供了代码生成工具，可以快速生成CRUD代码：

```bash
# 运行代码生成器（需要先实现）
mvn exec:java -Dexec.mainClass="com.company.project.generator.CodeGenerator"
```

### 数据库迁移

使用Flyway进行数据库版本管理：

```bash
# 执行数据库迁移
mvn flyway:migrate

# 查看迁移状态
mvn flyway:info
```

## 📊 监控和运维

### 应用监控

- **健康检查**: http://localhost:8080/admin/healthcheck
- **应用信息**: http://localhost:8080/admin/info
- **指标监控**: http://localhost:8080/admin/metrics
- **Druid监控**: http://localhost:8080/druid/

### 日志管理

日志文件位置：
- 应用日志: `./logs/application.log`
- 错误日志: `./logs/error.log`
- SQL日志: `./logs/sql.log`

### 性能调优

JVM参数建议：
```bash
-Xms512m -Xmx1024m
-XX:+UseG1GC
-XX:+UseContainerSupport
-XX:MaxRAMPercentage=75.0
```

## 🚀 部署指南

### 传统部署

1. **打包应用**
```bash
mvn clean package -DskipTests
```

2. **上传jar包到服务器**

3. **启动应用**
```bash
java -jar target/spring-boot-scaffold.jar --spring.profiles.active=prod
```

### 容器化部署

1. **构建镜像**
```bash
docker build -t your-registry/spring-boot-scaffold:1.0.0 .
```

2. **推送镜像**
```bash
docker push your-registry/spring-boot-scaffold:1.0.0
```

3. **部署到Kubernetes**
```bash
kubectl apply -f k8s/
```

## 🔐 安全配置

### 配置加密

使用Jasypt加密敏感配置：

```bash
# 加密配置值
java -cp jasypt-1.9.3.jar org.jasypt.intf.cli.JasyptPBEStringEncryptionCLI \
  input="your_password" \
  password="encryption_key" \
  algorithm=PBEWithMD5AndDES
```

### HTTPS配置

在生产环境中启用HTTPS：

```yaml
server:
  ssl:
    key-store: classpath:keystore.p12
    key-store-password: your_password
    key-store-type: PKCS12
    key-alias: your_alias
```

## 📈 扩展功能

### 集成Redis

取消注释Redis相关配置即可启用缓存功能。

### 集成消息队列

支持集成RabbitMQ、RocketMQ等消息队列。

### 集成搜索引擎

支持集成Elasticsearch进行全文搜索。

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 联系方式

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- 文档: [项目文档地址]
- 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢以下开源项目：
- [Spring Boot](https://spring.io/projects/spring-boot)
- [MyBatis Plus](https://baomidou.com/)
- [Swagger](https://swagger.io/)
- [Hutool](https://hutool.cn/)

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！
