package com.gwm.scaffold.example.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gwm.scaffold.example.entity.Series;
import com.gwm.scaffold.example.dto.SeriesCreateRequest;
import com.gwm.scaffold.example.dto.SeriesUpdateRequest;
import com.gwm.scaffold.example.dto.SeriesQueryRequest;
import com.gwm.scaffold.example.vo.SeriesVO;

import java.util.List;

/**
 * 服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface SeriesService extends IService<Series> {

    /**
     * 分页查询列表
     *
     * @param request 查询条件
     * @return 分页数据
     */
    IPage<SeriesVO> getSeriesPage(SeriesQueryRequest request);

    /**
     * 根据ID获取详情
     *
     * @param id ID
     * @return 详情
     */
    SeriesVO getSeriesById(Long id);

    /**
     * 创建
     *
     * @param request 创建请求
     * @return 详情
     */
    SeriesVO createSeries(SeriesCreateRequest request);

    /**
     * 更新
     *
     * @param request 更新请求
     * @return 详情
     */
    SeriesVO updateSeries(SeriesUpdateRequest request);

    /**
     * 删除
     *
     * @param id ID
     * @return 是否成功
     */
    boolean deleteSeries(Long id);

    /**
     * 批量删除
     *
     * @param ids ID列表
     * @return 删除数量
     */
    int batchDeleteSeriess(List<Long> ids);


}
