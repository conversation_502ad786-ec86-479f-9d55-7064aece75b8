<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwm.scaffold.example.mapper.UserMapper">

    <!-- 用户VO结果映射 -->
    <resultMap id="UserVOMap" type="com.gwm.scaffold.example.vo.UserVO">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="real_name" property="realName"/>
        <result column="email" property="email"/>
        <result column="mobile" property="mobile"/>
        <result column="gender" property="gender"/>
        <result column="age" property="age"/>
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="position" property="position"/>
        <result column="status" property="status"/>
        <result column="avatar" property="avatar"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_code" property="updateUserCode"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="selectUserVOColumns">
        u.id,
        u.username,
        u.real_name,
        u.email,
        u.mobile,
        u.gender,
        u.age,
        u.department_id,
        u.department_name,
        u.position,
        u.status,
        u.avatar,
        u.remark,
        u.create_time,
        u.create_user_code,
        u.create_user_name,
        u.update_time,
        u.update_user_code,
        u.update_user_name,
        u.version
    </sql>

    <!-- 查询条件 -->
    <sql id="whereConditions">
        <where>
            u.deleted = 0
            <if test="req.username != null and req.username != ''">
                AND u.username LIKE CONCAT('%', #{req.username}, '%')
            </if>
            <if test="req.realName != null and req.realName != ''">
                AND u.real_name LIKE CONCAT('%', #{req.realName}, '%')
            </if>
            <if test="req.email != null and req.email != ''">
                AND u.email LIKE CONCAT('%', #{req.email}, '%')
            </if>
            <if test="req.mobile != null and req.mobile != ''">
                AND u.mobile LIKE CONCAT('%', #{req.mobile}, '%')
            </if>
            <if test="req.gender != null">
                AND u.gender = #{req.gender}
            </if>
            <if test="req.departmentId != null">
                AND u.department_id = #{req.departmentId}
            </if>
            <if test="req.departmentName != null and req.departmentName != ''">
                AND u.department_name LIKE CONCAT('%', #{req.departmentName}, '%')
            </if>
            <if test="req.position != null and req.position != ''">
                AND u.position LIKE CONCAT('%', #{req.position}, '%')
            </if>
            <if test="req.status != null">
                AND u.status = #{req.status}
            </if>
            <if test="req.keyword != null and req.keyword != ''">
                AND (
                    u.username LIKE CONCAT('%', #{req.keyword}, '%')
                    OR u.real_name LIKE CONCAT('%', #{req.keyword}, '%')
                    OR u.email LIKE CONCAT('%', #{req.keyword}, '%')
                    OR u.mobile LIKE CONCAT('%', #{req.keyword}, '%')
                    OR u.department_name LIKE CONCAT('%', #{req.keyword}, '%')
                    OR u.position LIKE CONCAT('%', #{req.keyword}, '%')
                )
            </if>
            <if test="req.minAge != null">
                AND u.age >= #{req.minAge}
            </if>
            <if test="req.maxAge != null">
                AND u.age &lt;= #{req.maxAge}
            </if>
            <if test="req.createTimeStart != null and req.createTimeStart != ''">
                AND u.create_time >= #{req.createTimeStart}
            </if>
            <if test="req.createTimeEnd != null and req.createTimeEnd != ''">
                AND u.create_time &lt;= #{req.createTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultMap="UserVOMap">
        SELECT
        <include refid="selectUserVOColumns"/>
        FROM sys_user u
        <include refid="whereConditions"/>
        ORDER BY u.create_time DESC
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultMap="UserVOMap">
        SELECT
        <include refid="selectUserVOColumns"/>
        FROM sys_user u
        WHERE u.username = #{username}
          AND u.deleted = 0
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultMap="UserVOMap">
        SELECT
        <include refid="selectUserVOColumns"/>
        FROM sys_user u
        WHERE u.email = #{email}
          AND u.deleted = 0
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByMobile" resultMap="UserVOMap">
        SELECT
        <include refid="selectUserVOColumns"/>
        FROM sys_user u
        WHERE u.mobile = #{mobile}
          AND u.deleted = 0
    </select>

    <!-- 根据部门ID查询用户列表 -->
    <select id="selectByDepartmentId" resultMap="UserVOMap">
        SELECT
        <include refid="selectUserVOColumns"/>
        FROM sys_user u
        WHERE u.department_id = #{departmentId}
          AND u.deleted = 0
          AND u.status = 1
        ORDER BY u.create_time DESC
    </select>

    <!-- 统计用户数量 -->
    <select id="countUsers" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_user u
        <include refid="whereConditions"/>
    </select>

    <!-- 批量更新用户状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_user
        SET status = #{status},
            update_time = NOW()
        WHERE id IN
        <foreach collection="userIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

</mapper>
