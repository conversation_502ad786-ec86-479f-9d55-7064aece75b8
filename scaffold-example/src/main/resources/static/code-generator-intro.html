<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GWM Scaffold 代码生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.8);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .feature-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .file-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .file-type {
            background: rgba(102, 126, 234, 0.1);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .file-type-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }
        
        .file-type-desc {
            color: #7f8c8d;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .steps {
            counter-reset: step-counter;
        }
        
        .step {
            counter-increment: step-counter;
            background: rgba(255,255,255,0.8);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #667eea;
            position: relative;
            display: flex;
            align-items: flex-start;
        }
        
        .step:before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 20px;
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .step-content {
            margin-left: 20px;
        }
        
        .step-title {
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .step-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            text-decoration: none;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        
        .highlight-box h3 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .highlight-box p {
            color: #7f8c8d;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ 代码生成器</h1>
            <p>根据数据库表结构自动生成符合脚手架规范的完整CRUD代码</p>
        </div>
        
        <div class="content">
            <!-- 功能特点 -->
            <div class="section">
                <h2 class="section-title">🎯 功能特点</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🚀</span>
                        <div class="feature-title">一键生成</div>
                        <div class="feature-desc">根据数据库表一键生成完整的CRUD代码，包含所有业务层代码</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📋</span>
                        <div class="feature-title">多层架构</div>
                        <div class="feature-desc">自动生成Entity、Mapper、Service、Controller等各层代码</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🎨</span>
                        <div class="feature-title">模板定制</div>
                        <div class="feature-desc">基于FreeMarker模板，支持自定义代码模板</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📖</span>
                        <div class="feature-title">文档集成</div>
                        <div class="feature-desc">自动生成Swagger API文档注解</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🔧</span>
                        <div class="feature-title">配置灵活</div>
                        <div class="feature-desc">支持包名、输出路径、作者等配置</div>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">👁️</span>
                        <div class="feature-title">代码预览</div>
                        <div class="feature-desc">生成前可预览代码内容</div>
                    </div>
                </div>
            </div>
            
            <!-- 生成文件类型 -->
            <div class="section">
                <h2 class="section-title">📁 生成文件类型</h2>
                <div class="file-types">
                    <div class="file-type">
                        <div class="file-type-name">Entity</div>
                        <div class="file-type-desc">数据库表对应的Java实体类，包含字段映射和注解</div>
                    </div>
                    <div class="file-type">
                        <div class="file-type-name">Mapper</div>
                        <div class="file-type-desc">MyBatis Mapper接口，继承BaseMapper提供基础CRUD</div>
                    </div>
                    <div class="file-type">
                        <div class="file-type-name">MapperXml</div>
                        <div class="file-type-desc">MyBatis XML映射文件，包含自定义SQL</div>
                    </div>
                    <div class="file-type">
                        <div class="file-type-name">Service</div>
                        <div class="file-type-desc">业务逻辑服务接口定义</div>
                    </div>
                    <div class="file-type">
                        <div class="file-type-name">ServiceImpl</div>
                        <div class="file-type-desc">业务逻辑服务实现类</div>
                    </div>
                    <div class="file-type">
                        <div class="file-type-name">Controller</div>
                        <div class="file-type-desc">REST API控制器，包含完整的CRUD接口</div>
                    </div>
                </div>
            </div>
            
            <!-- 使用步骤 -->
            <div class="section">
                <h2 class="section-title">🔧 使用步骤</h2>
                <div class="steps">
                    <div class="step">
                        <div class="step-content">
                            <div class="step-title">选择数据表</div>
                            <div class="step-desc">从数据库表列表中选择要生成代码的表，系统会自动加载数据库中的所有表</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-content">
                            <div class="step-title">配置参数</div>
                            <div class="step-desc">设置模块名、包名、作者等基本信息，支持自动填充和智能推荐</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-content">
                            <div class="step-title">预览代码</div>
                            <div class="step-desc">可选择预览将要生成的代码内容，确保生成的代码符合预期</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-content">
                            <div class="step-title">生成代码</div>
                            <div class="step-desc">一键生成完整的业务代码，自动创建目录结构和文件</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 代码示例 -->
            <div class="section">
                <h2 class="section-title">💻 生成代码示例</h2>
                <p style="color: #7f8c8d; margin-bottom: 20px;">以下是代码生成器生成的典型代码示例：</p>
                
                <h3 style="color: #2c3e50; margin: 20px 0 10px 0;">Entity 实体类</h3>
                <div class="code-example">@Data
@TableName("sys_user")
@ApiModel(description = "用户信息")
public class User extends BaseSuperEntity implements BaseEntity {
    
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("用户ID")
    private Long id;
    
    @ApiModelProperty("用户名")
    private String username;
    
    @ApiModelProperty("邮箱")
    private String email;
}</div>
                
                <h3 style="color: #2c3e50; margin: 20px 0 10px 0;">Controller 控制器</h3>
                <div class="code-example">@RestController
@RequestMapping("/api/users")
@Api(tags = "用户管理")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @PostMapping
    @ApiOperation("新增用户")
    public Result&lt;User&gt; add(@RequestBody @Valid User user) {
        return userService.save(user) ? Result.success(user) : Result.error("新增失败");
    }
    
    @GetMapping("/{id}")
    @ApiOperation("获取用户详情")
    public Result&lt;User&gt; getById(@PathVariable Long id) {
        User user = userService.getById(id);
        return user != null ? Result.success(user) : Result.error("用户不存在");
    }
}</div>
            </div>
            
            <!-- 突出显示框 -->
            <div class="highlight-box">
                <h3>🎉 立即体验强大的代码生成功能</h3>
                <p>只需几分钟，就能生成完整的业务代码，大大提高开发效率！</p>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="/generator.html" class="btn">
                    ⚡ 打开代码生成器
                </a>
                <a href="/api/scaffold/docs" class="btn btn-outline">
                    📚 查看完整文档
                </a>
                <a href="/" class="btn btn-outline">
                    🏠 返回首页
                </a>
            </div>
        </div>
    </div>
</body>
</html>
