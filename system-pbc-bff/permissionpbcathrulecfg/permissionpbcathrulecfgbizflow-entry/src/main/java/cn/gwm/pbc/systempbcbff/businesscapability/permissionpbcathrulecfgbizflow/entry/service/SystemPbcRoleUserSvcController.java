package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRoleUserSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcRoleUserSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserBindBatchDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserUnbindDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.lang.Override;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限PBC-角色用户管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@RestController("PermissionPBCAthRuleCfgBizFlowSystemPbcRoleUserSvcCON")
public class SystemPbcRoleUserSvcController implements SystemPbcRoleUserSvcI {

    @Autowired
    private SystemPbcRoleUserSvc systemPbcRoleUserSvc;

    @Override
    public Response<Map<String, Object>> list(@RequestBody(required = true) RoleUserListDTO request) {
    	Map mapReturn = systemPbcRoleUserSvc.list(request);
		return Response.buildSuccess(mapReturn);
	}

    @Override
    public Response<Void> bind(@RequestBody(required = true) RoleUserBindDTO request) {
    	systemPbcRoleUserSvc.bind(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<Void> bindBatch(@RequestBody(required = true) RoleUserBindBatchDTO request) {
    	systemPbcRoleUserSvc.bindBatch(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<Void> unbind(@RequestBody(required = true) RoleUserUnbindDTO request) {
    	systemPbcRoleUserSvc.unbind(request);
		return Response.buildSuccess();
	}
}
