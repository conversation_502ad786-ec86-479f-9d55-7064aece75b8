package cn.gwm.pbc.systempbcbff;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * system-pbc-bff启动类
 *
 * <AUTHOR> 管理员
 * @date 2023.12.05
 */
@SpringBootApplication(scanBasePackages = { "cn.gwm.pbc.systempbcbff", "com.alibaba.bizworks" })
@EnableFeignClients(basePackages = { "cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel" })
public class Application {

    public static void main(String[] args) {
    	SpringApplication.run(Application.class, args);
	}
}
