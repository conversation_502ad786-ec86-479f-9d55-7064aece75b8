<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gwm.scaffold</groupId>
        <artifactId>scaffold-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>system-pbc-bff</artifactId>
    <packaging>pom</packaging>
    <name>system-pbc-bff</name>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-cloud-dependencies-version>2021.0.5</spring-cloud-dependencies-version>
        <springfox-boot-starter.version>3.0.0</springfox-boot-starter.version>
        <mapstruct-jdk8.version>1.4.2.Final</mapstruct-jdk8.version>
        <mapstruct-processor.version>1.4.2.Final</mapstruct-processor.version>
        <h2.version>2.1.214</h2.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <springcloud-alicloud-version>2021.0.5.0</springcloud-alicloud-version>
        <bizworks.bwaf.version>2.3.1</bizworks.bwaf.version>
        <mybatis-plus-extension.version>3.2.0</mybatis-plus-extension.version>
        <mybatis-plus-boot-starter.version>3.2.0</mybatis-plus-boot-starter.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.bizworks</groupId>
                <artifactId>bwaf-core-runtime</artifactId>
                <version>${bizworks.bwaf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.7.18</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${springcloud-alicloud-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus-extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct-processor.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct-jdk8.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.bizworks</groupId>
                <artifactId>bwaf-common-dto</artifactId>
                <version>${bizworks.bwaf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.bizworks</groupId>
                <artifactId>bwaf-core-specification-common</artifactId>
                <version>${bizworks.bwaf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.bizworks</groupId>
                <artifactId>bwaf-core-specification-ddd</artifactId>
                <version>${bizworks.bwaf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.bizworks</groupId>
                <artifactId>bwaf-core-specification-pbc</artifactId>
                <version>${bizworks.bwaf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.bizworks</groupId>
                <artifactId>bwaf-core-traffic-logging</artifactId>
                <version>${bizworks.bwaf.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.7.18</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <encoding>UTF-8</encoding>
                        <compilerArgs>
                            <arg>-parameters</arg>
                            <arg>-Xlint:deprecation</arg>
                            <arg>-Xlint:unchecked</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <modules>
        <module>permissionpbcathrulecfg</module>
        <module>system-pbc-bff-starter</module>
    </modules>
    <distributionManagement>
        <repository>
            <id>maven-release</id>
            <url>http://nexus.gwm.cn/repository/maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://nexus.gwm.cn/repository/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>

</project>