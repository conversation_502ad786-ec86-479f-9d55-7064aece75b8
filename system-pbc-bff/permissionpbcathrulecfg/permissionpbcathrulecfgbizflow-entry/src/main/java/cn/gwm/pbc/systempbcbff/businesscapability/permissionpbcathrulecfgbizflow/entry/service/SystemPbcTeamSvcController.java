package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcTeamSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcTeamSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.department.TeamTreeDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.lang.Override;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限PBC-用户组管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@RestController("PermissionPBCAthRuleCfgBizFlowSystemPbcTeamSvcCON")
public class SystemPbcTeamSvcController implements SystemPbcTeamSvcI {

    @Autowired
    private SystemPbcTeamSvc systemPbcTeamSvc;

    @Override
    public Response<Map<String, Object>> tree(@RequestBody(required = true) TeamTreeDTO request) {
    	Map mapReturn = systemPbcTeamSvc.tree(request);
		return Response.buildSuccess(mapReturn);
	}
}
