server:
  port: 8080
project:
  name: system-pbc-bff
spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:~/test
    username: sa
    password:
  swagger:
    enable: true
    basePackage: cn.gwm.pbc.systempbcbff
    version: 1.0.0
    title: 测试标题
    description: 测试描述
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  h2:
    console:
      settings:
        web-allow-others: true
        trace: true
      path: /h2-console
      enable: true

bizworks:
  appId: system-pbc-bff
  tenantId: replacedByBizworks
feign:
  httpclient:
    enabled: true
    connection-timeout: 3000
    max-connections: 500
    max-connections-per-route: 200

gwm:
  sso:
    url: http://sso.test.paas.gwm.cn
#    platform-code: c141b5c059e61b74c537efd1c08d7ab6
    platform-code: c141b5c059e61b74c537efd1c08d7ab6
  permission:
    platform-code: c141b5c059e61b74c537efd1c08d7ab6
    platform-secret: 063203ff0dc507710d8a92a3a2ac28b2
  open:
    url: https://gwapi.gwm.cn
    env: sandbox
    app-key: 7MX9779096B5
    app-secret: 158062b367a94a1e8e9be1ad12a2502a
auth:
  filter:
    enabled: true
  ignore:
    whites:
      - /system/sso/callback
      - /*/actuator
      - /app/get
      - /SystemPbcSsoCallbackSvc/ssoCallback/v1
center:
  url: http://platform-system-manage.test.paas.gwm.cn