package com.gwm.scaffold.data.annotation;

import java.lang.annotation.*;

/**
 * 数据权限注解
 * 
 * 用于标识需要进行数据权限控制的方法
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataPermission {

    /**
     * 权限类型
     */
    Type value() default Type.USER;

    /**
     * 权限字段名称
     */
    String field() default "create_user_code";

    /**
     * 权限表别名
     */
    String alias() default "";

    /**
     * 是否启用
     */
    boolean enabled() default true;

    /**
     * 权限类型枚举
     */
    enum Type {
        /**
         * 用户权限：只能查看自己创建的数据
         */
        USER,
        
        /**
         * 部门权限：只能查看本部门的数据
         */
        DEPT,
        
        /**
         * 部门及下级权限：可以查看本部门及下级部门的数据
         */
        DEPT_AND_CHILD,
        
        /**
         * 自定义权限：通过自定义逻辑控制
         */
        CUSTOM
    }
}
