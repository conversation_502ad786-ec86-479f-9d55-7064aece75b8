package com.gwm.scaffold.example.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwm.scaffold.auth.annotation.RequirePermission;
import com.gwm.scaffold.core.domain.Result;
import com.gwm.scaffold.example.dto.SeriesCreateRequest;
import com.gwm.scaffold.example.dto.SeriesUpdateRequest;
import com.gwm.scaffold.example.dto.SeriesQueryRequest;
import com.gwm.scaffold.example.service.SeriesService;
import com.gwm.scaffold.example.vo.SeriesVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 管理控制器
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Tag(name = "管理", description = "相关接口")
@RestController
@RequestMapping("/api/seriess")
public class SeriesController {

    @Autowired
    private SeriesService seriesService;

    /**
     * 分页查询列表
     */
    @Operation(summary = "分页查询列表", description = "支持多条件查询")
    @GetMapping
    @RequirePermission("series:list")
    public Result<IPage<SeriesVO>> list(SeriesQueryRequest request) {
        log.info("分页查询列表，参数：{}", request);
        
        IPage<SeriesVO> result = seriesService.getSeriesPage(request);
        return Result.success(result);
    }

    /**
     * 根据ID获取详情
     */
    @Operation(summary = "获取详情", description = "根据ID获取详细信息")
    @GetMapping("/{id}")
    @RequirePermission("series:read")
    public Result<SeriesVO> getById(@Parameter(description = "ID") @PathVariable Long id) {
        log.info("获取详情，ID：{}", id);
        
        SeriesVO result = seriesService.getSeriesById(id);
        return Result.success(result);
    }

    /**
     * 创建
     */
    @Operation(summary = "创建", description = "创建新的")
    @PostMapping
    @RequirePermission("series:create")
    public Result<SeriesVO> create(@Valid @RequestBody SeriesCreateRequest request) {
        log.info("创建，参数：{}", request);
        
        SeriesVO result = seriesService.createSeries(request);
        return Result.success(result);
    }

    /**
     * 更新
     */
    @Operation(summary = "更新", description = "更新信息")
    @PutMapping("/{id}")
    @RequirePermission("series:update")
    public Result<SeriesVO> update(
            @Parameter(description = "ID") @PathVariable Long id,
            @Valid @RequestBody SeriesUpdateRequest request) {
        log.info("更新，ID：{}，参数：{}", id, request);
        
        request.setId(id);
        SeriesVO result = seriesService.updateSeries(request);
        return Result.success(result);
    }

    /**
     * 删除
     */
    @Operation(summary = "删除", description = "根据ID删除")
    @DeleteMapping("/{id}")
    @RequirePermission("series:delete")
    public Result<String> delete(@Parameter(description = "ID") @PathVariable Long id) {
        log.info("删除，ID：{}", id);
        
        boolean success = seriesService.deleteSeries(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * 批量删除
     */
    @Operation(summary = "批量删除", description = "根据ID列表批量删除")
    @DeleteMapping("/batch")
    @RequirePermission("series:delete")
    public Result<String> batchDelete(@Parameter(description = "ID列表") @RequestBody List<Long> ids) {
        log.info("批量删除，ID列表：{}", ids);
        
        int count = seriesService.batchDeleteSeriess(ids);
        return Result.success("成功删除 " + count + " 个");
    }


}
