package com.gwm.scaffold.example.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.gwm.scaffold.core.entity.BaseSuperEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("T_SERIES")
@Schema(description = "信息")
public class Series extends BaseSuperEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id", hidden = true, requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    /**
     * name
     */
    @Schema(description = "name", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    /**
     * englishName
     */
    @Schema(description = "englishName")
    private String englishName;

    /**
     * brandId
     */
    @Schema(description = "brandId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long brandId;

    /**
     * type
     */
    @Schema(description = "type", requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;

    /**
     * level
     */
    @Schema(description = "level", requiredMode = Schema.RequiredMode.REQUIRED)
    private String level;

    /**
     * launchTime
     */
    @Schema(description = "launchTime", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date launchTime;

    /**
     * discontinueTime
     */
    @Schema(description = "discontinueTime")
    private Date discontinueTime;

    /**
     * minPrice
     */
    @Schema(description = "minPrice")
    private BigDecimal minPrice;

    /**
     * maxPrice
     */
    @Schema(description = "maxPrice")
    private BigDecimal maxPrice;

    /**
     * imageUrl
     */
    @Schema(description = "imageUrl")
    private String imageUrl;

    /**
     * description
     */
    @Schema(description = "description")
    private String description;

    /**
     * status
     */
    @Schema(description = "status")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    private Integer sortWeight;

    /**
     * deleted
     */
    @TableLogic
    @Schema(description = "deleted")
    private Integer deleted;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

}
