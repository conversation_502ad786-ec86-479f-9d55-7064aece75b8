package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcManageSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcSsoCallbackSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcSsoCallbackSvcI;
import java.lang.Override;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.ArrayList;
import java.util.stream.Collectors;

import com.alibaba.bizworks.core.runtime.context.UserContext;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.LoginRltVoClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.UserDetailDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 权限PBC-单点认证服务管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.15
 */
@RestController("PermissionPBCAthRuleCfgBizFlowSystemPbcSsoCallbackSvcCON")
public class SystemPbcSsoCallbackSvcController implements SystemPbcSsoCallbackSvcI {

    @Resource
    private SystemPbcSsoCallbackSvc systemPbcSsoCallbackSvc;
    @Autowired
    private SystemPbcManageSvc systemPbcManageSvc;
    
    /**
     * SSO回调结果缓存，5秒失效
     */
    private static final Cache<String, Response<LoginRltVoClientDTO>> SSO_CALLBACK_CACHE = 
        Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .maximumSize(1000)
            .build();

    @Override
    public Response<LoginRltVoClientDTO> ssoCallback() {
        // 使用用户代码作为缓存键
        String userCode = UserContext.getUserCode();
        String cacheKey = "sso_callback_user_" + userCode;
        
        // 尝试从缓存获取结果
        Response<LoginRltVoClientDTO> cachedResult = SSO_CALLBACK_CACHE.getIfPresent(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }
        
        // 先获取SSO回调结果
        LoginRltVoClientDTO loginRltVOClientDTO = systemPbcSsoCallbackSvc.ssoCallback();
        // 缓存未命中，执行角色查询逻辑
        UserDetailDTO request = new UserDetailDTO();
        request.setCode(userCode);
        Map<String, Object> rolesResult = systemPbcManageSvc.detail(request);
        
        // 提取角色代码列表
        List<String> roles = new ArrayList<>();
        if (rolesResult != null && rolesResult.get("roles") != null) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> rolesList = (List<Map<String, Object>>) rolesResult.get("roles");
            if (rolesList != null) {
                roles = rolesList.stream()
                    .map(role -> (String) role.get("code"))
                    .filter(code -> code != null)
                    .collect(Collectors.toList());
            }
        }
        
        loginRltVOClientDTO.setRoles(roles);
        
        Response<LoginRltVoClientDTO> result = Response.buildSuccess(loginRltVOClientDTO);
        
        // 将结果存入缓存
        SSO_CALLBACK_CACHE.put(cacheKey, result);
        
        return result;
    }
}
