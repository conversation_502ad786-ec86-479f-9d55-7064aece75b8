package com.gwm.scaffold.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwm.scaffold.example.entity.Series;
import com.gwm.scaffold.example.dto.SeriesQueryRequest;
import com.gwm.scaffold.example.vo.SeriesVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface SeriesMapper extends BaseMapper<Series> {

    /**
     * 分页查询列表
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 分页数据
     */
    IPage<Series> selectSeriesPage(Page<Series> page, @Param("request") SeriesQueryRequest request);


}
