package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.menu;

import com.alibaba.bizworks.core.specification.*;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@StructureObject(name = "MenuInfoItemVO", desc = "")
@Data
public class MenuInfoItemVO implements Serializable {
    /**
     * id
     */
    @Field(name = "id", desc = "")
    private String id;
    /**
     * code
     */
    @Field(name = "code", desc = "")
    private String code;
    /**
     * name
     */
    @Field(name = "name", desc = "")
    private String name;
    /**
     * url
     */
    @Field(name = "url", desc = "")
    private String url;
    /**
     * ico
     */
    @Field(name = "ico", desc = "")
    private String ico;
    /**
     * type
     */
    @Field(name = "type", desc = "")
    private Integer type;
    /**
     * path
     */
    @Field(name = "path", desc = "")
    private String path;
    /**
     * component
     */
    @Field(name = "component", desc = "")
    private String component;
    /**
     * remark
     */
    @Field(name = "remark", desc = "")
    private String remark;
    /**
     * ext1
     */
    @Field(name = "ext1", desc = "")
    private String ext1;
    /**
     * ext2
     */
    @Field(name = "ext2", desc = "")
    private String ext2;
    /**
     * ext3
     */
    @Field(name = "ext3", desc = "")
    private String ext3;
    /**
     * enable
     */
    @Field(name = "enable", desc = "")
    private Integer enable;
    /**
     * children
     */
    @Field(name = "children", desc = "")
    private List<MenuInfoItemVO> children;
    /**
     * parentId
     */
    @Field(name = "parentId", desc = "")
    @JSONField(name = "parent_id")
    private String parentId;
    /**
     * sortNo
     */
    @Field(name = "sortNo", desc = "")
    @JSONField(name = "sort_no")
    private Integer sortNo;
    /**
     * creatorCode
     */
    @Field(name = "creatorCode", desc = "")
    @JSONField(name = "creator_code")
    private String creatorCode;
    /**
     * createTime
     */
    @Field(name = "createTime", desc = "")
    @JSONField(name = "create_time")
    @JsonFormat(pattern = " yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * updatorCode
     */
    @Field(name = "updatorCode", desc = "")
    @JSONField(name = "updator_code")
    private String updatorCode;
    /**
     * updateTime
     */
    @Field(name = "updateTime", desc = "")
    @JSONField(name = "update_time")
    @JsonFormat(pattern = " yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * openMethod
     */
    @Field(name = "openMethod", desc = "")
    @JSONField(name = "open_method")
    private Integer openMethod;
    /**
     * linkFlag
     */
    @Field(name = "linkFlag", desc = "")
    @JSONField(name = "link_flag")
    private Integer linkFlag;

}
