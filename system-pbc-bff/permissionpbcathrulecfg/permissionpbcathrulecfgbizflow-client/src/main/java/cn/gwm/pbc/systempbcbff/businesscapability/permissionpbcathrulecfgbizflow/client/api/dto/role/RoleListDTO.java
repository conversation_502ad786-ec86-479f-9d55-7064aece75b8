package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role;

import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleListDTO", desc = "")
@Data
public class RoleListDTO implements Serializable {

    /**
     * 角色编码（应用下唯一）
     */
    @Field(name = "角色编码（应用下唯一）", desc = "角色编码（应用下唯一）")
    private String code;
    /**
     * 角色名称
     */
    @Field(name = "角色名称", desc = "角色名称")
    private String name;
    /**
     * 页码
     */
    @Field(name = "页码", desc = "页码")
    private Integer page;
    /**
     * 条数（默认返回10条）
     */
    @Field(name = "条数（默认返回10条）", desc = "条数（默认返回10条）")
    private Integer size;
    /**
     * 角色状态，0禁用1启用
     */
    @Field(name = "角色状态，0禁用1启用", desc = "角色状态，0禁用1启用")
    private String state;


}
