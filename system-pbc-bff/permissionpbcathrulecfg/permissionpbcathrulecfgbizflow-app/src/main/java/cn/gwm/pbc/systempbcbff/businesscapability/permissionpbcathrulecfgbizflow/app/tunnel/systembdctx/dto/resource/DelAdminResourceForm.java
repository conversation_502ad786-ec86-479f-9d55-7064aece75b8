package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("已接入平台删除菜单参数对象")
public class DelAdminResourceForm implements Serializable {

    @JsonIgnore
    private String account;

	@ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 菜单ID
     */
    @ApiModelProperty("菜单ID")
    private String id;

}