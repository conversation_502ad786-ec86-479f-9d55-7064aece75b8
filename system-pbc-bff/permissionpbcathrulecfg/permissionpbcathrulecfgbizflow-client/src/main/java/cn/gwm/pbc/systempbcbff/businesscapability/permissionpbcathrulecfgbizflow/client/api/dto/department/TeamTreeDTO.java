package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.department;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "TeamTreeDTO", desc = "")
@Data
public class TeamTreeDTO implements Serializable {

    /**
     * teamName
     */
    @Field(name = "teamName", desc = "")
    @Alias("team_name")
    private String teamName;

}
