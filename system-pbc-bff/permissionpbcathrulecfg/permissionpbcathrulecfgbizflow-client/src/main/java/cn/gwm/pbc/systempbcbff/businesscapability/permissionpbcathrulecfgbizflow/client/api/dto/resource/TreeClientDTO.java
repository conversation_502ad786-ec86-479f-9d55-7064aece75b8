package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource;

import com.alibaba.bizworks.core.specification.*;
import lombok.Data;
/**
 * <AUTHOR>
 */
@StructureObject(name = "TreeClientDTO", desc = "")
@Data
public class TreeClientDTO {

    /**
     *  类别（0-菜单管理，1-数据管理）
     *  category
     */
    @Field(name = "类别（0-菜单管理，1-数据管理）", desc = "类别（0-菜单管理，1-数据管理）category")
    private Integer category;

}
