package com.gwm.scaffold.example;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * GWM Scaffold 示例应用启动类
 * 
 * 展示如何使用GWM Scaffold脚手架快速构建企业级应用
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@SpringBootApplication
@MapperScan("com.gwm.scaffold.example.mapper")
@ComponentScan(basePackages = {"com.gwm.scaffold.example", "com.gwm.scaffold.openapi"})
public class ScaffoldExampleApplication {

    public static void main(String[] args) {
        SpringApplication.run(ScaffoldExampleApplication.class, args);
        
        System.out.println();
        System.out.println("🎉 GWM Scaffold 示例应用启动成功！");
        System.out.println();
        System.out.println("📱 应用访问地址:");
        System.out.println("   主页: http://localhost:8080");
        System.out.println("   用户管理: http://localhost:8080/api/users");
        System.out.println("   订单管理: http://localhost:8080/api/orders");
        System.out.println();
        System.out.println("📊 监控面板:");
        System.out.println("   监控首页: http://localhost:8080/scaffold/monitor");
        System.out.println();
        System.out.println("📚 API文档:");
        System.out.println("   文档首页: http://localhost:8080/scaffold/docs");
        System.out.println("   Swagger UI: http://localhost:8080/swagger-ui.html");
        System.out.println("   Bootstrap UI: http://localhost:8080/doc.html");
        System.out.println();
    }
}
