package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule.*;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;

import java.util.Map;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-规则服务")
public interface SystemPbcRuleSvc {
    /**
     * 权限中心-查询规则列表
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-查询规则列表")
    @Method(desc = "权限中心-查询规则列表", name = "权限中心-查询规则列表 v2-manage/admin/role/rule/list")
    Map<String, Object> list(@Parameter(name = "request", required = true) RuleListFormClientDTO request);
    /**
     * 权限中心-规则列表(下拉列表)
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则列表(下拉列表)")
    @Method(desc = "权限中心-规则列表(下拉列表)", name = "权限中心-规则列表(下拉列表) v2-manage/admin/role/rule/list/drop/down")
    Map<String, Object> listDropDown(@Parameter(name = "request", required = true) RuleListDownClientDTO request);
    /**
     * 权限中心-规则创建
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则创建")
    @Method(desc = "权限中心-规则创建", name = "权限中心-规则创建 v2-manage/admin/role/rule/create")
    Map<String, Object> create(@Parameter(name = "request", required = true) RuleCreateFormClientDTO request);
    /**
     * 权限中心-规则详情
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则详情")
    @Method(desc = "权限中心-规则详情", name = "权限中心-规则详情 v2-manage/admin/role/rule/detail")
    Map<String, Object> detail(@Parameter(name = "request", required = true) RuleDetailClientDTO request);
    /**
     * 权限中心-规则编辑
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则编辑")
    @Method(desc = "权限中心-规则编辑", name = "权限中心-规则编辑 v2-manage/admin/rule/edit")
    Map<String, Object> edit(@Parameter(name = "request", required = true) RuleEditFormClientDTO request);
    /**
     * 权限中心-规则删除
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则删除")
    @Method(desc = "权限中心-规则删除", name = "权限中心-规则删除 v2-manage/admin/role/rule/delete")
    Map<String, Object> delete(@Parameter(name = "request", required = true) RuleDelFormClientDTO request);
    /**
     * 权限中心-规则状态修改
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则状态修改")
    @Method(desc = "权限中心-规则状态修改", name = "权限中心-规则状态修改 v2-manage/admin/role/rule/edit/enable")
    Map<String, Object> editEnable(@Parameter(name = "request", required = true) RuleDelFormClientDTO request);

}
