package com.gwm.scaffold.example.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gwm.scaffold.example.entity.Product;
import com.gwm.scaffold.example.dto.ProductCreateRequest;
import com.gwm.scaffold.example.dto.ProductUpdateRequest;
import com.gwm.scaffold.example.dto.ProductQueryRequest;
import com.gwm.scaffold.example.vo.ProductVO;

import java.util.List;

/**
 * 产品信息表服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface ProductService extends IService<Product> {

    /**
     * 分页查询产品信息表列表
     *
     * @param request 查询条件
     * @return 产品信息表分页数据
     */
    IPage<ProductVO> getProductPage(ProductQueryRequest request);

    /**
     * 根据ID获取产品信息表详情
     *
     * @param id 产品信息表ID
     * @return 产品信息表详情
     */
    ProductVO getProductById(Long id);

    /**
     * 创建产品信息表
     *
     * @param request 创建请求
     * @return 产品信息表详情
     */
    ProductVO createProduct(ProductCreateRequest request);

    /**
     * 更新产品信息表
     *
     * @param request 更新请求
     * @return 产品信息表详情
     */
    ProductVO updateProduct(ProductUpdateRequest request);

    /**
     * 删除产品信息表
     *
     * @param id 产品信息表ID
     * @return 是否成功
     */
    boolean deleteProduct(Long id);

    /**
     * 批量删除产品信息表
     *
     * @param ids 产品信息表ID列表
     * @return 删除数量
     */
    int batchDeleteProducts(List<Long> ids);


}
