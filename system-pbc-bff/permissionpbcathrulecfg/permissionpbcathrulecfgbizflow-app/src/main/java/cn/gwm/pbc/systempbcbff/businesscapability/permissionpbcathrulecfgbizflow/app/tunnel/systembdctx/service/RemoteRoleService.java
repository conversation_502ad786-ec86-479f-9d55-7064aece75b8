package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.RemoteUserBindRoleDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback.RemoteRoleFallbackFactory;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthRequestInterceptor;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteRoleService", url = "EMPTY", configuration = {AuthRequestInterceptor.class}, fallback = RemoteRoleFallbackFactory.class)
public interface RemoteRoleService {

    /**
     * 查看角色下已绑定的用户（非分页）
     *
     * @param roleUserListAllDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/user/list/all")
    CommonRlt<JSONObject> roleUserListAll(@RequestBody RemoteRoleUserListAllDTO roleUserListAllDTO);

    /**
     * 解绑菜单
     *
     * @param roleUnbindMenuDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/resource/unbind")
    CommonRlt<JSONObject> roleUnbindMenu(@RequestBody RemoteRoleUnbindMenuDTO roleUnbindMenuDTO);

    /**
     * 创建角色
     *
     * @param roleCreateDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/create")
    CommonRlt<JSONObject> roleCreate(@RequestBody RemoteRoleCreateDTO roleCreateDTO);

    /**
     * 编辑角色
     *
     * @param roleEditDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/edit")
    CommonRlt<JSONObject> roleEdit(@RequestBody RemoteRoleEditDTO roleEditDTO);

    /**
     * 删除角色
     *
     * @param roleDeleteDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/delete")
    CommonRlt<JSONArray> roleDelete(@RequestBody RemoteRoleDeleteDTO roleDeleteDTO);

    /**
     * 查看角色详情
     *
     * @param roleDetailDTO
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/role/get")
    CommonRlt<JSONObject> roleDetail(@SpringQueryMap RemoteRoleDetailDTO roleDetailDTO);

    /**
     * 查询角色列表
     *
     * @param roleListDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/list")
    CommonRlt<JSONObject> roleList(@RequestBody RemoteRoleListDTO roleListDTO);

    /**
     * 启用禁用角色
     *
     * @param roleStateDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/state/batch")
    CommonRlt<JSONObject> roleState(@RequestBody RemoteRoleStateDTO roleStateDTO);

    /**
     * 角色绑定多用户
     *
     * @param roleBindUserDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/user/bind")
    CommonRlt<JSONArray> roleBindUser(@RequestBody RemoteRoleBindUserDTO roleBindUserDTO);

    /**
     * 角色用户绑定
     *
     * @param roleBindUsersDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/users/bind")
    CommonRlt<JSONObject> roleBindUsers(@RequestBody RemoteRoleBindUsersDTO roleBindUsersDTO);

    /**
     * 角色绑定菜单
     *
     * @param roleBindMenuDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/resource/bind")
    CommonRlt<JSONObject> roleBindMenu(@RequestBody RemoteRoleBindMenuDTO roleBindMenuDTO);

    /**
     * 用户绑定多角色
     *
     * @param userBindRoleDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/bind/user")
    CommonRlt<JSONObject> userBindRole(@RequestBody RemoteUserBindRoleDTO userBindRoleDTO);

    /**
     * 查询角色绑定菜单树
     *
     * @param roleMenuTreeDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/resource/tree")
    CommonRlt<JSONArray> roleMenuTree(@RequestBody RemoteRoleMenuTreeDTO roleMenuTreeDTO);

    /**
     * 解除角色下已绑定用户
     *
     * @param roleUnbindUserDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/user/unbind")
    CommonRlt<JSONObject> roleUnbindUser(@RequestBody RemoteRoleUnbindUserDTO roleUnbindUserDTO);

    /**
     * 查询角色已绑定用户
     *
     * @param roleUserListDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/user/list")
    CommonRlt<JSONObject> roleUserList(@RequestBody RemoteRoleUserListDTO roleUserListDTO);

}
