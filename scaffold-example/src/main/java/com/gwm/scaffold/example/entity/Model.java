package com.gwm.scaffold.example.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.gwm.scaffold.core.entity.BaseSuperEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("T_MODEL")
@Schema(description = "信息")
public class Model extends BaseSuperEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id", hidden = true, requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    /**
     * name
     */
    @Schema(description = "name", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    /**
     * seriesId
     */
    @Schema(description = "seriesId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long seriesId;

    /**
     * yearModel
     */
    @Schema(description = "yearModel", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer yearModel;

    /**
     * displacement
     */
    @Schema(description = "displacement")
    private BigDecimal displacement;

    /**
     * maxPower
     */
    @Schema(description = "maxPower")
    private Integer maxPower;

    /**
     * maxTorque
     */
    @Schema(description = "maxTorque")
    private Integer maxTorque;

    /**
     * transmission
     */
    @Schema(description = "transmission", requiredMode = Schema.RequiredMode.REQUIRED)
    private String transmission;

    /**
     * gearCount
     */
    @Schema(description = "gearCount")
    private Integer gearCount;

    /**
     * driveType
     */
    @Schema(description = "driveType", requiredMode = Schema.RequiredMode.REQUIRED)
    private String driveType;

    /**
     * fuelType
     */
    @Schema(description = "fuelType", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fuelType;

    /**
     * bodyStructure
     */
    @Schema(description = "bodyStructure", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bodyStructure;

    /**
     * seatCount
     */
    @Schema(description = "seatCount", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer seatCount;

    /**
     * length
     */
    @Schema(description = "length")
    private Integer length;

    /**
     * width
     */
    @Schema(description = "width")
    private Integer width;

    /**
     * height
     */
    @Schema(description = "height")
    private Integer height;

    /**
     * wheelbase
     */
    @Schema(description = "wheelbase")
    private Integer wheelbase;

    /**
     * curbWeight
     */
    @Schema(description = "curbWeight")
    private Integer curbWeight;

    /**
     * tankCapacity
     */
    @Schema(description = "tankCapacity")
    private Integer tankCapacity;

    /**
     * fuelConsumption
     */
    @Schema(description = "fuelConsumption")
    private BigDecimal fuelConsumption;

    /**
     * guidePrice
     */
    @Schema(description = "guidePrice", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal guidePrice;

    /**
     * launchTime
     */
    @Schema(description = "launchTime", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date launchTime;

    /**
     * discontinueTime
     */
    @Schema(description = "discontinueTime")
    private Date discontinueTime;

    /**
     * imageUrls
     */
    @Schema(description = "imageUrls")
    private String imageUrls;

    /**
     * configDetails
     */
    @Schema(description = "configDetails")
    private String configDetails;

    /**
     * status
     */
    @Schema(description = "status")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    private Integer sortWeight;

    /**
     * deleted
     */
    @TableLogic
    @Schema(description = "deleted")
    private Integer deleted;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

}
