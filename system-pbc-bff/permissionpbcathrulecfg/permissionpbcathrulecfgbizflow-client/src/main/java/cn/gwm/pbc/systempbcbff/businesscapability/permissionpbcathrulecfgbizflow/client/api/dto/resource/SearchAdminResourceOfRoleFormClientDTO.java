package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "SearchAdminResourceOfRoleFormClientDTO", desc = "null")
@Data
@ApiModel("查询已接入平台角色菜单参数对象")
public class SearchAdminResourceOfRoleFormClientDTO implements Serializable {

	@Field(name = "platformCode", desc = "")
    @ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 角色ID
     */
    @Field(name = "id", desc = "")
    @ApiModelProperty("角色ID")
    private String id;

    /**
     * 菜单名称
     */
    @Field(name = "resourceName", desc = "")
    @ApiModelProperty("菜单名称")
    @JsonProperty(value = "resource_name")
    private String resourceName;

    /**
     * 菜单编号
     */
    @Field(name = "resourceCode", desc = "")
    @ApiModelProperty("菜单编号")
    @JsonProperty(value = "resource_code")
    private String resourceCode;

    /**
     * 类别（0-菜单管理，1-数据管理）
     */
    @Field(name = "category", desc = "")
    @ApiModelProperty("类别（0-菜单管理，1-数据管理）")
    @JsonProperty(value = "category")
    private Integer category;

}