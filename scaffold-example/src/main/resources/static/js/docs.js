// 文档页面交互脚本

document.addEventListener('DOMContentLoaded', function() {
    initNavigation();
    initSmoothScroll();
    initCodeCopy();
    loadDynamicContent();
});

// 初始化导航
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.content-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有活动状态
            navLinks.forEach(l => l.classList.remove('active'));
            sections.forEach(s => s.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            
            // 显示对应的内容部分
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);
            if (targetSection) {
                targetSection.classList.add('active');
            }
            
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    });
}

// 初始化平滑滚动
function initSmoothScroll() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href.length > 1) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

// 初始化代码复制功能
function initCodeCopy() {
    const codeBlocks = document.querySelectorAll('.code-block, .code-example');
    
    codeBlocks.forEach(block => {
        // 创建复制按钮
        const copyBtn = document.createElement('button');
        copyBtn.className = 'copy-btn';
        copyBtn.innerHTML = '📋 复制';
        copyBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        // 设置相对定位
        block.style.position = 'relative';
        block.appendChild(copyBtn);
        
        // 鼠标悬停显示按钮
        block.addEventListener('mouseenter', () => {
            copyBtn.style.opacity = '1';
        });
        
        block.addEventListener('mouseleave', () => {
            copyBtn.style.opacity = '0';
        });
        
        // 复制功能
        copyBtn.addEventListener('click', async () => {
            const code = block.querySelector('code');
            const text = code ? code.textContent : block.textContent;
            
            try {
                await navigator.clipboard.writeText(text);
                copyBtn.innerHTML = '✅ 已复制';
                setTimeout(() => {
                    copyBtn.innerHTML = '📋 复制';
                }, 2000);
            } catch (err) {
                console.error('复制失败:', err);
                copyBtn.innerHTML = '❌ 复制失败';
                setTimeout(() => {
                    copyBtn.innerHTML = '📋 复制';
                }, 2000);
            }
        });
    });
}

// 加载动态内容
function loadDynamicContent() {
    // 加载工具类库详细信息
    loadToolsContent();

    // 加载代码生成器信息
    loadCodeGeneratorContent();

    // 加载认证鉴权信息
    loadAuthModuleContent();

    // 加载模块架构信息
    loadArchitectureContent();

    // 加载最佳实践内容
    loadBestPracticesContent();

    // 加载代码示例
    loadExamplesContent();

    // 加载部署指南
    loadDeploymentContent();
}

// 加载工具类库内容
function loadToolsContent() {
    fetch('/api/scaffold/tools')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateToolsSection(data.data);
            }
        })
        .catch(error => {
            console.error('加载工具类库信息失败:', error);
        });
}

// 更新工具类库部分
function updateToolsSection(toolsData) {
    const toolsSection = document.getElementById('tools');
    if (!toolsSection) return;

    // 这里可以根据API返回的数据动态更新工具类库内容
    // 由于内容较多，这里保持静态内容，实际项目中可以完全动态化
}

// 加载代码生成器内容
function loadCodeGeneratorContent() {
    fetch('/api/scaffold/code-generator')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCodeGeneratorSection(data.data);
            }
        })
        .catch(error => {
            console.error('加载代码生成器信息失败:', error);
        });
}

// 更新代码生成器部分
function updateCodeGeneratorSection(generatorData) {
    const generatorSection = document.getElementById('code-generator');
    if (!generatorSection) return;

    // 动态更新代码生成器的详细信息
    // 这里可以根据API返回的数据进一步丰富内容
    console.log('代码生成器数据:', generatorData);
}

// 加载认证鉴权内容
function loadAuthModuleContent() {
    fetch('/api/scaffold/auth-module')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateAuthModuleSection(data.data);
            }
        })
        .catch(error => {
            console.error('加载认证鉴权信息失败:', error);
        });
}

// 更新认证鉴权部分
function updateAuthModuleSection(authData) {
    const authSection = document.getElementById('auth-module');
    if (!authSection) return;

    // 动态更新认证鉴权的详细信息
    // 这里可以根据API返回的数据进一步丰富内容
    console.log('认证鉴权数据:', authData);
}

// 加载模块架构内容
function loadArchitectureContent() {
    fetch('/api/scaffold/architecture')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createArchitectureSection(data.data);
            }
        })
        .catch(error => {
            console.error('加载模块架构信息失败:', error);
        });
}

// 创建模块架构部分
function createArchitectureSection(architectureData) {
    const architectureSection = document.getElementById('architecture');
    if (!architectureSection) {
        // 如果不存在，创建架构部分
        const mainContent = document.querySelector('.main-content');
        const section = document.createElement('section');
        section.id = 'architecture';
        section.className = 'content-section';
        section.innerHTML = `
            <div class="section-header">
                <h1>🏗️ 模块架构</h1>
                <p class="section-desc">了解脚手架的模块组织和依赖关系</p>
            </div>
            <div id="architecture-content"></div>
        `;
        mainContent.appendChild(section);
    }
    
    // 更新架构内容
    const contentDiv = document.getElementById('architecture-content');
    if (contentDiv && architectureData.modules) {
        let html = '<div class="module-grid">';
        
        architectureData.modules.forEach(module => {
            html += `
                <div class="module-card">
                    <h3>${module.title}</h3>
                    <div class="module-badge ${module.required.toLowerCase()}">${module.required}</div>
                    <p>${module.description}</p>
                    <div class="module-features">
                        ${module.features.map(feature => `<span class="feature-tag">${feature}</span>`).join('')}
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        contentDiv.innerHTML = html;
    }
}

// 加载最佳实践内容
function loadBestPracticesContent() {
    fetch('/api/scaffold/best-practices')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createBestPracticesSection(data.data);
            }
        })
        .catch(error => {
            console.error('加载最佳实践信息失败:', error);
        });
}

// 创建最佳实践部分
function createBestPracticesSection(practicesData) {
    const practicesSection = document.getElementById('best-practices');
    if (!practicesSection) {
        const mainContent = document.querySelector('.main-content');
        const section = document.createElement('section');
        section.id = 'best-practices';
        section.className = 'content-section';
        section.innerHTML = `
            <div class="section-header">
                <h1>⭐ 最佳实践</h1>
                <p class="section-desc">遵循企业级开发规范，提高代码质量</p>
            </div>
            <div id="practices-content"></div>
        `;
        mainContent.appendChild(section);
    }
    
    // 更新最佳实践内容
    const contentDiv = document.getElementById('practices-content');
    if (contentDiv) {
        let html = '';
        
        // 项目结构
        if (practicesData.projectStructure) {
            html += `
                <div class="practice-section">
                    <h2>📁 项目结构规范</h2>
                    <div class="code-block">
                        <code>${practicesData.projectStructure.join('\n')}</code>
                    </div>
                </div>
            `;
        }
        
        // 编码规范
        if (practicesData.codingStandards) {
            html += `
                <div class="practice-section">
                    <h2>📝 编码规范</h2>
                    <div class="standards-grid">
            `;
            
            practicesData.codingStandards.forEach(standard => {
                html += `
                    <div class="standard-item">
                        <h3>${standard.title}</h3>
                        <p>${standard.description}</p>
                    </div>
                `;
            });
            
            html += '</div></div>';
        }
        
        // 代码示例
        if (practicesData.codeExamples) {
            html += `
                <div class="practice-section">
                    <h2>💻 代码示例</h2>
                    <div class="examples-grid">
            `;
            
            Object.entries(practicesData.codeExamples).forEach(([title, code]) => {
                html += `
                    <div class="example-item">
                        <h3>${title}</h3>
                        <div class="code-block">
                            <code>${code}</code>
                        </div>
                    </div>
                `;
            });
            
            html += '</div></div>';
        }
        
        contentDiv.innerHTML = html;
    }
}

// 加载代码示例内容
function loadExamplesContent() {
    // 创建代码示例部分
    const examplesSection = document.getElementById('examples');
    if (!examplesSection) {
        const mainContent = document.querySelector('.main-content');
        const section = document.createElement('section');
        section.id = 'examples';
        section.className = 'content-section';
        section.innerHTML = `
            <div class="section-header">
                <h1>📚 代码示例</h1>
                <p class="section-desc">实际开发中的代码示例和使用场景</p>
            </div>
            <div class="examples-content">
                <div class="example-category">
                    <h2>🏗️ 基础架构示例</h2>
                    <div class="example-item">
                        <h3>创建实体类</h3>
                        <div class="code-block">
                            <code>@Data
@TableName("sys_user")
public class User extends BaseSuperEntity implements BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String username;
    private String email;
    private String phone;
    
    // 继承了创建时间、创建人、更新时间、更新人等字段
}</code>
                        </div>
                    </div>
                    
                    <div class="example-item">
                        <h3>创建Service</h3>
                        <div class="code-block">
                            <code>@Service
public class UserServiceImpl extends ServiceImpl&lt;UserMapper, User&gt; implements UserService {
    
    @Override
    public Result&lt;User&gt; createUser(User user) {
        // 使用工具类生成唯一ID
        user.setId(Long.valueOf(UUIDUtil.getUniqueNo()));
        
        // 保存用户
        save(user);
        
        return Result.success("用户创建成功", user);
    }
}</code>
                        </div>
                    </div>
                </div>
                
                <div class="example-category">
                    <h2>🛠️ 工具类使用示例</h2>
                    <div class="example-item">
                        <h3>字符串处理</h3>
                        <div class="code-block">
                            <code>// 智能类型转换
Object value = StringUtil.convert("123"); // 自动转换为Integer
String fileName = StringUtil.getUniName("document.pdf"); // 生成唯一文件名

// 类型判断
boolean isNumber = StringUtil.isInteger("123");
boolean isArray = StringUtil.isArray("[1,2,3]");</code>
                        </div>
                    </div>
                    
                    <div class="example-item">
                        <h3>HTTP请求</h3>
                        <div class="code-block">
                            <code>// 发送POST请求
Map&lt;String, String&gt; headers = new HashMap&lt;&gt;();
headers.put("Content-Type", "application/json");
String response = HttpRequestUtil.post(jsonData, url, headers);

// 使用OkHttp
String response = OkHttpUtil.builder()
    .url("https://api.example.com/users")
    .post(userData)
    .headers(headers)
    .execute();</code>
                        </div>
                    </div>
                </div>
            </div>
        `;
        mainContent.appendChild(section);
    }
}

// 加载部署指南内容
function loadDeploymentContent() {
    const deploymentSection = document.getElementById('deployment');
    if (!deploymentSection) {
        const mainContent = document.querySelector('.main-content');
        const section = document.createElement('section');
        section.id = 'deployment';
        section.className = 'content-section';
        section.innerHTML = `
            <div class="section-header">
                <h1>🚀 部署指南</h1>
                <p class="section-desc">多种部署方式，适应不同的生产环境</p>
            </div>
            <div class="deployment-content">
                <div class="deployment-method">
                    <h2>📦 传统部署</h2>
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>打包应用</h3>
                            <div class="code-block">
                                <code>mvn clean package -DskipTests</code>
                            </div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>启动应用</h3>
                            <div class="code-block">
                                <code>java -jar target/my-app.jar --spring.profiles.active=prod</code>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="deployment-method">
                    <h2>🐳 Docker部署</h2>
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>构建镜像</h3>
                            <div class="code-block">
                                <code>docker build -t my-app:1.0.0 .</code>
                            </div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>运行容器</h3>
                            <div class="code-block">
                                <code>docker run -d -p 8080:8080 my-app:1.0.0</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        mainContent.appendChild(section);
    }
}

// 移动端菜单切换
function toggleMobileMenu() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('open');
}
