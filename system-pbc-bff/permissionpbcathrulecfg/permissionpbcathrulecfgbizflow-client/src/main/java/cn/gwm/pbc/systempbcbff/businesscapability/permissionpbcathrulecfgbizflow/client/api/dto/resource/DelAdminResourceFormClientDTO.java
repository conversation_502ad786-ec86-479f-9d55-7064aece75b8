package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "DelAdminResourceFormClientDTO", desc = "null")
@Data
@ApiModel("已接入平台删除菜单参数对象")
public class DelAdminResourceFormClientDTO implements Serializable {

    /**
     * account
     */
    @Field(name = "account", desc = "")
    @JsonIgnore
    private String account;

	@Field(name = "platformCode", desc = "")
    @ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 菜单ID
     */
    @Field(name = "id", desc = "")
    @ApiModelProperty("菜单ID")
    private String id;

}