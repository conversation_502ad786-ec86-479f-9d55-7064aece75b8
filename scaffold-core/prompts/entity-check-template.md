# 实体脚手架代码检查模板

## 🎯 使用目的
在代码架构设计阶段，检查业务需求涉及的所有实体是否已生成脚手架代码，确保开发依赖完整。

## 📋 检查流程

### 第一步：实体识别
从业务需求中识别所有涉及的实体：

```markdown
## 涉及实体分析

### 业务需求概述
{简要描述业务需求}

### 实体清单
根据业务需求分析，本次开发涉及以下实体：

| 序号 | 实体名称 | 业务含义 | 主要用途 |
|------|----------|----------|----------|
| 1    | User     | 用户信息 | 用户管理、权限控制 |
| 2    | Product  | 产品信息 | 产品展示、库存管理 |
| 3    | Order    | 订单信息 | 订单处理、支付流程 |
| ...  | ...      | ...      | ... |
```

### 第二步：代码库检索
检查每个实体对应的脚手架代码是否存在：

```markdown
## 脚手架代码生成状态检查

### 检查结果
| 实体名称 | Entity | Mapper | Service | Controller | XML映射 | 状态 |
|---------|--------|--------|---------|------------|---------|------|
| User    | ✅     | ✅     | ✅      | ✅         | ✅      | 已生成 |
| Product | ❌     | ❌     | ❌      | ❌         | ❌      | 未生成 |
| Order   | ✅     | ✅     | ❌     | ❌         | ❌      | 部分生成 |

### 状态说明
- ✅ 已生成：文件存在且符合规范
- ❌ 未生成：文件不存在或不符合规范
- 🔄 需更新：文件存在但需要更新
```

### 第三步：阻塞决策
根据检查结果决定是否阻塞流程：

```markdown
## 阻塞决策

### 决策结果
- [ ] ✅ 所有实体脚手架代码已生成，可以继续流程
- [x] 🚫 存在未生成的实体，需要阻塞流程

### 阻塞详情
**未生成实体清单:**
- Product: 完全未生成（Entity/Mapper/Service/Controller/XML）
- Order: 部分未生成（Service/Controller/XML）

**影响分析:**
- 无法进行业务逻辑开发
- 缺少基础的CRUD操作
- 影响API接口设计
```

## 🚫 阻塞处理模板

当发现未生成的实体时，使用以下模板：

```markdown
🚫 **流程阻塞 - 脚手架代码缺失**

**阻塞原因:**
以下实体缺少脚手架生成的基础代码，无法继续开发流程：

1. **Product实体**
   - 缺少组件: Entity、Mapper、Service、Controller、XML映射
   - 业务影响: 产品管理功能无法实现

2. **Order实体**  
   - 缺少组件: Service、Controller、XML映射
   - 业务影响: 订单处理逻辑无法完成

**解决方案:**
请使用脚手架生成器为上述实体生成基础代码：

### Product实体生成参数
```
表名: product
实体名: Product  
包路径: com.gwm.scaffold.product
生成组件: Entity + Mapper + Service + Controller
```

### Order实体补充生成参数
```
表名: order
实体名: Order
包路径: com.gwm.scaffold.order  
补充组件: Service + Controller
```

**操作步骤:**
1. 打开脚手架代码生成器
2. 按照上述参数配置生成
3. 验证生成的代码符合项目规范
4. 完成后回复"脚手架代码已生成"继续流程

**等待用户确认:** ⏳
```

## ✅ 继续流程模板

当所有实体都已生成时，使用以下模板：

```markdown
✅ **脚手架代码检查通过**

**检查结果:**
所有涉及的实体脚手架代码已生成完成：

| 实体名称 | 生成状态 | 文件路径 |
|---------|----------|----------|
| User    | ✅ 完整  | com.gwm.scaffold.user.* |
| Product | ✅ 完整  | com.gwm.scaffold.product.* |
| Order   | ✅ 完整  | com.gwm.scaffold.order.* |

**可以继续的开发任务:**
- ✅ 业务逻辑实现
- ✅ 复杂查询开发  
- ✅ API接口扩展
- ✅ 数据校验添加

**继续流程:** 进入代码调用流程设计阶段
```

## 🔍 检查要点

### 必须检查的文件
1. **Entity类**: `{EntityName}.java`
   - 继承BaseSuperEntity
   - 包含@TableName注解
   - 字段映射正确

2. **Mapper接口**: `{EntityName}Mapper.java`
   - 继承BaseMapper<Entity>
   - 包含@Mapper注解
   - 基础CRUD方法

3. **Service接口**: `{EntityName}Service.java`
   - 继承IService<Entity>
   - 定义业务方法

4. **Service实现**: `{EntityName}ServiceImpl.java`
   - 继承ServiceImpl<Mapper, Entity>
   - 实现Service接口
   - 包含@Service和@Transactional注解

5. **Controller类**: `{EntityName}Controller.java`
   - 包含@RestController注解
   - RESTful风格接口
   - 统一返回Result<T>

6. **XML映射**: `{EntityName}Mapper.xml`
   - 对应Mapper接口
   - 复杂查询SQL

### 检查标准
- 文件存在且可编译
- 命名符合项目规范
- 注解使用正确
- 继承关系正确
- 包路径符合规范

## 💡 使用建议

1. **自动化检查**: 可以编写脚本自动检查文件存在性
2. **增量检查**: 只检查本次需求新涉及的实体
3. **版本控制**: 检查结果可以记录到版本控制中
4. **团队协作**: 检查模板可以团队共享使用
