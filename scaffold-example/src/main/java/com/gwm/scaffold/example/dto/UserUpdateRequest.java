package com.gwm.scaffold.example.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 用户更新请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@ApiModel(description = "用户更新请求")
public class UserUpdateRequest {

    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    @NotNull(message = "用户ID不能为空")
    @Min(value = 1, message = "用户ID必须大于0")
    private Long id;

    @ApiModelProperty(value = "真实姓名", example = "张三")
    @Size(max = 50, message = "真实姓名长度不能超过50")
    private String realName;

    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100")
    private String email;

    @ApiModelProperty(value = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    @ApiModelProperty(value = "性别", example = "1", notes = "1:男 2:女")
    @Min(value = 1, message = "性别值不正确")
    @Max(value = 2, message = "性别值不正确")
    private Integer gender;

    @ApiModelProperty(value = "年龄", example = "25")
    @Min(value = 1, message = "年龄必须大于0")
    @Max(value = 150, message = "年龄不能超过150")
    private Integer age;

    @ApiModelProperty(value = "部门ID", example = "1")
    @Min(value = 1, message = "部门ID必须大于0")
    private Long departmentId;

    @ApiModelProperty(value = "部门名称", example = "技术部")
    @Size(max = 100, message = "部门名称长度不能超过100")
    private String departmentName;

    @ApiModelProperty(value = "职位", example = "Java开发工程师")
    @Size(max = 100, message = "职位长度不能超过100")
    private String position;

    @ApiModelProperty(value = "状态", example = "1", notes = "1:启用 0:禁用")
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;

    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    @Size(max = 500, message = "头像URL长度不能超过500")
    private String avatar;

    @ApiModelProperty(value = "备注", example = "这是一个测试用户")
    @Size(max = 500, message = "备注长度不能超过500")
    private String remark;

    @ApiModelProperty(value = "版本号", required = true, example = "1")
    @NotNull(message = "版本号不能为空")
    @Min(value = 0, message = "版本号不能为负数")
    private Integer version;
}
