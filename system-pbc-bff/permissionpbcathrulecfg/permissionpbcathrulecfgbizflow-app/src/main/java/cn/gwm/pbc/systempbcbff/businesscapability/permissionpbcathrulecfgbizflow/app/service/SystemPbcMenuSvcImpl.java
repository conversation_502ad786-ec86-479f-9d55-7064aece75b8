package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcMenuSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteMenuService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.menu.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.menu.MenuInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuDeleteDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuSaveDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import java.lang.Override;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
/**
 * <AUTHOR>
 */
@Component("PermissionPBCAthRuleCfgBizFlowSystemPbcMenuSvcBS")
public class SystemPbcMenuSvcImpl implements SystemPbcMenuSvc {

    /**
     * 数据菜单类型：2
     */
    public static final int TYPE_MENU_DATA = 2;

    @Resource
    private RemoteMenuService remoteMenuService;

    @Override
    public void save(MenuSaveDTO request)  throws BusinessException{
    	if (StringUtils.isBlank(request.getId())) {
            RemoteMenuCreateDTO remoteMenuCreateDTO = new RemoteMenuCreateDTO();
            BeanUtil.copyProperties(request, remoteMenuCreateDTO);
            this.create(remoteMenuCreateDTO);
        } else {
            RemoteMenuEditDTO remoteMenuEditDTO = new RemoteMenuEditDTO();
            BeanUtil.copyProperties(request, remoteMenuEditDTO);
            this.edit(remoteMenuEditDTO);
        }
	}

    @Override
    public void delete(MenuDeleteDTO request) {
    	RemoteMenuDeleteDTO remoteMenuDeleteDTO = new RemoteMenuDeleteDTO();
        BeanUtil.copyProperties(request, remoteMenuDeleteDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteMenuService.menuDelete(remoteMenuDeleteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}

    @Override
    public MenuInfoItemVO detail(MenuDetailDTO request) {
    	RemoteMenuDetailDTO remoteMenuDetailDTO = new RemoteMenuDetailDTO();
        BeanUtil.copyProperties(request, remoteMenuDetailDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteMenuService.menuDetail(remoteMenuDetailDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject result = jsonObjectCommonRlt.getData();
        return result.to(MenuInfoItemVO.class);
	}

    @Override
    public Map<String, Object> treeUser() {
    	RemoteMenuUserTreeDTO userTreeDTO = new RemoteMenuUserTreeDTO();
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteMenuService.menuUserTree(userTreeDTO);
        JSONObject rlt = null;
        if (jsonObjectCommonRlt.isSuccess()) {
            rlt = jsonObjectCommonRlt.getData();
        }
        JSONArray tree = rlt.getJSONArray("tree");
        List<MenuInfoItemVO> menuInfoItemVOS = tree.toJavaList(MenuInfoItemVO.class);
        List<MenuInfoItemVO> collect = menuInfoItemVOS.stream()
                .filter(
                        bean -> (bean != null && bean.getType() != TYPE_MENU_DATA)
                )
                .collect(Collectors.toList());
        rlt.put("tree", collect);
        return BeanUtil.beanToMap(rlt);
	}

    @Override
    public List<MenuInfoItemVO> treeAll() {
    	CommonRlt<JSONArray> jsonObjectCommonRlt = remoteMenuService.menuTree();
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONArray result = jsonObjectCommonRlt.getData();
        List<MenuInfoItemVO> data = result.toJavaList(MenuInfoItemVO.class);
        return data.stream()
                .filter(
                        bean -> (bean != null &&  bean.getType() != TYPE_MENU_DATA)
                )
                .collect(Collectors.toList());
	}

    private void create(RemoteMenuCreateDTO remoteMenuCreateDTO) throws BusinessException{
    	CommonRlt<JSONObject> jsonObjectCommonRlt = remoteMenuService.menuCreate(remoteMenuCreateDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}

    private void edit(RemoteMenuEditDTO remoteMenuEditDTO) throws BusinessException{
    	CommonRlt<JSONObject> jsonObjectCommonRlt = remoteMenuService.menuEdit(remoteMenuEditDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}
}
