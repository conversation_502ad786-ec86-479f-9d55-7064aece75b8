package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu;

import com.alibaba.bizworks.core.specification.*;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "MenuDeleteDTO", desc = "")
@Data
public class MenuDeleteDTO implements Serializable {

    /**
     * 菜单ID
     */
    @Field(name = "菜单ID", desc = "菜单ID")
    private String id;

}
