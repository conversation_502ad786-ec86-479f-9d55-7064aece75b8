package com.gwm.scaffold.web.config;

import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.google.common.collect.Lists;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Collections;
import java.util.List;

import static com.alibaba.fastjson.parser.Feature.OrderedField;
import static com.alibaba.fastjson.serializer.SerializerFeature.SortField;
import static com.alibaba.fastjson.serializer.SerializerFeature.WriteMapNullValue;
import static java.nio.charset.StandardCharsets.UTF_8;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * Web配置类
 * 
 * 提供Web层的通用配置，包括消息转换器、跨域配置等
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "scaffold.web")
public class WebConfig implements WebMvcConfigurer {

    /**
     * 日期时间格式
     */
    private String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * 是否启用跨域
     */
    private boolean corsEnabled = true;

    /**
     * 跨域配置
     */
    private CorsProperties cors = new CorsProperties();

    /**
     * 配置消息转换器
     *
     * @param converters Spring中消息转换器列表
     */
    @Override
    public void configureMessageConverters(@NotNull List<HttpMessageConverter<?>> converters) {
        // 配置FastJSON
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setDateFormat(dateTimeFormat);
        fastJsonConfig.setCharset(UTF_8);
        fastJsonConfig.setSerializerFeatures(SortField, WriteMapNullValue);
        fastJsonConfig.setFeatures(OrderedField);

        // FastJSON消息转换器
        FastJsonHttpMessageConverter jsonConverter = new FastJsonHttpMessageConverter();
        jsonConverter.setFastJsonConfig(fastJsonConfig);
        // 设置支持的媒体类型
        jsonConverter.setSupportedMediaTypes(Lists.newArrayList(APPLICATION_JSON));
        
        // 将FastJSON转换器添加到首位
        converters.add(0, jsonConverter);
        WebMvcConfigurer.super.configureMessageConverters(converters);
    }

    /**
     * 配置跨域
     *
     * @param registry 跨域注册器
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        if (corsEnabled) {
            registry.addMapping(cors.getPathPattern())
                    .allowedOriginPatterns(cors.getAllowedOriginPatterns())
                    .allowedMethods(cors.getAllowedMethods())
                    .allowedHeaders(cors.getAllowedHeaders())
                    .allowCredentials(cors.isAllowCredentials())
                    .maxAge(cors.getMaxAge());
        }
    }

    /**
     * 跨域配置属性
     */
    @Data
    public static class CorsProperties {
        /**
         * 路径模式
         */
        private String pathPattern = "/**";

        /**
         * 允许的源模式
         */
        private String[] allowedOriginPatterns = {"*"};

        /**
         * 允许的方法
         */
        private String[] allowedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};

        /**
         * 允许的头部
         */
        private String[] allowedHeaders = {"*"};

        /**
         * 是否允许凭证
         */
        private boolean allowCredentials = true;

        /**
         * 预检请求缓存时间（秒）
         */
        private long maxAge = 3600;
    }
}
