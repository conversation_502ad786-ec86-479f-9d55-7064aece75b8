server:
  port: 8021
  servlet:
    context-path: /ownedVehicle
project:
  name: system-pbc-bff
spring:
  swagger:
    enable: true
    basePackage: cn.gwm.pbc.systempbcbff
    version: 1.0.0
    title: 测试标题
    description: 测试描述
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  h2:
    console:
      settings:
        web-allow-others: true
        trace: true
      path: /h2-console
      enable: true

bizworks:
  appId: system-pbc-bff
  tenantId: replacedByBizworks
feign:
  httpclient:
    enabled: true
    connection-timeout: 3000
    max-connections: 500
    max-connections-per-route: 200

gwm:
  sso:
    url: http://auth.paas.gwm.cn
#    platform-code: a4194231074b09c1e591ea244b95f603
    platform-code: a99f77397dd6c4ab1d2199da05d05039
    pbcId: system-pbc-bff
    appId: ec02db285246741118716404d76ee0fb
  permission:
    platform-code: a99f77397dd6c4ab1d2199da05d05039
    platform-secret: 144919005e28f8ca06676959cb627b2e
  open:
    url: https://gwapi.gwm.cn
    env: rest
    app-key: 6521T6393840
    app-secret: 74e40fc2c6b841b1b7f1e319c4e7c8af
auth:
  filter:
    enabled: true
  ignore:
    whites:
      - /system/sso/callback
      - /*/actuator
      - /app/get
      - /SystemPbcSsoCallbackSvc/ssoCallback/v1
center:
  url: http://userroleserver.paas.gwm.cn