package com.gwm.scaffold.example.vo;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 品牌视图对象VO
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "品牌信息")
public class BrandVO {

    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID")
    private Long id;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String name;

    /**
     * 品牌英文名称
     */
    @Schema(description = "品牌英文名称")
    private String englishName;

    /**
     * 所属国家
     */
    @Schema(description = "所属国家")
    private String country;

    /**
     * 成立年份
     */
    @Schema(description = "成立年份")
    private Integer foundedYear;

    /**
     * 官网地址
     */
    @Schema(description = "官网地址")
    private String website;

    /**
     * LOGO图片URL
     */
    @Schema(description = "LOGO图片URL")
    private String logoUrl;

    /**
     * 品牌简介
     */
    @Schema(description = "品牌简介")
    private String description;

    /**
     * 状态（ACTIVE-启用，INACTIVE-禁用）
     */
    @Schema(description = "状态（ACTIVE-启用，INACTIVE-禁用）")
    private String status;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重")
    private Integer sortWeight;

    /**
     * 删除时间
     */
    @Schema(description = "删除时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人编码
     */
    @Schema(description = "创建人编码")
    private String createUserCode;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人编码
     */
    @Schema(description = "更新人编码")
    private String updateUserCode;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名")
    private String updateUserName;

    /**
     * 状态名称（用于前端显示）
     */
    @Schema(description = "状态名称（启用/禁用）")
    private String statusName;

    /**
     * 品牌数量（用于统计查询）
     */
    @Schema(description = "品牌数量")
    private Integer brandCount;

}
