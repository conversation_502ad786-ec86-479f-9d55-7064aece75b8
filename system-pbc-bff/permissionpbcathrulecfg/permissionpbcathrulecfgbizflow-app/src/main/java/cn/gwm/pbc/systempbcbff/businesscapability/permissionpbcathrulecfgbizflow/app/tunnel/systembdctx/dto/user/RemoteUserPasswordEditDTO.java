package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RemoteUserPasswordEditDTO implements Serializable {

    /**
     * 用户编码（职工号）
     */
    private String user_code;
    /**
     * 旧密码
     */
    private String old_passwd;
    /**
     * 新密码
     */
    private String new_passwd;
    /**
     * 确认密码
     */
    private String confirm_passwd;

}
