package com.gwm.scaffold.example.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 创建请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "创建请求")
public class SeriesCreateRequest {

    /**
     * name
     */
    @Schema(description = "name", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "name不能为空")
    @Size(max = 50, message = "name长度不能超过50")
    private String name;

    /**
     * englishName
     */
    @Schema(description = "englishName")
    @Size(max = 100, message = "englishName长度不能超过100")
    private String englishName;

    /**
     * brandId
     */
    @Schema(description = "brandId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "brandId不能为空")
    @Min(value = 1, message = "brandId必须大于0")
    private Long brandId;

    /**
     * type
     */
    @Schema(description = "type", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "type不能为空")
    @Size(max = 20, message = "type长度不能超过20")
    private String type;

    /**
     * level
     */
    @Schema(description = "level", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "level不能为空")
    @Size(max = 10, message = "level长度不能超过10")
    private String level;

    /**
     * launchTime
     */
    @Schema(description = "launchTime", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "launchTime不能为空")
    private Date launchTime;

    /**
     * discontinueTime
     */
    @Schema(description = "discontinueTime")
    private Date discontinueTime;

    /**
     * minPrice
     */
    @Schema(description = "minPrice")
    private BigDecimal minPrice;

    /**
     * maxPrice
     */
    @Schema(description = "maxPrice")
    private BigDecimal maxPrice;

    /**
     * imageUrl
     */
    @Schema(description = "imageUrl")
    @Size(max = 500, message = "imageUrl长度不能超过500")
    private String imageUrl;

    /**
     * description
     */
    @Schema(description = "description")
    @Size(max = 1000000000, message = "description长度不能超过1000000000")
    private String description;

    /**
     * status
     */
    @Schema(description = "status")
    @Size(max = 20, message = "status长度不能超过20")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    private Integer sortWeight;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

}
