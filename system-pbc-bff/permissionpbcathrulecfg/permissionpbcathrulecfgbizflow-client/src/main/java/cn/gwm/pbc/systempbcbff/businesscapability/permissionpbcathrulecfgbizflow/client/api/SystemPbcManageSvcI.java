package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.CustomUserResetPasswordDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.GroupUserTreeDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.RequestUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.SearchUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.TestDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.UserBindPlatformDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.UserDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.UserSaveDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.UserUnbindPlatformDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * 权限PBC-管理服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.13
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcManageSvcI-1702436246008")
@RequestMapping(path = "/PermissionPBCAthRuleCfgBizFlow")
public interface SystemPbcManageSvcI {

    /**
     * test
     * @param request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcManageSvc/test/v1")
    Response<TestDTO> test(@RequestBody(required = false) TestDTO request);

    /**
     * 权限中心-用户列表
     * Request URL: http://assemble-preview-cnp-bdtest.gwmit.cn/SYSTEM_PATH/system/user/list?page=1&size=10
     * @param request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcManageSvc/queryUser/v1")
    Response<Map<String, Object>> queryUser(@RequestBody(required = false) RequestUserListDTO request);

    /**
     * 权限中心-用户列表绑定 /user/bind/platform
     *
     * @param request request
     * @return Response 权限中心-用户列表绑定接口结果
     */
    @PostMapping(path = "/SystemPbcManageSvc/bindPlatform/v1")
    Response<Void> bindPlatform(@RequestBody(required = true) UserBindPlatformDTO request);

    /**
     * 权限中心-用户列表解绑平台 /user/unbind/platform
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcManageSvc/unbindPlatform/v1")
    Response<Void> unbindPlatform(@RequestBody(required = true) UserUnbindPlatformDTO request);

    /**
     * 权限中心-用户列表创建用户 system/user/save
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcManageSvc/save/v1")
    Response<Void> save(@RequestBody(required = true) UserSaveDTO request);

    /**
     * 权限中心-上传头像 system/user/avatar/upload
     *
     * @param request request
     * @return Response 权限中心-上传头像
     */
    @PostMapping(path = "/SystemPbcManageSvc/upload/v1")
    Response<Map<String, Object>> upload(@RequestParam(name = "file") MultipartFile request);

    /**
     * 权限中心-获取用户详情 system/user/detail
     *
     * @param request request
     * @return Response 权限中心-获取用户详情
     */
    @PostMapping(path = "/SystemPbcManageSvc/xx/laited/v1")
    Response<Map<String, Object>> detail(@RequestBody(required = true) UserDetailDTO request);

    /**
     * 权限中心-获取用户详情 system/user/groupUserTree
     *
     * @param request request
     * @return Response 权限中心-获取组织用户树
     */
    @PostMapping(path = "/SystemPbcManageSvc/groupUserTree/v1")
    Response<Map<String, Object>> groupUserTree(@RequestBody(required = true) GroupUserTreeDTO request);

    /**
     * 权限中心-搜索用户列表 system/user/searchUserList
     *
     * @param request request
     * @return Response 权限中心-搜索用户列表
     */
    @PostMapping(path = "/SystemPbcManageSvc/searchUserList/v1")
    Response<Map<String, Object>> searchUserList(@RequestBody(required = true) SearchUserListDTO request);

    /**
     * 权限中心-重置自定义用户密码 system/user/custom/password/reset
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcManageSvc/customUserPasswordReset/v1")
    Response<Void> customUserPasswordReset(@RequestBody(required = true) CustomUserResetPasswordDTO request);
}
