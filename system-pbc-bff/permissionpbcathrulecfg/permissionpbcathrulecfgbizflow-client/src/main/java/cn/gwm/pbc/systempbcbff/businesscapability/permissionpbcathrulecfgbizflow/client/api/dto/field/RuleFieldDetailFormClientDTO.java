package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleFieldDetailFormClientDTO", desc = "null")
@Data
@ApiModel("规则字段数据请求参数对象")
public class RuleFieldDetailFormClientDTO {

    /**
     * 字段名称
     */
    @Field(name = "字段名称", desc = "字段名称")
    @ApiModelProperty("角色ID（虚拟角色）")
    @JsonProperty(value = "role_set_id")
    private String roleSetId;

}
