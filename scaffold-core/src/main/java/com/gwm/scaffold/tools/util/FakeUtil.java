package com.gwm.scaffold.tools.util;

import com.github.javafaker.Faker;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.gwm.scaffold.tools.util.Constants.*;


/**
 * fake数据工具类
 *
 * <AUTHOR>
 * @date 2025/6/5
 */
@Slf4j
public final class FakeUtil {

    private static final Faker FAKER = new Faker();

    /**
     * 防止循环引用的最大递归深度
     */
    private static final int MAX_DEPTH = 3;

    /**
     * 集合最大元素数量
     */
    private static final int COLLECTION_MAX_SIZE = 5;

    /**
     * 使用 JavaFaker 自动填充对象的所有属性
     *
     * @param clazz 目标类的 Class 对象
     * @param <T>   目标类的类型
     * @return 填充好的对象实例
     */
    public static <T> T createFake(Class<T> clazz) {
        return createFake(clazz, new HashSet<>(), ZERO);
    }

    /**
     * 使用 JavaFaker 自动填充对象的所有属性
     *
     * @param clazz   目标类的 Class 对象
     * @param visited 记录已访问对象，防止循环引用
     * @param depth   深度记录
     * @param <T>     目标类的类型
     * @return 创建后的对象
     */
    @SuppressWarnings("unchecked")
    private static <T> T createFake(Class<T> clazz, Set<Object> visited, int depth) {
        // 处理基本类型
        if (clazz.isPrimitive() || clazz == String.class || clazz == Integer.class ||
                clazz == Long.class || clazz == Boolean.class || clazz == Double.class ||
                clazz == Float.class || clazz == Character.class || clazz == Byte.class ||
                clazz == Short.class || clazz == BigDecimal.class ||
                clazz == LocalDate.class || clazz == LocalDateTime.class || clazz == Date.class) {
            return (T) generatePrimitiveValue(clazz);
        }

        // 处理枚举
        if (clazz.isEnum()) {
            Object[] enumConstants = clazz.getEnumConstants();
            return (T) enumConstants[FAKER.number().numberBetween(0, enumConstants.length)];
        }

        // 处理集合
        if (Collection.class.isAssignableFrom(clazz)) {
            return (T) generateCollection(clazz, visited, depth);
        }

        // 处理 Map
        if (Map.class.isAssignableFrom(clazz)) {
            return (T) generateMap(clazz, visited, depth);
        }

        // 处理数组
        if (clazz.isArray()) {
            return (T) generateArray(clazz, visited, depth);
        }

        // 处理循环引用和深度限制
        if (depth > MAX_DEPTH) {
            return null;
        }

        try {
            // 创建实例
            T instance = createInstance(clazz);
            if (instance == null) {
                return null;
            }
            // 记录已访问对象，防止循环引用
            visited.add(instance);
            // 填充字段
            fillFields(instance, visited, depth + ONE);
            return instance;
        } catch (Exception e) {
            log.error("Error creating fake object for class: {}", clazz.getName(), e);
            return null;
        }
    }

    /**
     * 生成基本类型的随机值
     *
     * @param clazz 基本类型
     * @return 随机值
     */
    private static Object generatePrimitiveValue(Class<?> clazz) {
        if (clazz == String.class) {
            return FAKER.lorem().word();
        } else if (clazz == Integer.class || clazz == int.class) {
            return FAKER.number().numberBetween(1, 100);
        } else if (clazz == Long.class || clazz == long.class) {
            return FAKER.number().randomNumber();
        } else if (clazz == Boolean.class || clazz == boolean.class) {
            return FAKER.bool().bool();
        } else if (clazz == Double.class || clazz == double.class) {
            return FAKER.number().randomDouble(2, 1, 100);
        } else if (clazz == Float.class || clazz == float.class) {
            return (float) FAKER.number().randomDouble(2, 1, 100);
        } else if (clazz == Character.class || clazz == char.class) {
            return FAKER.lorem().character();
        } else if (clazz == Byte.class || clazz == byte.class) {
            return (byte) FAKER.number().numberBetween(0, 127);
        } else if (clazz == Short.class || clazz == short.class) {
            return (short) FAKER.number().numberBetween(0, 32767);
        } else if (clazz == BigDecimal.class) {
            return BigDecimal.valueOf(FAKER.number().randomDouble(2, 1, 100));
        } else if (clazz == LocalDate.class) {
            return LocalDate.now().minusDays(FAKER.number().numberBetween(1, 365));
        } else if (clazz == LocalDateTime.class) {
            return LocalDateTime.now().minusDays(FAKER.number().numberBetween(1, 365));
        } else if (clazz == Date.class) {
            return new Date();
        }
        return null;
    }

    /**
     * 创建类的实例
     *
     * @param clazz 指定类
     * @param <T>   指定类型
     * @return 实例
     */
    @SuppressWarnings("unchecked")
    private static <T> T createInstance(Class<T> clazz) {
        try {
            // 尝试无参构造函数
            Constructor<T> constructor = clazz.getDeclaredConstructor();
            constructor.setAccessible(true);
            return constructor.newInstance();
        } catch (Exception e) {
            // 尝试其他构造函数
            Constructor<?>[] constructors = clazz.getDeclaredConstructors();
            for (Constructor<?> constructor : constructors) {
                try {
                    constructor.setAccessible(true);
                    Class<?>[] parameterTypes = constructor.getParameterTypes();
                    Object[] args = new Object[parameterTypes.length];
                    for (int i = ZERO; i < parameterTypes.length; i++) {
                        args[i] = generatePrimitiveValue(parameterTypes[i]);
                    }
                    return (T) constructor.newInstance(args);
                } catch (Exception ex) {
                    // 忽略，尝试下一个构造函数
                }
            }
        }
        return null;
    }

    /**
     * 填充对象的所有字段
     *
     * @param object  对象
     * @param visited 记录已访问对象，防止循环引用
     * @param depth   深度记录
     * @throws IllegalAccessException
     */
    private static void fillFields(Object object, Set<Object> visited, int depth) throws IllegalAccessException {
        Class<?> clazz = object.getClass();
        // 处理所有字段（包括父类的字段）
        while (clazz != Object.class) {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                // 跳过静态字段和 transient 字段
                if (Modifier.isStatic(field.getModifiers()) || Modifier.isTransient(field.getModifiers())) {
                    continue;
                }
                field.setAccessible(true);
                // 如果字段已有值，则跳过
                if (field.get(object) != null) {
                    continue;
                }
                Class<?> fieldType = field.getType();
                // 处理基本类型
                if (fieldType.isPrimitive() || fieldType == String.class || fieldType == Integer.class ||
                        fieldType == Long.class || fieldType == Boolean.class || fieldType == Double.class ||
                        fieldType == Float.class || fieldType == Character.class || fieldType == Byte.class ||
                        fieldType == Short.class || fieldType == BigDecimal.class ||
                        fieldType == LocalDate.class || fieldType == LocalDateTime.class || fieldType == Date.class) {
                    field.set(object, generatePrimitiveValue(fieldType));
                    continue;
                }

                // 处理枚举
                if (fieldType.isEnum()) {
                    Object[] enumConstants = fieldType.getEnumConstants();
                    field.set(object, enumConstants[FAKER.number().numberBetween(ZERO, enumConstants.length)]);
                    continue;
                }

                // 处理集合
                if (Collection.class.isAssignableFrom(fieldType)) {
                    field.set(object, generateCollection(field, visited, depth));
                    continue;
                }

                // 处理 Map
                if (Map.class.isAssignableFrom(fieldType)) {
                    field.set(object, generateMap(field, visited, depth));
                    continue;
                }

                // 处理数组
                if (fieldType.isArray()) {
                    field.set(object, generateArray(fieldType, visited, depth));
                    continue;
                }

                // 处理嵌套对象
                if (!visited.contains(fieldType)) {
                    Object nestedObject = createFake(fieldType, visited, depth);
                    if (nestedObject != null) {
                        field.set(object, nestedObject);
                    }
                }
            }

            // 处理父类字段
            clazz = clazz.getSuperclass();
        }
    }

    /**
     * 生成集合类型（增强版，支持泛型类型识别）
     *
     * @param field   属性
     * @param visited 记录已访问对象，防止循环引用
     * @param depth   深度记录
     * @return 集合类型
     */
    private static Collection<Object> generateCollection(Field field, Set<Object> visited, int depth) {
        // 获取集合的泛型类型
        Type genericType = field.getGenericType();
        Class<?> elementType = Object.class;

        if (genericType instanceof ParameterizedType) {
            ParameterizedType paramType = (ParameterizedType) genericType;
            Type[] typeArgs = paramType.getActualTypeArguments();
            if (typeArgs.length > ZERO && typeArgs[ZERO] instanceof Class) {
                elementType = (Class<?>) typeArgs[ZERO];
            }
        }

        // 确定集合实现类型
        Collection<Object> collection = instantiateCollection(field.getType());
        // 随机生成 1-5 个元素
        // 生成集合元素
        for (int i = ZERO, size = FAKER.number().numberBetween(ONE, COLLECTION_MAX_SIZE); i < size; i++) {
            Object element = createFake(elementType, visited, depth);
            if (element != null) {
                collection.add(element);
            }
        }
        return collection;
    }

    /**
     * 生成集合类型
     *
     * @param collectionType 集合类型
     * @param visited        记录已访问对象，防止循环引用
     * @param depth          深度记录
     * @return 集合类型
     */
    private static Collection<Object> generateCollection(Class<?> collectionType, Set<Object> visited, int depth) {
        // 确定集合实现类型
        Collection<Object> collection = instantiateCollection(collectionType);
        // 随机生成 1-5 个元素
        // 假设集合元素类型为 Object（实际应该从泛型获取，但这里简化处理）
        for (int i = ZERO, size = FAKER.number().numberBetween(ONE, COLLECTION_MAX_SIZE); i < size; i++) {
            collection.add(createFake(Object.class, visited, depth));
        }
        return collection;
    }

    /**
     * 生成 Map 类型
     *
     * @param mapType map类型
     * @param visited 记录已访问对象，防止循环引用
     * @param depth   深度记录
     * @return 集合类型
     */
    private static Map<Object, Object> generateMap(Class<?> mapType, Set<Object> visited, int depth) {
        // 确定 Map 实现类型
        Map<Object, Object> map = instantiateMap(mapType);
        // 随机生成 1-5 个键值对
        for (int i = ZERO, size = FAKER.number().numberBetween(1, COLLECTION_MAX_SIZE); i < size; i++) {
            Object key = createFake(String.class, visited, depth);
            Object value = createFake(Object.class, visited, depth);
            map.put(key, value);
        }
        return map;
    }

    /**
     * 实例化集合
     *
     * @param collectionType 集合类型
     * @return 实例化后的集合
     */
    @SuppressWarnings(SUPPRESS_WARNINGS_VAL)
    private static Collection<Object> instantiateCollection(Class<?> collectionType) {
        if (!collectionType.isInterface()) {
            try {
                return (Collection<Object>) collectionType.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                // 忽略，使用默认实现
            }
        }
        if (List.class.isAssignableFrom(collectionType)) {
            return new ArrayList<>(COLLECTION_MAX_SIZE);
        } else if (Set.class.isAssignableFrom(collectionType)) {
            return new HashSet<>(8, 1);
        } else if (Queue.class.isAssignableFrom(collectionType)) {
            return new LinkedList<>();
        } else {
            return new ArrayList<>(COLLECTION_MAX_SIZE);
        }
    }

    /**
     * 生成 Map 类型（增强版，支持泛型类型识别）
     *
     * @param field   map元素属性
     * @param visited 记录已访问对象，防止循环引用
     * @param depth   深度记录
     * @return map
     */
    private static Map<Object, Object> generateMap(Field field, Set<Object> visited, int depth) {
        // 获取 Map 的泛型类型
        Type genericType = field.getGenericType();
        Class<?> keyType = Object.class;
        Class<?> valueType = Object.class;

        if (genericType instanceof ParameterizedType) {
            ParameterizedType paramType = (ParameterizedType) genericType;
            Type[] typeArgs = paramType.getActualTypeArguments();
            if (typeArgs.length > ONE) {
                if (typeArgs[ZERO] instanceof Class) {
                    keyType = (Class<?>) typeArgs[ZERO];
                }
                if (typeArgs[ONE] instanceof Class) {
                    valueType = (Class<?>) typeArgs[ONE];
                }
            }
        }
        // 确定 Map 实现类型
        Map<Object, Object> map = instantiateMap(field.getType());
        // 随机生成 1-5 个键值对
        // 生成键值对
        for (int i = ZERO, size = FAKER.number().numberBetween(ONE, COLLECTION_MAX_SIZE); i < size; i++) {
            Object key = createFake(keyType, visited, depth);
            Object value = createFake(valueType, visited, depth);
            if (key != null && value != null) {
                map.put(key, value);
            }
        }
        return map;
    }

    /**
     * 实例化 Map
     *
     * @param mapType map类型
     * @return 实例化后的map
     */
    @SuppressWarnings(SUPPRESS_WARNINGS_VAL)
    private static Map<Object, Object> instantiateMap(Class<?> mapType) {
        if (!mapType.isInterface()) {
            try {
                return (Map<Object, Object>) mapType.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                // 忽略，使用默认实现
            }
        }
        if (SortedMap.class.isAssignableFrom(mapType)) {
            return new TreeMap<>();
        } else {
            return new HashMap<>(8, 1);
        }
    }

    /**
     * 生成数组
     *
     * @param arrayType 数组类型
     * @param visited   记录已访问对象，防止循环引用
     * @param depth     深度记录
     * @return 实例化后的数组
     */
    private static Object generateArray(Class<?> arrayType, Set<Object> visited, int depth) {
        Class<?> componentType = arrayType.getComponentType();
        // 随机生成 1-5 个元素
        int size = FAKER.number().numberBetween(ONE, COLLECTION_MAX_SIZE);
        Object array = Array.newInstance(componentType, size);
        for (int i = ZERO; i < size; i++) {
            Array.set(array, i, createFake(componentType, visited, depth));
        }
        return array;
    }

    private FakeUtil() {
    }
}
