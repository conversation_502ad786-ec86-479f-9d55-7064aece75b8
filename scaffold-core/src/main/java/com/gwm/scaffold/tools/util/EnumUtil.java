package com.gwm.scaffold.tools.util;

import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.tools.vo.EnumDTO;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gwm.scaffold.tools.util.FrontUtil.convertFrontType;

/**
 * enum工具类
 *
 * <AUTHOR>
 * @date 2025/4/12
 */
public final class EnumUtil {

    /**
     * 将枚举类转化为可输出对象
     *
     * @param enumName 枚举类名
     * @return 可输出对象
     */
    public static Map<String, List<EnumDTO<?>>> convertEnum(String enumName) {
        if (!StringUtils.hasText(enumName)) {
            return null;
        }
        if (!enumName.contains(PACKAGE_DELIMITER)) {
            enumName = "com.gwm.yourproject.enums." + enumName;
        }
        try {
            return convertEnum(Class.forName(enumName));
        } catch (ClassNotFoundException e) {
            throw new ServiceException("枚举对象不存在");
        }
    }

    /**
     * 将枚举类转化为可输出对象
     *
     * @param enumClass 枚举类型
     * @return 可输出对象
     */
    public static Map<String, List<EnumDTO<?>>> convertEnum(Class<?> enumClass) {
        if (null == enumClass) {
            return null;
        }
        if (!enumClass.isEnum()) {
            throw new ServiceException("非枚举类型");
        }
        Enum<?>[] enumConstants = (Enum<?>[]) enumClass.getEnumConstants();
        if (null == enumConstants) {
            return null;
        }
        Map<String, List<EnumDTO<?>>> result = new HashMap<>(16, 1);
        List<EnumDTO<?>> list;
        final String enumDefaultField = "$VALUES";
        for (Enum<?> constant : enumConstants) {
            try {
                // 通过反射获取枚举实例的所有字段
                Field[] fields = constant.getClass().getDeclaredFields();
                list = new ArrayList<>(fields.length);
                int modifiers;
                for (Field field : fields) {
                    // 获取字段的修饰符
                    modifiers = field.getModifiers();
                    // 检查字段是否为 private final 修饰
                    if (Modifier.isPrivate(modifiers) && Modifier.isFinal(modifiers) && !field.isEnumConstant() && !enumDefaultField.equals(field.getName())) {
                        field.setAccessible(true);
                        list.add(new EnumDTO<>()
                                .setName(field.getName())
                                .setValue(field.get(constant))
                                .setType(convertFrontType(field.getType())));
                    }
                }
                result.put(constant.name(), list);
            } catch (Exception e) {
                throw new ServiceException("遍历枚举类异常");
            }
        }
        return result;
    }

    private static final String PACKAGE_DELIMITER = ".";

    private EnumUtil() {
    }
}
