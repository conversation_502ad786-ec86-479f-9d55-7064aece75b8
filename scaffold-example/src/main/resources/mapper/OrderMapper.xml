<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwm.scaffold.example.mapper.OrderMapper">

    <!-- 订单VO结果映射 -->
    <resultMap id="OrderVOResultMap" type="com.gwm.scaffold.example.vo.OrderVO">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="customer_id" property="customerId"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
        <result column="customer_email" property="customerEmail"/>
        <result column="product_name" property="productName"/>
        <result column="product_sku" property="productSku"/>
        <result column="quantity" property="quantity"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="actual_amount" property="actualAmount"/>
        <result column="order_status" property="orderStatus"/>
        <result column="order_status_desc" property="orderStatusDesc"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_method_desc" property="paymentMethodDesc"/>
        <result column="payment_time" property="paymentTime"/>
        <result column="shipping_address" property="shippingAddress"/>
        <result column="shipping_phone" property="shippingPhone"/>
        <result column="shipping_name" property="shippingName"/>
        <result column="shipping_time" property="shippingTime"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_code" property="updateUserCode"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 订单状态统计结果映射 -->
    <resultMap id="OrderStatusCountResultMap" type="com.gwm.scaffold.example.mapper.OrderMapper$OrderStatusCount">
        <result column="order_status" property="orderStatus"/>
        <result column="count" property="count"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        id, order_no, customer_id, customer_name, customer_phone, customer_email,
        product_name, product_sku, quantity, unit_price, total_amount, discount_amount, actual_amount,
        order_status, payment_method, payment_time, shipping_address, shipping_phone, shipping_name,
        shipping_time, delivery_time, remark, create_time, create_user_code, create_user_name,
        update_time, update_user_code, update_user_name, version
    </sql>

    <!-- 扩展查询字段（包含状态描述） -->
    <sql id="ExtendedColumns">
        <include refid="BaseColumns"/>,
        CASE order_status
            WHEN 1 THEN '待支付'
            WHEN 2 THEN '已支付'
            WHEN 3 THEN '已发货'
            WHEN 4 THEN '已完成'
            WHEN 5 THEN '已取消'
            ELSE '未知状态'
        END as order_status_desc,
        CASE payment_method
            WHEN 1 THEN '支付宝'
            WHEN 2 THEN '微信支付'
            WHEN 3 THEN '银行卡'
            WHEN 4 THEN '现金'
            ELSE '未知方式'
        END as payment_method_desc
    </sql>

    <!-- 查询条件 -->
    <sql id="WhereConditions">
        <where>
            deleted = 0
            <if test="request.orderNo != null and request.orderNo != ''">
                AND order_no LIKE CONCAT('%', #{request.orderNo}, '%')
            </if>
            <if test="request.customerId != null">
                AND customer_id = #{request.customerId}
            </if>
            <if test="request.customerName != null and request.customerName != ''">
                AND customer_name LIKE CONCAT('%', #{request.customerName}, '%')
            </if>
            <if test="request.customerPhone != null and request.customerPhone != ''">
                AND customer_phone LIKE CONCAT('%', #{request.customerPhone}, '%')
            </if>
            <if test="request.productName != null and request.productName != ''">
                AND product_name LIKE CONCAT('%', #{request.productName}, '%')
            </if>
            <if test="request.orderStatus != null">
                AND order_status = #{request.orderStatus}
            </if>
            <if test="request.paymentMethod != null">
                AND payment_method = #{request.paymentMethod}
            </if>
            <if test="request.createTimeStart != null">
                AND create_time >= #{request.createTimeStart}
            </if>
            <if test="request.createTimeEnd != null">
                AND create_time &lt;= #{request.createTimeEnd}
            </if>
            <if test="request.paymentTimeStart != null">
                AND payment_time >= #{request.paymentTimeStart}
            </if>
            <if test="request.paymentTimeEnd != null">
                AND payment_time &lt;= #{request.paymentTimeEnd}
            </if>
            <if test="request.shippingTimeStart != null">
                AND shipping_time >= #{request.shippingTimeStart}
            </if>
            <if test="request.shippingTimeEnd != null">
                AND shipping_time &lt;= #{request.shippingTimeEnd}
            </if>
            <if test="request.deliveryTimeStart != null">
                AND delivery_time >= #{request.deliveryTimeStart}
            </if>
            <if test="request.deliveryTimeEnd != null">
                AND delivery_time &lt;= #{request.deliveryTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 排序条件 -->
    <sql id="OrderByConditions">
        <choose>
            <when test="request.sortField != null and request.sortField != ''">
                ORDER BY ${request.sortField}
                <choose>
                    <when test="request.sortOrder != null and request.sortOrder.toLowerCase() == 'asc'">
                        ASC
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 分页查询订单列表 -->
    <select id="selectOrderPage" resultMap="OrderVOResultMap">
        SELECT
        <include refid="ExtendedColumns"/>
        FROM sys_order
        <include refid="WhereConditions"/>
        <include refid="OrderByConditions"/>
    </select>

    <!-- 根据ID查询订单详情 -->
    <select id="selectOrderById" resultMap="OrderVOResultMap">
        SELECT
        <include refid="ExtendedColumns"/>
        FROM sys_order
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 根据订单号查询订单 -->
    <select id="selectByOrderNo" resultType="com.gwm.scaffold.example.entity.Order">
        SELECT
        <include refid="BaseColumns"/>
        FROM sys_order
        WHERE order_no = #{orderNo} AND deleted = 0
    </select>

    <!-- 根据客户ID查询订单列表 -->
    <select id="selectOrdersByCustomerId" resultMap="OrderVOResultMap">
        SELECT
        <include refid="ExtendedColumns"/>
        FROM sys_order
        WHERE customer_id = #{customerId} AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 统计订单数量按状态分组 -->
    <select id="selectOrderCountByStatus" resultMap="OrderStatusCountResultMap">
        SELECT
            order_status,
            COUNT(*) as count
        FROM sys_order
        WHERE deleted = 0
        GROUP BY order_status
        ORDER BY order_status
    </select>

</mapper>
