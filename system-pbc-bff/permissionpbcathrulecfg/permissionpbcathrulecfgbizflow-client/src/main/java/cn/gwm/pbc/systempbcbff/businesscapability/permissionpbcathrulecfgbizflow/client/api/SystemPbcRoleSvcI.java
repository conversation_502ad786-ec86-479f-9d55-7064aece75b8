package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.role.RoleInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleDeleteDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleSaveDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 权限PBC-角色管理服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.15
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcRoleSvcI-1702610562227")
@RequestMapping(path = "/PermissionPBCAthRuleCfgBizFlow")
public interface SystemPbcRoleSvcI {

    /**
     * 权限中心-编辑角色 system/role/save
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcRoleSvc/save/v1")
    Response<Void> save(@RequestBody(required = true) RoleSaveDTO request);

    /**
     * 权限中心-删除角色 system/role/delete
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcRoleSvc/delete/v1")
    Response<Void> delete(@RequestBody(required = true) RoleDeleteDTO request);

    /**
     * 权限中心-获取角色详情 system/role/detail
     *
     * @param request request
     * @return Response 权限中心-获取角色详情
     */
    @PostMapping(path = "/SystemPbcRoleSvc/detail/v1")
    Response<RoleInfoItemVO> detail(@RequestBody(required = true) RoleDetailDTO request);

    /**
     * 权限中心-查询角色列表 system/role/list
     *
     * @param request request
     * @return Response 权限中心-查询角色列表
     */
    @PostMapping(path = "/SystemPbcRoleSvc/list/v1")
    Response<Map<String, Object>> list(@RequestBody(required = true) RoleListDTO request);
}
