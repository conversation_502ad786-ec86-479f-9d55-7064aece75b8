package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleUserBindBatchDTO", desc = "")
@Data
public class RoleUserBindBatchDTO implements Serializable {
    /**
     * 角色id
     */
    @Field(name = "角色id", desc = "角色id")
    private Long id;

    /**
     * userIds
     */
    @Field(name = "userIds", desc = "")
    @Alias("user_ids")
    private String[] userIds;

    /**
     * groupIds
     */
    @Field(name = "groupIds", desc = "")
    @Alias("group_ids")
    private String[] groupIds;

}
