<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>品牌新增/编辑页 - 线框图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .wireframe {
            background-color: white;
            border: 2px solid #333;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 30px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #333;
            background: white;
            cursor: pointer;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .form-row {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        .form-label {
            width: 120px;
            font-weight: bold;
        }
        .required {
            color: red;
        }
        .form-control {
            padding: 8px;
            border: 1px solid #ccc;
            width: 300px;
        }
        .form-control.large {
            width: 400px;
        }
        .form-control.small {
            width: 100px;
        }
        .form-hint {
            color: #666;
            font-size: 12px;
            margin-left: 10px;
        }
        .textarea {
            height: 80px;
            resize: vertical;
        }
        .upload-area {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            background-color: #f9f9f9;
            width: 300px;
        }
        .preview-area {
            border: 1px solid #ccc;
            width: 150px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f0f0f0;
            margin-top: 10px;
        }
        .radio-group {
            display: flex;
            gap: 20px;
        }
        .radio-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div class="wireframe">
        <div class="header">
            <div class="title">品牌信息 > 新增品牌</div>
            <div class="buttons">
                <button class="btn">保存</button>
                <button class="btn">取消</button>
            </div>
        </div>

        <div class="section">
            <div class="section-title">基础信息</div>
            <div class="form-row">
                <label class="form-label">品牌名称<span class="required">*</span>:</label>
                <input type="text" class="form-control" placeholder="请输入品牌名称">
                <span class="form-hint">(2-50字符)</span>
            </div>
            <div class="form-row">
                <label class="form-label">英文名称:</label>
                <input type="text" class="form-control" placeholder="请输入英文名称">
                <span class="form-hint">(可选)</span>
            </div>
            <div class="form-row">
                <label class="form-label">所属国别<span class="required">*</span>:</label>
                <select class="form-control">
                    <option>请选择国别</option>
                    <option>德国</option>
                    <option>日本</option>
                    <option>美国</option>
                    <option>中国</option>
                </select>
                <span class="form-hint">(必选)</span>
            </div>
            <div class="form-row">
                <label class="form-label">成立时间:</label>
                <select class="form-control">
                    <option>请选择年份</option>
                    <option>2024</option>
                    <option>2023</option>
                    <option>2022</option>
                </select>
                <span class="form-hint">(可选)</span>
            </div>
            <div class="form-row">
                <label class="form-label">官网地址:</label>
                <input type="url" class="form-control large" placeholder="https://www.example.com">
                <span class="form-hint">(可选)</span>
            </div>
        </div>

        <div class="section">
            <div class="section-title">品牌标识</div>
            <div class="form-row">
                <label class="form-label">品牌LOGO:</label>
                <div>
                    <div class="upload-area">
                        <div>点击上传图片</div>
                        <div class="form-hint">支持jpg/png/gif，最大2MB</div>
                    </div>
                    <div class="preview-area">
                        预览图片区域
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">详细信息</div>
            <div class="form-row">
                <label class="form-label">品牌简介:</label>
                <textarea class="form-control large textarea" placeholder="请输入品牌简介"></textarea>
                <span class="form-hint">(最大1000字符)</span>
            </div>
        </div>

        <div class="section">
            <div class="section-title">其他设置</div>
            <div class="form-row">
                <label class="form-label">状态:</label>
                <div class="radio-group">
                    <div class="radio-item">
                        <input type="radio" name="status" value="active" checked>
                        <label>启用</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" name="status" value="inactive">
                        <label>禁用</label>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <label class="form-label">排序权重:</label>
                <input type="number" class="form-control small" value="100">
                <span class="form-hint">(数字)</span>
            </div>
        </div>
    </div>
</body>
</html>