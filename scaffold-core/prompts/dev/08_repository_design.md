# 08 数据访问层规范

## 数据访问层设计原则

### 核心原则
- **所有数据访问层必须继承`com.baomidou.mybatisplus.core.mapper.BaseMapper<T>`**
- 数据访问层负责与数据库交互，提供CRUD操作
- 封装SQL语句和数据库操作细节
- 返回领域实体或原始数据，不处理业务逻辑
- 保持方法粒度适中，既不过大也不过小
- 考虑性能优化，如批量操作和查询优化

### 数据访问层职责
- 执行CRUD基本操作（通过BaseMapper提供）
- 实现复杂查询和自定义SQL
- 处理数据库映射和类型转换
- 优化数据库访问性能
- 提供批量操作支持

## MyBatis-Plus映射器设计

### 统一规范要求
- **必须使用`@Mapper`注解标记接口**
- **必须继承`com.baomidou.mybatisplus.core.mapper.BaseMapper<T>`获取基本CRUD功能**
- 自定义方法遵循MyBatis命名规范
- 复杂查询使用XML配置文件或注解方式
- 推荐配合使用`IService`和`ServiceImpl`增强功能
- 禁止使用其他ORM框架的Repository模式

### 映射器接口示例
```java
/**
 * 品牌数据访问接口
 * 必须继承BaseMapper获取基础CRUD功能
 */
@Mapper
public interface BrandMapper extends BaseMapper<Brand> {
    
    /**
     * 根据品牌名称模糊查询
     *
     * @param brandName 品牌名称关键词
     * @return 品牌列表
     */
    List<Brand> selectByBrandNameLike(@Param("brandName") String brandName);
    
    /**
     * 根据国家/地区查询品牌
     *
     * @param country 国家/地区
     * @return 品牌列表
     */
    List<Brand> selectByCountry(@Param("country") String country);
    
    /**
     * 查询品牌及其关联的厂商信息
     *
     * @param id 品牌ID
     * @return 品牌信息（包含厂商列表）
     */
    BrandDetailDTO selectBrandWithManufacturers(@Param("id") Long id);
    
    /**
     * 批量插入品牌
     *
     * @param brands 品牌列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<Brand> brands);
    
    /**
     * 统计各国家/地区的品牌数量
     *
     * @return 统计结果
     */
    List<BrandCountDTO> countByCountry();
    
    /**
     * 自定义复杂查询（使用条件构造器）
     * 注意：简单查询优先使用BaseMapper提供的方法配合条件构造器
     *
     * @param queryWrapper 查询条件构造器
     * @return 品牌列表
     */
    default List<Brand> selectByCondition(Wrapper<Brand> queryWrapper) {
        return this.selectList(queryWrapper);
    }
}
```

### XML映射文件示例

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwm.globalvehicle.masterdata.brand.repository.BrandMapper">

    <!-- 自定义结果映射 -->
    <resultMap id="BrandDetailMap" type="com.gwm.globalvehicle.masterdata.brand.dto.BrandDetailDTO">
        <id property="id" column="id"/>
        <result property="brandCode" column="brand_code"/>
        <result property="brandNameCn" column="brand_name_cn"/>
        <result property="brandNameEn" column="brand_name_en"/>
        <result property="brandLogoUrl" column="brand_logo_url"/>
        <result property="brandCountry" column="brand_country"/>
        <result property="brandPositioning" column="brand_positioning"/>
        <result property="status" column="status"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateTime" column="update_time"/>
        <collection property="manufacturers" ofType="com.gwm.globalvehicle.masterdata.manufacturer.dto.ManufacturerDTO">
            <id property="id" column="m_id"/>
            <result property="manufacturerCode" column="m_code"/>
            <result property="manufacturerNameCn" column="m_name_cn"/>
            <result property="manufacturerType" column="m_type"/>
        </collection>
    </resultMap>

    <!-- 根据品牌名称模糊查询 -->
    <select id="selectByBrandNameLike" resultType="com.gwm.globalvehicle.masterdata.domain.Brand">
        SELECT * FROM cmp_brand
        WHERE (brand_name_cn LIKE CONCAT('%', #{brandName}, '%')
        OR brand_name_en LIKE CONCAT('%', #{brandName}, '%'))
        AND status = 1
        ORDER BY update_time DESC
    </select>

    <!-- 根据国家/地区查询品牌 -->
    <select id="selectByCountry" resultType="com.gwm.globalvehicle.masterdata.domain.Brand">
        SELECT * FROM cmp_brand
        WHERE brand_country = #{country}
        AND status = 1
        ORDER BY update_time DESC
    </select>

    <!-- 查询品牌及其关联的厂商信息 -->
    <select id="selectBrandWithManufacturers" resultMap="BrandDetailMap">
        SELECT b.*,
        m.id AS m_id,
        m.manufacturer_code AS m_code,
        m.manufacturer_name_cn AS m_name_cn,
        m.manufacturer_type AS m_type
        FROM cmp_brand b
        LEFT JOIN cmp_manufacturer m ON b.id = m.brand_id AND m.status = 1
        WHERE b.id = #{id}
        AND b.status = 1
    </select>

    <!-- 批量插入品牌 -->
    <insert id="batchInsert">
        INSERT INTO cmp_brand (
        brand_code, brand_name_cn, brand_name_en, brand_logo_url,
        brand_country, brand_positioning, status,
        create_user_id, create_time, update_user_id, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.brandCode}, #{item.brandNameCn}, #{item.brandNameEn}, #{item.brandLogoUrl},
            #{item.brandCountry}, #{item.brandPositioning}, #{item.status},
            #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 统计各国家/地区的品牌数量 -->
    <select id="countByCountry" resultType="com.gwm.globalvehicle.masterdata.brand.dto.BrandCountDTO">
        SELECT brand_country AS country, COUNT(*) AS count
        FROM cmp_brand
        WHERE status = 1
        GROUP BY brand_country
        ORDER BY count DESC
    </select>
</mapper>
```

## BaseMapper基础功能使用

### CRUD基本操作
继承BaseMapper后，自动获得以下基础CRUD功能：

```java
/**
 * BaseMapper提供的基础功能示例
 */
@Service
public class BrandService {
    
    @Autowired
    private BrandMapper brandMapper;
    
    /**
     * 插入一条记录
     */
    public int saveBrand(Brand brand) {
        return brandMapper.insert(brand);
    }
    
    /**
     * 根据ID删除记录
     */
    public int deleteBrandById(Long id) {
        return brandMapper.deleteById(id);
    }
    
    /**
     * 根据条件删除记录
     */
    public int deleteBrandByCondition(String brandCode) {
        LambdaQueryWrapper<Brand> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Brand::getBrandCode, brandCode);
        return brandMapper.delete(wrapper);
    }
    
    /**
     * 根据ID更新记录
     */
    public int updateBrand(Brand brand) {
        return brandMapper.updateById(brand);
    }
    
    /**
     * 根据条件更新记录
     */
    public int updateBrandByCondition(Brand brand, String brandCode) {
        LambdaUpdateWrapper<Brand> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Brand::getBrandCode, brandCode);
        return brandMapper.update(brand, wrapper);
    }
    
    /**
     * 根据ID查询记录
     */
    public Brand getBrandById(Long id) {
        return brandMapper.selectById(id);
    }
    
    /**
     * 根据条件查询一条记录
     */
    public Brand getBrandByCode(String brandCode) {
        LambdaQueryWrapper<Brand> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Brand::getBrandCode, brandCode);
        return brandMapper.selectOne(wrapper);
    }
    
    /**
     * 查询所有记录
     */
    public List<Brand> getAllBrands() {
        return brandMapper.selectList(null);
    }
    
    /**
     * 根据条件查询记录列表
     */
    public List<Brand> getBrandsByCondition(LambdaQueryWrapper<Brand> wrapper) {
        return brandMapper.selectList(wrapper);
    }
    
    /**
     * 根据条件统计记录数
     */
    public Long countBrands(LambdaQueryWrapper<Brand> wrapper) {
        return brandMapper.selectCount(wrapper);
    }
}
```

## 查询方法命名规范

### 自定义方法命名
当BaseMapper提供的基础功能无法满足需求时，可以添加自定义方法：

- `selectXxx`: 查询方法
- `insertXxx`: 插入方法  
- `updateXxx`: 更新方法
- `deleteXxx`: 删除方法
- `countXxx`: 统计方法
- `batchXxx`: 批量操作方法

### 推荐的命名示例
```java
/**
 * 推荐的自定义方法命名规范
 */
@Mapper
public interface BrandMapper extends BaseMapper<Brand> {
    
    // 查询方法
    List<Brand> selectByBrandNameLike(@Param("brandName") String brandName);
    List<Brand> selectActiveByCountry(@Param("country") String country);
    BrandDetailDTO selectBrandWithManufacturers(@Param("id") Long id);
    
    // 统计方法
    Long countByCountry(@Param("country") String country);
    List<BrandCountDTO> countGroupByCountry();
    
    // 批量操作方法
    int batchInsert(@Param("list") List<Brand> brands);
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);
    
    // 复杂业务查询
    List<Brand> selectBrandsByManufacturerType(@Param("manufacturerType") String manufacturerType);
}
```

## MyBatis-Plus条件构造器使用

### LambdaQueryWrapper使用规范
优先使用条件构造器进行动态查询，减少自定义SQL：

```java
/**
 * 条件构造器使用示例
 */
@Service
public class BrandQueryService {
    
    @Autowired
    private BrandMapper brandMapper;
    
    /**
     * 使用条件构造器进行复杂查询
     */
    public List<Brand> getBrandsByCondition(BrandQueryDTO queryDTO) {
        LambdaQueryWrapper<Brand> queryWrapper = new LambdaQueryWrapper<>();
        
        // 设置默认查询启用状态的品牌
        queryWrapper.eq(Brand::getStatus, queryDTO.getStatus() != null ? queryDTO.getStatus() : 1);
        
        // 按品牌代码查询
        queryWrapper.eq(StringUtils.hasText(queryDTO.getBrandCode()), 
                        Brand::getBrandCode, queryDTO.getBrandCode());
        
        // 按品牌名称模糊查询
        if (StringUtils.hasText(queryDTO.getBrandName())) {
            queryWrapper.and(wrapper -> wrapper
                .like(Brand::getBrandNameCn, queryDTO.getBrandName())
                .or()
                .like(Brand::getBrandNameEn, queryDTO.getBrandName())
            );
        }
        
        // 按国家/地区查询
        queryWrapper.eq(StringUtils.hasText(queryDTO.getBrandCountry()), 
                        Brand::getBrandCountry, queryDTO.getBrandCountry());
        
        // 按品牌定位查询
        queryWrapper.eq(StringUtils.hasText(queryDTO.getBrandPositioning()), 
                        Brand::getBrandPositioning, queryDTO.getBrandPositioning());
        
        // 按更新时间倒序排序
        queryWrapper.orderByDesc(Brand::getUpdateTime);
        
        // 使用BaseMapper的selectList方法
        return brandMapper.selectList(queryWrapper);
    }
    
    /**
     * 使用UpdateWrapper进行条件更新
     */
    public int updateBrandStatus(List<Long> ids, Integer status, Long updateUserId) {
        LambdaUpdateWrapper<Brand> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Brand::getId, ids)
                    .set(Brand::getStatus, status)
                    .set(Brand::getUpdateUserId, updateUserId)
                    .set(Brand::getUpdateTime, LocalDateTime.now());
        
        // 使用BaseMapper的update方法
        return brandMapper.update(null, updateWrapper);
    }
}
```

### 分页查询规范
```java
/**
 * 分页查询实现
 */
@Service
public class BrandPageService {
    
    @Autowired
    private BrandMapper brandMapper;
    
    /**
     * 分页查询品牌
     */
    public IPage<Brand> getBrandPage(BrandQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<Brand> queryWrapper = buildQueryWrapper(queryDTO);
        
        // 构建分页参数
        Page<Brand> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        
        // 设置排序
        if (StringUtils.hasText(queryDTO.getSortField())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(camelToUnderscore(queryDTO.getSortField()));
            orderItem.setAsc("asc".equalsIgnoreCase(queryDTO.getSortOrder()));
            page.addOrder(orderItem);
        } else {
            // 默认按更新时间倒序
            page.addOrder(OrderItem.desc("update_time"));
        }
        
        // 使用BaseMapper的selectPage方法
        return brandMapper.selectPage(page, queryWrapper);
    }
    
    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Brand> buildQueryWrapper(BrandQueryDTO queryDTO) {
        LambdaQueryWrapper<Brand> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(Brand::getStatus, queryDTO.getStatus() != null ? queryDTO.getStatus() : 1)
                   .eq(StringUtils.hasText(queryDTO.getBrandCode()), Brand::getBrandCode, queryDTO.getBrandCode())
                   .eq(StringUtils.hasText(queryDTO.getBrandCountry()), Brand::getBrandCountry, queryDTO.getBrandCountry())
                   .eq(StringUtils.hasText(queryDTO.getBrandPositioning()), Brand::getBrandPositioning, queryDTO.getBrandPositioning());
        
        // 品牌名称模糊查询
        if (StringUtils.hasText(queryDTO.getBrandName())) {
            queryWrapper.and(wrapper -> wrapper
                .like(Brand::getBrandNameCn, queryDTO.getBrandName())
                .or()
                .like(Brand::getBrandNameEn, queryDTO.getBrandName())
            );
        }
        
        return queryWrapper;
    }
}
```

## Service层集成规范

### IService和ServiceImpl使用
推荐配合使用MyBatis-Plus提供的IService和ServiceImpl：

```java
/**
 * 品牌服务接口
 */
public interface IBrandService extends IService<Brand> {
    
    /**
     * 根据品牌代码获取品牌
     */
    Brand getBrandByCode(String brandCode);
    
    /**
     * 分页查询品牌
     */
    IPage<Brand> getBrandPage(BrandQueryDTO queryDTO);
    
    /**
     * 批量更新品牌状态
     */
    boolean updateBrandStatus(List<Long> ids, Integer status);
}

/**
 * 品牌服务实现类
 */
@Service
public class BrandServiceImpl extends ServiceImpl<BrandMapper, Brand> implements IBrandService {
    
    @Override
    public Brand getBrandByCode(String brandCode) {
        LambdaQueryWrapper<Brand> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Brand::getBrandCode, brandCode)
               .eq(Brand::getStatus, 1);
        return this.getOne(wrapper);
    }
    
    @Override
    public IPage<Brand> getBrandPage(BrandQueryDTO queryDTO) {
        LambdaQueryWrapper<Brand> wrapper = buildQueryWrapper(queryDTO);
        Page<Brand> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return this.page(page, wrapper);
    }
    
    @Override
    public boolean updateBrandStatus(List<Long> ids, Integer status) {
        LambdaUpdateWrapper<Brand> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(Brand::getId, ids)
               .set(Brand::getStatus, status)
               .set(Brand::getUpdateTime, LocalDateTime.now());
        return this.update(wrapper);
    }
    
    // ... other methods
}
```

## 性能优化最佳实践

### 查询优化
- **优先使用BaseMapper + 条件构造器**，减少自定义SQL
- 只查询必要的字段，使用`select()`方法指定字段
- 使用索引字段作为查询条件
- 合理使用分页和限制结果集大小

### 批量操作优化
```java
/**
 * 批量操作优化示例
 */
@Service
@Transactional
public class BrandBatchService extends ServiceImpl<BrandMapper, Brand> {
    
    /**
     * 批量保存品牌（推荐使用ServiceImpl的saveBatch方法）
     */
    public boolean batchSaveBrands(List<Brand> brands) {
        // 使用MyBatis-Plus提供的批量保存方法
        return this.saveBatch(brands, 1000); // 每批1000条
    }
    
    /**
     * 批量更新品牌（使用自定义SQL）
     */
    public int batchUpdateBrands(List<Brand> brands) {
        return baseMapper.batchUpdate(brands);
    }
    
    /**
     * 批量删除品牌（逻辑删除）
     */
    public boolean batchDeleteBrands(List<Long> ids) {
        return this.removeByIds(ids);
    }
}
```

### 条件构造器性能优化
```java
/**
 * 条件构造器性能优化示例
 */
public class BrandQueryOptimizer {
    
    /**
     * 优化的查询方法
     */
    public List<Brand> getOptimizedBrands(BrandQueryDTO queryDTO) {
        LambdaQueryWrapper<Brand> wrapper = new LambdaQueryWrapper<>();
        
        // 1. 优先使用索引字段作为查询条件
        wrapper.eq(Brand::getStatus, 1); // status字段有索引
        
        // 2. 精确匹配放在前面
        wrapper.eq(StringUtils.hasText(queryDTO.getBrandCode()), 
                  Brand::getBrandCode, queryDTO.getBrandCode()); // brand_code有唯一索引
        
        // 3. 范围查询和模糊查询放在后面
        if (StringUtils.hasText(queryDTO.getBrandName())) {
            wrapper.and(w -> w.like(Brand::getBrandNameCn, queryDTO.getBrandName())
                            .or()
                            .like(Brand::getBrandNameEn, queryDTO.getBrandName()));
        }
        
        // 4. 只查询需要的字段
        wrapper.select(Brand::getId, Brand::getBrandCode, Brand::getBrandNameCn, 
                      Brand::getBrandNameEn, Brand::getBrandCountry);
        
        // 5. 限制结果集大小
        wrapper.last("LIMIT 1000");
        
        return brandMapper.selectList(wrapper);
    }
}
```

## 数据库迁移与版本控制

### Flyway/Liquibase使用
- 使用迁移脚本管理数据库结构变更
- 按顺序编号迁移脚本
- 每个迁移脚本应当是幂等的
- 生产环境变更先在测试环境验证

### 命名规范
- 使用时间戳或版本号前缀
- 简要描述变更内容
- 使用统一的命名风格

### 迁移脚本示例
```sql
-- V1.0.0__Create_brand_table.sql
CREATE TABLE cmp_brand (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
    brand_code VARCHAR(50) NOT NULL COMMENT '品牌代码',
    brand_name_cn VARCHAR(100) NOT NULL COMMENT '品牌中文名称',
    brand_name_en VARCHAR(100) NULL COMMENT '品牌英文名称',
    brand_logo_url VARCHAR(500) NULL COMMENT '品牌Logo URL',
    brand_country VARCHAR(50) NULL COMMENT '品牌所属国家/地区',
    brand_positioning VARCHAR(20) NULL COMMENT '品牌定位',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态(1:启用,0:停用)',
    version INT DEFAULT 0 COMMENT '版本号',
    create_user_id BIGINT NOT NULL COMMENT '创建人ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user_id BIGINT NULL COMMENT '更新人ID',
    update_time DATETIME NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE INDEX idx_brand_code (brand_code),
    INDEX idx_brand_name (brand_name_cn, brand_name_en),
    INDEX idx_brand_country (brand_country),
    INDEX idx_update_time (update_time)
) COMMENT='品牌信息表';

-- V1.0.1__Add_brand_group_column.sql
ALTER TABLE cmp_brand
ADD COLUMN brand_group VARCHAR(100) NULL COMMENT '品牌集团' AFTER brand_country;
``` 