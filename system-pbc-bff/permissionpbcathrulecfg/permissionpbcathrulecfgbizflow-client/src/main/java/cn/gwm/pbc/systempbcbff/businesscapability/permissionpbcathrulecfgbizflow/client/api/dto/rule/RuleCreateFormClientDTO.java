package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleCreateFormClientDTO", desc = "null")
@Data
@ApiModel("规则创建请求参数对象")
public class RuleCreateFormClientDTO implements Serializable {

    /**
     * 操作人账号
     */
    @Field(name = "account", desc = "")
    @JsonIgnore
    @ApiModelProperty("操作人账号")
    private String account;

    /**
     * 规则名称
     */
    @Field(name = "规则名称", desc = "规则名称")
    @ApiModelProperty("规则名称")
    private String name;

    /**
     * 规则内容
     */
    @Field(name = "规则内容", desc = "规则内容")
    @ApiModelProperty("规则内容")
    private String content;

    /**
     * 是否启用（0禁用，1启用，默认1）
     */
    @Field(name = "是否启用（0禁用，1启用，默认1）", desc = "是否启用（0禁用，1启用，默认1）")
    @ApiModelProperty("是否启用（0禁用，1启用，默认1）")
    private Integer enable;

    /**
     * 备注
     */
    @Field(name = "备注", desc = "备注")
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 角色ID（虚拟角色）
     */
    @Field(name = "角色ID（虚拟角色）", desc = "角色ID（虚拟角色）")
    @ApiModelProperty("角色ID")
    @JsonProperty(value = "role_set_id")
    private Long roleSetId;
}