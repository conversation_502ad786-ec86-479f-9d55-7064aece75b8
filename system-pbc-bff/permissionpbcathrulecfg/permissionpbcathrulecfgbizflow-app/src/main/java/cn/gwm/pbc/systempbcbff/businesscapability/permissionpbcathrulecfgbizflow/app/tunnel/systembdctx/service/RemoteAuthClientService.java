package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback.RemoteAuthFallbackFactory;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.net.URI;

/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteAuthClientService", url = "EMPTY", fallback = RemoteAuthFallbackFactory.class)
public interface RemoteAuthClientService {

    /**
     * token信息交换
     *
     * @param uri
     * @param platformCode
     * @param accessToken
     * @return
     */
    @GetMapping(value = "/authenticate/check_token")
    CommonRlt<JSONObject> exchange(URI uri, @RequestParam("platform_code") String platformCode, @RequestParam("access_token") String accessToken);
}
