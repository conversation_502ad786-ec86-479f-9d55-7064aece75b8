package com.gwm.scaffold.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwm.scaffold.example.entity.Product;
import com.gwm.scaffold.example.dto.ProductQueryRequest;
import com.gwm.scaffold.example.vo.ProductVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品信息表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 分页查询产品信息表列表
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 产品信息表分页数据
     */
    IPage<Product> selectProductPage(Page<Product> page, @Param("request") ProductQueryRequest request);


}
