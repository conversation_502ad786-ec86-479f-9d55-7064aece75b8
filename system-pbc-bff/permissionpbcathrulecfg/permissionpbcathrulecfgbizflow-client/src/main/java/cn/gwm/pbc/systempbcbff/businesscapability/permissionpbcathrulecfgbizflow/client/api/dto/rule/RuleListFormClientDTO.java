package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleListFormClientDTO", desc = "null")
@Data
@ApiModel("规则列表请求参数对象")
public class RuleListFormClientDTO implements Serializable {

    /**
     * 当前页
     */
    @Field(name = "page", desc = "")
    @ApiModelProperty(value = "当前页")
    private Integer page;

    /**
     * 每页显示几条
     */
    @Field(name = "size", desc = "")
    @ApiModelProperty(value = "每页显示几条")
    private Integer size ;

    /**
     * 角色ID（虚拟角色）
     */
    @Field(name = "角色ID（虚拟角色）", desc = "角色ID（虚拟角色）")
    @ApiModelProperty("角色ID")
    @JsonProperty(value = "role_set_id")
    private Long roleSetId;

    /**
     * 规则名称
     */
    @Field(name = "规则名称", desc = "规则名称")
    @ApiModelProperty("规则名称")
    @JsonProperty(value = "rule_name")
    private String ruleName;

    /**
     * 是否启用（0禁用，1启用，默认1）
     */
    @Field(name = "是否启用（0禁用，1启用，默认1）", desc = "是否启用（0禁用，1启用，默认1）")
    @ApiModelProperty("是否启用（0禁用，1启用，默认1）")
    private Integer enable;
}