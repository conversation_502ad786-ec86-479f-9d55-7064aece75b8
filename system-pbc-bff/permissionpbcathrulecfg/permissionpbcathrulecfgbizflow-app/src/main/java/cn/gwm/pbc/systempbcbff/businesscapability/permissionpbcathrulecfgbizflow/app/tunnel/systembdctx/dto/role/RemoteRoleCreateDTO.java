package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RemoteRoleCreateDTO implements Serializable {

    private String code;
    private String ext1;
    private String ext2;
    private String ext3;
    private String name;
    private String remark;
    private Integer sort_no;
    private String state;
}
