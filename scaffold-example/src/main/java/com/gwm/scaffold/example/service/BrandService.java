package com.gwm.scaffold.example.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gwm.scaffold.example.entity.Brand;
import com.gwm.scaffold.example.dto.BrandCreateRequest;
import com.gwm.scaffold.example.dto.BrandUpdateRequest;
import com.gwm.scaffold.example.dto.BrandQueryRequest;
import com.gwm.scaffold.example.vo.BrandVO;

import java.util.List;

/**
 * 服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface BrandService extends IService<Brand> {

    /**
     * 分页查询列表
     *
     * @param request 查询条件
     * @return 分页数据
     */
    IPage<BrandVO> getBrandPage(BrandQueryRequest request);

    /**
     * 根据ID获取详情
     *
     * @param id ID
     * @return 详情
     */
    BrandVO getBrandById(Long id);

    /**
     * 创建
     *
     * @param request 创建请求
     * @return 详情
     */
    BrandVO createBrand(BrandCreateRequest request);

    /**
     * 更新
     *
     * @param request 更新请求
     * @return 详情
     */
    BrandVO updateBrand(BrandUpdateRequest request);

    /**
     * 删除
     *
     * @param id ID
     * @return 是否成功
     */
    boolean deleteBrand(Long id);

    /**
     * 批量删除
     *
     * @param ids ID列表
     * @return 删除数量
     */
    int batchDeleteBrands(List<Long> ids);

    /**
     * 获取启用状态的品牌列表
     *
     * @return 启用品牌列表
     */
    List<BrandVO> getActiveBrandList();

    /**
     * 更新品牌状态
     *
     * @param id 品牌ID
     * @param status 新状态
     * @return 是否成功
     */
    boolean updateBrandStatus(Long id, String status);

    /**
     * 根据国别统计品牌数量
     *
     * @return 统计结果
     */
    List<BrandVO> getBrandCountByCountry();

}
