package com.gwm.scaffold.example.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 创建请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "创建请求")
public class ModelCreateRequest {

    /**
     * name
     */
    @Schema(description = "name", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "name不能为空")
    @Size(max = 100, message = "name长度不能超过100")
    private String name;

    /**
     * seriesId
     */
    @Schema(description = "seriesId", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "seriesId不能为空")
    @Min(value = 1, message = "seriesId必须大于0")
    private Long seriesId;

    /**
     * yearModel
     */
    @Schema(description = "yearModel", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "yearModel不能为空")
    @Min(value = 1, message = "yearModel必须大于0")
    private Integer yearModel;

    /**
     * displacement
     */
    @Schema(description = "displacement")
    private BigDecimal displacement;

    /**
     * maxPower
     */
    @Schema(description = "maxPower")
    private Integer maxPower;

    /**
     * maxTorque
     */
    @Schema(description = "maxTorque")
    private Integer maxTorque;

    /**
     * transmission
     */
    @Schema(description = "transmission", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "transmission不能为空")
    @Size(max = 20, message = "transmission长度不能超过20")
    private String transmission;

    /**
     * gearCount
     */
    @Schema(description = "gearCount")
    private Integer gearCount;

    /**
     * driveType
     */
    @Schema(description = "driveType", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "driveType不能为空")
    @Size(max = 10, message = "driveType长度不能超过10")
    private String driveType;

    /**
     * fuelType
     */
    @Schema(description = "fuelType", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "fuelType不能为空")
    @Size(max = 20, message = "fuelType长度不能超过20")
    private String fuelType;

    /**
     * bodyStructure
     */
    @Schema(description = "bodyStructure", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "bodyStructure不能为空")
    @Size(max = 20, message = "bodyStructure长度不能超过20")
    private String bodyStructure;

    /**
     * seatCount
     */
    @Schema(description = "seatCount", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "seatCount不能为空")
    @Min(value = 1, message = "seatCount必须大于0")
    private Integer seatCount;

    /**
     * length
     */
    @Schema(description = "length")
    private Integer length;

    /**
     * width
     */
    @Schema(description = "width")
    private Integer width;

    /**
     * height
     */
    @Schema(description = "height")
    private Integer height;

    /**
     * wheelbase
     */
    @Schema(description = "wheelbase")
    private Integer wheelbase;

    /**
     * curbWeight
     */
    @Schema(description = "curbWeight")
    private Integer curbWeight;

    /**
     * tankCapacity
     */
    @Schema(description = "tankCapacity")
    private Integer tankCapacity;

    /**
     * fuelConsumption
     */
    @Schema(description = "fuelConsumption")
    private BigDecimal fuelConsumption;

    /**
     * guidePrice
     */
    @Schema(description = "guidePrice", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "guidePrice不能为空")
    private BigDecimal guidePrice;

    /**
     * launchTime
     */
    @Schema(description = "launchTime", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "launchTime不能为空")
    private Date launchTime;

    /**
     * discontinueTime
     */
    @Schema(description = "discontinueTime")
    private Date discontinueTime;

    /**
     * imageUrls
     */
    @Schema(description = "imageUrls")
    @Size(max = 2147483647, message = "imageUrls长度不能超过2147483647")
    private String imageUrls;

    /**
     * configDetails
     */
    @Schema(description = "configDetails")
    @Size(max = 2147483647, message = "configDetails长度不能超过2147483647")
    private String configDetails;

    /**
     * status
     */
    @Schema(description = "status")
    @Size(max = 20, message = "status长度不能超过20")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    private Integer sortWeight;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

}
