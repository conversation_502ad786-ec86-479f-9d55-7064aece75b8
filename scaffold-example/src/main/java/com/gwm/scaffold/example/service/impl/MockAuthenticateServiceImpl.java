package com.gwm.scaffold.example.service.impl;

import com.gwm.scaffold.auth.domain.LoginResult;
import com.gwm.scaffold.auth.domain.LoginUserInfo;
import com.gwm.scaffold.auth.service.AuthenticateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 模拟认证服务实现
 * 
 * 展示如何实现AuthenticateService接口
 * 在实际项目中，这里会对接真实的认证系统
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "scaffold.auth.sso.enabled", havingValue = "false", matchIfMissing = true)
public class MockAuthenticateServiceImpl implements AuthenticateService {

    @Override
    public LoginResult checkToken(String token) {
        log.debug("检查Token：{}", token);
        
        if (!StringUtils.hasText(token)) {
            return LoginResult.failure("Token不能为空");
        }
        
        // 模拟Token校验逻辑
        if ("invalid_token".equals(token)) {
            return LoginResult.failure("Token无效");
        }
        
        if ("expired_token".equals(token)) {
            return LoginResult.failure("Token已过期");
        }
        
        // 模拟根据Token获取用户信息
        LoginUserInfo userInfo = mockGetUserInfoByToken(token);
        if (userInfo == null) {
            return LoginResult.failure("用户不存在");
        }
        
        return LoginResult.success("success", userInfo);
    }

    @Override
    public LoginResult refreshToken(String refreshToken) {
        return null;
    }

    @Override
    public boolean logout(String token) {
        return false;
    }

    @Override
    public boolean hasPermission(String userCode, String permission) {
        log.debug("检查用户权限，用户：{}，权限：{}", userCode, permission);
        
        if (!StringUtils.hasText(userCode) || !StringUtils.hasText(permission)) {
            return false;
        }
        
        // 模拟权限检查逻辑
        // 管理员拥有所有权限
        if ("admin".equals(userCode)) {
            return true;
        }

        // 模拟不同用户的权限
        switch (userCode) {
            case "zhangsan":
                // 张三只有用户和订单读取权限
                return permission.startsWith("user:read") || permission.startsWith("order:read");
            case "lisi":
                // 李四有用户读取和创建权限，订单读取和创建权限
                return permission.startsWith("user:read") || permission.startsWith("user:create") ||
                       permission.startsWith("order:read") || permission.startsWith("order:create");
            case "wangwu":
                // 王五有用户和订单的所有权限
                return permission.startsWith("user:") || permission.startsWith("order:");
            default:
                // 其他用户默认只有读取权限
                return permission.endsWith(":read");
        }
    }

    @Override
    public boolean hasRole(String userCode, String role) {
        log.debug("检查用户角色，用户：{}，角色：{}", userCode, role);
        
        if (!StringUtils.hasText(userCode) || !StringUtils.hasText(role)) {
            return false;
        }
        
        // 模拟角色检查逻辑
        switch (userCode) {
            case "admin":
                return "admin".equals(role) || "user".equals(role);
            case "zhangsan":
            case "lisi":
            case "wangwu":
                return "user".equals(role);
            default:
                return "guest".equals(role);
        }
    }

    /**
     * 模拟根据Token获取用户信息
     */
    private LoginUserInfo mockGetUserInfoByToken(String token) {
        // 这里模拟不同的Token对应不同的用户
        switch (token) {
            case "admin_token":
                return LoginUserInfo.builder()
                        .userCode("admin")
                        .userName("管理员")
                        .departmentId(2L)
                        .departmentName("管理部")
                        .build();
            case "zhangsan_token":
                return LoginUserInfo.builder()
                        .userCode("zhangsan")
                        .userName("张三")
                        .departmentId(1L)
                        .departmentName("技术部")
                        .build();
            case "lisi_token":
                return LoginUserInfo.builder()
                        .userCode("lisi")
                        .departmentId(1L)
                        .userName("李四")
                        .departmentId(1L)
                        .departmentName("技术部")
                        .build();
            case "wangwu_token":
                return LoginUserInfo.builder()
                        .userCode("wangwu")
                        .userName("王五")
                        .departmentId(1L)
                        .departmentName("技术部")
                        .build();
            default:
                // 默认Token，用于测试
                if (token.startsWith("test_")) {
                    String userCode = token.replace("test_", "");
                    return LoginUserInfo.builder()
                            .userCode(userCode)
                            .userName("测试用户" + userCode)
                            .departmentId(3L)
                            .departmentName("测试部")
                            .build();
                }
                return null;
        }
    }
}
