package com.gwm.scaffold.example.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 品牌状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Getter
@AllArgsConstructor
public enum BrandStatusEnum {

    /**
     * 启用
     */
    ACTIVE("ACTIVE", "启用"),

    /**
     * 禁用
     */
    INACTIVE("INACTIVE", "禁用");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取状态名称
     *
     * @param code 状态码
     * @return 状态名称
     */
    public static String getNameByCode(String code) {
        if (code == null) {
            return "未知";
        }
        
        for (BrandStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        
        return "未知";
    }

    /**
     * 检查状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        if (code == null) {
            return false;
        }
        
        for (BrandStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return true;
            }
        }
        
        return false;
    }

}
