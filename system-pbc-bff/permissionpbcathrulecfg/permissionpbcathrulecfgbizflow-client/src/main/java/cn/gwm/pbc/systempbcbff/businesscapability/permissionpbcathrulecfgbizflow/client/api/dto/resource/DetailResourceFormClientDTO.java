package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "DetailResourceFormClientDTO", desc = "null")
@Data
@ApiModel("详情菜单参数对象")
public class DetailResourceFormClientDTO implements Serializable {

	@Field(name = "adminPlatformCode", desc = "")
    @ApiModelProperty("平台码")
    @JsonProperty(value = "admin_platform_code")
    private String adminPlatformCode;

    /**
     * 菜单ID
     */
    @Field(name = "id", desc = "")
    @ApiModelProperty("菜单ID")
    private String id;

}