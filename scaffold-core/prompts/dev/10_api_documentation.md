# 10 API文档规范

## API文档设计原则

### 核心原则
- API文档应当准确、完整地描述接口行为
- 使用标准化的格式和结构
- 提供足够的示例和说明
- 与实际API保持同步
- 考虑不同受众的需求（前端开发、测试、产品等）

### 文档工具选择
- 使用Swagger/OpenAPI规范进行API文档化
- 通过注解自动生成API文档
- 提供交互式API测试界面
- 支持导出多种格式（JSON、YAML、HTML等）
- 集成到CI/CD流程确保文档更新

## Swagger/OpenAPI注解使用

### 基础注解
- `@Api`: 标记控制器类，提供标签信息
- `@ApiOperation`: 描述操作方法
- `@ApiParam`: 描述方法参数
- `@ApiResponse`和`@ApiResponses`: 描述可能的响应
- `@ApiModel`: 描述模型类
- `@ApiModelProperty`: 描述模型属性

### 控制器注解示例
```java
@RestController
@RequestMapping("/api/v1/brands")
@Api(tags = "品牌管理", description = "品牌信息的增删改查接口")
public class BrandController {
    
    private final BrandService brandService;
    
    public BrandController(BrandService brandService) {
        this.brandService = brandService;
    }
    
    @PostMapping
    @ApiOperation(
        value = "创建品牌", 
        notes = "创建新的品牌信息，品牌代码不能重复",
        response = AjaxResult.class
    )
    @ApiResponses({
        @ApiResponse(code = 200, message = "创建成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 409, message = "品牌代码已存在")
    })
    public AjaxResult<Long> createBrand(
            @ApiParam(value = "品牌创建信息", required = true)
            @Valid @RequestBody BrandCreateDTO createDTO) {
        // 创建品牌逻辑
        Long brandId = brandService.createBrand(createDTO);
        return AjaxResult.success(brandId);
    }
    
    @GetMapping("/{id}")
    @ApiOperation(
        value = "查询品牌详情", 
        notes = "根据品牌ID查询详细信息",
        response = AjaxResult.class
    )
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功"),
        @ApiResponse(code = 404, message = "品牌不存在")
    })
    public AjaxResult<BrandDetailVO> getBrandById(
            @ApiParam(value = "品牌ID", required = true, example = "1")
            @PathVariable("id") @Min(1) Long id) {
        // 查询品牌逻辑
        BrandDetailVO detailVO = brandService.getBrandById(id);
        return AjaxResult.success(detailVO);
    }
    
    // 其他API方法...
}
```

### 模型注解示例
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "品牌创建请求")
public class BrandCreateDTO {
    
    @ApiModelProperty(value = "品牌代码", required = true, example = "BMW", position = 1)
    @NotBlank(message = "品牌代码不能为空")
    @Size(max = 50, message = "品牌代码长度不能超过50个字符")
    private String brandCode;
    
    @ApiModelProperty(value = "品牌中文名称", required = true, example = "宝马", position = 2)
    @NotBlank(message = "品牌中文名称不能为空")
    @Size(max = 100, message = "品牌中文名称长度不能超过100个字符")
    private String brandNameCn;
    
    @ApiModelProperty(value = "品牌英文名称", example = "BMW", position = 3)
    @Size(max = 100, message = "品牌英文名称长度不能超过100个字符")
    private String brandNameEn;
    
    @ApiModelProperty(value = "品牌所属国家/地区", example = "德国", position = 4)
    @Size(max = 50, message = "国家/地区长度不能超过50个字符")
    private String brandCountry;
    
    @ApiModelProperty(value = "品牌定位", example = "豪华", position = 5, 
                      allowableValues = "经济型,舒适型,豪华型,超豪华型,性能型")
    @Size(max = 20, message = "品牌定位长度不能超过20个字符")
    private String brandPositioning;
    
    @ApiModelProperty(value = "品牌Logo URL", example = "http://example.com/logo.png", position = 6)
    @Size(max = 500, message = "品牌Logo URL长度不能超过500个字符")
    private String brandLogoUrl;
}
```

### 响应示例
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "统一API响应结果")
public class AjaxResult<T> {
    
    @ApiModelProperty(value = "状态码", example = "200")
    private Integer code;
    
    @ApiModelProperty(value = "消息", example = "操作成功")
    private String message;
    
    @ApiModelProperty(value = "数据")
    private T data;
    
    @ApiModelProperty(value = "时间戳", example = "1609459200000")
    private Long timestamp;
    
    // 静态工厂方法
}
```

## 文档内容规范

### API描述
- 简明扼要地描述API的用途
- 说明API的业务场景
- 提供API的使用限制和注意事项
- 包含权限要求和访问控制信息
- 描述与其他API的关系

### 参数描述
- 详细说明每个参数的含义
- 指定参数的数据类型和格式
- 标注参数是否必须
- 提供参数的约束条件（如长度、范围等）
- 提供有意义的示例值

### 响应描述
- 描述成功响应的结构和含义
- 说明所有可能的错误响应
- 提供响应示例
- 解释特殊字段的含义
- 说明分页和排序规则（如适用）

### 代码示例
- 提供请求和响应的示例
- 包含常见用例的示例
- 展示如何处理错误情况
- 提供多种语言或工具的调用示例
- 确保示例可执行且有效

## API版本管理

### 版本策略
- 在URL路径中包含版本号（如`/api/v1/brands`）
- 主要版本号表示不兼容的API变更
- 次要版本号表示向后兼容的功能添加
- 修订版本号表示向后兼容的问题修复
- 记录API的发布日期和变更历史

### 版本迁移
- 提供版本之间的迁移指南
- 说明弃用的API和参数
- 设置合理的过渡期和弃用策略
- 提供新旧版本的映射关系
- 考虑向后兼容性设计

## Swagger配置

### 基础配置
```java
@Configuration
@EnableSwagger2
public class SwaggerConfig {
    
    @Bean
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.gwm.globalvehicle.masterdata"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo())
                .useDefaultResponseMessages(false)
                .globalResponseMessage(RequestMethod.GET, globalResponseMessages())
                .globalResponseMessage(RequestMethod.POST, globalResponseMessages())
                .globalResponseMessage(RequestMethod.PUT, globalResponseMessages())
                .globalResponseMessage(RequestMethod.DELETE, globalResponseMessages());
    }
    
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("全球车型库主数据管理 API")
                .description("全球车型库主数据管理模块的RESTful API文档")
                .version("1.0.0")
                .contact(new Contact("开发团队", "http://www.gwm.com.cn", "<EMAIL>"))
                .build();
    }
    
    private List<ResponseMessage> globalResponseMessages() {
        return Arrays.asList(
                new ResponseMessageBuilder().code(400).message("请求参数错误").build(),
                new ResponseMessageBuilder().code(401).message("未授权").build(),
                new ResponseMessageBuilder().code(403).message("禁止访问").build(),
                new ResponseMessageBuilder().code(404).message("资源不存在").build(),
                new ResponseMessageBuilder().code(500).message("服务器内部错误").build()
        );
    }
}
```

### 分组配置
```java
@Configuration
@EnableSwagger2
public class SwaggerConfig {
    
    @Bean
    public Docket brandApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("品牌管理")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.gwm.globalvehicle.masterdata.brand"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(brandApiInfo());
    }
    
    @Bean
    public Docket manufacturerApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("厂商管理")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.gwm.globalvehicle.masterdata.manufacturer"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(manufacturerApiInfo());
    }
    
    @Bean
    public Docket vehicleModelApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("车型管理")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.gwm.globalvehicle.masterdata.vehicle"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(vehicleModelApiInfo());
    }
    
    private ApiInfo brandApiInfo() {
        return new ApiInfoBuilder()
                .title("品牌管理 API")
                .description("品牌信息的增删改查接口")
                .version("1.0.0")
                .build();
    }
    
    private ApiInfo manufacturerApiInfo() {
        return new ApiInfoBuilder()
                .title("厂商管理 API")
                .description("厂商信息的增删改查接口")
                .version("1.0.0")
                .build();
    }
    
    private ApiInfo vehicleModelApiInfo() {
        return new ApiInfoBuilder()
                .title("车型管理 API")
                .description("车型信息的增删改查接口")
                .version("1.0.0")
                .build();
    }
}
```

## 接口说明文档

### 完整接口示例
```
# 品牌管理接口

## 创建品牌

创建新的品牌信息。

### 请求

- 方法: POST
- URL: /api/v1/brands
- Content-Type: application/json

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
| ----- | --- | --- | --- | --- |
| brandCode | String | 是 | 品牌代码，唯一标识 | "BMW" |
| brandNameCn | String | 是 | 品牌中文名称 | "宝马" |
| brandNameEn | String | 否 | 品牌英文名称 | "BMW" |
| brandCountry | String | 否 | 品牌所属国家/地区 | "德国" |
| brandPositioning | String | 否 | 品牌定位 | "豪华" |
| brandLogoUrl | String | 否 | 品牌Logo URL | "http://example.com/logo.png" |

### 请求示例

```json
{
  "brandCode": "BMW",
  "brandNameCn": "宝马",
  "brandNameEn": "BMW",
  "brandCountry": "德国",
  "brandPositioning": "豪华",
  "brandLogoUrl": "http://example.com/logo.png"
}
```

### 响应

#### 成功响应

- 状态码: 200
- Content-Type: application/json

```json
{
  "code": 200,
  "message": "操作成功",
  "data": 1,
  "timestamp": 1609459200000
}
```

#### 错误响应

##### 请求参数错误

- 状态码: 400
- Content-Type: application/json

```json
{
  "code": 400,
  "message": "品牌代码不能为空, 品牌中文名称不能为空",
  "data": null,
  "timestamp": 1609459200000
}
```

##### 品牌代码已存在

- 状态码: 409
- Content-Type: application/json

```json
{
  "code": 1002,
  "message": "品牌代码已存在: BMW",
  "data": null,
  "timestamp": 1609459200000
}
```

### 权限要求

- 需要管理员权限或品牌管理权限

### 备注

- 品牌代码创建后不可修改
- 品牌定位可选值: 经济型, 舒适型, 豪华型, 超豪华型, 性能型
```

## API测试说明

### 测试工具
- 使用Swagger UI进行在线测试
- 使用Postman进行复杂场景测试
- 编写自动化测试脚本

### 测试环境
- 开发环境: `http://dev-api.example.com`
- 测试环境: `http://test-api.example.com`
- 生产环境: `http://api.example.com`

### 测试账号
- 提供测试账号和权限信息
- 说明如何获取访问令牌
- 提供测试数据集

## API变更管理

### 变更通知
- 提前通知API变更计划
- 说明变更影响范围和向后兼容性
- 提供变更时间表
- 说明旧版本的支持策略

### 变更日志
- 记录所有API变更
- 按版本号组织变更记录
- 区分功能新增、变更和弃用
- 提供变更原因和迁移指南

### 变更日志示例
```
# API变更日志

## v1.1.0 (2023-12-01)

### 新增
- 新增品牌排序接口 `/api/v1/brands/sort`
- 品牌列表查询接口新增排序参数 `sortField`和`sortOrder`
- 品牌详情接口新增关联车型信息

### 变更
- 品牌创建接口新增品牌组字段 `brandGroup`
- 品牌查询接口返回结果增加创建人和更新人信息

### 弃用
- 弃用品牌批量创建接口 `/api/v1/brands/batch`，将在v1.2.0移除
- 弃用品牌查询参数 `keyword`，使用 `brandName`代替

## v1.0.0 (2023-11-01)

### 初始版本
- 品牌管理基础接口
- 厂商管理基础接口
- 车型管理基础接口 