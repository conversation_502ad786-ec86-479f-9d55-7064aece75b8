package ${packageName}.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${packageName}.entity.${entityName};
import ${packageName}.dto.${queryRequestName};
import ${packageName}.vo.${voName};
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ${tableInfo.tableComment!entityName}Mapper接口
 * 
 * <AUTHOR>
 * @date ${date}
 */
@Mapper
public interface ${mapperName} extends BaseMapper<${entityName}> {

    /**
     * 分页查询${tableInfo.tableComment!entityName}列表
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return ${tableInfo.tableComment!entityName}分页数据
     */
    IPage<${entityName}> select${entityName}Page(Page<${entityName}> page, @Param("request") ${queryRequestName} request);


}
