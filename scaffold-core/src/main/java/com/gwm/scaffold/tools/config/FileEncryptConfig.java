package com.gwm.scaffold.tools.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR> 王明莹
 * @Description 文件加密解密配置
 * @Date
 **/
@Data
@Configuration
public class FileEncryptConfig {
    @Value("${file.encrypt.appKey}")
    private String appKey;

    @Value("${file.encrypt.appSecret}")
    private String appSecret;

    @Value("${file.encrypt.host}")
    private String host;

    @Value("${file.encrypt.env}")
    private String env;

    private static FileEncryptConfig config;

    public static FileEncryptConfig getConfig() {
        return config;
    }

    @PostConstruct
    public void init() {
        config = this;
    }
}
