# Spring Boot 脚手架 AI 编码规范

## 技术栈约束（严格遵循）
- Spring Boot 2.7.18 版本
- MyBatis Plus 3.5.3.1 版本
- MySQL 8.0.33 版本
- JDK 1.8 版本
- Maven 构建工具
- 禁止引入与现有技术栈冲突的依赖

## 架构规范（强制执行）
- 严格遵循分层架构：Controller → Service → Mapper → Entity
- 禁止跨层调用，Controller不能直接调用Mapper
- 禁止循环依赖
- 所有业务逻辑必须在Service层实现
- 数据访问层使用Mapper接口，不是Repository

## 包结构规范（基于实际代码）
- 基础包名：com.gwm.scaffold.{模块名}
- Controller类放在 controller 包下
- Service接口放在 service 包下，实现类放在 service.impl 包下
- Mapper接口放在 mapper 包下（不是repository）
- 实体类放在 entity 包下
- DTO类放在 dto 包下
- VO类放在 vo 包下
- 配置类放在 config 包下

## 命名规范（基于实际代码）
- 类名使用 PascalCase（首字母大写的驼峰命名）
- 方法名和字段名使用 camelCase（首字母小写的驼峰命名）
- 常量使用 UPPER_SNAKE_CASE（全大写下划线分隔）
- 数据库字段使用 snake_case（小写下划线分隔）
- DTO命名：{Entity}CreateRequest、{Entity}UpdateRequest、{Entity}QueryRequest
- VO命名：{Entity}VO（不使用ListVO）
- Mapper命名：{Entity}Mapper（不是Repository）

## 代码规范（基于实际代码）
- 所有类和方法必须添加中文注释
- 使用 @Slf4j 注解进行日志记录
- Service实现类必须添加 @Transactional(rollbackFor = Exception.class) 注解
- 查询方法不需要添加 @Transactional(readOnly = true) 注解
- Controller方法必须使用 @Valid 进行参数校验
- 实体类必须继承 BaseSuperEntity（不是BaseEntity）
- Mapper接口必须继承 BaseMapper<T> 并添加 @Mapper 注解
- 使用统一的 Result<T> 作为API响应格式
- 异常处理使用 ServiceException（不是BusinessException）

## 注解使用规范
- Controller类：@RestController、@RequestMapping、@Slf4j、@Validated
- Service实现类：@Service、@Transactional、@Slf4j
- Mapper接口：@Mapper
- 实体类：@Data、@TableName、@EqualsAndHashCode(callSuper = true)
- 配置类：@Configuration
- 参数校验：@Valid、@NotNull、@NotBlank、@Size等

## API设计规范
- 使用RESTful风格的URL设计
- GET请求用于查询，POST用于创建，PUT用于更新，DELETE用于删除
- 统一使用 /api/ 作为API前缀（不强制v1）
- 返回结果统一使用 Result<T> 包装
- 分页查询使用 IPage<T> 包装

## 数据库操作规范（基于baomidou封装）
- 优先使用 MyBatis Plus 的 BaseMapper<T> 提供的原始接口
- Service层继承 ServiceImpl<Mapper, Entity> 使用通用CRUD方法
- 复杂查询在Mapper接口中定义方法，编写对应的 XML 映射文件
- 使用 LambdaQueryWrapper 构建动态查询条件
- 分页查询使用 Page<T> 对象
- 实体类使用 @TableId(type = IdType.AUTO) 自增主键
- 逻辑删除使用 @TableLogic 注解，deleted 字段值为 0（未删除）或 1（已删除）
- 乐观锁使用 @Version 注解

## 异常处理规范
- 业务异常使用 ServiceException（不是BusinessException）
- 参数校验异常使用 @Valid 注解自动处理
- 系统异常统一在 GlobalExceptionHandler 中处理
- 异常信息必须使用中文描述
- ServiceException构造：new ServiceException("错误信息") 或 new ServiceException("错误信息", 错误码)

## 工具类复用（必须使用脚手架提供的工具类）
- 时间处理使用 DateUtil（com.gwm.scaffold.tools.util.DateUtil）
- 字符串处理使用 StringUtil（com.gwm.scaffold.tools.util.StringUtil）
- 对象处理使用 ObjectUtil（com.gwm.scaffold.tools.util.ObjectUtil）
- 分页处理使用 PageUtil（com.gwm.scaffold.tools.util.PageUtil）
- 参数校验使用 ValidationUtil（com.gwm.scaffold.web.util.ValidationUtil）
- 加密处理使用 EncryptUtil（com.gwm.scaffold.tools.util.EncryptUtil）
- HTTP请求使用 OkHttpUtil（com.gwm.scaffold.tools.util.OkHttpUtil）
- 枚举处理使用 EnumUtil（com.gwm.scaffold.tools.util.EnumUtil）

## 代码质量要求
- 单个方法代码行数不超过 50 行
- 单个类代码行数不超过 500 行
- 方法参数不超过 5 个
- 避免使用魔法数字，定义为常量
- 避免深层嵌套，最多 3 层
- 必须处理所有可能的异常情况

## 测试规范
- 为所有Service方法编写单元测试
- 测试类命名：{ClassName}Test
- 使用 @SpringBootTest 注解
- 使用 @Transactional 注解确保测试数据回滚
- 测试方法命名：test{MethodName}_{Scenario}

## 日志规范
- 使用 @Slf4j 注解
- 关键业务操作必须记录日志
- 异常必须记录错误日志
- 日志级别：DEBUG（调试信息）、INFO（一般信息）、WARN（警告）、ERROR（错误）
- 日志内容使用中文描述

## 性能优化
- 避免 N+1 查询问题
- 大数据量查询使用分页
- 合理使用缓存
- 避免在循环中进行数据库操作
- 使用批量操作处理大量数据

## 安全规范
- 所有用户输入必须进行校验
- 敏感信息不能记录在日志中
- 使用参数化查询防止SQL注入
- 接口访问需要权限控制
- 密码等敏感信息必须加密存储
