package com.gwm.scaffold.example.controller;

import com.gwm.scaffold.auth.annotation.RequirePermission;
import com.gwm.scaffold.auth.service.AuthenticateService;
import com.gwm.scaffold.auth.util.LoginUserUtil;
import com.gwm.scaffold.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 鉴权测试控制器
 * 
 * 用于测试鉴权功能是否正常工作
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Tag(name = "鉴权测试", description = "用于测试鉴权功能的接口")
@RestController
@RequestMapping("/test/auth")
public class AuthTestController {

    @Autowired
    private AuthenticateService authenticateService;

    /**
     * 无权限要求的接口
     */
    @Operation(summary = "无权限要求", description = "测试无权限要求的接口")
    @GetMapping("/public")
    public Result<Map<String, Object>> publicEndpoint() {
        log.info("访问无权限要求的接口");
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "这是一个无权限要求的接口");
        result.put("timestamp", System.currentTimeMillis());
        
        // 尝试获取当前用户信息
//        try {
//            String userCode = LoginUserUtil.getUserCode();
//            String userName = LoginUserUtil.getUserName();
//            result.put("currentUser", userCode);
//            result.put("currentUserName", userName);
//            result.put("hasUserInfo", userCode != null);
//        } catch (Exception e) {
//            result.put("hasUserInfo", false);
//            result.put("error", e.getMessage());
//        }
        
        return Result.success(result);
    }

    /**
     * 需要权限的接口
     */
    @Operation(summary = "需要权限", description = "测试需要权限的接口")
    @GetMapping("/protected")
    @RequirePermission("test:read")
    public Result<Map<String, Object>> protectedEndpoint() {
        log.info("访问需要权限的接口");
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "这是一个需要test:read权限的接口");
        result.put("timestamp", System.currentTimeMillis());
        result.put("currentUser", LoginUserUtil.getUserCode());
        result.put("currentUserName", LoginUserUtil.getUserName());
        
        return Result.success(result);
    }

    /**
     * 需要订单读取权限的接口
     */
    @Operation(summary = "需要订单权限", description = "测试需要订单读取权限的接口")
    @GetMapping("/order-read")
    @RequirePermission("order:read")
    public Result<Map<String, Object>> orderReadEndpoint() {
        log.info("访问需要订单读取权限的接口");
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "这是一个需要order:read权限的接口");
        result.put("timestamp", System.currentTimeMillis());
        result.put("currentUser", LoginUserUtil.getUserCode());
        result.put("currentUserName", LoginUserUtil.getUserName());
        
        return Result.success(result);
    }

    /**
     * 获取当前用户信息
     */
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/user-info")
    public Result<Map<String, Object>> getUserInfo() {
        log.info("获取当前用户信息");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("userCode", LoginUserUtil.getUserCode());
            result.put("userName", LoginUserUtil.getUserName());
            result.put("token", LoginUserUtil.getToken());
            result.put("ip", LoginUserUtil.getIp());
            result.put("departmentName", LoginUserUtil.getDepartmentName());
            result.put("unitId", LoginUserUtil.getUnitId());
            result.put("hasUserInfo", true);
        } catch (Exception e) {
            result.put("hasUserInfo", false);
            result.put("error", e.getMessage());
        }
        
        return Result.success(result);
    }

    /**
     * 测试POST请求的鉴权
     */
    @Operation(summary = "POST请求鉴权测试", description = "测试POST请求的鉴权功能")
    @PostMapping("/post-test")
    @RequirePermission("test:write")
    public Result<Map<String, Object>> postTest(@RequestBody Map<String, Object> params) {
        log.info("POST请求鉴权测试，参数：{}", params);
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "POST请求鉴权测试成功");
        result.put("receivedParams", params);
        result.put("currentUser", LoginUserUtil.getUserCode());
        result.put("timestamp", System.currentTimeMillis());
        
        return Result.success(result);
    }

    /**
     * 检查当前使用的认证服务实现
     */
    @Operation(summary = "检查认证服务", description = "检查当前使用的是哪个认证服务实现")
    @GetMapping("/service-info")
    public Result<Map<String, Object>> getServiceInfo() {
        log.info("检查当前认证服务实现");

        Map<String, Object> result = new HashMap<>();
        result.put("serviceClass", authenticateService.getClass().getSimpleName());
        result.put("serviceFullName", authenticateService.getClass().getName());
        result.put("timestamp", System.currentTimeMillis());

        return Result.success(result);
    }
}
