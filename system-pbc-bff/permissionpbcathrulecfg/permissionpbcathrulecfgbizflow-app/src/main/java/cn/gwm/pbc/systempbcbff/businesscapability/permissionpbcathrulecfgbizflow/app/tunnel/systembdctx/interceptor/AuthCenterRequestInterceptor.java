package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor;

import com.alibaba.bizworks.core.runtime.context.UserContext;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.json.JacksonJsonParser;
import org.springframework.boot.json.JsonParser;

import java.util.Collections;
import java.util.Map;

/**
 * FeignClient认证拦截器
 * <AUTHOR> 100013880
 */
@Slf4j
public class AuthCenterRequestInterceptor implements RequestInterceptor {

    @Value("${gwm.sso.platform-code}")
    private String platformCode;

    @Value("${center.url}")
    private String urlTarget;

    private final JsonParser jsonParser = new JacksonJsonParser();

    @Override
    public void apply(RequestTemplate template) {

        String userCode = UserContext.getUserCode();
        template.target(urlTarget);
        String userExt = UserContext.getUserExt();
        if (userExt != null && !userExt.isEmpty()) {
            Map<String, Object> userExtMap = jsonParser.parseMap(userExt);
            String token = (String) userExtMap.get("token");
            template.header("pbc_token", token);
            template.header("Authorization", token);
        }
        template.header("platform_code", platformCode);
        template.header("account", Collections.singletonList(userCode));

    }

}