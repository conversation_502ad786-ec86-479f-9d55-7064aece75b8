package com.gwm.scaffold.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwm.scaffold.example.dto.UserQueryRequest;
import com.gwm.scaffold.example.entity.User;
import com.gwm.scaffold.example.vo.UserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户Mapper接口
 * 
 * 展示如何使用MyBatis Plus的BaseMapper
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 分页查询用户列表
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 用户列表
     */
    IPage<UserVO> selectUserPage(Page<UserVO> page, @Param("req") UserQueryRequest request);

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    UserVO selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    UserVO selectByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    UserVO selectByMobile(@Param("mobile") String mobile);

    /**
     * 根据部门ID查询用户列表
     *
     * @param departmentId 部门ID
     * @return 用户列表
     */
    List<UserVO> selectByDepartmentId(@Param("departmentId") Long departmentId);

    /**
     * 统计用户数量
     *
     * @param request 查询条件
     * @return 用户数量
     */
    Long countUsers(@Param("req") UserQueryRequest request);

    /**
     * 批量更新用户状态
     *
     * @param userIds 用户ID列表
     * @param status  状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("userIds") List<Long> userIds, @Param("status") Integer status);
}
