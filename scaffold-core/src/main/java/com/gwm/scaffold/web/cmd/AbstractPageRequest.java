package com.gwm.scaffold.web.cmd;

import com.alibaba.cola.dto.Command;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 查询分页传参
 *
 * <AUTHOR> 王明莹
 * @date 2025.02.18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@Accessors(chain = true)
public abstract class AbstractPageRequest extends Command {

    private static final long serialVersionUID = 1L;
    public static final String ASC = "ASC";
    public static final String DESC = "DESC";

    /**
     * 默认每页数量
     */
    private static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序顺序
     */
    private String orderDirection = DESC;

    /**
     * 分组字段
     */
    private String groupBy;

    /**
     * 是否需要总数
     */
    private boolean needTotalCount = true;

    /**
     * 当前页
     */
    private int current;

    /**
     * 每页显示条数
     */
    private int size;

    /**
     * 偏移量
     */
    private long offset;

    public int getCurrent() {
        return Math.max(current, 1);
    }

    public int getSize() {
        if (size < 1) {
            size = DEFAULT_PAGE_SIZE;
        }
        return size;
    }

    public AbstractPageRequest setSize(int size) {
        if (size < 1) {
            size = DEFAULT_PAGE_SIZE;
        }
        this.size = size;
        return this;
    }

    public AbstractPageRequest setOffset(long offset) {
        if (offset < 1) {
            this.offset = (long) (getCurrent() - 1) * getSize();
        }
        return this;
    }

    public long getOffset() {
        if (offset < 1) {
            offset = (long) (getCurrent() - 1) * getSize();
        }
        return offset;
    }

    public AbstractPageRequest setOrderDirection(String orderDirection) {
        if (ASC.equalsIgnoreCase(orderDirection) || DESC.equalsIgnoreCase(orderDirection)) {
            this.orderDirection = orderDirection;
        }
        return this;
    }
}
