package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.team.RemoteTeamListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.team.RemoteTeamTreeDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback.RemoteTeamFallbackFactory;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthRequestInterceptor;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteTeamService", url = "EMPTY", configuration = {AuthRequestInterceptor.class}, fallback = RemoteTeamFallbackFactory.class)
public interface RemoteTeamService {

    /**
     * 查询应用下用户组树形列表
     *
     * @param teamTreeDTO
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/team/searchTeamTree")
    CommonRlt<JSONObject> teamTree(@SpringQueryMap RemoteTeamTreeDTO teamTreeDTO);

    /**
     * 根据工号查询应用下用户组列表
     *
     * @param teamListDTO
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/team/getUserTeamList")
    CommonRlt<JSONObject> teamList(@SpringQueryMap RemoteTeamListDTO teamListDTO);


}
