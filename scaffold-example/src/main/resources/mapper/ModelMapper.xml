<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwm.scaffold.example.mapper.ModelMapper">

    <!-- 结果映射 -->
    <resultMap id="ModelResultMap" type="com.gwm.scaffold.example.entity.Model">
        <id column="ID" property="id"/>
        <result column="NAME" property="name"/>
        <result column="SERIES_ID" property="seriesId"/>
        <result column="YEAR_MODEL" property="yearModel"/>
        <result column="DISPLACEMENT" property="displacement"/>
        <result column="MAX_POWER" property="maxPower"/>
        <result column="MAX_TORQUE" property="maxTorque"/>
        <result column="TRANSMISSION" property="transmission"/>
        <result column="GEAR_COUNT" property="gearCount"/>
        <result column="DRIVE_TYPE" property="driveType"/>
        <result column="FUEL_TYPE" property="fuelType"/>
        <result column="BODY_STRUCTURE" property="bodyStructure"/>
        <result column="SEAT_COUNT" property="seatCount"/>
        <result column="LENGTH" property="length"/>
        <result column="WIDTH" property="width"/>
        <result column="HEIGHT" property="height"/>
        <result column="WHEELBASE" property="wheelbase"/>
        <result column="CURB_WEIGHT" property="curbWeight"/>
        <result column="TANK_CAPACITY" property="tankCapacity"/>
        <result column="FUEL_CONSUMPTION" property="fuelConsumption"/>
        <result column="GUIDE_PRICE" property="guidePrice"/>
        <result column="LAUNCH_TIME" property="launchTime"/>
        <result column="DISCONTINUE_TIME" property="discontinueTime"/>
        <result column="IMAGE_URLS" property="imageUrls"/>
        <result column="CONFIG_DETAILS" property="configDetails"/>
        <result column="STATUS" property="status"/>
        <result column="SORT_WEIGHT" property="sortWeight"/>
        <result column="DELETED" property="deleted"/>
        <result column="DELETED_TIME" property="deletedTime"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_USER_CODE" property="createUserCode"/>
        <result column="CREATE_USER_NAME" property="createUserName"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="UPDATE_USER_CODE" property="updateUserCode"/>
        <result column="UPDATE_USER_NAME" property="updateUserName"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        ID, NAME, SERIES_ID, YEAR_MODEL, DISPLACEMENT, MAX_POWER, MAX_TORQUE, TRANSMISSION, GEAR_COUNT, DRIVE_TYPE, FUEL_TYPE, BODY_STRUCTURE, SEAT_COUNT, LENGTH, WIDTH, HEIGHT, WHEELBASE, CURB_WEIGHT, TANK_CAPACITY, FUEL_CONSUMPTION, GUIDE_PRICE, LAUNCH_TIME, DISCONTINUE_TIME, IMAGE_URLS, CONFIG_DETAILS, STATUS, SORT_WEIGHT, DELETED, DELETED_TIME, CREATE_TIME, CREATE_USER_CODE, CREATE_USER_NAME, UPDATE_TIME, UPDATE_USER_CODE, UPDATE_USER_NAME
    </sql>

    <!-- 分页查询列表 -->
    <select id="selectModelPage" resultMap="ModelResultMap">
        SELECT
        <include refid="BaseColumns"/>
        FROM T_MODEL
        <where>
            deleted = 0
            <if test="request.id != null">
                AND ID = #{request.id}
            </if>
            <if test="request.name != null and request.name != ''">
                AND NAME LIKE CONCAT('%', #{request.name}, '%')
            </if>
            <if test="request.seriesId != null">
                AND SERIES_ID = #{request.seriesId}
            </if>
            <if test="request.yearModel != null">
                AND YEAR_MODEL = #{request.yearModel}
            </if>
            <if test="request.displacement != null">
                AND DISPLACEMENT = #{request.displacement}
            </if>
            <if test="request.maxPower != null">
                AND MAX_POWER = #{request.maxPower}
            </if>
            <if test="request.maxTorque != null">
                AND MAX_TORQUE = #{request.maxTorque}
            </if>
            <if test="request.transmission != null and request.transmission != ''">
                AND TRANSMISSION LIKE CONCAT('%', #{request.transmission}, '%')
            </if>
            <if test="request.gearCount != null">
                AND GEAR_COUNT = #{request.gearCount}
            </if>
            <if test="request.driveType != null and request.driveType != ''">
                AND DRIVE_TYPE LIKE CONCAT('%', #{request.driveType}, '%')
            </if>
            <if test="request.fuelType != null and request.fuelType != ''">
                AND FUEL_TYPE LIKE CONCAT('%', #{request.fuelType}, '%')
            </if>
            <if test="request.bodyStructure != null and request.bodyStructure != ''">
                AND BODY_STRUCTURE LIKE CONCAT('%', #{request.bodyStructure}, '%')
            </if>
            <if test="request.seatCount != null">
                AND SEAT_COUNT = #{request.seatCount}
            </if>
            <if test="request.length != null">
                AND LENGTH = #{request.length}
            </if>
            <if test="request.width != null">
                AND WIDTH = #{request.width}
            </if>
            <if test="request.height != null">
                AND HEIGHT = #{request.height}
            </if>
            <if test="request.wheelbase != null">
                AND WHEELBASE = #{request.wheelbase}
            </if>
            <if test="request.curbWeight != null">
                AND CURB_WEIGHT = #{request.curbWeight}
            </if>
            <if test="request.tankCapacity != null">
                AND TANK_CAPACITY = #{request.tankCapacity}
            </if>
            <if test="request.fuelConsumption != null">
                AND FUEL_CONSUMPTION = #{request.fuelConsumption}
            </if>
            <if test="request.guidePrice != null">
                AND GUIDE_PRICE = #{request.guidePrice}
            </if>
            <if test="request.launchTimeStart != null">
                AND LAUNCH_TIME >= #{request.launchTimeStart}
            </if>
            <if test="request.launchTimeEnd != null">
                AND LAUNCH_TIME &lt;= #{request.launchTimeEnd}
            </if>
            <if test="request.discontinueTimeStart != null">
                AND DISCONTINUE_TIME >= #{request.discontinueTimeStart}
            </if>
            <if test="request.discontinueTimeEnd != null">
                AND DISCONTINUE_TIME &lt;= #{request.discontinueTimeEnd}
            </if>
            <if test="request.imageUrls != null and request.imageUrls != ''">
                AND IMAGE_URLS LIKE CONCAT('%', #{request.imageUrls}, '%')
            </if>
            <if test="request.configDetails != null and request.configDetails != ''">
                AND CONFIG_DETAILS LIKE CONCAT('%', #{request.configDetails}, '%')
            </if>
            <if test="request.status != null and request.status != ''">
                AND STATUS LIKE CONCAT('%', #{request.status}, '%')
            </if>
            <if test="request.sortWeight != null">
                AND SORT_WEIGHT = #{request.sortWeight}
            </if>
            <if test="request.deletedTimeStart != null">
                AND DELETED_TIME >= #{request.deletedTimeStart}
            </if>
            <if test="request.deletedTimeEnd != null">
                AND DELETED_TIME &lt;= #{request.deletedTimeEnd}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>



</mapper>
