package com.gwm.scaffold.example.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwm.scaffold.auth.annotation.RequirePermission;
import com.gwm.scaffold.core.domain.Result;
import com.gwm.scaffold.example.dto.BrandCreateRequest;
import com.gwm.scaffold.example.dto.BrandUpdateRequest;
import com.gwm.scaffold.example.dto.BrandQueryRequest;
import com.gwm.scaffold.example.service.BrandService;
import com.gwm.scaffold.example.vo.BrandVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 品牌管理控制器
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Tag(name = "品牌管理", description = "品牌相关接口")
@RestController
@RequestMapping("/api/brands")
public class BrandController {

    @Autowired
    private BrandService brandService;

    /**
     * 分页查询品牌列表
     */
    @Operation(summary = "分页查询品牌列表", description = "支持多条件查询")
    @GetMapping
    @RequirePermission("brand:list")
    public Result<IPage<BrandVO>> list(BrandQueryRequest request) {
        log.info("分页查询品牌列表，参数：{}", request);

        IPage<BrandVO> result = brandService.getBrandPage(request);
        return Result.success(result);
    }

    /**
     * 根据ID获取品牌详情
     */
    @Operation(summary = "获取品牌详情", description = "根据ID获取品牌详细信息")
    @GetMapping("/{id}")
    @RequirePermission("brand:read")
    public Result<BrandVO> getById(@Parameter(description = "品牌ID") @PathVariable Long id) {
        log.info("获取品牌详情，ID：{}", id);

        BrandVO result = brandService.getBrandById(id);
        return Result.success(result);
    }

    /**
     * 创建品牌
     */
    @Operation(summary = "创建品牌", description = "创建新的品牌")
    @PostMapping
    @RequirePermission("brand:create")
    public Result<BrandVO> create(@Valid @RequestBody BrandCreateRequest request) {
        log.info("创建品牌，参数：{}", request);

        BrandVO result = brandService.createBrand(request);
        return Result.success(result);
    }

    /**
     * 更新品牌
     */
    @Operation(summary = "更新品牌", description = "更新品牌信息")
    @PutMapping("/{id}")
    @RequirePermission("brand:update")
    public Result<BrandVO> update(
            @Parameter(description = "品牌ID") @PathVariable Long id,
            @Valid @RequestBody BrandUpdateRequest request) {
        log.info("更新品牌，ID：{}，参数：{}", id, request);

        request.setId(id);
        BrandVO result = brandService.updateBrand(request);
        return Result.success(result);
    }

    /**
     * 删除品牌
     */
    @Operation(summary = "删除品牌", description = "根据ID删除品牌")
    @DeleteMapping("/{id}")
    @RequirePermission("brand:delete")
    public Result<String> delete(@Parameter(description = "品牌ID") @PathVariable Long id) {
        log.info("删除品牌，ID：{}", id);

        boolean success = brandService.deleteBrand(id);
        return success ? Result.success("品牌删除成功") : Result.error("品牌删除失败");
    }

    /**
     * 批量删除品牌
     */
    @Operation(summary = "批量删除品牌", description = "根据ID列表批量删除品牌")
    @DeleteMapping("/batch")
    @RequirePermission("brand:delete")
    public Result<String> batchDelete(@Parameter(description = "品牌ID列表") @RequestBody List<Long> ids) {
        log.info("批量删除品牌，ID列表：{}", ids);

        int count = brandService.batchDeleteBrands(ids);
        return Result.success("成功删除 " + count + " 个品牌");
    }

    /**
     * 获取启用状态的品牌列表
     */
    @Operation(summary = "获取启用品牌列表", description = "获取所有启用状态的品牌")
    @GetMapping("/active")
    @RequirePermission("brand:list")
    public Result<List<BrandVO>> getActiveBrands() {
        log.info("获取启用品牌列表");

        List<BrandVO> result = brandService.getActiveBrandList();
        return Result.success(result);
    }

    /**
     * 更新品牌状态
     */
    @Operation(summary = "更新品牌状态", description = "更新品牌的启用/禁用状态")
    @PutMapping("/{id}/status")
    @RequirePermission("brand:update")
    public Result<String> updateStatus(
            @Parameter(description = "品牌ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam String status) {
        log.info("更新品牌状态，ID：{}，状态：{}", id, status);

        boolean success = brandService.updateBrandStatus(id, status);
        return success ? Result.success("状态更新成功") : Result.error("状态更新失败");
    }

    /**
     * 按国别统计品牌数量
     */
    @Operation(summary = "按国别统计品牌", description = "统计各国别的品牌数量")
    @GetMapping("/statistics/country")
    @RequirePermission("brand:list")
    public Result<List<BrandVO>> getBrandCountByCountry() {
        log.info("按国别统计品牌数量");

        List<BrandVO> result = brandService.getBrandCountByCountry();
        return Result.success(result);
    }

}
