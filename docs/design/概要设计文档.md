# 品牌、车系、车型主数据管理系统概要设计文档

## 1. 文档概述

### 1.1 文档目的
本文档旨在为品牌、车系、车型管理系统提供概要设计方案，包括系统架构、核心业务流程时序图、接口设计和数据库设计等内容，为详细设计和开发实现提供指导。

### 1.2 文档范围
- 系统整体架构设计
- 核心业务流程时序图
- RESTful API接口设计
- 数据库表结构设计
- 关键技术选型

### 1.3 参考文档
- 品牌车系车型管理系统PRD v1.0
- 系统线框图原型设计

## 2. 系统架构设计

### 2.1 整体架构
系统采用前后端分离的架构模式，基于微服务架构设计。

### 2.2 技术选型

| 层次 | 技术栈 | 说明 |
|------|--------|------|
| 前端 | Vue.js 3 + Element Plus | 管理后台界面 |
| 网关 | Spring Cloud Gateway | API网关 |
| 后端 | Spring Boot 3 + Spring Cloud | 微服务框架 |
| 数据库 | MySQL 8.0 | 主数据存储 |
| 缓存 | Redis 7.0 | 缓存和会话存储 |
| 搜索 | Elasticsearch 8.0 | 全文搜索 |
| 文件存储 | MinIO | 对象存储 |
| 消息队列 | RabbitMQ | 异步消息处理 |
| 监控 | Prometheus + Grafana | 系统监控 |
## 3. 核心业务流程时序图

### 3.1 品牌管理时序图

#### 3.1.1 品牌新增流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as API网关
    participant B as 品牌服务
    participant D as 数据库
    participant R as Redis
    participant A as 审计服务

    U->>F: 填写品牌信息
    F->>F: 前端校验
    F->>G: POST /api/brands
    G->>G: 鉴权验证
    G->>B: 转发请求
    B->>B: 业务校验
    B->>D: 检查品牌名称唯一性
    D-->>B: 返回检查结果
    B->>D: 插入品牌数据
    D-->>B: 返回插入结果
    B->>R: 清除相关缓存
    B->>A: 记录操作日志
    B-->>G: 返回成功结果
    G-->>F: 返回响应
    F-->>U: 显示操作结果
```

#### 3.1.2 品牌删除流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as API网关
    participant B as 品牌服务
    participant S as 车系服务
    participant D as 数据库
    participant W as 审批服务
    participant A as 审计服务

    U->>F: 点击删除品牌
    F->>G: DELETE /api/brands/{id}
    G->>B: 转发删除请求
    B->>S: 检查关联车系
    S->>D: 查询车系数据
    D-->>S: 返回车系列表
    S-->>B: 返回关联检查结果
    
    alt 有关联车系
        B-->>G: 返回错误信息
        G-->>F: 返回错误响应
        F-->>U: 提示不能删除
    else 无关联车系且为管理员
        B->>D: 软删除品牌
        D-->>B: 返回删除结果
        B->>A: 记录删除日志
        B-->>G: 返回成功结果
        G-->>F: 返回成功响应
        F-->>U: 显示删除成功
    else 无关联车系但非管理员
        B->>W: 创建删除审批申请
        W->>D: 保存审批申请
        W-->>B: 返回申请结果
        B-->>G: 返回审批中状态
        G-->>F: 返回审批响应
        F-->>U: 提示已提交审批
    end
```

### 3.2 车型对比功能时序图
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as API网关
    participant M as 车型服务
    participant R as Redis
    participant D as 数据库

    U->>F: 选择对比车型
    F->>F: 本地存储选中车型ID
    U->>F: 点击对比按钮
    F->>G: POST /api/models/compare
    Note over F,G: 请求体包含车型ID列表
    G->>M: 转发对比请求
    M->>R: 检查缓存
    
    alt 缓存命中
        R-->>M: 返回缓存数据
    else 缓存未命中
        M->>D: 批量查询车型详情
        D-->>M: 返回车型数据
        M->>R: 缓存对比结果
    end
    
    M->>M: 处理对比数据
    M-->>G: 返回对比结果
    G-->>F: 返回对比数据
    F->>F: 渲染对比表格
    F-->>U: 显示对比结果
```

### 3.3 价格变动审批流程时序图
```mermaid
sequenceDiagram
    participant U as 申请人
    participant F as 前端
    participant G as API网关
    participant A as 审批服务
    participant M as 车型服务
    participant N as 通知服务
    participant D as 数据库
    participant MQ as 消息队列

    U->>F: 修改车型价格
    F->>G: PUT /api/models/{id}
    G->>M: 转发价格修改请求
    M->>M: 计算价格变动幅度
    
    alt 价格变动超过阈值
        M->>A: 创建审批申请
        A->>D: 保存审批申请
        A->>MQ: 发送审批通知消息
        MQ->>N: 处理通知消息
        N->>N: 发送邮件/站内信
        A-->>M: 返回审批申请ID
        M-->>G: 返回审批中状态
        G-->>F: 返回审批响应
        F-->>U: 提示已提交审批
    else 价格变动在允许范围内
        M->>D: 直接更新价格
        D-->>M: 返回更新结果
        M-->>G: 返回成功结果
        G-->>F: 返回成功响应
        F-->>U: 显示修改成功
    end
```

### 3.4 数据导入流程时序图
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as API网关
    participant I as 导入服务
    participant V as 校验服务
    participant B as 品牌服务
    participant FS as 文件服务
    participant D as 数据库
    participant MQ as 消息队列

    U->>F: 上传Excel文件
    F->>G: POST /api/import/brands
    G->>I: 转发导入请求
    I->>FS: 保存上传文件
    FS-->>I: 返回文件路径
    I->>I: 解析Excel文件
    I->>V: 批量数据校验
    V->>V: 格式校验
    V->>B: 业务逻辑校验
    B->>D: 检查数据重复性
    D-->>B: 返回检查结果
    B-->>V: 返回校验结果
    V-->>I: 返回校验报告
    
    I->>MQ: 发送异步导入任务
    I-->>G: 返回任务ID
    G-->>F: 返回任务状态
    F-->>U: 显示导入进度
    
    MQ->>I: 处理导入任务
    I->>D: 批量插入有效数据
    D-->>I: 返回插入结果
    I->>D: 保存导入结果报告
    I->>MQ: 发送完成通知
    MQ->>F: WebSocket推送完成状态
    F-->>U: 显示导入完成结果
```

## 4. 接口设计

### 4.1 接口设计原则
- 遵循RESTful API设计规范
- 统一的响应格式
- 完善的错误处理机制
- 支持分页、排序、过滤
- 接口版本控制

### 4.2 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

### 4.3 品牌管理接口

#### 4.3.1 品牌列表查询
```
GET /api/v1/brands

Query Parameters:
- name: string (可选) - 品牌名称，支持模糊查询
- country: string (可选) - 国别
- status: string (可选) - 状态 (ACTIVE/INACTIVE)
- page: integer (默认1) - 页码
- size: integer (默认10) - 每页数量
- sort: string (可选) - 排序字段,格式: field,direction

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [
      {
        "id": 1,
        "name": "奔驰",
        "englishName": "Mercedes-Benz",
        "country": "德国",
        "foundedYear": 1926,
        "website": "https://www.mercedes-benz.com",
        "logoUrl": "https://example.com/logo/benz.png",
        "description": "豪华汽车品牌",
        "status": "ACTIVE",
        "seriesCount": 15,
        "sortWeight": 100,
        "createdBy": "admin",
        "createdTime": "2024-01-01T00:00:00Z",
        "updatedBy": "admin",
        "updatedTime": "2024-01-15T10:30:00Z"
      }
    ],
    "totalElements": 100,
    "totalPages": 10,
    "currentPage": 1,
    "pageSize": 10,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

#### 4.3.2 品牌详情查询
```
GET /api/v1/brands/{id}

Path Parameters:
- id: integer (必填) - 品牌ID

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "name": "奔驰",
    "englishName": "Mercedes-Benz",
    "country": "德国",
    "foundedYear": 1926,
    "website": "https://www.mercedes-benz.com",
    "logoUrl": "https://example.com/logo/benz.png",
    "description": "豪华汽车品牌",
    "status": "ACTIVE",
    "sortWeight": 100,
    "createdBy": "admin",
    "createdTime": "2024-01-01T00:00:00Z",
    "updatedBy": "admin",
    "updatedTime": "2024-01-15T10:30:00Z",
    "seriesList": [
      {
        "id": 1,
        "name": "C级",
        "type": "SEDAN",
        "level": "B",
        "status": "ON_SALE"
      }
    ]
  }
}
```

#### 4.3.3 品牌新增
```
POST /api/v1/brands

Request Body:
{
  "name": "奔驰",
  "englishName": "Mercedes-Benz",
  "country": "德国",
  "foundedYear": 1926,
  "website": "https://www.mercedes-benz.com",
  "logoUrl": "https://example.com/logo/benz.png",
  "description": "豪华汽车品牌",
  "status": "ACTIVE",
  "sortWeight": 100
}

Response:
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "name": "奔驰",
    // ... 其他字段
  }
}
```

#### 4.3.4 品牌更新
```
PUT /api/v1/brands/{id}

Path Parameters:
- id: integer (必填) - 品牌ID

Request Body:
{
  "name": "奔驰",
  "englishName": "Mercedes-Benz",
  "country": "德国",
  "foundedYear": 1926,
  "website": "https://www.mercedes-benz.com",
  "logoUrl": "https://example.com/logo/benz.png",
  "description": "豪华汽车品牌",
  "status": "ACTIVE",
  "sortWeight": 100
}

Response:
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    // ... 更新后的数据
  }
}
```

#### 4.3.5 品牌删除
```
DELETE /api/v1/brands/{id}

Path Parameters:
- id: integer (必填) - 品牌ID

Response:
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 4.4 车系管理接口

#### 4.4.1 车系列表查询
```
GET /api/v1/series

Query Parameters:
- name: string (可选) - 车系名称
- brandId: integer (可选) - 品牌ID
- type: string (可选) - 车系类型
- level: string (可选) - 级别
- status: string (可选) - 状态
- page: integer (默认1) - 页码
- size: integer (默认10) - 每页数量
- sort: string (可选) - 排序字段

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [
      {
        "id": 1,
        "name": "C级",
        "englishName": "C-Class",
        "brandId": 1,
        "brandName": "奔驰",
        "type": "SEDAN",
        "level": "B",
        "launchTime": "2020-01-01",
        "discontinueTime": null,
        "minPrice": 30.0,
        "maxPrice": 50.0,
        "imageUrl": "https://example.com/series/c-class.jpg",
        "description": "中型豪华轿车",
        "status": "ON_SALE",
        "modelCount": 8,
        "sortWeight": 100,
        "createdTime": "2024-01-01T00:00:00Z",
        "updatedTime": "2024-01-15T10:30:00Z"
      }
    ],
    "totalElements": 50,
    "totalPages": 5,
    "currentPage": 1,
    "pageSize": 10
  }
}
```

### 4.5 车型管理接口

#### 4.5.1 车型列表查询
```
GET /api/v1/models

Query Parameters:
- name: string (可选) - 车型名称
- brandId: integer (可选) - 品牌ID
- seriesId: integer (可选) - 车系ID
- yearModel: integer (可选) - 年款
- minPrice: decimal (可选) - 最低价格
- maxPrice: decimal (可选) - 最高价格
- fuelType: string (可选) - 燃料类型
- driveType: string (可选) - 驱动方式
- status: string (可选) - 状态
- page: integer (默认1) - 页码
- size: integer (默认10) - 每页数量

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [
      {
        "id": 1,
        "name": "C 200 L 运动版",
        "seriesId": 1,
        "seriesName": "C级",
        "brandId": 1,
        "brandName": "奔驰",
        "yearModel": 2024,
        "displacement": 1.5,
        "maxPower": 150,
        "maxTorque": 280,
        "transmission": "9AT",
        "gearCount": 9,
        "driveType": "RWD",
        "fuelType": "GASOLINE",
        "bodyStructure": "SEDAN",
        "seatCount": 5,
        "length": 4751,
        "width": 1816,
        "height": 1462,
        "wheelbase": 2865,
        "curbWeight": 1590,
        "tankCapacity": 66,
        "fuelConsumption": 6.8,
        "guidePrice": 32.52,
        "launchTime": "2024-01-15",
        "discontinueTime": null,
        "imageUrls": [
          "https://example.com/models/c200l-1.jpg",
          "https://example.com/models/c200l-2.jpg"
        ],
        "configDetails": {
          "safety": {
            "airbags": 8,
            "abs": true,
            "esp": true
          },
          "comfort": {
            "airCondition": "自动空调",
            "seats": "真皮座椅"
          }
        },
        "status": "ON_SALE",
        "sortWeight": 100,
        "createdTime": "2024-01-01T00:00:00Z",
        "updatedTime": "2024-01-15T10:30:00Z"
      }
    ],
    "totalElements": 200,
    "totalPages": 20,
    "currentPage": 1,
    "pageSize": 10
  }
}
```

#### 4.5.2 车型对比接口
```
POST /api/v1/models/compare

Request Body:
{
  "modelIds": [1, 2, 3]
}

Response:
{
  "code": 200,
  "message": "对比成功",
  "data": {
    "models": [
      {
        "id": 1,
        "name": "C 200 L 运动版",
        "brandName": "奔驰",
        "seriesName": "C级",
        "guidePrice": 32.52,
        "displacement": 1.5,
        "maxPower": 150,
        "maxTorque": 280,
        "transmission": "9AT",
        "driveType": "RWD",
        "fuelConsumption": 6.8,
        // ... 其他对比参数
      },
      {
        "id": 2,
        "name": "320i M运动套装",
        "brandName": "宝马",
        "seriesName": "3系",
        "guidePrice": 31.89,
        "displacement": 2.0,
        "maxPower": 135,
        "maxTorque": 300,
        "transmission": "8AT",
        "driveType": "RWD",
        "fuelConsumption": 7.1,
        // ... 其他对比参数
      }
    ],
    "compareResult": {
      "bestPrice": 2,
      "bestPower": 1,
      "bestFuelEconomy": 1
    }
  }
}
```

### 4.6 审批管理接口

#### 4.6.1 审批列表查询
```
GET /api/v1/approvals

Query Parameters:
- type: string (可选) - 申请类型
- status: string (可选) - 审批状态
- applicant: string (可选) - 申请人
- startTime: string (可选) - 开始时间
- endTime: string (可选) - 结束时间
- page: integer (默认1) - 页码
- size: integer (默认10) - 每页数量

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [
      {
        "id": 1,
        "approvalNo": "PCA20240115001",
        "type": "PRICE_CHANGE",
        "title": "奔驰C200L价格调整",
        "applicant": "张三",
        "applicantId": 1,
        "applyTime": "2024-01-15T10:30:00Z",
        "status": "PENDING",
        "urgency": "NORMAL",
        "expectedTime": "2024-01-17T18:00:00Z",
        "currentApprover": "李四",
        "changeContent": "价格从32.52万调整为29.80万",
        "reason": "厂商官方降价促销活动"
      }
    ],
    "totalElements": 25,
    "totalPages": 3,
    "currentPage": 1,
    "pageSize": 10
  }
}
```

#### 4.6.2 审批详情查询
```
GET /api/v1/approvals/{id}

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "approvalNo": "PCA20240115001",
    "type": "PRICE_CHANGE",
    "title": "奔驰C200L价格调整",
    "applicant": "张三",
    "applicantId": 1,
    "applyTime": "2024-01-15T10:30:00Z",
    "status": "PENDING",
    "urgency": "NORMAL",
    "expectedTime": "2024-01-17T18:00:00Z",
    "reason": "厂商官方降价促销活动，有效期至2024年3月31日",
    "impactAssessment": "提升市场竞争力，预计销量增长15%",
    "changeDetails": [
      {
        "objectType": "MODEL",
        "objectId": 1,
        "objectName": "奔驰 C 200 L 运动版",
        "field": "guidePrice",
        "oldValue": "32.52",
        "newValue": "29.80",
        "changeRate": "-8.37%"
      }
    ],
    "attachments": [
      {
        "id": 1,
        "fileName": "官方降价通知.pdf",
        "fileSize": 2359296,
        "fileUrl": "https://example.com/files/notice.pdf"
      }
    ],
    "approvalHistory": [
      {
        "step": 1,
        "approver": "张三",
        "action": "SUBMIT",
        "time": "2024-01-15T10:30:00Z",
        "comment": "提交价格变动申请"
      },
      {
        "step": 2,
        "approver": "李四",
        "action": "APPROVE",
        "time": "2024-01-15T14:20:00Z",
        "comment": "价格调整合理，附件齐全，建议通过"
      }
    ],
    "nextApprover": "王五",
    "flowDefinition": {
      "steps": [
        {"step": 1, "name": "申请提交", "approver": "申请人"},
        {"step": 2, "name": "数据管理员审核", "approver": "数据管理员"},
        {"step": 3, "name": "超级管理员审批", "approver": "超级管理员"},
        {"step": 4, "name": "执行变更", "approver": "系统"}
      ]
    }
  }
}
```

### 4.7 统计分析接口

#### 4.7.1 数据概览接口
```
GET /api/v1/statistics/overview

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "brandCount": 156,
    "seriesCount": 892,
    "modelCount": 3247,
    "onSaleModelCount": 2156,
    "brandDistribution": [
      {"country": "德国", "count": 55, "percentage": 35.3},
      {"country": "日本", "count": 44, "percentage": 28.2},
      {"country": "美国", "count": 23, "percentage": 14.7},
      {"country": "中国", "count": 19, "percentage": 12.2},
      {"country": "其他", "count": 15, "percentage": 9.6}
    ],
    "fuelTypeDistribution": [
      {"type": "GASOLINE", "count": 2110, "percentage": 65.0},
      {"type": "HYBRID", "count": 649, "percentage": 20.0},
      {"type": "ELECTRIC", "count": 325, "percentage": 10.0},
      {"type": "DIESEL", "count": 163, "percentage": 5.0}
    ],
    "priceDistribution": [
      {"range": "10万以下", "count": 487, "percentage": 15.0},
      {"range": "10-20万", "count": 1136, "percentage": 35.0},
      {"range": "20-30万", "count": 811, "percentage": 25.0},
      {"range": "30-50万", "count": 487, "percentage": 15.0},
      {"range": "50万以上", "count": 326, "percentage": 10.0}
    ],
    "latestModels": [
      {
        "id": 1,
        "name": "奔驰 C 200 L",
        "brandName": "奔驰",
        "launchTime": "2024-01-15"
      },
      {
        "id": 2,
        "name": "宝马 320i M",
        "brandName": "宝马",
        "launchTime": "2024-01-12"
      }
    ]
  }
}
```

### 4.8 文件管理接口

#### 4.8.1 文件上传接口
```
POST /api/v1/files/upload

Request:
Content-Type: multipart/form-data
- file: 上传的文件
- type: 文件类型 (BRAND_LOGO/SERIES_IMAGE/MODEL_IMAGE/ATTACHMENT)
- category: 文件分类 (可选)

Response:
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "fileId": "f123456789",
    "fileName": "logo.png",
    "fileSize": 1024000,
    "fileType": "image/png",
    "fileUrl": "https://example.com/files/logo.png",
    "thumbnailUrl": "https://example.com/files/logo_thumb.png"
  }
}
```

### 4.9 数据导入导出接口

#### 4.9.1 数据导入接口
```
POST /api/v1/import/{type}

Path Parameters:
- type: string (必填) - 导入类型 (brands/series/models)

Request:
Content-Type: multipart/form-data
- file: Excel文件

Response:
{
  "code": 200,
  "message": "导入任务已创建",
  "data": {
    "taskId": "task123456",
    "status": "PROCESSING",
    "fileName": "brands.xlsx",
    "totalRows": 100,
    "processedRows": 0,
    "successRows": 0,
    "failedRows": 0,
    "startTime": "2024-01-15T10:30:00Z"
  }
}
```

#### 4.9.2 导入任务状态查询
```
GET /api/v1/import/tasks/{taskId}

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "taskId": "task123456",
    "status": "COMPLETED",
    "fileName": "brands.xlsx",
    "totalRows": 100,
    "processedRows": 100,
    "successRows": 85,
    "failedRows": 15,
    "successRate": 85.0,
    "startTime": "2024-01-15T10:30:00Z",
    "endTime": "2024-01-15T10:35:00Z",
    "errorFileUrl": "https://example.com/files/error_report.xlsx",
    "failedRecords": [
      {
        "rowNumber": 3,
        "data": {"name": "奔驰", "country": "德国"},
        "error": "品牌名称已存在"
      }
    ]
  }
}
```

#### 4.9.3 数据导出接口
```
POST /api/v1/export/{type}

Path Parameters:
- type: string (必填) - 导出类型 (brands/series/models)

Request Body:
{
  "scope": "CURRENT_QUERY",
  "format": "EXCEL",
  "fields": ["name", "country", "status", "createdTime"],
  "filters": {
    "status": "ACTIVE",
    "country": "德国"
  },
  "includeHeader": true,
  "includeStatistics": true,
  "compress": false,
  "fileName": "品牌数据_20240115"
}

Response:
{
  "code": 200,
  "message": "导出任务已创建",
  "data": {
    "taskId": "export123456",
    "status": "PROCESSING",
    "estimatedTime": "2024-01-15T10:35:00Z"
  }
}
```## 
5. 数据库设计

### 5.1 数据库设计原则
- 遵循第三范式，避免数据冗余
- 合理使用索引提升查询性能
- 统一的字段命名规范
- 软删除机制保证数据安全
- 完善的审计字段记录

### 5.2 核心表结构设计

#### 5.2.1 品牌表 (t_brand)
```sql
CREATE TABLE t_brand (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '品牌ID',
    name VARCHAR(50) NOT NULL COMMENT '品牌名称',
    english_name VARCHAR(100) COMMENT '英文名称',
    country VARCHAR(50) NOT NULL COMMENT '所属国家',
    founded_year SMALLINT COMMENT '成立年份',
    website VARCHAR(255) COMMENT '官网地址',
    logo_url VARCHAR(500) COMMENT 'LOGO图片URL',
    description TEXT COMMENT '品牌简介',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    sort_weight INT DEFAULT 100 COMMENT '排序权重',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    deleted_time DATETIME COMMENT '删除时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) NOT NULL COMMENT '创建人编码',
    create_user_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) COMMENT '更新人编码',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',
    
    UNIQUE KEY uk_brand_name (name, deleted) COMMENT '品牌名称唯一索引',
    UNIQUE KEY uk_brand_english_name (english_name, deleted) COMMENT '英文名称唯一索引',
    KEY idx_brand_country (country) COMMENT '国家索引',
    KEY idx_brand_status (status) COMMENT '状态索引',
    KEY idx_brand_create_time (create_time) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='品牌表';
```

#### 5.2.2 车系表 (t_series)
```sql
CREATE TABLE t_series (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '车系ID',
    name VARCHAR(50) NOT NULL COMMENT '车系名称',
    english_name VARCHAR(100) COMMENT '英文名称',
    brand_id BIGINT NOT NULL COMMENT '所属品牌ID',
    type ENUM('SEDAN', 'SUV', 'MPV', 'COUPE', 'PICKUP', 'HATCHBACK') NOT NULL COMMENT '车系类型',
    level ENUM('A', 'B', 'C', 'D') NOT NULL COMMENT '级别',
    launch_time DATE NOT NULL COMMENT '上市时间',
    discontinue_time DATE COMMENT '停产时间',
    min_price DECIMAL(8,2) COMMENT '最低指导价(万元)',
    max_price DECIMAL(8,2) COMMENT '最高指导价(万元)',
    image_url VARCHAR(500) COMMENT '车系图片URL',
    description TEXT COMMENT '车系简介',
    status ENUM('ON_SALE', 'DISCONTINUED', 'COMING_SOON') DEFAULT 'COMING_SOON' COMMENT '状态',
    sort_weight INT DEFAULT 100 COMMENT '排序权重',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    deleted_time DATETIME COMMENT '删除时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) NOT NULL COMMENT '创建人编码',
    create_user_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) COMMENT '更新人编码',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',
    
    UNIQUE KEY uk_series_brand_name (brand_id, name, deleted) COMMENT '同品牌下车系名称唯一',
    UNIQUE KEY uk_series_english_name (english_name, deleted) COMMENT '英文名称唯一索引',
    KEY idx_series_brand_id (brand_id) COMMENT '品牌ID索引',
    KEY idx_series_type (type) COMMENT '车系类型索引',
    KEY idx_series_level (level) COMMENT '级别索引',
    KEY idx_series_status (status) COMMENT '状态索引',
    KEY idx_series_launch_time (launch_time) COMMENT '上市时间索引',
    KEY idx_series_create_time (create_time) COMMENT '创建时间索引',
    
    CONSTRAINT fk_series_brand FOREIGN KEY (brand_id) REFERENCES t_brand(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车系表';
```

#### 5.2.3 车型表 (t_model)
```sql
CREATE TABLE t_model (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '车型ID',
    name VARCHAR(100) NOT NULL COMMENT '车型名称',
    series_id BIGINT NOT NULL COMMENT '所属车系ID',
    year_model SMALLINT NOT NULL COMMENT '年款',
    displacement DECIMAL(3,1) COMMENT '排量(L)',
    max_power INT COMMENT '最大功率(kW)',
    max_torque INT COMMENT '最大扭矩(N·m)',
    transmission VARCHAR(20) NOT NULL COMMENT '变速箱类型',
    gear_count TINYINT COMMENT '档位数',
    drive_type ENUM('FWD', 'RWD', 'AWD') NOT NULL COMMENT '驱动方式',
    fuel_type ENUM('GASOLINE', 'DIESEL', 'HYBRID', 'ELECTRIC', 'PLUG_IN_HYBRID') NOT NULL COMMENT '燃料类型',
    body_structure ENUM('SEDAN', 'HATCHBACK', 'SUV', 'MPV', 'COUPE', 'PICKUP') NOT NULL COMMENT '车身结构',
    seat_count TINYINT NOT NULL COMMENT '座位数',
    length INT COMMENT '车长(mm)',
    width INT COMMENT '车宽(mm)',
    height INT COMMENT '车高(mm)',
    wheelbase INT COMMENT '轴距(mm)',
    curb_weight INT COMMENT '整备质量(kg)',
    tank_capacity INT COMMENT '油箱容积(L)',
    fuel_consumption DECIMAL(3,1) COMMENT '综合油耗(L/100km)',
    guide_price DECIMAL(8,2) NOT NULL COMMENT '指导价(万元)',
    launch_time DATE NOT NULL COMMENT '上市时间',
    discontinue_time DATE COMMENT '停售时间',
    image_urls JSON COMMENT '车型图片URL数组',
    config_details JSON COMMENT '配置详情JSON',
    status ENUM('ON_SALE', 'DISCONTINUED', 'COMING_SOON') DEFAULT 'COMING_SOON' COMMENT '状态',
    sort_weight INT DEFAULT 100 COMMENT '排序权重',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    deleted_time DATETIME COMMENT '删除时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) NOT NULL COMMENT '创建人编码',
    create_user_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) COMMENT '更新人编码',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',
    
    UNIQUE KEY uk_model_series_name (series_id, name, deleted) COMMENT '同车系下车型名称唯一',
    KEY idx_model_series_id (series_id) COMMENT '车系ID索引',
    KEY idx_model_year (year_model) COMMENT '年款索引',
    KEY idx_model_price (guide_price) COMMENT '价格索引',
    KEY idx_model_fuel_type (fuel_type) COMMENT '燃料类型索引',
    KEY idx_model_drive_type (drive_type) COMMENT '驱动方式索引',
    KEY idx_model_status (status) COMMENT '状态索引',
    KEY idx_model_launch_time (launch_time) COMMENT '上市时间索引',
    KEY idx_model_create_time (create_time) COMMENT '创建时间索引',
    
    CONSTRAINT fk_model_series FOREIGN KEY (series_id) REFERENCES t_series(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车型表';
```

#### 5.2.4 用户表 (t_user)
```sql
CREATE TABLE t_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    status ENUM('ACTIVE', 'INACTIVE', 'LOCKED') DEFAULT 'ACTIVE' COMMENT '状态',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    deleted_time DATETIME COMMENT '删除时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) COMMENT '创建人编码',
    create_user_name VARCHAR(50) COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) COMMENT '更新人编码',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',
    
    UNIQUE KEY uk_user_username (username, deleted) COMMENT '用户名唯一索引',
    UNIQUE KEY uk_user_email (email, deleted) COMMENT '邮箱唯一索引',
    KEY idx_user_status (status) COMMENT '状态索引',
    KEY idx_user_create_time (create_time) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

#### 5.2.5 角色表 (t_role)
```sql
CREATE TABLE t_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    code VARCHAR(50) NOT NULL COMMENT '角色编码',
    description VARCHAR(255) COMMENT '角色描述',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    deleted_time DATETIME COMMENT '删除时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) NOT NULL COMMENT '创建人编码',
    create_user_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) COMMENT '更新人编码',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',
    
    UNIQUE KEY uk_role_code (code, deleted) COMMENT '角色编码唯一索引',
    KEY idx_role_status (status) COMMENT '状态索引',
    KEY idx_role_create_time (create_time) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';
```

#### 5.2.6 用户角色关联表 (t_user_role)
```sql
CREATE TABLE t_user_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) NOT NULL COMMENT '创建人编码',
    create_user_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) COMMENT '更新人编码',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',
    
    UNIQUE KEY uk_user_role (user_id, role_id) COMMENT '用户角色唯一索引',
    KEY idx_user_role_user_id (user_id) COMMENT '用户ID索引',
    KEY idx_user_role_role_id (role_id) COMMENT '角色ID索引',
    KEY idx_user_role_create_time (create_time) COMMENT '创建时间索引',
    
    CONSTRAINT fk_user_role_user FOREIGN KEY (user_id) REFERENCES t_user(id),
    CONSTRAINT fk_user_role_role FOREIGN KEY (role_id) REFERENCES t_role(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';
```

#### 5.2.7 审批申请表 (t_approval)
```sql
CREATE TABLE t_approval (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '审批ID',
    approval_no VARCHAR(50) NOT NULL COMMENT '审批编号',
    type ENUM('PRICE_CHANGE', 'DATA_DELETE', 'BATCH_OPERATION') NOT NULL COMMENT '申请类型',
    title VARCHAR(200) NOT NULL COMMENT '申请标题',
    applicant_id BIGINT NOT NULL COMMENT '申请人ID',
    apply_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    status ENUM('PENDING', 'APPROVED', 'REJECTED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '审批状态',
    urgency ENUM('LOW', 'MEDIUM', 'HIGH') DEFAULT 'LOW' COMMENT '紧急程度',
    expected_time DATETIME COMMENT '期望完成时间',
    reason TEXT NOT NULL COMMENT '申请原因',
    impact_assessment TEXT COMMENT '影响评估',
    current_step INT DEFAULT 1 COMMENT '当前审批步骤',
    current_approver_id BIGINT COMMENT '当前审批人ID',
    final_approver_id BIGINT COMMENT '最终审批人ID',
    final_approve_time DATETIME COMMENT '最终审批时间',
    final_comment TEXT COMMENT '最终审批意见',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_approval_no (approval_no) COMMENT '审批编号唯一索引',
    KEY idx_approval_applicant (applicant_id) COMMENT '申请人索引',
    KEY idx_approval_status (status) COMMENT '状态索引',
    KEY idx_approval_type (type) COMMENT '类型索引',
    KEY idx_approval_apply_time (apply_time) COMMENT '申请时间索引',
    KEY idx_approval_current_approver (current_approver_id) COMMENT '当前审批人索引',
    
    CONSTRAINT fk_approval_applicant FOREIGN KEY (applicant_id) REFERENCES t_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批申请表';
```

#### 5.2.8 审批变更详情表 (t_approval_change)
```sql
CREATE TABLE t_approval_change (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '变更ID',
    approval_id BIGINT NOT NULL COMMENT '审批ID',
    object_type ENUM('BRAND', 'SERIES', 'MODEL') NOT NULL COMMENT '变更对象类型',
    object_id BIGINT NOT NULL COMMENT '变更对象ID',
    object_name VARCHAR(100) NOT NULL COMMENT '变更对象名称',
    field_name VARCHAR(50) NOT NULL COMMENT '变更字段名',
    field_label VARCHAR(50) NOT NULL COMMENT '变更字段标签',
    old_value TEXT COMMENT '原值',
    new_value TEXT COMMENT '新值',
    change_rate DECIMAL(5,2) COMMENT '变动幅度(%)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    KEY idx_approval_change_approval_id (approval_id) COMMENT '审批ID索引',
    KEY idx_approval_change_object (object_type, object_id) COMMENT '变更对象索引',
    
    CONSTRAINT fk_approval_change_approval FOREIGN KEY (approval_id) REFERENCES t_approval(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批变更详情表';
```

#### 5.2.9 审批历史表 (t_approval_history)
```sql
CREATE TABLE t_approval_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史ID',
    approval_id BIGINT NOT NULL COMMENT '审批ID',
    step INT NOT NULL COMMENT '审批步骤',
    approver_id BIGINT NOT NULL COMMENT '审批人ID',
    approver_name VARCHAR(50) NOT NULL COMMENT '审批人姓名',
    action ENUM('SUBMIT', 'APPROVE', 'REJECT', 'CANCEL', 'MODIFY') NOT NULL COMMENT '操作类型',
    comment TEXT COMMENT '审批意见',
    approve_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '审批时间',
    
    KEY idx_approval_history_approval_id (approval_id) COMMENT '审批ID索引',
    KEY idx_approval_history_approver (approver_id) COMMENT '审批人索引',
    KEY idx_approval_history_time (approve_time) COMMENT '审批时间索引',
    
    CONSTRAINT fk_approval_history_approval FOREIGN KEY (approval_id) REFERENCES t_approval(id),
    CONSTRAINT fk_approval_history_approver FOREIGN KEY (approver_id) REFERENCES t_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批历史表';
```

#### 5.2.10 文件表 (t_file)
```sql
CREATE TABLE t_file (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    file_id VARCHAR(50) NOT NULL COMMENT '文件唯一标识',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_url VARCHAR(500) NOT NULL COMMENT '访问URL',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(100) NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    category ENUM('BRAND_LOGO', 'SERIES_IMAGE', 'MODEL_IMAGE', 'ATTACHMENT', 'IMPORT', 'EXPORT') NOT NULL COMMENT '文件分类',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    status ENUM('ACTIVE', 'DELETED') DEFAULT 'ACTIVE' COMMENT '状态',
    ref_type VARCHAR(50) COMMENT '关联类型',
    ref_id BIGINT COMMENT '关联ID',
    uploaded_by BIGINT NOT NULL COMMENT '上传人ID',
    uploaded_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    
    UNIQUE KEY uk_file_id (file_id) COMMENT '文件ID唯一索引',
    KEY idx_file_category (category) COMMENT '文件分类索引',
    KEY idx_file_ref (ref_type, ref_id) COMMENT '关联对象索引',
    KEY idx_file_uploader (uploaded_by) COMMENT '上传人索引',
    KEY idx_file_upload_time (uploaded_time) COMMENT '上传时间索引',
    
    CONSTRAINT fk_file_uploader FOREIGN KEY (uploaded_by) REFERENCES t_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件表';
```

#### 5.2.11 操作日志表 (t_operation_log)
```sql
CREATE TABLE t_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    trace_id VARCHAR(50) COMMENT '链路追踪ID',
    user_id BIGINT COMMENT '操作用户ID',
    username VARCHAR(50) COMMENT '用户名',
    operation_type ENUM('CREATE', 'UPDATE', 'DELETE', 'QUERY', 'LOGIN', 'LOGOUT', 'IMPORT', 'EXPORT') NOT NULL COMMENT '操作类型',
    module VARCHAR(50) NOT NULL COMMENT '操作模块',
    function_name VARCHAR(100) NOT NULL COMMENT '功能名称',
    object_type VARCHAR(50) COMMENT '操作对象类型',
    object_id BIGINT COMMENT '操作对象ID',
    object_name VARCHAR(100) COMMENT '操作对象名称',
    old_data JSON COMMENT '操作前数据',
    new_data JSON COMMENT '操作后数据',
    operation_result ENUM('SUCCESS', 'FAILURE') NOT NULL COMMENT '操作结果',
    error_message TEXT COMMENT '错误信息',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params JSON COMMENT '请求参数',
    response_time INT COMMENT '响应时间(ms)',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    KEY idx_operation_log_user (user_id) COMMENT '用户索引',
    KEY idx_operation_log_type (operation_type) COMMENT '操作类型索引',
    KEY idx_operation_log_module (module) COMMENT '模块索引',
    KEY idx_operation_log_object (object_type, object_id) COMMENT '操作对象索引',
    KEY idx_operation_log_time (operation_time) COMMENT '操作时间索引',
    KEY idx_operation_log_result (operation_result) COMMENT '操作结果索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
```

#### 5.2.12 导入导出任务表 (t_import_export_task)
```sql
CREATE TABLE t_import_export_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    task_id VARCHAR(50) NOT NULL COMMENT '任务唯一标识',
    task_type ENUM('IMPORT', 'EXPORT') NOT NULL COMMENT '任务类型',
    data_type ENUM('BRAND', 'SERIES', 'MODEL') NOT NULL COMMENT '数据类型',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_url VARCHAR(500) COMMENT '文件URL',
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '任务状态',
    total_rows INT DEFAULT 0 COMMENT '总行数',
    processed_rows INT DEFAULT 0 COMMENT '已处理行数',
    success_rows INT DEFAULT 0 COMMENT '成功行数',
    failed_rows INT DEFAULT 0 COMMENT '失败行数',
    success_rate DECIMAL(5,2) DEFAULT 0 COMMENT '成功率',
    error_file_url VARCHAR(500) COMMENT '错误报告文件URL',
    error_message TEXT COMMENT '错误信息',
    config_json JSON COMMENT '任务配置JSON',
    result_json JSON COMMENT '任务结果JSON',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    
    UNIQUE KEY uk_task_id (task_id) COMMENT '任务ID唯一索引',
    KEY idx_task_type (task_type) COMMENT '任务类型索引',
    KEY idx_task_data_type (data_type) COMMENT '数据类型索引',
    KEY idx_task_status (status) COMMENT '任务状态索引',
    KEY idx_task_creator (created_by) COMMENT '创建人索引',
    KEY idx_task_created_time (created_time) COMMENT '创建时间索引',
    
    CONSTRAINT fk_task_creator FOREIGN KEY (created_by) REFERENCES t_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导入导出任务表';
```

### 5.3 数据库索引优化策略

#### 5.3.1 主要查询场景索引
1. **品牌查询优化**
   - 品牌名称模糊查询：使用全文索引
   - 国别筛选：单列索引
   - 状态筛选：单列索引
   - 创建时间排序：单列索引

2. **车系查询优化**
   - 品牌关联查询：外键索引
   - 车系类型筛选：单列索引
   - 级别筛选：单列索引
   - 复合查询：(brand_id, type, status)复合索引

3. **车型查询优化**
   - 车系关联查询：外键索引
   - 价格区间查询：单列索引
   - 燃料类型筛选：单列索引
   - 复合查询：(series_id, fuel_type, status)复合索引

#### 5.3.2 分页查询优化
```sql
-- 使用覆盖索引优化分页查询
CREATE INDEX idx_model_page_cover ON t_model(status, created_time, id);

-- 优化的分页查询SQL
SELECT m.* FROM t_model m 
INNER JOIN (
    SELECT id FROM t_model 
    WHERE status = 'ON_SALE' 
    ORDER BY created_time DESC 
    LIMIT 20 OFFSET 100
) t ON m.id = t.id;
```

### 5.4 数据库分库分表策略

#### 5.4.1 垂直分库
- **用户权限库**：用户、角色、权限相关表
- **业务数据库**：品牌、车系、车型相关表
- **审批流程库**：审批、工作流相关表
- **日志监控库**：操作日志、系统监控表

#### 5.4.2 水平分表（预留）
当数据量增长到一定规模时，考虑对以下表进行分表：
- **操作日志表**：按月分表，保留近12个月数据
- **车型表**：按品牌ID哈希分表
- **文件表**：按上传时间分表

### 5.5 数据备份与恢复策略

#### 5.5.1 备份策略
- **全量备份**：每日凌晨2点执行
- **增量备份**：每4小时执行一次
- **日志备份**：实时备份binlog
- **异地备份**：每周同步到异地机房

#### 5.5.2 数据保留策略
- **业务数据**：永久保留
- **操作日志**：保留1年
- **审批记录**：保留3年
- **导入导出记录**：保留6个月
- **文件数据**：根据引用情况清理

### 5.6 数据库监控指标

#### 5.6.1 性能监控
- **连接数监控**：当前连接数、最大连接数
- **查询性能**：慢查询日志、查询响应时间
- **锁等待**：死锁检测、锁等待时间
- **缓存命中率**：InnoDB缓冲池命中率

#### 5.6.2 容量监控
- **存储空间**：数据文件大小、日志文件大小
- **表空间**：各表的数据量增长趋势
- **索引效率**：索引使用情况、冗余索引检测

## 6. 总结

本概要设计文档从系统架构、核心业务流程、接口设计和数据库设计四个维度，为品牌车系车型管理系统提供了完整的技术设计方案。

### 6.1 设计亮点
1. **微服务架构**：采用Spring Cloud微服务架构，支持系统的可扩展性和可维护性
2. **完整的审批流程**：设计了灵活的审批工作流，支持复杂的业务审批场景
3. **丰富的时序图**：详细描述了核心业务流程的交互过程
4. **RESTful API**：遵循REST规范，提供统一的接口设计
5. **完善的数据库设计**：考虑了性能优化、数据安全、扩展性等多个方面

### 6.2 技术特色
- 前后端分离架构，支持多端访问
- 基于角色的权限控制系统
- 完整的操作日志和审计功能
- 支持大文件上传和批量数据处理
- 灵活的数据导入导出功能

### 6.3 后续工作
1. 详细设计阶段：细化各个模块的具体实现
2. 开发实现阶段：按照设计文档进行编码实现
3. 测试验证阶段：功能测试、性能测试、安全测试
4. 部署上线阶段：生产环境部署和运维监控