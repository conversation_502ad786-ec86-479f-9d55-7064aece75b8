-- H2数据库兼容的DDL语句
-- 汽车品牌、车系、车型表结构

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS t_model;
DROP TABLE IF EXISTS t_series;
DROP TABLE IF EXISTS t_brand;

-- 创建品牌表
CREATE TABLE t_brand (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    english_name VA<PERSON>HAR(100),
    country VARCHAR(50) NOT NULL,
    founded_year SMALLINT,
    website VARCHAR(255),
    logo_url VARCHAR(500),
    description TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    sort_weight INT DEFAULT 100,
    deleted TINYINT DEFAULT 0,
    deleted_time DATETIME,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_user_code VARCHAR(50) NOT NULL,
    create_user_name VARCHAR(50) NOT NULL,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_user_code VARCHAR(50),
    update_user_name VARCHAR(50)
);

-- 创建品牌表索引
CREATE UNIQUE INDEX uk_brand_name ON t_brand(name, deleted);
CREATE UNIQUE INDEX uk_brand_english_name ON t_brand(english_name, deleted);
CREATE INDEX idx_brand_country ON t_brand(country);
CREATE INDEX idx_brand_status ON t_brand(status);
CREATE INDEX idx_brand_create_time ON t_brand(create_time);

-- 创建车系表
CREATE TABLE t_series (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    english_name VARCHAR(100),
    brand_id BIGINT NOT NULL,
    type VARCHAR(20) NOT NULL,
    level VARCHAR(10) NOT NULL,
    launch_time DATE NOT NULL,
    discontinue_time DATE,
    min_price DECIMAL(8,2),
    max_price DECIMAL(8,2),
    image_url VARCHAR(500),
    description TEXT,
    status VARCHAR(20) DEFAULT 'COMING_SOON',
    sort_weight INT DEFAULT 100,
    deleted TINYINT DEFAULT 0,
    deleted_time DATETIME,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_user_code VARCHAR(50) NOT NULL,
    create_user_name VARCHAR(50) NOT NULL,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_user_code VARCHAR(50),
    update_user_name VARCHAR(50),
    
    CONSTRAINT fk_series_brand FOREIGN KEY (brand_id) REFERENCES t_brand(id)
);

-- 创建车系表索引
CREATE UNIQUE INDEX uk_series_brand_name ON t_series(brand_id, name, deleted);
CREATE UNIQUE INDEX uk_series_english_name ON t_series(english_name, deleted);
CREATE INDEX idx_series_brand_id ON t_series(brand_id);
CREATE INDEX idx_series_type ON t_series(type);
CREATE INDEX idx_series_level ON t_series(level);
CREATE INDEX idx_series_status ON t_series(status);
CREATE INDEX idx_series_launch_time ON t_series(launch_time);
CREATE INDEX idx_series_create_time ON t_series(create_time);

-- 创建车型表
CREATE TABLE t_model (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    series_id BIGINT NOT NULL,
    year_model SMALLINT NOT NULL,
    displacement DECIMAL(3,1),
    max_power INT,
    max_torque INT,
    transmission VARCHAR(20) NOT NULL,
    gear_count TINYINT,
    drive_type VARCHAR(10) NOT NULL,
    fuel_type VARCHAR(20) NOT NULL,
    body_structure VARCHAR(20) NOT NULL,
    seat_count TINYINT NOT NULL,
    length INT,
    width INT,
    height INT,
    wheelbase INT,
    curb_weight INT,
    tank_capacity INT,
    fuel_consumption DECIMAL(3,1),
    guide_price DECIMAL(8,2) NOT NULL,
    launch_time DATE NOT NULL,
    discontinue_time DATE,
    image_urls CLOB,
    config_details CLOB,
    status VARCHAR(20) DEFAULT 'COMING_SOON',
    sort_weight INT DEFAULT 100,
    deleted TINYINT DEFAULT 0,
    deleted_time DATETIME,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_user_code VARCHAR(50) NOT NULL,
    create_user_name VARCHAR(50) NOT NULL,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_user_code VARCHAR(50),
    update_user_name VARCHAR(50),
    
    CONSTRAINT fk_model_series FOREIGN KEY (series_id) REFERENCES t_series(id)
);

-- 创建车型表索引
CREATE UNIQUE INDEX uk_model_series_name ON t_model(series_id, name, deleted);
CREATE INDEX idx_model_series_id ON t_model(series_id);
CREATE INDEX idx_model_year ON t_model(year_model);
CREATE INDEX idx_model_price ON t_model(guide_price);
CREATE INDEX idx_model_fuel_type ON t_model(fuel_type);
CREATE INDEX idx_model_drive_type ON t_model(drive_type);
CREATE INDEX idx_model_status ON t_model(status);
CREATE INDEX idx_model_launch_time ON t_model(launch_time);
CREATE INDEX idx_model_create_time ON t_model(create_time);
