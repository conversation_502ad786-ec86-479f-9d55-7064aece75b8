package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.user;

import com.alibaba.bizworks.core.specification.*;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 */
@StructureObject(name = "UserInfoItemVO", desc = "")
@Data
public class UserInfoItemVO implements Serializable {

    /**
     * id
     */
    @Field(name = "id", desc = "")
    @JSONField(name = "user_id")
    private String id;
    /**
     * code
     */
    @Field(name = "code", desc = "")
    @JSONField(name = "user_code")
    private String code;

    /**
     * userCode
     */
    @Field(name = "userCode", desc = "")
    @JSONField(name = "code")
    private String userCode;

    /**
     * type
     */
    @Field(name = "type", desc = "")
    @JSONField(name = "user_type")
    private Integer type;
    /**
     * alias
     */
    @Field(name = "alias", desc = "")
    private String alias;
    /**
     * enable
     */
    @Field(name = "enable", desc = "")
    private Integer enable;
    /**
     * org
     */
    @Field(name = "org", desc = "")
    @JSONField(name = "org_str")
    private String org;

    /**
     * creatorCode
     */
    @Field(name = "creatorCode", desc = "")
    private String creatorCode;
    /**
     * creatorName
     */
    @Field(name = "creatorName", desc = "")
    private String creatorName;
    /**
     * createTime
     */
    @Field(name = "createTime", desc = "")
    @JsonFormat(pattern = " yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * updatorCode
     */
    @Field(name = "updatorCode", desc = "")
    private String updatorCode;
    /**
     * updatorName
     */
    @Field(name = "updatorName", desc = "")
    private String updatorName;
    /**
     * updateTime
     */
    @Field(name = "updateTime", desc = "")
    @JsonFormat(pattern = " yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


}
