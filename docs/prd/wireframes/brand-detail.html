<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>品牌详情页 - 线框图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .wireframe {
            background-color: white;
            border: 2px solid #333;
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 30px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #333;
            background: white;
            cursor: pointer;
        }
        .brand-info {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }
        .logo-area {
            width: 120px;
            height: 80px;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
        }
        .brand-details {
            flex: 1;
        }
        .brand-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .brand-meta {
            color: #666;
            margin-bottom: 10px;
        }
        .brand-website {
            color: #0066cc;
            text-decoration: underline;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
        }
        .description {
            padding: 15px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            line-height: 1.6;
        }
        .series-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #ccc;
        }
        .series-table th,
        .series-table td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: left;
        }
        .series-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        .action-btn {
            padding: 4px 8px;
            border: 1px solid #333;
            background: white;
            font-size: 12px;
        }
        .log-section {
            border: 1px solid #ccc;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .log-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .log-item:last-child {
            border-bottom: none;
        }
        .log-time {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="wireframe">
        <div class="header">
            <div class="title">品牌详情 > 奔驰</div>
            <div class="buttons">
                <button class="btn">编辑</button>
                <button class="btn">返回列表</button>
            </div>
        </div>

        <div class="brand-info">
            <div class="logo-area">
                [LOGO图片]
            </div>
            <div class="brand-details">
                <div class="brand-name">奔驰 (Mercedes-Benz)</div>
                <div class="brand-meta">德国 | 1926年 | 启用状态</div>
                <div class="brand-website">官网: https://www.mercedes-benz.com</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">品牌简介：</div>
            <div class="description">
                梅赛德斯-奔驰是世界知名的豪华汽车品牌，以其卓越的工程技术、创新设计和高品质制造而闻名。
                自1926年成立以来，奔驰一直致力于为客户提供最优质的汽车产品和服务，
                在豪华轿车、SUV、跑车等各个细分市场都占据重要地位。
            </div>
        </div>

        <div class="section">
            <div class="section-header">
                <div class="section-title">关联车系 (15个)</div>
                <button class="btn">新增车系</button>
            </div>
            <table class="series-table">
                <thead>
                    <tr>
                        <th>车系名称</th>
                        <th>类型</th>
                        <th>级别</th>
                        <th>状态</th>
                        <th>上市时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>C级</td>
                        <td>轿车</td>
                        <td>B级</td>
                        <td>在售</td>
                        <td>2020-01-01</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>E级</td>
                        <td>轿车</td>
                        <td>C级</td>
                        <td>在售</td>
                        <td>2019-05-15</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>S级</td>
                        <td>轿车</td>
                        <td>D级</td>
                        <td>在售</td>
                        <td>2018-03-20</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn">查看</button>
                                <button class="action-btn">编辑</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <div class="section-title">操作日志</div>
            <div class="log-section">
                <div class="log-item">
                    <div class="log-time">2024-01-15 10:30</div>
                    <div>张三 编辑了品牌信息</div>
                </div>
                <div class="log-item">
                    <div class="log-time">2024-01-10 14:20</div>
                    <div>李四 新增了C级车系</div>
                </div>
                <div class="log-item">
                    <div class="log-time">2024-01-05 09:15</div>
                    <div>王五 更新了品牌简介</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>