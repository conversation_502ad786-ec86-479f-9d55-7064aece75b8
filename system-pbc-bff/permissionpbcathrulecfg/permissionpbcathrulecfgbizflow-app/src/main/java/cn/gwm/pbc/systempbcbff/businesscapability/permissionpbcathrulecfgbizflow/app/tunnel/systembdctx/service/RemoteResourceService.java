package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource.AddAdminResourceForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource.DelAdminResourceForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource.EditAdminResourceForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthCenterRequestInterceptor;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteResourceService", url = "EMPTY", configuration = {AuthCenterRequestInterceptor.class})
public interface RemoteResourceService {

    /**
     * 查询菜单树形结构
     * @param adminPlatformCode
     * @param category
     * @return
     */

    @GetMapping("/admin/resource/tree")
    CommonRlt searchTree(@ApiParam(name = "admin_platform_code", value = "接入平台码", required = true)
                         @RequestParam(name = "admin_platform_code") String adminPlatformCode,
                                     @ApiParam(name = "category", value = "类别（0-菜单管理，1-数据管理）", required = true)
                         @RequestParam(name = "category") Integer category);

    /**
     * 查询用户菜单(树形结构)
     * @param platformCode
     * @param account
     * @return CommonRlt
     */
    @ApiOperation(
            value = "查询用户菜单(树形结构)",
            notes = "查询用户菜单(树形结构)",
            httpMethod = "GET"
    )
    @GetMapping("/admin/resource/user/tree")
    CommonRlt searchMyTree(@ApiParam(name = "platform_code", value = "平台Code", required = true)
                           @RequestHeader(name = "platform_code") String platformCode,
                           @ApiParam(name = "account", value = "操作人账号", required = true)
                           @RequestHeader(name = "account") String account);

    /**
     * 创建菜单
     * @param addAdminResourceForm
     * @return CommonRlt
     */
    @ApiOperation(
            value = "创建菜单",
            notes = "创建菜单",
            httpMethod = "POST"
    )
    @PostMapping("/admin/resource/create")
    CommonRlt add(@Validated @RequestBody AddAdminResourceForm addAdminResourceForm);


    /**
     *  根据菜单ID获取详情
     * @param id
     * @param adminPlatformCode
     * @return CommonRlt
     */
    @GetMapping("/admin/resource/get")
    CommonRlt searchResourceById(@ApiParam(name = "id", value = "菜单ID")
                                 @RequestParam(name = "id", required = true) String id,
                                 @ApiParam(name = "admin_platform_code", value = "接入平台code")
                                 @RequestParam(name = "admin_platform_code", required = true) String adminPlatformCode);

    /**
     * 编辑菜单
     * @param editAdminResourceForm
     * @return CommonRlt
     */
    @ApiOperation(
            value = "编辑菜单",
            notes = "编辑菜单",
            httpMethod = "POST"
    )
    @PostMapping("/admin/resource/edit")
    CommonRlt editResource(@Validated @RequestBody EditAdminResourceForm editAdminResourceForm);

    /**
     * 删除菜单
     * @param delAdminResourceForm
     * @return CommonRlt<JSONObject>
     */
    @ApiOperation(
            value = "删除菜单",
            notes = "删除菜单",
            httpMethod = "POST"
    )
    @PostMapping("/admin/resource/delete")
    CommonRlt delResource(@Validated @RequestBody DelAdminResourceForm delAdminResourceForm);

    /**
     * 异步加载资源树
     * @param adminPlatformCode
     * @param category
     * @param id
     * @return CommonRlt<JSONObject>
     */
    @ApiOperation(
            value = "异步加载资源树",
            notes = "异步加载资源树",
            httpMethod = "GET"
    )
    @GetMapping("/admin/resource/asyncLoading/resourceTree")
    CommonRlt asyncLoadingResourceTree(
                                       @ApiParam(name = "admin_platform_code", value = "接入平台码", required = true)
                                       @RequestParam(name = "admin_platform_code") String adminPlatformCode,
                                       @ApiParam(name = "category", value = "类别（0-菜单管理，1-数据管理）", required = true)
                                       @RequestParam(name = "category") Integer category,
                                       @ApiParam(name = "id", value = "资源ID")
                                       @RequestParam(name = "id", required = false) String id);

    /**
     * 通过名称模糊查询资源树
     * @param adminPlatformCode
     * @param category
     * @param name
     * @return CommonRlt
     */
    @ApiOperation(
            value = "通过名称模糊查询资源树",
            notes = "通过名称模糊查询资源树",
            httpMethod = "GET"
    )
    @GetMapping("/admin/resource/searchResourceTree")
    CommonRlt searchResourceTree(
                                 @ApiParam(name = "admin_platform_code", value = "接入平台码", required = true)
                                 @RequestParam(name = "admin_platform_code") String adminPlatformCode,
                                 @ApiParam(name = "category", value = "类别（0-菜单管理，1-数据管理）", required = true)
                                 @RequestParam(name = "category") Integer category,
                                 @ApiParam(name = "name", value = "资源名称", required = true)
                                 @RequestParam(name = "name") String name);
}
