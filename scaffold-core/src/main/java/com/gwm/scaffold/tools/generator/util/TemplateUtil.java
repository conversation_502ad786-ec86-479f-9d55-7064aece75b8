package com.gwm.scaffold.tools.generator.util;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

/**
 * 模板处理工具类
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
public class TemplateUtil {

    private Configuration configuration;

    @PostConstruct
    public void init() {
        try {
            configuration = new Configuration(Configuration.VERSION_2_3_31);
            configuration.setClassForTemplateLoading(this.getClass(), "/templates/generator");
            configuration.setDefaultEncoding("UTF-8");
            log.debug("FreeMarker模板引擎初始化成功");
        } catch (Exception e) {
            log.error("FreeMarker模板引擎初始化失败", e);
            throw new RuntimeException("FreeMarker模板引擎初始化失败", e);
        }
    }

    /**
     * 处理模板
     *
     * @param templateName 模板名称
     * @param data 模板数据
     * @return 处理后的内容
     */
    public String processTemplate(String templateName, Map<String, Object> data) throws IOException, TemplateException {
        Template template = configuration.getTemplate(templateName);
        StringWriter writer = new StringWriter();
        template.process(data, writer);
        return writer.toString();
    }
}
