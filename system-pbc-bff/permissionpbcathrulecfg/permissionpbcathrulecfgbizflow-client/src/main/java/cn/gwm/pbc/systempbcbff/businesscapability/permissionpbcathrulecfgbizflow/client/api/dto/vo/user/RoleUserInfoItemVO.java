package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.user;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleUserInfoItemVO", desc = "")
@Data
public class RoleUserInfoItemVO implements Serializable {

    /**
     * id
     */
    @Field(name = "id", desc = "")
    private String id;
    /**
     * code
     */
    @Field(name = "code", desc = "")
    private String code;
    /**
     * name
     */
    @Field(name = "name", desc = "")
    private String name;
    /**
     * remark
     */
    @Field(name = "remark", desc = "")
    private String remark;
    /**
     * custom
     */
    @Field(name = "custom", desc = "")
    private Integer custom;
    /**
     * userType
     */
    @Field(name = "userType", desc = "")
    private Integer userType;
    /**
     * sysCustom
     */
    @Field(name = "sysCustom", desc = "")
    private Integer sysCustom;
    /**
     * sysAutomatic
     */
    @Field(name = "sysAutomatic", desc = "")
    private Integer sysAutomatic;
    /**
     * creatorCode
     */
    @Field(name = "creatorCode", desc = "")
    private String creatorCode;
    /**
     * creatorName
     */
    @Field(name = "creatorName", desc = "")
    private String creatorName;
    /**
     * createTime
     */
    @Field(name = "createTime", desc = "")
    @JsonFormat(pattern = " yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * updatorCode
     */
    @Field(name = "updatorCode", desc = "")
    private String updatorCode;
    /**
     * updatorName
     */
    @Field(name = "updatorName", desc = "")
    private String updatorName;
    /**
     * updateTime
     */
    @Field(name = "updateTime", desc = "")
    @JsonFormat(pattern = " yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
