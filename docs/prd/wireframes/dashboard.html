<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据概览页 - 线框图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .wireframe {
            background-color: white;
            border: 2px solid #333;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 30px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #333;
            background: white;
            cursor: pointer;
        }
        .metrics-row {
            display: flex;
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            flex: 1;
            border: 1px solid #ccc;
            padding: 20px;
            text-align: center;
            background-color: #f9f9f9;
        }
        .metric-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
        }
        .charts-row {
            display: flex;
            gap: 30px;
            margin-bottom: 40px;
        }
        .chart-container {
            flex: 1;
            border: 1px solid #ccc;
            padding: 20px;
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        .chart-placeholder {
            height: 200px;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f9f9f9;
            margin-bottom: 15px;
        }
        .chart-legend {
            font-size: 14px;
            line-height: 1.6;
        }
        .legend-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            display: inline-block;
            margin-right: 8px;
            border: 1px solid #ccc;
        }
        .bottom-row {
            display: flex;
            gap: 30px;
        }
        .price-chart {
            flex: 2;
        }
        .latest-models {
            flex: 1;
            border: 1px solid #ccc;
            padding: 20px;
        }
        .model-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .model-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
        .model-item:last-child {
            border-bottom: none;
        }
        .model-date {
            color: #666;
            font-size: 12px;
        }
        .model-name {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="wireframe">
        <div class="header">
            <div class="title">数据概览</div>
            <div class="buttons">
                <button class="btn">刷新</button>
                <button class="btn">导出</button>
            </div>
        </div>

        <div class="metrics-row">
            <div class="metric-card">
                <div class="metric-title">品牌总数</div>
                <div class="metric-value">156</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">车系总数</div>
                <div class="metric-value">892</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">车型总数</div>
                <div class="metric-value">3,247</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">在售车型</div>
                <div class="metric-value">2,156</div>
            </div>
        </div>

        <div class="charts-row">
            <div class="chart-container">
                <div class="chart-title">品牌分布 (按国别)</div>
                <div class="chart-placeholder">
                    [饼图]
                </div>
                <div class="chart-legend">
                    <div class="legend-item">
                        <span><span class="legend-color" style="background-color: #ff6b6b;"></span>德国</span>
                        <span>35%</span>
                    </div>
                    <div class="legend-item">
                        <span><span class="legend-color" style="background-color: #4ecdc4;"></span>日本</span>
                        <span>28%</span>
                    </div>
                    <div class="legend-item">
                        <span><span class="legend-color" style="background-color: #45b7d1;"></span>美国</span>
                        <span>15%</span>
                    </div>
                    <div class="legend-item">
                        <span><span class="legend-color" style="background-color: #f9ca24;"></span>中国</span>
                        <span>12%</span>
                    </div>
                    <div class="legend-item">
                        <span><span class="legend-color" style="background-color: #6c5ce7;"></span>其他</span>
                        <span>10%</span>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-title">车型分布 (按燃料类型)</div>
                <div class="chart-placeholder">
                    [柱状图]
                </div>
                <div class="chart-legend">
                    <div class="legend-item">
                        <span><span class="legend-color" style="background-color: #ff6b6b;"></span>汽油</span>
                        <span>65%</span>
                    </div>
                    <div class="legend-item">
                        <span><span class="legend-color" style="background-color: #4ecdc4;"></span>混动</span>
                        <span>20%</span>
                    </div>
                    <div class="legend-item">
                        <span><span class="legend-color" style="background-color: #45b7d1;"></span>电动</span>
                        <span>10%</span>
                    </div>
                    <div class="legend-item">
                        <span><span class="legend-color" style="background-color: #f9ca24;"></span>柴油</span>
                        <span>5%</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="bottom-row">
            <div class="chart-container price-chart">
                <div class="chart-title">价格分布</div>
                <div class="chart-placeholder">
                    [直方图]
                </div>
                <div class="chart-legend">
                    <div class="legend-item">
                        <span>10万以下</span>
                        <span>15%</span>
                    </div>
                    <div class="legend-item">
                        <span>10-20万</span>
                        <span>35%</span>
                    </div>
                    <div class="legend-item">
                        <span>20-30万</span>
                        <span>25%</span>
                    </div>
                    <div class="legend-item">
                        <span>30-50万</span>
                        <span>15%</span>
                    </div>
                    <div class="legend-item">
                        <span>50万以上</span>
                        <span>10%</span>
                    </div>
                </div>
            </div>

            <div class="latest-models">
                <div class="chart-title">最新上市车型</div>
                <ul class="model-list">
                    <li class="model-item">
                        <div>
                            <div class="model-date">2024-01-15</div>
                            <div class="model-name">奔驰 C 200 L</div>
                        </div>
                    </li>
                    <li class="model-item">
                        <div>
                            <div class="model-date">2024-01-12</div>
                            <div class="model-name">宝马 320i M</div>
                        </div>
                    </li>
                    <li class="model-item">
                        <div>
                            <div class="model-date">2024-01-10</div>
                            <div class="model-name">奥迪 A4L 40</div>
                        </div>
                    </li>
                    <li class="model-item">
                        <div>
                            <div class="model-date">2024-01-08</div>
                            <div class="model-name">特斯拉 Model 3</div>
                        </div>
                    </li>
                    <li class="model-item">
                        <div>
                            <div class="model-date">2024-01-05</div>
                            <div class="model-name">比亚迪 汉EV</div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>