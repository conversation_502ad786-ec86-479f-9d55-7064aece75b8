package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("规则字段列表请求参数对象")
public class RuleFieldListForm implements Serializable {

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer page = 1;

    /**
     * 每页显示几条
     */
    @ApiModelProperty(value = "每页显示几条")
    private Integer size = 10;

    /**
     * 类型（1人事字段，2自定义字段）
     */
    @ApiModelProperty("类型（1人事字段，2自定义字段）")
    private Integer type;
}