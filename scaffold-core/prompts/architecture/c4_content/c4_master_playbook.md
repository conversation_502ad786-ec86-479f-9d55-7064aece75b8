# C4上下文图生成自动化执行剧本 v1.0

## 1. 总体目标
根据用户指定的PRD文档，自动化、无人干预地完成**上下文分析**和**PlantUML代码生成**两个核心步骤。本剧本通过严格的角色分离和任务交接，确保最终输出的图表代码是高质量、格式规范且内容准确的。

## 2. 角色定义
- **需求分析师 (Requirements Analyst)**: 严格遵循 `c4_analyst_prompt.md` 指令的AI角色。负责解读非结构化的PRD文档，并提取出结构化的上下文信息。
- **图表生成器 (Diagram Generator)**: 严格遵循 `c4_generator_prompt.md` 指令的AI角色。负责读取结构化的上下文信息，并将其转换为精确的、符合所有美学规范的PlantUML代码。

## 3. 自动化工作流 (Workflow)
你（AI助手）将作为总调度官，在用户单次触发后，严格按以下顺序执行：

### **步骤A: 初始化 (Initialization)**
1.  **接收输入**: 用户通过 `@` 引用提供一份PRD文档，例如 `@path/to/my_prd.md`。
2.  **定义输出文件**:
    *   根据PRD文件名，预先定义将要生成的两个核心产物，并保存在 `architecture/c4/` 目录下：
        *   中间分析文件: `architecture/c4/[PRD文件名]_context_analysis.md`
        *   最终图表代码文件: `architecture/c4/[PRD文件名]_c4_context.puml`
    *   向用户宣告这两个即将被创建的文件名。

### **步骤B: 核心任务执行 (Core Task Execution)**

#### **任务1: 分析与提取 (Analyze & Extract)**
1.  **切换角色**: 切换为 **需求分析师 (Requirements Analyst)**。
2.  **执行任务**:
    *   **输入**: 原始PRD文档 + `architecture/prompts/c4_analyst_prompt.md`。
    *   **动作**: 完整读取PRD，提取系统名称、核心价值、用户角色、外部系统及其交互关系。
    *   **输出**: 在 `architecture/c4/` 目录下创建并写入中间分析文件 `[PRD文件名]_context_analysis.md`。
3.  **检查与验证**:
    *   **动作**: 验证上一步创建的 `..._context_analysis.md` 文件是否存在且内容不为空。
    *   **失败处理**: 如果文件无效，中止流程并向用户报告"分析步骤失败"。

#### **任务2: 生成与输出 (Generate & Output)**
1.  **切换角色**: 切换为 **图表生成器 (Diagram Generator)**。
2.  **执行任务**:
    *   **输入**: 上一步生成的中间分析文件 `..._context_analysis.md` + `architecture/prompts/c4_generator_prompt.md`。
    *   **动作**: 读取这份结构清晰、无歧义的分析文件，将其中的实体和关系转换为PlantUML代码。
    *   **输出**: 在 `architecture/c4/` 目录下创建并写入最终图表代码文件 `..._c4_context.puml`。
3.  **检查与验证**:
    *   **动作**: 验证上一步创建的 `..._c4_context.puml` 文件是否存在，且内容包含`@startuml`和`@enduml`，确保其为有效的PlantUML文件。
    *   **失败处理**: 如果文件无效，中止流程并向用户报告"生成步骤失败"。

### **步骤C: 完成与交付 (Completion & Delivery)**
1.  **状态报告**: 向用户报告整个自动化流程已成功执行完毕。
2.  **成果交付**:
    *   明确告知用户，`..._c4_context.puml` 文件已保存到指定目录。
    *   **同时，将该文件的完整内容直接显示在聊天窗口中**，方便用户即时复制使用。

## 4. 执行开始
请确认你已理解以上所有指令。当用户引用此剧本并提供PRD文档时，请从【步骤A】开始执行。 