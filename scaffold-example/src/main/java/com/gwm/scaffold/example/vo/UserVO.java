package com.gwm.scaffold.example.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 用户视图对象VO
 * 
 * 展示如何使用VO返回数据给前端
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@ApiModel(description = "用户信息")
public class UserVO {

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "用户名", example = "zhangsan")
    private String username;

    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String realName;

    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String mobile;

    @ApiModelProperty(value = "性别", example = "1", notes = "1:男 2:女")
    private Integer gender;

    @ApiModelProperty(value = "性别名称", example = "男")
    private String genderName;

    @ApiModelProperty(value = "年龄", example = "25")
    private Integer age;

    @ApiModelProperty(value = "部门ID", example = "1")
    private Long departmentId;

    @ApiModelProperty(value = "部门名称", example = "技术部")
    private String departmentName;

    @ApiModelProperty(value = "职位", example = "Java开发工程师")
    private String position;

    @ApiModelProperty(value = "状态", example = "1", notes = "1:启用 0:禁用")
    private Integer status;

    @ApiModelProperty(value = "状态名称", example = "启用")
    private String statusName;

    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @ApiModelProperty(value = "备注", example = "这是一个测试用户")
    private String remark;

    @ApiModelProperty(value = "创建时间", example = "2025-01-10 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "创建人工号", example = "admin")
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "更新时间", example = "2025-01-10 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "更新人工号", example = "admin")
    private String updateUserCode;

    @ApiModelProperty(value = "更新人姓名", example = "管理员")
    private String updateUserName;

    @ApiModelProperty(value = "版本号", example = "1")
    private Integer version;

    /**
     * 获取性别名称
     */
    public String getGenderName() {
        if (gender == null) {
            return "";
        }
        return gender == 1 ? "男" : "女";
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "";
        }
        return status == 1 ? "启用" : "禁用";
    }
}
