# Controller接口层代码生成提示词

请基于接口文档和开发规范，为【竞品车型管理】模块生成Controller层**接口定义代码**（只生成接口结构，不生成具体实现）。

## 第一步：规范文档深度学习
### 必读规范文档（按优先级排序）
1. **核心规范**：
   - `docs/dev/rule/index.md` - 了解整体开发规范体系
   - `docs/dev/rule/06_controller_design.md` - Controller层设计原则与规范
   - `docs/dev/rule/05_dto_design.md` - DTO设计原则与分类规范
   - `docs/dev/rule/03_naming_conventions.md` - 命名约定规范

2. **接口规范**：
   - `docs/接口文档/竞品车型管理功能接口文档.md` - 业务接口定义与数据结构

3. **辅助规范**：
   - `docs/dev/rule/09_exception_handling.md` - 异常处理策略
   - `docs/dev/rule/01_architecture_principles.md` - 架构设计原则

### 关键规范要点提取
从上述文档中重点关注：
- Controller层的职责边界和设计原则
- RESTful API设计规范（URL路径、HTTP方法、状态码）
- DTO分类体系（CreateDTO、UpdateDTO、QueryDTO、VO等）
- 统一响应格式（AjaxResult、PageResult）
- 参数校验和异常处理策略
- 命名约定和注解使用规范

## 第二步：接口结构定义要求

### 生成内容清单
**只生成以下内容，不实现具体业务逻辑**：
1. **Controller类框架**：类注解、继承关系、字段注入
2. **接口方法签名**：方法名、参数类型、返回值类型、HTTP注解
3. **接口注释文档**：API文档注解、参数说明、响应说明
4. **DTO类定义**：请求DTO、响应VO、查询参数DTO
5. **方法体框架**：仅包含TODO注释，不写具体实现

### Controller基础结构要求
```java
/**
 * 【模块名称】管理控制器
 * 负责处理【具体业务】相关的HTTP请求
 */
@RestController
@RequestMapping("/api/v1/【路径】")
@Slf4j
@Api(tags = "【模块名称】管理接口")
public class 【模块名称】Controller extends BaseController {
    
    @Resource
    private 【模块名称】Service 【模块名称小写】Service;
    
    // TODO: 接口方法定义...
}
```

### 接口方法定义规范
**每个接口方法必须包含**：
1. **完整的API文档注解**：
   - `@ApiOperation` - 接口功能描述
   - `@ApiParam` - 参数说明
   - `@ApiResponse` - 响应说明
   
2. **HTTP方法注解**：
   - 根据业务操作选择合适的HTTP方法
   - 设置正确的路径参数和查询参数
   
3. **参数校验注解**：
   - `@Valid` 用于DTO参数校验
   - `@PathVariable`、`@RequestParam`、`@RequestBody` 等参数绑定
   
4. **方法签名**：
   - 明确的方法名（体现业务操作）
   - 正确的参数类型和返回值类型
   - 统一使用 `AjaxResult<T>` 作为返回值

**方法体结构**：
```java
@ApiOperation(value = "【操作描述】", notes = "【详细说明】")
@GetMapping("【路径】")
public AjaxResult<【返回类型】> 【方法名】(【参数列表】) {
    // TODO: 调用Service层方法
    // TODO: 处理返回结果
    // TODO: 封装统一响应格式
    return null; // 临时返回，待具体实现
}
```

### DTO设计要求
**根据接口文档生成对应的DTO类**：

1. **创建请求DTO** (`【模块】CreateDTO`)：
   - 包含创建资源所需的所有字段
   - 添加必要的校验注解（@NotNull、@NotBlank、@Size等）
   - 提供完整的API文档注解

2. **更新请求DTO** (`【模块】UpdateDTO`)：
   - 包含可更新的字段（通常不包含ID和创建时间）
   - 字段可为可选（支持部分更新）
   - 添加适当的校验注解

3. **查询请求DTO** (`【模块】QueryDTO`)：
   - 继承 `PageRequest` 基类
   - 包含所有查询条件字段
   - 支持模糊查询和精确查询

4. **响应VO** (`【模块】VO`)：
   - 包含前端展示所需的所有字段
   - 添加格式化注解（如日期格式化）
   - 可包含衍生字段（如状态描述）

## 第三步：具体生成要求

### 根据接口文档生成接口列表
基于`docs/接口文档/竞品车型管理功能接口文档.md`中的接口定义，生成以下Controller方法：

1. **竞品车型管理接口**：
   - 竞品车型列表查询 (GET /api/v1/competitor/vehicles)
   - 新增竞品车型 (POST /api/v1/competitor/vehicles)
   - 编辑竞品车型 (PUT /api/v1/competitor/vehicles/{id})
   - 删除竞品车型 (DELETE /api/v1/competitor/vehicles/{id})
   - 获取竞品车型详情 (GET /api/v1/competitor/vehicles/{id})
   - 竞品车型数据导出 (POST /api/v1/competitor/vehicles/export)
   - 关注状态管理 (PUT /api/v1/competitor/vehicles/{id}/follow)

2. **竞品车款关注接口**：
   - 新增竞品车款关注 (POST /api/v1/competitor/relations/{relationId}/model-focus)
   - 编辑竞品车款关注 (PUT /api/v1/competitor/model-focus/{focusId})
   - 删除竞品车款关注 (DELETE /api/v1/competitor/model-focus/{focusId})
   - 竞品车款关注列表查询 (GET /api/v1/competitor/relations/{relationId}/model-focus)

3. **四级层级级联查询接口**：
   - 品牌列表查询 (GET /api/v1/hierarchy/brands)
   - 生产厂家级联查询 (GET /api/v1/hierarchy/manufacturers)
   - 车型级联查询 (GET /api/v1/hierarchy/models)
   - 车款级联查询 (GET /api/v1/hierarchy/trims)

4. **本品品牌查询接口**：
   - 本品品牌列表 (GET /api/v1/own-brands)

5. **数据验证接口**：
   - 竞品关系验证 (POST /api/v1/competitive-vehicles/validate)

### 输出格式要求
**请按以下顺序输出**：

1. **Controller主类**：
   - 完整的类定义框架
   - 所有接口方法的签名和注解
   - 方法体仅包含TODO注释

2. **DTO类定义**：
   - 按功能模块组织DTO类
   - 每个DTO类包含完整的字段定义和注解
   - 按照CreateDTO、UpdateDTO、QueryDTO、VO的顺序

3. **接口规范说明**：
   - 列出生成的接口清单
   - 说明各接口的用途和参数要求
   - 标注需要特别注意的设计要点

## 第四步：质量检查要点

### 生成代码检查清单
- [ ] 所有类和方法都有完整的JavaDoc注释
- [ ] 所有接口方法都有API文档注解
- [ ] DTO类包含必要的校验注解
- [ ] HTTP方法和路径参数设置正确
- [ ] 返回值类型统一使用AjaxResult
- [ ] 分页查询使用PageRequest和PageResult
- [ ] 命名符合约定规范
- [ ] 没有具体的业务逻辑实现代码
- [ ] 遵循Controller层设计原则

**注意**：此提示词旨在生成接口定义框架，不包含具体的业务逻辑实现。生成的代码应该作为开发的起点，后续需要根据业务需求完善具体实现。