package com.gwm.scaffold.tools.util;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 前端工具
 *
 * <AUTHOR>
 * @date 2025/4/12
 */
public final class FrontUtil {

    /**
     * 将后端类型转化为前端类型
     *
     * @param clazz 后端类型
     * @param <T>   后端类型泛型
     * @return 前端类型（string、number、array、object）
     */
    public static <T> String convertFrontType(Class<T> clazz) {
        if (clazz == String.class) {
            return "string";
        }
        if (clazz == Integer.class ||
                clazz == Long.class ||
                clazz == Double.class ||
                clazz == Short.class ||
                clazz == Float.class ||
                clazz == Byte.class ||
                clazz == BigDecimal.class) {
            return "number";
        }
        if (clazz == Boolean.class) {
            return "boolean";
        }
        if (List.class.isAssignableFrom(clazz) || Set.class.isAssignableFrom(clazz) || clazz.isArray()) {
            return "array";
        }
        return "object";
    }

    private FrontUtil(){}
}
