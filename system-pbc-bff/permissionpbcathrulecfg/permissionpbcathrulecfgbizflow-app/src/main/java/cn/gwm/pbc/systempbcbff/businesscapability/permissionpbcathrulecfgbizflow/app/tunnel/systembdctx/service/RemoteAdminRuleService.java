package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule.RuleCreateForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule.RuleDelForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule.RuleEditForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule.RuleListFormDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthCenterRequestInterceptor;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteAdminRuleService", url = "EMPTY", configuration = {AuthCenterRequestInterceptor.class})
public interface RemoteAdminRuleService {

    /**
     * 规则列表
     * @param RuleListForm
     * @return CommonRlt<JSONObject>
     */
    @GetMapping(value = "/admin/role/rule/list")
    CommonRlt<JSONObject> ruleList(@RequestBody RuleListFormDTO RuleListForm);

    /**
     * 规则列表(下拉列表)
     * @param roleSetId
     * @return CommonRlt<JSONObject>
     */
    @GetMapping("/admin/role/rule/list/drop/down")
    CommonRlt<JSONObject> listDropDown(
                           @RequestParam(name = "role_id") String roleSetId);

    /**
     * 规则创建
     * @param ruleCreateForm
     * @return CommonRlt
     */
    @PostMapping("/admin/role/rule/create")
    CommonRlt create(@RequestBody RuleCreateForm ruleCreateForm);


    /**
     *规则详情
     * @param ruleId
     * @return CommonRlt
     */
    @GetMapping("/admin/role/rule/detail")
    CommonRlt detail(@ApiParam(name = "rule_id", value = "规则ID", required = true)
                     @RequestParam(name = "rule_id") Long ruleId);

    /**
     *  规则编辑
     * @param ruleEditForm
     * @return CommonRlt
     */
    @PostMapping("/admin/rule/edit")
    CommonRlt edit(@RequestBody RuleEditForm ruleEditForm);

    /**
     *  规则删除
     * @param ruleDelForm
     * @return CommonRlt
     */
    @PostMapping("/admin/role/rule/delete")
    CommonRlt delete(@RequestBody RuleDelForm ruleDelForm);

    /**
     *  规则状态修改
     * @param ruleDelForm
     * @return CommonRlt
     */
    @PostMapping("/admin/role/rule/edit/enable")
    CommonRlt editEnable(@RequestBody RuleDelForm ruleDelForm);
}
