package com.gwm.scaffold.tools.util;



import com.gwm.scaffold.tools.vo.DiffResult;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 王明莹
 * @Description List数组差异计算算法
 * @Date
 **/
public final class CollectionDiffUtil {
    public static <T, ID> DiffResult<T> diff(List<T> oldList, List<T> newList, Function<T, ID> idExtractor) {
        if (newList == null) {
            newList = Collections.emptyList();
        }
        if (oldList == null) {
            oldList = Collections.emptyList();
        }

        Map<ID, T> oldMap = oldList.stream()
                .filter(item -> idExtractor.apply(item) != null)
                .collect(Collectors.toMap(idExtractor, Function.identity()));

        Map<ID, T> newMap = newList.stream()
                .filter(item -> idExtractor.apply(item) != null)
                .collect(Collectors.toMap(idExtractor, Function.identity()));

        List<T> toInsert = new ArrayList<>();
        List<T> toUpdate = new ArrayList<>();
        List<T> toDelete = new ArrayList<>(oldList);

        for (T newItem : newList) {
            ID id = idExtractor.apply(newItem);
            if (id == null) {
                toInsert.add(newItem);
            } else if (oldMap.containsKey(id)) {
                toUpdate.add(newItem);
                toDelete.remove(oldMap.get(id));
            } else {
                toInsert.add(newItem);
            }
        }

        return new DiffResult<>(toInsert, toUpdate, toDelete);
    }

    private CollectionDiffUtil() {
    }
}
