package com.gwm.scaffold.docs.util;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * API文档工具类
 * 
 * 提供API文档相关的工具方法
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
public final class ApiDocUtil {

    /**
     * 获取类的API文档信息
     *
     * @param clazz 类
     * @return API文档信息
     */
    public static Map<String, Object> getClassApiInfo(Class<?> clazz) {
        Map<String, Object> info = new HashMap<>();
        
        // 获取ApiModel注解
        ApiModel apiModel = clazz.getAnnotation(ApiModel.class);
        if (apiModel != null) {
            info.put("value", apiModel.value());
            info.put("description", apiModel.description());
            info.put("parent", apiModel.parent());
            info.put("discriminator", apiModel.discriminator());
            info.put("subTypes", apiModel.subTypes());
        }
        
        info.put("className", clazz.getSimpleName());
        info.put("packageName", clazz.getPackage().getName());
        
        return info;
    }

    /**
     * 获取方法的API文档信息
     *
     * @param method 方法
     * @return API文档信息
     */
    public static Map<String, Object> getMethodApiInfo(Method method) {
        Map<String, Object> info = new HashMap<>();
        
        // 获取ApiOperation注解
        ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
        if (apiOperation != null) {
            info.put("value", apiOperation.value());
            info.put("notes", apiOperation.notes());
            info.put("tags", apiOperation.tags());
            info.put("response", apiOperation.response());
            info.put("responseContainer", apiOperation.responseContainer());
            info.put("httpMethod", apiOperation.httpMethod());
            info.put("nickname", apiOperation.nickname());
            info.put("produces", apiOperation.produces());
            info.put("consumes", apiOperation.consumes());
            info.put("protocols", apiOperation.protocols());
            info.put("hidden", apiOperation.hidden());
        }
        
        info.put("methodName", method.getName());
        info.put("returnType", method.getReturnType().getSimpleName());
        
        return info;
    }

    /**
     * 获取字段的API文档信息
     *
     * @param field 字段
     * @return API文档信息
     */
    public static Map<String, Object> getFieldApiInfo(Field field) {
        Map<String, Object> info = new HashMap<>();
        
        // 获取ApiModelProperty注解
        ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
        if (apiModelProperty != null) {
            info.put("value", apiModelProperty.value());
            info.put("name", apiModelProperty.name());
            info.put("allowableValues", apiModelProperty.allowableValues());
            info.put("access", apiModelProperty.access());
            info.put("notes", apiModelProperty.notes());
            info.put("dataType", apiModelProperty.dataType());
            info.put("required", apiModelProperty.required());
            info.put("position", apiModelProperty.position());
            info.put("hidden", apiModelProperty.hidden());
            info.put("example", apiModelProperty.example());
            info.put("readOnly", apiModelProperty.readOnly());
            info.put("reference", apiModelProperty.reference());
            info.put("allowEmptyValue", apiModelProperty.allowEmptyValue());
        }
        
        info.put("fieldName", field.getName());
        info.put("fieldType", field.getType().getSimpleName());
        
        return info;
    }

    /**
     * 生成标准的API操作描述
     *
     * @param operation 操作类型
     * @param resource  资源名称
     * @return API操作描述
     */
    public static String generateApiOperation(String operation, String resource) {
        switch (operation.toLowerCase()) {
            case "get":
            case "query":
            case "list":
                return "查询" + resource + "列表";
            case "getbyid":
            case "detail":
                return "获取" + resource + "详情";
            case "create":
            case "add":
            case "post":
                return "创建" + resource;
            case "update":
            case "modify":
            case "put":
                return "更新" + resource;
            case "delete":
            case "remove":
                return "删除" + resource;
            case "export":
                return "导出" + resource + "数据";
            case "import":
                return "导入" + resource + "数据";
            case "download":
                return "下载" + resource + "文件";
            case "upload":
                return "上传" + resource + "文件";
            default:
                return operation + resource;
        }
    }

    /**
     * 生成标准的API操作备注
     *
     * @param operation 操作类型
     * @param resource  资源名称
     * @return API操作备注
     */
    public static String generateApiNotes(String operation, String resource) {
        switch (operation.toLowerCase()) {
            case "get":
            case "query":
            case "list":
                return "分页查询" + resource + "列表，支持条件筛选和排序";
            case "getbyid":
            case "detail":
                return "根据ID获取" + resource + "的详细信息";
            case "create":
            case "add":
            case "post":
                return "创建新的" + resource + "记录";
            case "update":
            case "modify":
            case "put":
                return "更新指定" + resource + "的信息";
            case "delete":
            case "remove":
                return "删除指定的" + resource + "记录";
            case "export":
                return "导出" + resource + "数据到Excel文件";
            case "import":
                return "从Excel文件导入" + resource + "数据";
            case "download":
                return "下载" + resource + "相关文件";
            case "upload":
                return "上传" + resource + "相关文件";
            default:
                return "执行" + resource + "相关的" + operation + "操作";
        }
    }

    /**
     * 检查类是否有API文档注解
     *
     * @param clazz 类
     * @return 是否有API文档注解
     */
    public static boolean hasApiAnnotation(Class<?> clazz) {
        return clazz.isAnnotationPresent(ApiModel.class);
    }

    /**
     * 检查方法是否有API文档注解
     *
     * @param method 方法
     * @return 是否有API文档注解
     */
    public static boolean hasApiAnnotation(Method method) {
        return method.isAnnotationPresent(ApiOperation.class);
    }

    /**
     * 检查字段是否有API文档注解
     *
     * @param field 字段
     * @return 是否有API文档注解
     */
    public static boolean hasApiAnnotation(Field field) {
        return field.isAnnotationPresent(ApiModelProperty.class);
    }

    /**
     * 获取所有支持的注解类型
     *
     * @return 注解类型数组
     */
    public static Class<? extends Annotation>[] getSupportedAnnotations() {
        return new Class[]{
                ApiModel.class,
                ApiOperation.class,
                ApiModelProperty.class,
                ApiParam.class
        };
    }

    private ApiDocUtil() {
    }
}
