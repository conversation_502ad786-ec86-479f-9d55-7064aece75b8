package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.role;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleInfoItemVO", desc = "")
@Data
public class RoleInfoItemVO implements Serializable {

    /**
     * id
     */
    @Field(name = "id", desc = "")
    private String id;
    /**
     * code
     */
    @Field(name = "code", desc = "")
    private String code;
    /**
     * name
     */
    @Field(name = "name", desc = "")
    private String name;
    /**
     * remark
     */
    @Field(name = "remark", desc = "")
    private String remark;
    /**
     * ext1
     */
    @Field(name = "ext1", desc = "")
    private String ext1;
    /**
     * ext2
     */
    @Field(name = "ext2", desc = "")
    private String ext2;
    /**
     * enable
     */
    @Field(name = "enable", desc = "")
    private Integer enable;
    /**
     * sortNo
     */
    @Field(name = "sortNo", desc = "")
    private Integer sortNo;
    /**
     * deleteFlag
     */
    @Field(name = "deleteFlag", desc = "")
    private Integer deleteFlag;
    /**
     * creatorCode
     */
    @Field(name = "creatorCode", desc = "")
    private String creatorCode;
    /**
     * creatorName
     */
    @Field(name = "creatorName", desc = "")
    private String creatorName;
    /**
     * createTime
     */
    @Field(name = "createTime", desc = "")
    @JsonFormat(pattern = " yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * updatorCode
     */
    @Field(name = "updatorCode", desc = "")
    private String updatorCode;
    /**
     * updatorName
     */
    @Field(name = "updatorName", desc = "")
    private String updatorName;
    /**
     * updateTime
     */
    @Field(name = "updateTime", desc = "")
    @JsonFormat(pattern = " yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
