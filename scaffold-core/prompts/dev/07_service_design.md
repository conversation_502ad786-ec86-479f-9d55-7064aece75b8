# 07 服务层规范

## 服务层设计原则

### 核心原则
- 服务层包含业务逻辑，是系统的核心
- 遵循单一职责原则，每个服务类专注于特定领域
- 通过接口和实现类分离，提高扩展性和可测试性
- 集中处理事务管理和业务规则
- 对外隐藏实现细节，只暴露必要的方法

### 服务层职责
- 执行业务规则和业务流程
- 协调多个资源和服务
- 处理事务管理
- 数据转换和业务校验
- 领域实体和DTO之间的转换

## 服务接口设计

### 接口定义规范
- 使用业务名词 + Service命名（如`BrandService`）
- 方法命名应反映业务操作
- 参数和返回值使用DTO或基本类型，避免直接暴露领域实体
- 声明可能抛出的业务异常
- 提供完整的方法文档注释

### 接口示例
```java
/**
 * 品牌管理服务接口
 */
public interface BrandService {
    
    /**
     * 创建品牌
     * 
     * @param createDTO 品牌创建请求
     * @return 品牌ID
     * @throws BusinessException 如果品牌代码已存在
     */
    Long createBrand(BrandCreateDTO createDTO);
    
    /**
     * 分页查询品牌列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<BrandVO> getBrandList(BrandQueryDTO queryDTO);
    
    /**
     * 根据ID查询品牌详情
     * 
     * @param id 品牌ID
     * @return 品牌详情
     * @throws ResourceNotFoundException 如果品牌不存在
     */
    BrandDetailVO getBrandById(Long id);
    
    /**
     * 更新品牌信息
     * 
     * @param id 品牌ID
     * @param updateDTO 更新请求
     * @throws ResourceNotFoundException 如果品牌不存在
     */
    void updateBrand(Long id, BrandUpdateDTO updateDTO);
    
    /**
     * 删除品牌
     * 
     * @param id 品牌ID
     * @throws ResourceNotFoundException 如果品牌不存在
     * @throws BusinessException 如果品牌下存在关联数据
     */
    void deleteBrand(Long id);
    
    /**
     * 批量删除品牌
     * 
     * @param ids 品牌ID列表
     * @throws BusinessException 如果任一品牌下存在关联数据
     */
    void batchDeleteBrands(List<Long> ids);
    
    /**
     * 获取所有启用状态的品牌
     * 
     * @return 品牌列表
     */
    List<BrandListItemVO> getAllActiveBrands();
    
    /**
     * 检查品牌代码是否存在
     * 
     * @param brandCode 品牌代码
     * @return 是否存在
     */
    boolean existsByCode(String brandCode);
}
```

## 服务实现类设计

### 实现类规范
- 实现类命名为接口名 + Impl（如`BrandServiceImpl`）
- 使用`@Service`注解标记服务实现类
- 依赖注入使用构造器注入，避免字段注入
- 方法实现应清晰、易于理解
- 复杂逻辑拆分为多个私有方法
- 使用`@Transactional`管理事务

### 服务实现类示例
```java
/**
 * 品牌管理服务实现
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class BrandServiceImpl implements BrandService {
    
    private final BrandMapper brandMapper;
    private final ManufacturerService manufacturerService;
    private final BrandConverter brandConverter;
    
    public BrandServiceImpl(BrandMapper brandMapper, 
                          ManufacturerService manufacturerService,
                          BrandConverter brandConverter) {
        this.brandMapper = brandMapper;
        this.manufacturerService = manufacturerService;
        this.brandConverter = brandConverter;
    }
    
    @Override
    public Long createBrand(BrandCreateDTO createDTO) {
        log.info("开始创建品牌: {}", createDTO.getBrandCode());
        
        // 业务校验
        validateCreateRequest(createDTO);
        
        // DTO转换为实体
        Brand brand = brandConverter.fromCreateDTO(createDTO);
        
        // 设置默认值和审计信息
        setupBrandDefaults(brand);
        
        // 保存品牌
        brandMapper.insert(brand);
        log.info("品牌创建成功: id={}, code={}", brand.getId(), brand.getBrandCode());
        
        return brand.getId();
    }
    
    @Override
    @Transactional(readOnly = true)
    public PageResult<BrandVO> getBrandList(BrandQueryDTO queryDTO) {
        log.info("查询品牌列表: {}", queryDTO);
        
        // 构建查询条件
        LambdaQueryWrapper<Brand> queryWrapper = buildQueryWrapper(queryDTO);
        
        // 分页查询
        Page<Brand> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        if (StringUtils.hasText(queryDTO.getSortField())) {
            page.setOrders(buildOrderItems(queryDTO));
        }
        
        IPage<Brand> brandPage = brandMapper.selectPage(page, queryWrapper);
        
        // 转换结果
        List<BrandVO> brandVOList = brandPage.getRecords().stream()
            .map(brandConverter::toVO)
            .collect(Collectors.toList());
        
        return PageResult.<BrandVO>builder()
            .records(brandVOList)
            .total(brandPage.getTotal())
            .size(brandPage.getSize())
            .current(brandPage.getCurrent())
            .pages(brandPage.getPages())
            .build();
    }
    
    @Override
    @Transactional(readOnly = true)
    public BrandDetailVO getBrandById(Long id) {
        log.info("查询品牌详情: id={}", id);
        
        // 查询品牌
        Brand brand = getBrandEntityById(id);
        
        // 转换为VO
        BrandDetailVO detailVO = brandConverter.toDetailVO(brand);
        
        // 查询关联数据
        enrichBrandDetail(detailVO);
        
        return detailVO;
    }
    
    @Override
    public void updateBrand(Long id, BrandUpdateDTO updateDTO) {
        log.info("更新品牌信息: id={}, updateDTO={}", id, updateDTO);
        
        // 查询品牌
        Brand brand = getBrandEntityById(id);
        
        // 更新实体
        brandConverter.updateFromDTO(updateDTO, brand);
        
        // 设置审计信息
        brand.setUpdateUserId(getCurrentUserId());
        brand.setUpdateTime(LocalDateTime.now());
        
        // 保存更新
        brandMapper.updateById(brand);
        log.info("品牌更新成功: id={}", id);
    }
    
    @Override
    public void deleteBrand(Long id) {
        log.info("删除品牌: id={}", id);
        
        // 查询品牌
        Brand brand = getBrandEntityById(id);
        
        // 检查是否有关联数据
        checkRelatedData(id);
        
        // 逻辑删除
        brand.setStatus(0); // 设置为停用状态
        brand.setUpdateUserId(getCurrentUserId());
        brand.setUpdateTime(LocalDateTime.now());
        brandMapper.updateById(brand);
        
        log.info("品牌删除成功: id={}", id);
    }
    
    // ... 其他公共方法实现
    
    /**
     * 根据ID获取品牌实体
     */
    private Brand getBrandEntityById(Long id) {
        Brand brand = brandMapper.selectById(id);
        if (brand == null) {
            throw new ResourceNotFoundException("品牌不存在: " + id);
        }
        return brand;
    }
    
    /**
     * 验证创建请求
     */
    private void validateCreateRequest(BrandCreateDTO createDTO) {
        // 检查品牌代码是否已存在
        LambdaQueryWrapper<Brand> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Brand::getBrandCode, createDTO.getBrandCode())
                   .eq(Brand::getStatus, 1);
        
        if (brandMapper.selectCount(queryWrapper) > 0) {
            throw new BusinessException(ResponseCodeEnum.DATA_ALREADY_EXISTS, 
                    "品牌代码已存在: " + createDTO.getBrandCode());
        }
    }
    
    /**
     * 设置品牌默认值
     */
    private void setupBrandDefaults(Brand brand) {
        brand.setStatus(1); // 默认启用
        brand.setCreateUserId(getCurrentUserId());
        brand.setUpdateUserId(getCurrentUserId());
        brand.setCreateTime(LocalDateTime.now());
        brand.setUpdateTime(LocalDateTime.now());
    }
    
    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Brand> buildQueryWrapper(BrandQueryDTO queryDTO) {
        LambdaQueryWrapper<Brand> wrapper = new LambdaQueryWrapper<>();
        
        // 设置默认查询启用状态的品牌
        wrapper.eq(Brand::getStatus, queryDTO.getStatus() != null ? queryDTO.getStatus() : 1);
        
        // 按品牌代码查询
        if (StringUtils.hasText(queryDTO.getBrandCode())) {
            wrapper.eq(Brand::getBrandCode, queryDTO.getBrandCode());
        }
        
        // 按品牌名称模糊查询
        if (StringUtils.hasText(queryDTO.getBrandName())) {
            wrapper.like(Brand::getBrandNameCn, queryDTO.getBrandName())
                  .or()
                  .like(Brand::getBrandNameEn, queryDTO.getBrandName());
        }
        
        // 按国家/地区查询
        if (StringUtils.hasText(queryDTO.getBrandCountry())) {
            wrapper.eq(Brand::getBrandCountry, queryDTO.getBrandCountry());
        }
        
        // 按品牌定位查询
        if (StringUtils.hasText(queryDTO.getBrandPositioning())) {
            wrapper.eq(Brand::getBrandPositioning, queryDTO.getBrandPositioning());
        }
        
        return wrapper;
    }
    
    /**
     * 构建排序项
     */
    private List<OrderItem> buildOrderItems(BrandQueryDTO queryDTO) {
        String sortField = queryDTO.getSortField();
        String sortOrder = StringUtils.hasText(queryDTO.getSortOrder()) ? 
                           queryDTO.getSortOrder() : "asc";
        
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(camelToUnderscore(sortField));
        orderItem.setAsc("asc".equalsIgnoreCase(sortOrder));
        
        return Collections.singletonList(orderItem);
    }
    
    /**
     * 驼峰转下划线
     */
    private String camelToUnderscore(String camel) {
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, camel);
    }
    
    /**
     * 丰富品牌详情信息
     */
    private void enrichBrandDetail(BrandDetailVO detailVO) {
        Long brandId = detailVO.getId();
        
        // 查询厂商列表
        List<ManufacturerVO> manufacturers = manufacturerService.getManufacturersByBrandId(brandId, null);
        detailVO.setManufacturers(manufacturers);
        detailVO.setManufacturerCount(manufacturers.size());
        
        // 设置创建/更新用户信息
        detailVO.setCreateUserName(getUsernameById(detailVO.getCreateUserId()));
        detailVO.setUpdateUserName(getUsernameById(detailVO.getUpdateUserId()));
        
        // 查询车型数量
        detailVO.setVehicleModelCount(getVehicleModelCount(brandId));
    }
    
    /**
     * 获取用户名
     */
    private String getUsernameById(Long userId) {
        // 实际项目中，这里应该调用用户服务获取用户信息
        return "系统用户(" + userId + ")";
    }
    
    /**
     * 获取车型数量
     */
    private Integer getVehicleModelCount(Long brandId) {
        // 实际项目中，这里应该调用车型服务获取车型数量
        return 0;
    }
    
    /**
     * 检查关联数据
     */
    private void checkRelatedData(Long brandId) {
        // 检查是否存在关联的厂商
        int manufacturerCount = manufacturerService.countByBrandId(brandId);
        if (manufacturerCount > 0) {
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR, 
                    String.format("品牌(ID=%d)下存在%d个厂商，无法删除", brandId, manufacturerCount));
        }
    }
    
    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        // 实际项目中，这里应该从安全上下文中获取当前用户ID
        return 1L;
    }
}
```

## 事务管理

### 事务注解使用
- 类级别`@Transactional`用于所有修改操作
- 只读操作使用`@Transactional(readOnly = true)`优化性能
- 显式指定回滚异常`@Transactional(rollbackFor = Exception.class)`
- 对于特殊场景，方法级别注解覆盖类级别注解
- 合理设置事务传播行为和隔离级别

### 事务传播行为选择
- **REQUIRED**（默认）：当前有事务则加入，否则创建新事务
- **REQUIRES_NEW**：创建新事务，挂起当前事务
- **SUPPORTS**：当前有事务则加入，否则非事务执行
- **NOT_SUPPORTED**：非事务执行，挂起当前事务
- **MANDATORY**：当前必须有事务，否则抛出异常
- **NEVER**：非事务执行，如当前存在事务则抛出异常
- **NESTED**：嵌套事务，当前有事务则创建嵌套事务

### 事务使用示例
```java
// 类级别默认事务配置
@Service
@Transactional(rollbackFor = Exception.class)
public class BrandServiceImpl implements BrandService {
    
    // 只读事务优化查询性能
    @Override
    @Transactional(readOnly = true)
    public PageResult<BrandVO> getBrandList(BrandQueryDTO queryDTO) {
        // 查询逻辑
    }
    
    // 使用REQUIRES_NEW创建独立事务
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void importBrands(List<BrandImportDTO> importList) {
        // 导入逻辑
    }
}
```

## 业务逻辑处理

### 参数校验
- 控制器层：使用注解进行基本校验
- 服务层：进行复杂业务规则校验
- 校验失败抛出适当的业务异常
- 封装通用验证逻辑到独立方法

### 防御式编程
- 检查输入参数非空
- 处理边界条件和特殊情况
- 避免空指针和类型转换异常
- 预先检查资源是否存在

### 错误处理
- 使用自定义业务异常表达业务规则违反
- 异常信息应明确具体错误
- 适当记录异常日志
- 不在服务层捕获并吞掉异常

## 服务间调用

### 直接调用
- 通过依赖注入调用其他服务
- 避免循环依赖
- 考虑使用事件驱动解耦
- 服务间调用应明确边界和职责

### 异步调用
- 使用`@Async`注解实现异步调用
- 非关键路径操作考虑异步处理
- 提供回调或事件通知机制
- 合理配置线程池参数

```java
@Service
public class AsyncNotificationService {
    
    @Async("notificationExecutor")
    public CompletableFuture<Void> sendNotification(NotificationDTO dto) {
        // 异步发送通知逻辑
        return CompletableFuture.completedFuture(null);
    }
}
```

### 批量操作优化
- 使用批量插入/更新替代循环操作
- 大数据量分批处理
- 考虑使用并行流或自定义线程池处理并行任务
- 提供进度跟踪和错误恢复机制

```java
@Override
public void batchImportBrands(List<BrandImportDTO> importList) {
    // 分批处理
    Lists.partition(importList, 100).forEach(batch -> {
        // 批量保存
        List<Brand> brands = batch.stream()
            .map(brandConverter::fromImportDTO)
            .collect(Collectors.toList());
        brandMapper.batchInsert(brands);
    });
}
```

## 缓存策略

### 缓存使用
- 使用`@Cacheable`、`@CachePut`和`@CacheEvict`注解实现缓存
- 合理设置缓存的过期时间
- 避免缓存大对象或过多数据
- 提供缓存预热和手动刷新机制

```java
@Override
@Cacheable(value = "brand", key = "#id", unless = "#result == null")
public BrandDetailVO getBrandById(Long id) {
    // 查询逻辑
}

@Override
@CacheEvict(value = "brand", key = "#id")
public void updateBrand(Long id, BrandUpdateDTO updateDTO) {
    // 更新逻辑
}

@Override
@CacheEvict(value = "brand", allEntries = true)
public void refreshCache() {
    log.info("刷新品牌缓存");
}
```

### 缓存一致性
- 确保缓存与数据库同步
- 更新或删除操作时清除相关缓存
- 设置合理的缓存过期策略
- 考虑使用消息通知跨服务缓存失效

## 服务监控与可观测性

### 日志记录
- 记录关键业务操作和参数
- 使用MDC记录上下文信息
- 异常日志应包含详细信息
- 不同场景使用合适的日志级别

### 性能指标
- 使用AOP记录方法执行时间
- 统计服务调用次数和错误率
- 监控事务执行情况
- 记录资源使用情况（内存、连接等）

```java
@Aspect
@Component
@Slf4j
public class ServicePerformanceAspect {
    
    @Around("execution(* com.gwm.globalvehicle.masterdata..service.*.*(..))")
    public Object logPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        try {
            return joinPoint.proceed();
        } finally {
            long executionTime = System.currentTimeMillis() - start;
            log.debug("{}.{} 执行时间: {}ms", 
                    joinPoint.getSignature().getDeclaringTypeName(),
                    joinPoint.getSignature().getName(),
                    executionTime);
        }
    }
}
``` 