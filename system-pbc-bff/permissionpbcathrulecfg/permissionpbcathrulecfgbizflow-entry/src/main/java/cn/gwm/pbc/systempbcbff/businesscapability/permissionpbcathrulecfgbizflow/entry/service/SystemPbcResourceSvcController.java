package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcResourceSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRuleSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcResourceSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcRuleSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 权限PBC-资源管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@RestController("PermissionPBCAthResourceCfgBizFlowSystemPbcRuleSvcCON")
public class SystemPbcResourceSvcController implements SystemPbcResourceSvcI {

    @Autowired
    private SystemPbcResourceSvc systemPbcResourceSvc;

    @Override
    public Map<String, Object> asyncLoadingResourceTree(AsyncClientDTO request) {
        return systemPbcResourceSvc.asyncLoadingResourceTree(request);
    }

    @Override
    public Map<String, Object> searchResourceTree(SearchClientDTO request) {
        return systemPbcResourceSvc.searchResourceTree(request);
    }

    @Override
    public Map<String, Object> searchTree(TreeClientDTO request) {
        return systemPbcResourceSvc.tree(request);
    }

    @Override
    public Map<String, Object> create(AddAdminResourceFormClientDTO request) {
        return systemPbcResourceSvc.create(request);
    }

    @Override
    public Map<String, Object> editResource(EditAdminResourceFormClientDTO request) {
        return systemPbcResourceSvc.editResource(request);
    }

    @Override
    public Map<String, Object> delResource(DelAdminResourceFormClientDTO request) {
        return systemPbcResourceSvc.delResource(request);
    }

    /**
     * 权限中心-根据菜单ID获取详情 v2-/admin/resource/get
     *
     * @param request request
     * @return Response 权限中心-根据菜单ID获取详情
     */
    @Override
    public Map<String, Object> searchResourceById(DetailResourceFormClientDTO request) {
        return systemPbcResourceSvc.searchResourceById(request);
    }
}
