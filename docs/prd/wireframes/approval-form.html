<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审批申请页 - 线框图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .wireframe {
            background-color: white;
            border: 2px solid #333;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 30px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #333;
            background: white;
            cursor: pointer;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .form-row {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-start;
        }
        .form-label {
            width: 120px;
            font-weight: bold;
            margin-top: 8px;
        }
        .required {
            color: red;
        }
        .form-control {
            padding: 8px;
            border: 1px solid #ccc;
            width: 300px;
        }
        .form-control.large {
            width: 500px;
        }
        .form-control.small {
            width: 150px;
        }
        .form-hint {
            color: #666;
            font-size: 12px;
            margin-left: 10px;
            margin-top: 8px;
        }
        .textarea {
            height: 80px;
            resize: vertical;
        }
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 8px;
        }
        .radio-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            background-color: #f9f9f9;
            width: 400px;
            margin-top: 8px;
        }
        .file-list {
            margin-top: 10px;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
            width: 400px;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .change-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .change-table th,
        .change-table td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: left;
        }
        .change-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .change-input {
            width: 100%;
            padding: 5px;
            border: 1px solid #ccc;
        }
        .add-row-btn {
            margin-top: 10px;
            padding: 5px 10px;
            border: 1px solid #333;
            background: white;
            cursor: pointer;
        }
        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin-top: 15px;
            border-radius: 4px;
        }
        .warning-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="wireframe">
        <div class="header">
            <div class="title">提交审批申请</div>
            <div class="buttons">
                <button class="btn">提交申请</button>
                <button class="btn">保存草稿</button>
                <button class="btn">取消</button>
            </div>
        </div>

        <div class="section">
            <div class="section-title">申请基本信息</div>
            <div class="form-row">
                <label class="form-label">申请类型<span class="required">*</span>:</label>
                <select class="form-control">
                    <option>请选择申请类型</option>
                    <option>价格变动审批</option>
                    <option>数据删除审批</option>
                    <option>批量操作审批</option>
                </select>
            </div>
            <div class="form-row">
                <label class="form-label">申请标题<span class="required">*</span>:</label>
                <input type="text" class="form-control large" placeholder="请输入申请标题">
                <div class="form-hint">简要描述本次申请的内容</div>
            </div>
            <div class="form-row">
                <label class="form-label">紧急程度<span class="required">*</span>:</label>
                <div class="radio-group">
                    <div class="radio-item">
                        <input type="radio" name="urgency" value="low" checked>
                        <label>普通</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" name="urgency" value="medium">
                        <label>紧急</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" name="urgency" value="high">
                        <label>特急</label>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <label class="form-label">期望完成时间:</label>
                <input type="datetime-local" class="form-control">
                <div class="form-hint">可选，用于紧急申请</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">变更详情</div>
            <table class="change-table">
                <thead>
                    <tr>
                        <th>变更对象</th>
                        <th>变更字段</th>
                        <th>原值</th>
                        <th>新值</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <select class="change-input">
                                <option>奔驰 C 200 L 运动版</option>
                                <option>宝马 320i M运动套装</option>
                            </select>
                        </td>
                        <td>
                            <select class="change-input">
                                <option>指导价</option>
                                <option>状态</option>
                                <option>上市时间</option>
                            </select>
                        </td>
                        <td><input type="text" class="change-input" value="32.52万元" readonly></td>
                        <td><input type="text" class="change-input" placeholder="请输入新值"></td>
                        <td><button class="add-row-btn">删除</button></td>
                    </tr>
                </tbody>
            </table>
            <button class="add-row-btn">+ 添加变更项</button>
            
            <div class="warning-box">
                <div class="warning-title">⚠️ 审批提醒</div>
                <div>检测到价格变动超过10%，需要超级管理员审批。预计审批时间：1-2个工作日。</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">申请原因</div>
            <div class="form-row">
                <label class="form-label">详细说明<span class="required">*</span>:</label>
                <textarea class="form-control large textarea" placeholder="请详细说明本次变更的原因、背景和必要性..."></textarea>
                <div class="form-hint">请提供充分的理由支持本次申请</div>
            </div>
            <div class="form-row">
                <label class="form-label">影响评估:</label>
                <textarea class="form-control large textarea" placeholder="请评估本次变更可能产生的影响..."></textarea>
                <div class="form-hint">包括对业务、用户、系统的影响</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">相关附件</div>
            <div class="form-row">
                <label class="form-label">上传文件:</label>
                <div>
                    <div class="upload-area">
                        <div>点击上传或拖拽文件到此处</div>
                        <div style="font-size: 12px; color: #666; margin-top: 10px;">
                            支持 PDF、Word、Excel、图片格式，单个文件最大10MB
                        </div>
                    </div>
                    <div class="file-list">
                        <div class="file-item">
                            <span>📄 官方降价通知.pdf (2.3MB)</span>
                            <button style="border: none; background: none; color: red; cursor: pointer;">删除</button>
                        </div>
                        <div class="file-item">
                            <span>📊 市场分析报告.xlsx (1.8MB)</span>
                            <button style="border: none; background: none; color: red; cursor: pointer;">删除</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">审批流程预览</div>
            <div style="padding: 15px; border: 1px solid #ccc; background-color: #f9f9f9;">
                <div style="margin-bottom: 10px;"><strong>审批流程：</strong></div>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="padding: 5px 10px; border: 1px solid #2196f3; background: #e3f2fd;">申请提交</span>
                    <span>→</span>
                    <span style="padding: 5px 10px; border: 1px solid #ff9800; background: #fff3e0;">数据管理员审核</span>
                    <span>→</span>
                    <span style="padding: 5px 10px; border: 1px solid #4caf50; background: #e8f5e8;">超级管理员审批</span>
                    <span>→</span>
                    <span style="padding: 5px 10px; border: 1px solid #9c27b0; background: #f3e5f5;">执行变更</span>
                </div>
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    预计审批时间：1-2个工作日
                </div>
            </div>
        </div>
    </div>
</body>
</html>