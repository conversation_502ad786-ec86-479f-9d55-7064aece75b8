package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.*;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * 权限PBC-角色资源服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcRoleResourceSvcI-1702533187760")
@RequestMapping(path = "/PermissionPBCAthRoleResourceCfgBizFlow")
public interface SystemPbcRoleResourceSvcI {

    /**
     * 权限中心-查询角色菜单树（返回所有树和选择节点） v2-/manage/admin/role/resource/Info
     *
     * @param request request
     * @return Response 权限中心-查询角色菜单树（返回所有树和选择节点）
     */
    @PostMapping(path = "/SystemPbcResourceSvc/searchResourceOfRoleInfo/v1")
    Map<String, Object> searchResourceOfRoleInfo(@RequestBody(required = true) SearchAdminResourceOfRoleFormClientDTO request);

}
