package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserUnbindDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 权限PBC-角色Team管理服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcRoleTeamSvcI-1702535854477")
@RequestMapping(path = "/PermissionPBCAthRuleCfgBizFlow")
public interface SystemPbcRoleTeamSvcI {

    /**
     * 权限中心-查询角色用户列表 system/role/team/list
     *
     * @param request request
     * @return Response 权限中心-查询角色用户列表
     */
    @PostMapping(path = "/SystemPbcRoleTeamSvc/list/v1")
    Response<Map<String, Object>> list(@RequestBody(required = true) RoleUserListDTO request);

    /**
     * 权限中心-角色下绑定用户 system/role/team/bind
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcRoleTeamSvc/bind/v1")
    Response<Void> bind(@RequestBody(required = true) RoleUserBindDTO request);

    /**
     * 权限中心-角色下解绑用户 system/role/team/unbind
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcRoleTeamSvc/unbind/v1")
    Response<Void> unbind(@RequestBody(required = true) RoleUserUnbindDTO request);
}
