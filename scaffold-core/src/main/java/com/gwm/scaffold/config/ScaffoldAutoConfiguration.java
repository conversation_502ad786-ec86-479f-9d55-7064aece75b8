package com.gwm.scaffold.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Scaffold 自动配置类
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(ScaffoldProperties.class)
@Import({
    ScaffoldCoreAutoConfiguration.class,
    ScaffoldAuthAutoConfiguration.class,
    ScaffoldWebAutoConfiguration.class,
    ScaffoldDataAutoConfiguration.class,
    ScaffoldToolsAutoConfiguration.class,
    ScaffoldMonitorAutoConfiguration.class,
    ScaffoldDocsAutoConfiguration.class
})
public class ScaffoldAutoConfiguration {

    private final ScaffoldProperties scaffoldProperties;

    public ScaffoldAutoConfiguration(ScaffoldProperties scaffoldProperties) {
        this.scaffoldProperties = scaffoldProperties;
        printBanner();
        printModuleStatus();
    }

    /**
     * 打印启动横幅
     */
    private void printBanner() {
        log.info("");
        log.info("   ____                __  __       _     _");
        log.info("  / ___|_      ___ __ |  \\/  |     | |   | |");
        log.info(" | |  _\\ \\ /\\ / / '_ \\| |\\/| |     | |   | |");
        log.info(" | |_| |\\ V  V /| | | | |  | |     |_|   |_|");
        log.info("  \\____| \\_/\\_/ |_| |_|_|  |_|     (_)   (_)");
        log.info("");
        log.info("  :: GWM Scaffold ::        (v{})", scaffoldProperties.getApplication().getVersion());
        log.info("");
        log.info("应用名称: {}", scaffoldProperties.getApplication().getName());
        log.info("应用描述: {}", scaffoldProperties.getApplication().getDescription());
        log.info("开发团队: {}", scaffoldProperties.getApplication().getTeam());
        log.info("");
    }

    /**
     * 打印模块状态
     */
    private void printModuleStatus() {
        log.info("=== GWM Scaffold 模块状态 ===");
        log.info("核心模块 (Core): ✅ 已启用");
        log.info("认证模块 (Auth): {}", scaffoldProperties.getModules().isAuth() ? "✅ 已启用" : "❌ 已禁用");
        log.info("Web模块 (Web): {}", scaffoldProperties.getModules().isWeb() ? "✅ 已启用" : "❌ 已禁用");
        log.info("数据模块 (Data): {}", scaffoldProperties.getModules().isData() ? "✅ 已启用" : "❌ 已禁用");
        log.info("工具模块 (Tools): {}", scaffoldProperties.getModules().isTools() ? "✅ 已启用" : "❌ 已禁用");
        log.info("监控模块 (Monitor): {}", scaffoldProperties.getModules().isMonitor() ? "✅ 已启用" : "❌ 已禁用");
        log.info("文档模块 (Docs): {}", scaffoldProperties.getModules().isDocs() ? "✅ 已启用" : "❌ 已禁用");
        log.info("=============================");
        
        if (scaffoldProperties.getModules().isMonitor()) {
            log.info("📊 监控面板: http://localhost:8080/scaffold/monitor");
        }
        if (scaffoldProperties.getModules().isDocs()) {
            log.info("📚 API文档: http://localhost:8080/scaffold/docs");
            log.info("📚 Swagger UI: http://localhost:8080/swagger-ui.html");
            log.info("📚 Bootstrap UI: http://localhost:8080/doc.html");
        }
        log.info("");
    }
}
