package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role;

import com.alibaba.bizworks.core.specification.*;
import lombok.Data;
import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleDetailDTO", desc = "")
@Data
public class RoleDetailDTO implements Serializable {

    /**
     * id
     */
    @Field(name = "id", desc = "")
    private String id;

}
