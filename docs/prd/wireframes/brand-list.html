<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>品牌列表页 - 线框图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .wireframe {
            background-color: white;
            border: 2px solid #333;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #333;
            background: white;
            cursor: pointer;
        }
        .search-section {
            padding: 15px;
            border: 1px solid #ccc;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .search-row {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 10px;
        }
        .form-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .form-control {
            padding: 5px;
            border: 1px solid #ccc;
            width: 120px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: left;
        }
        .table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .checkbox {
            width: 15px;
            height: 15px;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        .action-btn {
            padding: 4px 8px;
            border: 1px solid #333;
            background: white;
            font-size: 12px;
        }
        .footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-top: 1px solid #ccc;
        }
        .batch-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .pagination {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="wireframe">
        <div class="header">
            <div class="title">品牌管理</div>
            <div class="buttons">
                <button class="btn">新增品牌</button>
                <button class="btn">批量导入</button>
            </div>
        </div>

        <div class="search-section">
            <div class="search-row">
                <span>搜索条件：</span>
            </div>
            <div class="search-row">
                <div class="form-group">
                    <label>品牌名称:</label>
                    <input type="text" class="form-control" placeholder="请输入品牌名称">
                </div>
                <div class="form-group">
                    <label>国别:</label>
                    <select class="form-control">
                        <option>请选择国别</option>
                        <option>德国</option>
                        <option>日本</option>
                        <option>美国</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>状态:</label>
                    <select class="form-control">
                        <option>请选择状态</option>
                        <option>启用</option>
                        <option>禁用</option>
                    </select>
                </div>
                <button class="btn">搜索</button>
                <button class="btn">重置</button>
            </div>
        </div>

        <table class="table">
            <thead>
                <tr>
                    <th><input type="checkbox" class="checkbox"></th>
                    <th>品牌名称</th>
                    <th>国别</th>
                    <th>状态</th>
                    <th>车系数量</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><input type="checkbox" class="checkbox"></td>
                    <td>奔驰</td>
                    <td>德国</td>
                    <td>启用</td>
                    <td>15</td>
                    <td>2024-01-01</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">删除</button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="checkbox" class="checkbox"></td>
                    <td>宝马</td>
                    <td>德国</td>
                    <td>启用</td>
                    <td>12</td>
                    <td>2024-01-02</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">删除</button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="checkbox" class="checkbox"></td>
                    <td>奥迪</td>
                    <td>德国</td>
                    <td>禁用</td>
                    <td>8</td>
                    <td>2024-01-03</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">删除</button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="footer">
            <div class="batch-actions">
                <span>批量操作:</span>
                <button class="btn">启用</button>
                <button class="btn">禁用</button>
                <button class="btn">删除</button>
            </div>
            <div class="pagination">
                <span>共100条 第1/10页</span>
                <button class="btn">上一页</button>
                <button class="btn">下一页</button>
            </div>
        </div>
    </div>
</body>
</html>