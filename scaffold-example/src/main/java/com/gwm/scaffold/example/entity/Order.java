package com.gwm.scaffold.example.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.gwm.scaffold.core.entity.BaseSuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 * 
 * 展示如何使用Scaffold的基础实体类和注解
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_order")
@ApiModel(description = "订单信息")
public class Order extends BaseSuperEntity {

    /**
     * 订单ID
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "订单ID", example = "1")
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true, example = "ORD202501150001")
    private String orderNo;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID", example = "1")
    private Long customerId;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名", required = true, example = "张三")
    private String customerName;

    /**
     * 客户电话
     */
    @ApiModelProperty(value = "客户电话", example = "13800138001")
    private String customerPhone;

    /**
     * 客户邮箱
     */
    @ApiModelProperty(value = "客户邮箱", example = "<EMAIL>")
    private String customerEmail;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", required = true, example = "iPhone 15 Pro Max 256GB")
    private String productName;

    /**
     * 商品SKU
     */
    @ApiModelProperty(value = "商品SKU", example = "IPHONE15PM256GB-BLACK")
    private String productSku;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量", required = true, example = "1")
    private Integer quantity;

    /**
     * 商品单价
     */
    @ApiModelProperty(value = "商品单价", required = true, example = "9999.00")
    private BigDecimal unitPrice;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额", required = true, example = "9999.00")
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    @ApiModelProperty(value = "优惠金额", example = "500.00")
    private BigDecimal discountAmount;

    /**
     * 实际支付金额
     */
    @ApiModelProperty(value = "实际支付金额", required = true, example = "9499.00")
    private BigDecimal actualAmount;

    /**
     * 订单状态 (1:待支付 2:已支付 3:已发货 4:已完成 5:已取消)
     */
    @ApiModelProperty(value = "订单状态", example = "1", notes = "1:待支付 2:已支付 3:已发货 4:已完成 5:已取消")
    private Integer orderStatus;

    /**
     * 支付方式 (1:支付宝 2:微信 3:银行卡 4:现金)
     */
    @ApiModelProperty(value = "支付方式", example = "1", notes = "1:支付宝 2:微信 3:银行卡 4:现金")
    private Integer paymentMethod;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间", example = "2025-01-10 10:30:00")
    private LocalDateTime paymentTime;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址", example = "北京市朝阳区建国门外大街1号")
    private String shippingAddress;

    /**
     * 收货电话
     */
    @ApiModelProperty(value = "收货电话", example = "13800138001")
    private String shippingPhone;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名", example = "张三")
    private String shippingName;

    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间", example = "2025-01-11 09:00:00")
    private LocalDateTime shippingTime;

    /**
     * 送达时间
     */
    @ApiModelProperty(value = "送达时间", example = "2025-01-12 14:30:00")
    private LocalDateTime deliveryTime;

    /**
     * 订单备注
     */
    @ApiModelProperty(value = "订单备注", example = "请小心轻放")
    private String remark;

    /**
     * 版本号（乐观锁）
     */
    @Version
    @ApiModelProperty(value = "版本号", hidden = true)
    private Integer version;

    /**
     * 逻辑删除标记 (0:未删除 1:已删除)
     */
    @TableLogic
    @ApiModelProperty(value = "删除标记", hidden = true)
    private Integer deleted;

    /**
     * 订单状态枚举
     */
    public enum OrderStatus {
        PENDING_PAYMENT(1, "待支付"),
        PAID(2, "已支付"),
        SHIPPED(3, "已发货"),
        COMPLETED(4, "已完成"),
        CANCELLED(5, "已取消");

        private final Integer code;
        private final String description;

        OrderStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static OrderStatus fromCode(Integer code) {
            for (OrderStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 支付方式枚举
     */
    public enum PaymentMethod {
        ALIPAY(1, "支付宝"),
        WECHAT(2, "微信支付"),
        BANK_CARD(3, "银行卡"),
        CASH(4, "现金");

        private final Integer code;
        private final String description;

        PaymentMethod(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static PaymentMethod fromCode(Integer code) {
            for (PaymentMethod method : values()) {
                if (method.code.equals(code)) {
                    return method;
                }
            }
            return null;
        }
    }
}
