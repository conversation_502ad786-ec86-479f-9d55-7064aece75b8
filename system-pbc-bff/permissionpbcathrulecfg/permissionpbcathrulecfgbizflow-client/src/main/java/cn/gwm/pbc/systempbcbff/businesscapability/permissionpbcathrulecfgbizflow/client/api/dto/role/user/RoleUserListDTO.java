package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;
import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleUserListDTO", desc = "")
@Data
public class RoleUserListDTO implements Serializable {

    /**
     * 角色id
     */
    @Field(name = "角色id", desc = "角色id")
    private String id;
    /**
     * 页码（默认为1）
     */
    @Field(name = "页码（默认为1）", desc = "页码（默认为1）")
    private Integer page;
    /**
     * 每页显示条数（默认为10）
     */
    @Field(name = "每页显示条数（默认为10）", desc = "每页显示条数（默认为10）")
    private Integer size;
    /**
     * 用户账号
     */
    @Field(name = "用户账号", desc = "用户账号")
    @Alias("user_code")
    private String code;

    /**
     * 用户姓名
     */
    @Field(name = "用户姓名", desc = "用户姓名")
    @Alias("user_name")
    private String name;

}
