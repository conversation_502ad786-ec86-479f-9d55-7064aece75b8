# 竞品车型管理系统 - 领域对象和Mapper生成提示词

## 系统背景
你是一个专业的Java后端开发工程师，正在为竞品车型管理系统开发领域对象和基础Mapper接口。请严格遵循以下技术规范和设计原则。

## 核心设计原则
1. **第一性原理**：从业务本质出发，设计清晰的领域模型
2. **DRY原则**：避免重复代码，提取公共组件
3. **KISS原则**：保持简单直接，避免过度设计
4. **SOLID原则**：单一职责、开闭原则、接口隔离等
5. **YAGNI原则**：只实现当前需要的功能，避免过度设计

## 技术栈规范
- **ORM框架**：MyBatis-Plus 3.x
- **数据库**：MySQL 8.0
- **验证框架**：Spring Validation (JSR-303)
- **文档注解**：Swagger/OpenAPI 3.0
- **工具库**：Lombok

## 领域对象(Domain Entity)生成规范

### 基本要求
1. **包路径结构**：`com.gwm.globalvehicle.{模块名}.domain.{实体名}`
2. **命名规范**：采用驼峰命名法，类名使用名词，表达业务概念
3. **注解使用**：严格按照规范使用MyBatis-Plus和验证注解

### 必需注解和组件
```java
// 类级别注解
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("表名")
@ApiModel(description = "实体描述")

// 字段级别注解
@TableId(value = "id", type = IdType.AUTO)           // 主键字段
@TableField("字段名")                                 // 普通字段
@TableField(value = "字段名", fill = FieldFill.INSERT) // 自动填充字段
@NotNull/@NotBlank/@Size等                           // 验证注解
@ApiModelProperty(value = "字段描述", example = "示例值") // API文档注解
```

### 统一审计字段
每个实体必须包含以下审计字段：
```java
@TableField(value = "create_user_id", fill = FieldFill.INSERT)
@ApiModelProperty(value = "创建人ID", hidden = true)
private Long createUserId;

@TableField(value = "create_time", fill = FieldFill.INSERT)
@ApiModelProperty(value = "创建时间", hidden = true)
private LocalDateTime createTime;

@TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
@ApiModelProperty(value = "更新人ID", hidden = true)
private Long updateUserId;

@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
@ApiModelProperty(value = "更新时间", hidden = true)
private LocalDateTime updateTime;
```

## Mapper接口生成规范

### 基本要求
1. **必须继承BaseMapper**：所有Mapper接口必须继承`com.baomidou.mybatisplus.core.mapper.BaseMapper<T>`
2. **包路径结构**：`com.gwm.globalvehicle.{模块名}.mapper.{实体名}Mapper`
3. **保持简洁**：只继承BaseMapper，不添加任何自定义方法
4. **注解使用**：必须使用@Mapper注解

### 标准模板
```java
package com.gwm.globalvehicle.{模块名}.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gwm.globalvehicle.{模块名}.domain.{实体名};
import org.apache.ibatis.annotations.Mapper;

/**
 * {实体中文名}数据访问接口
 * 继承BaseMapper获取基础CRUD功能
 */
@Mapper
public interface {实体名}Mapper extends BaseMapper<{实体名}> {
    // 基础CRUD功能由BaseMapper提供
    // 自定义方法可后续根据业务需要添加
}
```

### 设计原则
1. **最小化设计**：初始只提供BaseMapper的基础功能
2. **后续扩展**：根据实际业务需求逐步添加自定义方法
3. **避免过度设计**：不预先添加可能用不到的方法
4. **保持一致性**：所有Mapper接口保持相同的结构和风格

## 数据库表映射信息

### 主要业务表
1. **品牌表** (cmp_brand)
2. **生产厂家表** (cmp_manufacturer) 
3. **车型表** (cmp_car_series)
4. **车款表** (cmp_car_model)
5. **竞品车型表** (cmp_competitor_vehicle)
6. **竞品关系表** (cmp_competitor_relation)
7. **竞品车款关注表** (cmp_competitor_model_focus)

### 关联关系
- 品牌 -> 生产厂家 (一对多)
- 生产厂家 -> 车型 (一对多)  
- 车型 -> 车款 (一对多)
- 竞品车型 -> 竞品关系 (一对多)
- 竞品关系 -> 车款关注 (一对多)

## 生成指令模板

### 单表生成指令
```
请根据数据库表{表名}生成最简洁的Java领域对象和基础Mapper接口：

表名：{表名}
业务模块：{模块名}
实体描述：{业务描述}

要求：
1. 严格遵循上述技术规范
2. 领域对象包含完整的字段验证注解和API文档注解
3. Mapper接口只继承BaseMapper，不添加任何自定义方法
4. 生成中文注释
5. 保持代码简洁干净
```

### 批量生成指令
```
请批量生成竞品车型管理系统核心业务表的最简洁版领域对象和基础Mapper接口：

核心表列表：
- cmp_brand (品牌表)
- cmp_manufacturer (生产厂家表)  
- cmp_car_series (车型表)
- cmp_car_model (车款表)
- cmp_competitor_vehicle (竞品车型表)
- cmp_competitor_relation (竞品关系表)
- cmp_competitor_model_focus (竞品车款关注表)

要求：
1. 保持代码风格一致和简洁
2. 所有Mapper接口只继承BaseMapper
3. 专注于基础数据结构定义
4. 遵循所有技术规范
5. 不添加任何自定义方法
```

## 质量检查清单

生成完成后，请检查以下项目：

### 领域对象检查
- [ ] 类名和包路径符合规范
- [ ] 注解使用完整准确
- [ ] 字段类型与数据库匹配
- [ ] 包含审计字段
- [ ] 验证注解配置正确
- [ ] API文档注解完整

### Mapper接口检查  
- [ ] 继承BaseMapper
- [ ] 使用@Mapper注解
- [ ] 包路径正确
- [ ] **不包含任何自定义方法**
- [ ] 注释简洁明确

### 代码质量检查
- [ ] 遵循DRY/KISS/SOLID原则
- [ ] 中文注释完整
- [ ] 代码简洁干净
- [ ] 文件结构清晰 