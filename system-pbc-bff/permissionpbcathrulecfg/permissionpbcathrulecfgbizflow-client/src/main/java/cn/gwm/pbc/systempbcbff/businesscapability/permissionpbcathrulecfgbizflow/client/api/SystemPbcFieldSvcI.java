package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDetailFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDropDownListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldSettingsFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.AdminRemoveUsersOfRoleFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleBindGroupFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.SearchAdminUsersOfRoleFormClientDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * 权限PBC-规则字段服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcFieldSvcI-1702533187760")
@RequestMapping(path = "/PermissionPBCAthFieldCfgBizFlow")
public interface SystemPbcFieldSvcI {

    /**
     * 权限中心-规则字段设置列表 v2-/manage/admin/rule/field/list
     *
     * @param request request
     * @return Response 权限中心-规则字段设置列表
     */
    @PostMapping(path = "/SystemPbcFieldSvc/fieldList/v1")
    Map<String, Object> fieldList(@RequestBody(required = true) RuleFieldListFormClientDTO request);

    /**
     * 权限中心-规则字段设置 v2-/manage/admin/role/rule/field/Settings
     *
     * @param request request
     * @return Response 权限中心-规则字段设置
     */
    @PostMapping(path = "/SystemPbcFieldSvc/fieldSettings/v1")
    Map<String, Object> fieldSettings(@RequestBody(required = true) RuleFieldSettingsFormClientDTO request);

    /**
     * 权限中心-规则字段设置详情 v2-/manage/admin/role/rule/field/detail
     *
     * @param request request
     * @return Response 权限中心-规则字段设置详情
     */
    @PostMapping(path = "/SystemPbcFieldSvc/fieldDetail/v1")
    Map<String, Object> fieldDetail(@RequestBody(required = true) RuleFieldDetailFormClientDTO request);

    /**
     * 权限中心-规则字段下拉列表 v2-/manage/admin/rule/field/dropDown/list
     *
     * @param request request
     * @return Response 权限中心-规则字段下拉列表
     */
    @PostMapping(path = "/SystemPbcFieldSvc/dropDownList/v1")
    Map<String, Object> dropDownList(@RequestBody(required = true) RuleFieldDropDownListFormClientDTO request);

    /**
     * 权限中心-规则字段设置临时 v2-/manage/admin/rule/field/fieldSettingsTemp
     *
     * @param request request
     * @return Response 权限中心-规则字段设置临时
     */
    @PostMapping(path = "/SystemPbcFieldSvc/fieldSettingsTemp/v1")
    Map<String, Object> fieldSettingsTemp(@RequestBody(required = true) RuleFieldSettingsFormClientDTO request);

}
