# GWM Scaffold 代码生成器

## 功能介绍

GWM Scaffold 代码生成器是一个基于数据库表结构自动生成符合脚手架规范代码的工具。它可以根据指定的数据库表，生成完整的 Controller、Service、Mapper、Entity、DTO、VO 等代码文件。

## 主要特性

- 🚀 **一键生成**: 根据数据库表结构一键生成完整的业务代码
- 📋 **符合规范**: 生成的代码完全符合 GWM Scaffold 脚手架规范
- 🎨 **模板化**: 基于 FreeMarker 模板引擎，支持自定义模板
- 🔧 **可配置**: 支持多种配置选项，满足不同需求
- 🌐 **Web界面**: 提供友好的 Web 界面进行操作
- 📝 **代码预览**: 支持生成前预览代码内容

## 生成的代码结构

```
src/main/java/com/your/package/
├── entity/          # 实体类
│   └── User.java
├── mapper/          # Mapper接口
│   └── UserMapper.java
├── service/         # Service接口
│   └── UserService.java
├── service/impl/    # Service实现类
│   └── UserServiceImpl.java
├── controller/      # Controller控制器
│   └── UserController.java
├── dto/             # 数据传输对象
│   ├── UserCreateRequest.java
│   ├── UserUpdateRequest.java
│   └── UserQueryRequest.java
└── vo/              # 视图对象
    └── UserVO.java

src/main/resources/mapper/
└── UserMapper.xml   # MyBatis XML映射文件
```

## 使用方式

### 1. 编程方式使用

```java
@Autowired
private CodeGenerator codeGenerator;

@Test
public void generateCode() {
    GeneratorConfig config = new GeneratorConfig("sys_user", "user");
    config.setBusinessName("用户");
    config.setFunctionName("用户管理");
    config.setPackageName("com.your.project");
    config.setOutputPath("scaffold-example/src/main/java/com/your/project");
    config.setOverwrite(true);
    
    Map<String, Object> result = codeGenerator.generateCode(config);
    System.out.println("生成结果: " + result.get("success"));
}
```

### 2. 配置说明

#### GeneratorConfig 配置项

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| tableName | String | ✅ | - | 数据库表名 |
| moduleName | String | ✅ | - | 模块名称 |
| packageName | String | ❌ | com.gwm.scaffold.example | 包名 |
| author | String | ❌ | scaffold | 作者 |
| outputPath | String | ❌ | scaffold-example/src/main/java/com/gwm/scaffold/example | 输出路径 |
| overwrite | boolean | ❌ | false | 是否覆盖已存在文件 |
| tablePrefix | String | ❌ | - | 表前缀（生成类名时会去掉） |
| businessName | String | ❌ | moduleName | 业务名称 |
| functionName | String | ❌ | moduleName + "管理" | 功能描述 |
| apiPrefix | String | ❌ | /api | API路径前缀 |
| generateSwagger | boolean | ❌ | true | 是否生成Swagger注解 |
| generateValidation | boolean | ❌ | true | 是否生成校验注解 |
| generatePermission | boolean | ❌ | true | 是否生成权限注解 |

### 3. 支持的数据库类型

- MySQL
- H2
- PostgreSQL
- Oracle
- SQL Server
- 其他支持JDBC的数据库

### 4. 字段类型映射

| 数据库类型 | Java类型 | 说明 |
|------------|----------|------|
| VARCHAR, CHAR, TEXT | String | 字符串类型 |
| INT, INTEGER | Integer | 整数类型 |
| BIGINT | Long | 长整数类型 |
| DECIMAL, NUMERIC | BigDecimal | 精确小数类型 |
| DOUBLE | Double | 双精度浮点数 |
| FLOAT | Float | 单精度浮点数 |
| BOOLEAN, BIT | Boolean | 布尔类型 |
| DATE | Date | 日期类型 |
| DATETIME, TIMESTAMP | LocalDateTime | 日期时间类型 |
| BLOB | byte[] | 二进制类型 |

## 模板文件

代码生成器使用 FreeMarker 模板引擎，模板文件位于 `src/main/resources/templates/generator/` 目录下：

- `entity.ftl` - 实体类模板
- `mapper.ftl` - Mapper接口模板
- `mapper-xml.ftl` - Mapper XML模板
- `service.ftl` - Service接口模板
- `service-impl.ftl` - Service实现类模板
- `controller.ftl` - Controller模板
- `create-request-dto.ftl` - 创建请求DTO模板
- `update-request-dto.ftl` - 更新请求DTO模板
- `query-request-dto.ftl` - 查询请求DTO模板
- `vo.ftl` - VO模板

可以根据项目需要修改这些模板文件来自定义生成的代码。

## 注意事项

1. **数据库连接**: 确保应用已正确连接到数据库
2. **表结构**: 建议表有主键，并且字段有适当的注释
3. **文件覆盖**: 开启覆盖选项时会覆盖已存在的文件，请谨慎使用
4. **权限**: 确保有足够的文件系统写权限
5. **编码**: 生成的文件使用 UTF-8 编码

## 常见问题

### Q: 生成的代码编译报错？
A: 检查包名是否正确，确保相关依赖已添加到项目中。

### Q: 如何修改生成的代码风格？
A: 修改对应的 FreeMarker 模板文件即可。

### Q: 支持哪些数据库？
A: 支持 MySQL、H2 等主流数据库，理论上支持所有 JDBC 兼容的数据库。

### Q: 如何自定义模板？
A: 修改 `src/main/resources/templates/generator/` 目录下的 `.ftl` 文件。

## 更新日志

### v1.0.0 (2025-01-17)
- 初始版本发布
- 支持基本的代码生成功能
- 提供完整的模板文件
- 支持多种数据库类型
