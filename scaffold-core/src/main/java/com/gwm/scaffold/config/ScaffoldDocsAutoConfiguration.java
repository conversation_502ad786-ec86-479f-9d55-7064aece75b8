package com.gwm.scaffold.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Scaffold Docs 模块自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.modules.docs", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(name = "com.gwm.scaffold.docs.config.DocsAutoConfiguration")
@Import({
    com.gwm.scaffold.docs.config.DocsAutoConfiguration.class
})
public class ScaffoldDocsAutoConfiguration {

    public ScaffoldDocsAutoConfiguration() {
        log.debug("GWM Scaffold Docs 模块已启用");
    }
}
