package ${packageName}.entity;

<#list tableInfo.imports as import>
import ${import};
</#list>
import com.baomidou.mybatisplus.annotation.*;
import com.gwm.scaffold.core.entity.BaseSuperEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ${tableInfo.tableComment!entityName}实体类
 * 
 * <AUTHOR>
 * @date ${date}
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("${tableInfo.tableName}")
@Schema(description = "${tableInfo.tableComment!entityName}信息")
public class ${entityName} extends BaseSuperEntity {

<#list tableInfo.columns as column>
    <#if !column.createTime && !column.updateTime && !column.createUser && !column.updateUser>
    /**
     * ${column.columnComment!column.javaField}
     */
    <#if column.primaryKey>
    @TableId(type = IdType.AUTO)
    </#if>
    <#if column.logicDelete>
    @TableLogic
    </#if>
    <#if column.version>
    @Version
    </#if>
    @Schema(description = "${column.columnComment!column.javaField}"<#if column.primaryKey>, hidden = true</#if><#if !column.nullable>, requiredMode = Schema.RequiredMode.REQUIRED</#if>)
    private ${column.javaType} ${column.javaField};

    </#if>
</#list>
}
