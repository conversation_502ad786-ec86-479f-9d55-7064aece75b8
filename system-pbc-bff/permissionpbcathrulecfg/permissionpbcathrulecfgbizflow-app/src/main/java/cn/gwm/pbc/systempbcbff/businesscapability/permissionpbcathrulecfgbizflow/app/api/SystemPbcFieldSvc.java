package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDetailFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDropDownListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldSettingsFormClientDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;

import java.util.Map;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-规则字段服务")
public interface SystemPbcFieldSvc {
    /**
     * 权限中心-规则字段设置列表
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则字段设置列表")
    @Method(desc = "权限中心-规则字段设置列表", name = "权限中心-规则字段设置列表 v2-/manage/admin/rule/field/list")
    Map<String, Object> fieldList(@Parameter(name = "request", required = true) RuleFieldListFormClientDTO request);
    /**
     * 权限中心-规则字段设置
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则字段设置")
    @Method(desc = "权限中心-规则字段设置", name = "权限中心-规则字段设置 v2-/manage/admin/role/rule/field/Settings")
    Map<String, Object> fieldSettings(@Parameter(name = "request", required = true) RuleFieldSettingsFormClientDTO request);
    /**
     * 权限中心-规则字段设置详情
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则字段设置详情")
    @Method(desc = "权限中心-规则字段设置详情", name = "权限中心-规则字段设置详情 v2-/manage/admin/role/rule/field/detail")
    Map<String, Object> fieldDetail(@Parameter(name = "request", required = true) RuleFieldDetailFormClientDTO request);
    /**
     * 权限中心-规则字段下拉列表
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则字段下拉列表")
    @Method(desc = "权限中心-规则字段下拉列表", name = "权限中心-规则字段下拉列表 v2-/manage/admin/rule/field/dropDown/list")
    Map<String, Object> dropDownList(@Parameter(name = "request", required = true) RuleFieldDropDownListFormClientDTO request);
    /**
     * 权限中心-规则字段设置临时
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-规则字段设置临时")
    @Method(desc = "权限中心-规则字段设置临时", name = "权限中心-规则字段设置临时 v2-/manage/admin/rule/field/fieldSettingsTemp")
    Map<String, Object> fieldSettingsTemp(@Parameter(name = "request", required = true) RuleFieldSettingsFormClientDTO request);

}
