<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审批详情页 - 线框图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .wireframe {
            background-color: white;
            border: 2px solid #333;
            padding: 20px;
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 30px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #333;
            background: white;
            cursor: pointer;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #ccc;
            padding: 20px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        .info-label {
            width: 120px;
            font-weight: bold;
            color: #666;
        }
        .info-value {
            flex: 1;
        }
        .change-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .change-table th,
        .change-table td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: left;
        }
        .change-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .old-value {
            color: #f44336;
            text-decoration: line-through;
        }
        .new-value {
            color: #4caf50;
            font-weight: bold;
        }
        .attachments {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .attachment-item {
            padding: 8px 12px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            border-radius: 4px;
            cursor: pointer;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline-item {
            position: relative;
            padding: 15px 0;
            border-left: 2px solid #e0e0e0;
            padding-left: 20px;
            margin-bottom: 10px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 20px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #2196f3;
        }
        .timeline-time {
            color: #666;
            font-size: 14px;
        }
        .timeline-content {
            margin-top: 5px;
        }
        .approval-actions {
            background-color: #f9f9f9;
            padding: 20px;
            border: 2px solid #2196f3;
        }
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .btn-approve {
            background-color: #4caf50;
            color: white;
            border: 1px solid #4caf50;
        }
        .btn-reject {
            background-color: #f44336;
            color: white;
            border: 1px solid #f44336;
        }
        .btn-modify {
            background-color: #ff9800;
            color: white;
            border: 1px solid #ff9800;
        }
        .comment-area {
            width: 100%;
            height: 80px;
            padding: 10px;
            border: 1px solid #ccc;
            resize: vertical;
        }
        .form-row {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="wireframe">
        <div class="header">
            <div class="title">价格变动审批 - PCA20240115001</div>
            <button class="btn">返回列表</button>
        </div>

        <div class="section">
            <div class="section-title">申请信息</div>
            <div class="info-row">
                <div class="info-label">申请编号:</div>
                <div class="info-value">PCA20240115001</div>
            </div>
            <div class="info-row">
                <div class="info-label">申请人:</div>
                <div class="info-value">张三</div>
            </div>
            <div class="info-row">
                <div class="info-label">申请时间:</div>
                <div class="info-value">2024-01-15 10:30:25</div>
            </div>
            <div class="info-row">
                <div class="info-label">变更类型:</div>
                <div class="info-value">车型价格调整</div>
            </div>
            <div class="info-row">
                <div class="info-label">紧急程度:</div>
                <div class="info-value">普通</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">变更详情</div>
            <table class="change-table">
                <thead>
                    <tr>
                        <th>变更项目</th>
                        <th>原值</th>
                        <th>新值</th>
                        <th>变动幅度</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>车型名称</td>
                        <td colspan="3">奔驰 C 200 L 运动版</td>
                    </tr>
                    <tr>
                        <td>指导价</td>
                        <td class="old-value">32.52万元</td>
                        <td class="new-value">29.80万元</td>
                        <td>-8.37% (超过10%阈值)</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <div class="section-title">变更原因</div>
            <p>厂商官方降价促销活动，根据市场竞争情况和销售策略调整，此次降价活动有效期至2024年3月31日。降价幅度符合厂商指导政策，有助于提升市场竞争力。</p>
        </div>

        <div class="section">
            <div class="section-title">相关附件</div>
            <div class="attachments">
                <div class="attachment-item">📄 官方降价通知.pdf</div>
                <div class="attachment-item">📄 价格调整函.pdf</div>
                <div class="attachment-item">📄 市场分析报告.xlsx</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">审批历史</div>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-time">2024-01-15 10:30</div>
                    <div class="timeline-content">
                        <strong>张三</strong> 提交价格变动申请
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-time">2024-01-15 14:20</div>
                    <div class="timeline-content">
                        <strong>李四 (数据管理员)</strong> 审核通过<br>
                        <em>审核意见：价格调整合理，附件齐全，建议通过。</em>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-time">待处理</div>
                    <div class="timeline-content">
                        等待超级管理员最终审批...
                    </div>
                </div>
            </div>
        </div>

        <div class="section approval-actions">
            <div class="section-title">审批操作</div>
            <div class="action-buttons">
                <button class="btn btn-approve">通过</button>
                <button class="btn btn-reject">驳回</button>
                <button class="btn btn-modify">要求修改</button>
            </div>
            <div class="form-row">
                <label class="form-label">审批意见:</label>
                <textarea class="comment-area" placeholder="请输入审批意见..."></textarea>
            </div>
            <div class="action-buttons">
                <button class="btn">提交审批</button>
                <button class="btn">保存草稿</button>
            </div>
        </div>
    </div>
</body>
</html>