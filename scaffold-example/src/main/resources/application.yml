# GWM Scaffold 示例应用配置
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: scaffold-example
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 主数据源配置
      url: ********************************************************************************************************************************
      username: cwb_admin
      password: 6XE)Tcr6sH
      driver-class-name: com.mysql.jdbc.Driver
      
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin
      
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
  
  # H2数据库控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.gwm.scaffold.example.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# GWM Scaffold配置
scaffold:
  # 应用信息
  application:
    name: "GWM Scaffold 示例应用"
    version: "1.0.0"
    description: "展示如何使用GWM Scaffold快速构建企业级应用"
    team: "GWM开发团队"
    email: "<EMAIL>"
    homepage: "https://github.com/gwm/scaffold"
  
  # 模块配置
  modules:
    auth: true      # 启用认证模块
    web: true       # 启用Web增强模块
    data: true      # 启用数据访问模块
    tools: true     # 启用工具模块
    monitor: true   # 启用监控模块
    docs: true      # 启用文档模块
  
  # 认证配置
  auth:
    enabled: true
    # 认证鉴权分离配置
    separation:
      enabled: false   # 暂时禁用认证鉴权分离模式，使用传统模式
      default-strategy: REQUIRED  # 默认策略：REQUIRED | OPTIONAL | SKIP | TOKEN_ONLY
      strict-mode: false  # 是否启用严格模式（要求所有接口都有注解）
      path-rules:
        # 自动跳过认证的路径模式
        skip-patterns:
          - /public/**
          - /health/**
          - /info/**
        # 自动可选认证的路径模式
        optional-patterns:
          - /content/**
          - /recommend/**
        # 自动仅验证Token的路径模式
        token-only-patterns:
          - /api/v*/data/**
          - /api/*/external/**
    # 白名单路径
    white-list:
      - /
      - /h2-console/**
      - /druid/**
      - /error
      - /favicon.ico
      - /actuator/**
      - /scaffold/**
      - /swagger-ui.html
      - /doc.html
      - /api/generator/**
      - /api/scaffold/**
      - /generator.html
      - /webjars/**
      - /swagger-resources/**
      - /v2/api-docs
      - /test/auth/public
      - /api/auth-example/public
      - /api/simple-test/public
      - /api/brands/**
    # SSO配置
    sso:
      enabled: true   # 启用真实SSO认证
      platform-code: "1"
      check-token-url: http://sso.test.paas.gwm.cn
      get-origin-url: "http://org.example.com/api/organization"
      domain: http://localhost:8021/ownedVehicle/

openPlatform:
  mdm:
    #人力主数据沙河环境配置
    appKey: 716XXX6S23XX
    appSecret: 5bb361b8435a408099ce68a47f1172f7
    #查询组织信息 （树形结构）
    deptTreeUrl: https://gwapi.gwm.cn/rest/hr/staff/domain/org/tree
    #国家地区主数据
    regionUrl: https://gwapi.gwm.cn/rest/public/mdm/basicdata/countryregion/country-region/page
    #根据组织ID查询所有父级组织ID
    getPrentIds: https://gwapi.gwm.cn/rest/hr/staff/domain/org/getParentGroupIds
    #根据组织ID查询详情
    getOrgDetail: https://gwapi.gwm.cn/rest/hr/staff/domain/org/get
    #通过工号查询组织列表(包含当前组织)
    getOrigin: https://gwapi.gwm.cn/rest/hr/staff/domain/org/get
    #通过工号查询所在组织及上级领导信息 (工号、姓名)
    getLeader: https://gwapi.gwm.cn/rest/hr/staff/domain/employee/leader
    #获取部门下的员工列表
    getEmployee: https://gwapi.gwm.cn/rest/hr/staff/domain/employee/list
    #获取利润中心
    getCostCenter: https://gwapi.gwm.cn/rest/mdm/fac/cost_center/page
    getCompany: https://gwapi.gwm.cn/rest/mdm/incorporatedCompany/incorporated-company-info/page


    # Web配置
  web:
    # 跨域配置
    cors-enabled: true
    cors:
      path-pattern: "/**"
      allowed-origin-patterns: ["*"]
      allowed-methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
      allowed-headers: ["*"]
      allow-credentials: true
      max-age: 3600
    
    # 请求日志配置
    request-log:
      enabled: false
  
  # 数据访问配置
  data:
    mybatis-plus:
      # 防全表更新删除
      block-attack-enabled: true
      # 乐观锁
      optimistic-locker-enabled: true
      # 分页配置
      pagination:
        enabled: true
        db-type: h2
        overflow: true
        max-limit: 500
  
  # 监控配置
  monitor:
    enabled: true
    dashboard:
      enabled: true
      path: "/scaffold/monitor"
      refresh-interval: 5000
      require-auth: false
    metrics:
      jvm: true
      database: true
      business: true
      http: true
  
  # 文档配置
  docs:
    enabled: true

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: alpha
    tags-sorter: alpha
  packages-to-scan: com.gwm.scaffold.example.controller
  paths-to-match: /api/**

# 日志配置
logging:
  level:
    com.gwm.scaffold.example: DEBUG
    com.gwm.scaffold: INFO
    org.springframework.web: INFO
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true
