package com.gwm.scaffold.tools.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 分页工具类
 * 
 * 提供分页数据转换和处理功能
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
public final class PageUtil {

    /**
     * 空分页对象
     */
    public static final IPage<?> EMPTY_PAGE = new Page<>(0, 0);

    /**
     * 方法缓存
     */
    private static final Cache<MethodKey, Method> METHOD_CACHE = Caffeine.newBuilder()
            .maximumSize(128)
            .expireAfterAccess(7 * 24 * 60, TimeUnit.MINUTES)
            .build();

    /**
     * 私有构造方法，防止实例化
     */
    private PageUtil() {
    }

    /**
     * 分页数据转换
     *
     * @param source    源分页对象
     * @param converter 转换函数
     * @param <S>       源数据类型
     * @param <T>       目标数据类型
     * @return 转换后的分页对象
     */
    public static <S, T> IPage<T> convert(IPage<S> source, Function<S, T> converter) {
        if (source == null || source.getRecords() == null) {
            return new Page<>();
        }

        Page<T> target = new Page<>(source.getCurrent(), source.getSize());
        target.setTotal(source.getTotal());

        List<T> targetRecords = new ArrayList<>();
        for (S sourceRecord : source.getRecords()) {
            if (sourceRecord != null) {
                T targetRecord = converter.apply(sourceRecord);
                if (targetRecord != null) {
                    targetRecords.add(targetRecord);
                }
            }
        }
        target.setRecords(targetRecords);

        return target;
    }

    /**
     * 使用转换器的默认方法进行转换
     *
     * @param source    源分页对象
     * @param converter 转换器对象
     * @param <S>       源数据类型
     * @param <T>       目标数据类型
     * @return 转换后的分页对象
     */
    public static <S, T> IPage<T> convert(IPage<S> source, Object converter) {
        return convert(source, converter, "toDTO");
    }

    /**
     * 使用转换器的指定方法进行转换
     *
     * @param source     源分页对象
     * @param converter  转换器对象
     * @param methodName 方法名
     * @param <S>        源数据类型
     * @param <T>        目标数据类型
     * @return 转换后的分页对象
     */
    @SuppressWarnings("unchecked")
    public static <S, T> IPage<T> convert(IPage<S> source, Object converter, String methodName) {
        if (source == null || source.getRecords() == null || converter == null) {
            return new Page<>();
        }

        try {
            MethodKey key = new MethodKey(converter.getClass(), methodName);
            Method method = METHOD_CACHE.get(key, k -> {
                try {
                    Method[] methods = converter.getClass().getDeclaredMethods();
                    for (Method m : methods) {
                        if (m.getName().equals(methodName) && m.getParameterCount() == 1) {
                            m.setAccessible(true);
                            return m;
                        }
                    }
                    throw new NoSuchMethodException("Method not found: " + methodName);
                } catch (Exception e) {
                    log.error("获取转换方法失败", e);
                    return null;
                }
            });

            if (method == null) {
                log.error("转换方法不存在: {}", methodName);
                return new Page<>();
            }

            return convert(source, item -> {
                try {
                    return (T) method.invoke(converter, item);
                } catch (Exception e) {
                    log.error("调用转换方法失败", e);
                    return null;
                }
            });

        } catch (Exception e) {
            log.error("分页数据转换失败", e);
            return new Page<>();
        }
    }

    /**
     * 创建分页对象
     *
     * @param current 当前页
     * @param size    每页大小
     * @param <T>     数据类型
     * @return 分页对象
     */
    public static <T> IPage<T> createPage(long current, long size) {
        return new Page<>(current, size);
    }

    /**
     * 创建分页对象（带数据）
     *
     * @param current 当前页
     * @param size    每页大小
     * @param total   总数
     * @param records 数据列表
     * @param <T>     数据类型
     * @return 分页对象
     */
    public static <T> IPage<T> createPage(long current, long size, long total, List<T> records) {
        Page<T> page = new Page<>(current, size);
        page.setTotal(total);
        page.setRecords(records != null ? records : new ArrayList<>());
        return page;
    }

    /**
     * 创建空分页对象
     *
     * @param <T> 数据类型
     * @return 空分页对象
     */
    @SuppressWarnings("unchecked")
    public static <T> IPage<T> emptyPage() {
        return (IPage<T>) EMPTY_PAGE;
    }

    /**
     * 判断分页是否为空
     *
     * @param page 分页对象
     * @return 是否为空
     */
    public static boolean isEmpty(IPage<?> page) {
        return page == null || page.getRecords() == null || page.getRecords().isEmpty();
    }

    /**
     * 判断分页是否不为空
     *
     * @param page 分页对象
     * @return 是否不为空
     */
    public static boolean isNotEmpty(IPage<?> page) {
        return !isEmpty(page);
    }

    /**
     * 方法缓存键
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class MethodKey {
        private Class<?> clazz;
        private String methodName;
    }
}
