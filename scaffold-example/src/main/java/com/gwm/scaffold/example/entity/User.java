package com.gwm.scaffold.example.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.gwm.scaffold.core.entity.BaseSuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户实体类
 * 
 * 展示如何使用Scaffold的基础实体类和注解
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
@ApiModel(description = "用户信息")
public class User extends BaseSuperEntity {

    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long id;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", required = true, example = "zhangsan")
    private String username;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", required = true, example = "123456")
    private String password;

    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String realName;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String mobile;

    /**
     * 性别 (1:男 2:女)
     */
    @ApiModelProperty(value = "性别", example = "1", notes = "1:男 2:女")
    private Integer gender;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄", example = "25")
    private Integer age;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID", example = "1")
    private Long departmentId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称", example = "技术部")
    private String departmentName;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位", example = "Java开发工程师")
    private String position;

    /**
     * 状态 (1:启用 0:禁用)
     */
    @ApiModelProperty(value = "状态", example = "1", notes = "1:启用 0:禁用")
    private Integer status;

    /**
     * 头像URL
     */
    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "这是一个测试用户")
    private String remark;

    /**
     * 版本号（乐观锁）
     */
    @Version
    @ApiModelProperty(value = "版本号", hidden = true)
    private Integer version;

    /**
     * 逻辑删除标记 (0:未删除 1:已删除)
     */
    @TableLogic
    @ApiModelProperty(value = "删除标记", hidden = true)
    private Integer deleted;
}
