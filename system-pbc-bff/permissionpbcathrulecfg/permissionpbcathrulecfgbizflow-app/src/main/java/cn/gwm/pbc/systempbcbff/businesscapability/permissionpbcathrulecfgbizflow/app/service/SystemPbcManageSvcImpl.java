package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcManageSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.PlatformUserFeign;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.*;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.bizworks.core.runtime.context.UserContext;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import java.lang.Override;
import java.util.Base64;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 权限PBC-管理服务商业能力服务实现类
 *
 * <AUTHOR> 管理员
 * @date 2023.12.13
 */
@Component("PermissionPBCAthRuleCfgBizFlowSystemPbcManageSvcBS")
@Slf4j
public class SystemPbcManageSvcImpl implements SystemPbcManageSvc {

    @Autowired
    private PlatformUserFeign platformUserFeign;

    @Override
    public TestDTO test(TestDTO request) {
    	return null;
	}

    @Override
    public Map<String, Object> queryUser(RequestUserListDTO request) {
        RemotePlatformUserListDTO searchUserListForm = new RemotePlatformUserListDTO();
        BeanUtil.copyProperties(request, searchUserListForm);

        CommonRlt<JSONObject> jsonObjectCommonRlt = platformUserFeign.platformUserList(searchUserListForm);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject result = jsonObjectCommonRlt.getData();
        if (result != null) {
            JSONArray rows = result.getJSONArray("rows");
            List<UserInfoItemVO> roleInfoItemVOS = rows.toJavaList(UserInfoItemVO.class);
            result.put("rows", roleInfoItemVOS);
        }
        return BeanUtil.beanToMap(result);
    }

    @Override
    public void bindPlatform(UserBindPlatformDTO request) {
    	RemotePlatformUserBindDTO param = new RemotePlatformUserBindDTO();
        BeanUtil.copyProperties(request, param);
        CommonRlt<JSONObject> result = platformUserFeign.platformUserBind(param);
        if (!result.isSuccess()) {
            throw new BusinessException(result.getMessage());
        }
	}

    @Override
    public void unbindPlatform(UserUnbindPlatformDTO request) {
    	RemotePlatformUserUnbindDTO param = new RemotePlatformUserUnbindDTO();
        BeanUtil.copyProperties(request, param);
        CommonRlt<JSONObject> result = platformUserFeign.platformUserUnbind(param);
        if (!result.isSuccess()) {
            throw new BusinessException(result.getMessage());
        }
	}

    @Override
    public void save(UserSaveDTO request) {
    	String id = request.getId();
        if (id == null) {
            this.create(request);
        } else {
            this.edit(request);
        }
	}

    @Override
    public Map<String, Object> upload(MultipartFile request) {
    	RemoteUserUploadAvatarDTO remoteDTO = new RemoteUserUploadAvatarDTO();
        remoteDTO.setFile(request);
        CommonRlt<JSONObject> jsonObjectCommonRlt = platformUserFeign.uploadAvatar(request);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt.getData());
	}

    @Override
    public Map<String, Object> detail(UserDetailDTO request) {

        String userCode = UserContext.getUserCode();
        log.info("业务层获取的 userCode = {}", userCode);

        RemoteUserDetailDTO remoteDTO = new RemoteUserDetailDTO();
        BeanUtil.copyProperties(request, remoteDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = platformUserFeign.userDetail(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject userInfo = jsonObjectCommonRlt.getData();
        RemoteUserRoleListDTO remoteUserRoleListDTO = new RemoteUserRoleListDTO();
        BeanUtil.copyProperties(request, remoteUserRoleListDTO);
        CommonRlt<JSONArray> jsonArrayCommonRlt = platformUserFeign.userRoleList(remoteUserRoleListDTO);
        if (!jsonArrayCommonRlt.isSuccess()) {
            throw new BusinessException(jsonArrayCommonRlt.getMessage());
        }
        JSONArray result = jsonArrayCommonRlt.getData();
        userInfo.put("roles", result);
        return BeanUtil.beanToMap(userInfo);
	}

    @Override
    public Map<String, Object> groupUserTree(GroupUserTreeDTO request) {
    	if (request.getGroupId() == null) {
            request.setGroupId(1L);
        }
        RemoteGroupUserTreeDTO remoteDTO = new RemoteGroupUserTreeDTO();
        BeanUtil.copyProperties(request, remoteDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = platformUserFeign.groupUserTree(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt.getData());
	}

    @Override
    public Map<String, Object> searchUserList(SearchUserListDTO request) {
    	RemoteSearchUserListDTO remoteDTO = new RemoteSearchUserListDTO();
        BeanUtil.copyProperties(request, remoteDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = platformUserFeign.searchUserList(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject result = jsonObjectCommonRlt.getData();
        if (result != null) {
            JSONArray rows = result.getJSONArray("rows");
            List<UserInfoItemVO> roleInfoItemVOS = rows.toJavaList(UserInfoItemVO.class);
            result.put("rows", roleInfoItemVOS);
        }
        return result;
	}

    @Override
    public void customUserPasswordReset(CustomUserResetPasswordDTO request) {
    	String currentAccount = UserContext.getUserCode();
        request.setDefaultPassword(Base64.getEncoder().encodeToString("123.Com".getBytes()));
        RemoteCustomUserResetPasswordDTO remoteDTO = new RemoteCustomUserResetPasswordDTO();
        BeanUtil.copyProperties(request, remoteDTO);
        remoteDTO.setOperator_code(currentAccount);
        CommonRlt<JSONObject> jsonObjectCommonRlt = platformUserFeign.resetCustomUserPassword(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}

    private void create(UserSaveDTO request) {
    	RemoteUserCreateDTO remoteDTO = new RemoteUserCreateDTO();
        BeanUtil.copyProperties(request, remoteDTO);
        remoteDTO.setCode(request.getCode());
        remoteDTO.setEnable(1);
        CommonRlt<JSONObject> jsonObjectCommonRlt = platformUserFeign.userCreate(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject userInfo = jsonObjectCommonRlt.getData();
        if (userInfo == null) {
            throw new BusinessException("用户创建失败");
        }
        String id = userInfo.getString("id");
        request.setId(id);
        RemotePlatformUserBindDTO remotePlatformUserBindDTO = new RemotePlatformUserBindDTO();
        BeanUtil.copyProperties(request, remotePlatformUserBindDTO);

        jsonObjectCommonRlt = platformUserFeign.platformUserBind(remotePlatformUserBindDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        String code = userInfo.getString("code");
        RemoteUserBindRoleDTO remoteUserBindRoleDTO = new RemoteUserBindRoleDTO();
        BeanUtil.copyProperties(request, remoteUserBindRoleDTO);
        remoteUserBindRoleDTO.setUser_code(code);
        CommonRlt<JSONObject> objectCommonRlt = platformUserFeign.userBindRole(remoteUserBindRoleDTO);
        if (!objectCommonRlt.isSuccess()) {
            throw new BusinessException(objectCommonRlt.getMessage());
        }
	}

    private void edit(UserSaveDTO request) {
    	RemoteUserDetailDTO remoteDTO = new RemoteUserDetailDTO();
        BeanUtil.copyProperties(request, remoteDTO);

        CommonRlt<JSONObject> jsonObjectCommonRlt = platformUserFeign.userDetail(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject userInfo = jsonObjectCommonRlt.getData();
        RemoteUserEditDTO userEditDTO = new RemoteUserEditDTO();
        userEditDTO.setUser_code(userInfo.getString("code"));
        userEditDTO.setAlias(request.getAlias());
        userEditDTO.setEnable(userInfo.getInteger("enable"));
        userEditDTO.setHead_pic(request.getHeadPic());
        userEditDTO.setMobile(request.getMobile());
        jsonObjectCommonRlt = platformUserFeign.userModify(userEditDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        String code = userInfo.getString("code");
        RemoteUserBindRoleDTO remoteUserBindRoleDTO = new RemoteUserBindRoleDTO();
        BeanUtil.copyProperties(request, remoteUserBindRoleDTO);
        remoteUserBindRoleDTO.setUser_code(code);
        CommonRlt<JSONObject> objectCommonRlt = platformUserFeign.userBindRole(remoteUserBindRoleDTO);
        if (!objectCommonRlt.isSuccess()) {
            throw new BusinessException(objectCommonRlt.getMessage());
        }
	}
}
