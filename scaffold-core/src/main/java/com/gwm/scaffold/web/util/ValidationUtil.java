package com.gwm.scaffold.web.util;

import com.gwm.scaffold.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Collection;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 参数校验工具类
 * 
 * 提供常用的参数校验功能
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
public final class ValidationUtil {

    private static final ValidatorFactory VALIDATOR_FACTORY = Validation.buildDefaultValidatorFactory();
    private static final Validator VALIDATOR = VALIDATOR_FACTORY.getValidator();

    // 常用正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$");
    
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(
            "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");

    /**
     * 校验对象
     *
     * @param object 待校验对象
     * @param groups 校验组
     * @throws ServiceException 校验失败时抛出
     */
    public static void validate(Object object, Class<?>... groups) {
        if (object == null) {
            throw new ServiceException("校验对象不能为null");
        }

        Set<ConstraintViolation<Object>> violations = VALIDATOR.validate(object, groups);
        if (!violations.isEmpty()) {
            StringBuilder message = new StringBuilder("参数校验失败：");
            for (ConstraintViolation<Object> violation : violations) {
                message.append(violation.getMessage()).append("；");
            }
            throw new ServiceException(message.toString(), 400);
        }
    }

    /**
     * 校验字符串非空
     *
     * @param value 字符串值
     * @param name  字段名称
     */
    public static void notBlank(String value, String name) {
        if (!StringUtils.hasText(value)) {
            throw new ServiceException(name + "不能为空", 400);
        }
    }

    /**
     * 校验对象非空
     *
     * @param value 对象值
     * @param name  字段名称
     */
    public static void notNull(Object value, String name) {
        if (value == null) {
            throw new ServiceException(name + "不能为null", 400);
        }
    }

    /**
     * 校验集合非空
     *
     * @param collection 集合
     * @param name       字段名称
     */
    public static void notEmpty(Collection<?> collection, String name) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new ServiceException(name + "不能为空", 400);
        }
    }

    /**
     * 校验数组非空
     *
     * @param array 数组
     * @param name  字段名称
     */
    public static void notEmpty(Object[] array, String name) {
        if (array == null || array.length == 0) {
            throw new ServiceException(name + "不能为空", 400);
        }
    }

    /**
     * 校验字符串长度
     *
     * @param value     字符串值
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @param name      字段名称
     */
    public static void length(String value, int minLength, int maxLength, String name) {
        if (value == null) {
            return;
        }
        int length = value.length();
        if (length < minLength || length > maxLength) {
            throw new ServiceException(name + "长度必须在" + minLength + "-" + maxLength + "之间", 400);
        }
    }

    /**
     * 校验数值范围
     *
     * @param value 数值
     * @param min   最小值
     * @param max   最大值
     * @param name  字段名称
     */
    public static void range(Number value, Number min, Number max, String name) {
        if (value == null) {
            return;
        }
        double doubleValue = value.doubleValue();
        double minValue = min.doubleValue();
        double maxValue = max.doubleValue();
        
        if (doubleValue < minValue || doubleValue > maxValue) {
            throw new ServiceException(name + "必须在" + min + "-" + max + "之间", 400);
        }
    }

    /**
     * 校验邮箱格式
     *
     * @param email 邮箱地址
     * @param name  字段名称
     */
    public static void email(String email, String name) {
        if (!StringUtils.hasText(email)) {
            return;
        }
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            throw new ServiceException(name + "格式不正确", 400);
        }
    }

    /**
     * 校验手机号格式
     *
     * @param mobile 手机号
     * @param name   字段名称
     */
    public static void mobile(String mobile, String name) {
        if (!StringUtils.hasText(mobile)) {
            return;
        }
        if (!MOBILE_PATTERN.matcher(mobile).matches()) {
            throw new ServiceException(name + "格式不正确", 400);
        }
    }

    /**
     * 校验身份证号格式
     *
     * @param idCard 身份证号
     * @param name   字段名称
     */
    public static void idCard(String idCard, String name) {
        if (!StringUtils.hasText(idCard)) {
            return;
        }
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            throw new ServiceException(name + "格式不正确", 400);
        }
    }

    /**
     * 校验正则表达式
     *
     * @param value   字符串值
     * @param pattern 正则表达式
     * @param name    字段名称
     * @param message 错误信息
     */
    public static void pattern(String value, String pattern, String name, String message) {
        if (!StringUtils.hasText(value)) {
            return;
        }
        if (!Pattern.matches(pattern, value)) {
            throw new ServiceException(name + message, 400);
        }
    }

    /**
     * 自定义断言
     *
     * @param condition 条件
     * @param message   错误信息
     */
    public static void isTrue(boolean condition, String message) {
        if (!condition) {
            throw new ServiceException(message, 400);
        }
    }

    /**
     * 自定义断言
     *
     * @param condition 条件
     * @param message   错误信息
     */
    public static void isFalse(boolean condition, String message) {
        if (condition) {
            throw new ServiceException(message, 400);
        }
    }

    private ValidationUtil() {
    }
}
