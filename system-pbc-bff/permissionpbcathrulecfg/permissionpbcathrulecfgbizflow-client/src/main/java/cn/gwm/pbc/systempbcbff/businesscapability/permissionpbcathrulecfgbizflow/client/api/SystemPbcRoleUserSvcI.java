package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserBindBatchDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserUnbindDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 权限PBC-角色用户管理服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcRoleUserSvcI-1702535201474")
@RequestMapping(path = "/PermissionPBCAthRuleCfgBizFlow")
public interface SystemPbcRoleUserSvcI {

    /**
     * 权限中心-查询角色用户列表 system/role/user/list
     *
     * @param request request
     * @return Response 权限中心-查询角色用户列表
     */
    @PostMapping(path = "/SystemPbcRoleUserSvc/list/v1")
    Response<Map<String, Object>> list(@RequestBody(required = true) RoleUserListDTO request);

    /**
     * 权限中心-角色下绑定用户 system/role/user/bind
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcRoleUserSvc/bind/v1")
    Response<Void> bind(@RequestBody(required = true) RoleUserBindDTO request);

    /**
     * 权限中心-角色下绑定用户（批量） system/role/user/bindBatch
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcRoleUserSvc/bindBatch/v1")
    Response<Void> bindBatch(@RequestBody(required = true) RoleUserBindBatchDTO request);

    /**
     * 权限中心-角色下解绑用户 system/role/user/unbind
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcRoleUserSvc/unbind/v1")
    Response<Void> unbind(@RequestBody(required = true) RoleUserUnbindDTO request);
}
