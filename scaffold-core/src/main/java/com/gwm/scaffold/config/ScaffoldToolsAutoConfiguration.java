package com.gwm.scaffold.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * Scaffold Tools 模块自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.modules.tools", havingValue = "true", matchIfMissing = true)
public class ScaffoldToolsAutoConfiguration {

    public ScaffoldToolsAutoConfiguration() {
        log.debug("GWM Scaffold Tools 模块已启用");
    }
}
