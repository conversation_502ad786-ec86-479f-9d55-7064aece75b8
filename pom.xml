<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.gwm.scaffold</groupId>
    <artifactId>scaffold-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>GWM Scaffold Parent</name>
    <description>GWM企业级Spring Boot脚手架父工程</description>
    
    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        
        <!-- Spring Boot版本 -->
        <spring-boot.version>2.7.18</spring-boot.version>
        
        <!-- 第三方依赖版本 -->
        <mybatis-plus.version>*******</mybatis-plus.version>
        <druid.version>1.2.16</druid.version>
        <mysql.version>8.0.33</mysql.version>
        <fastjson.version>1.2.83</fastjson.version>
        <hutool.version>5.8.16</hutool.version>
        <guava.version>31.1-jre</guava.version>
        <okhttp3.version>4.10.0</okhttp3.version>
        <commons-io.version>2.11.0</commons-io.version>
        <easyexcel.version>3.2.1</easyexcel.version>
        <javafaker.version>1.0.2</javafaker.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <swagger.version>2.10.5</swagger.version>
        <swagger-bootstrap-ui.version>1.9.6</swagger-bootstrap-ui.version>
        <swagger-annotations.version>1.6.9</swagger-annotations.version>
        <springdoc.version>1.6.15</springdoc.version>
        <caffeine.version>2.9.3</caffeine.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <jackson.version>2.15.2</jackson.version>
        <jasypt.version>3.0.4</jasypt.version>
        <arthas.version>3.6.7</arthas.version>
        <p6spy.version>1.8.1</p6spy.version>
        <encryption.version>1.1.0</encryption.version>
        <gwm-framework-oss.version>1.1.2</gwm-framework-oss.version>
        <cola.version>4.3.2</cola.version>
        <freemarker.version>2.3.31</freemarker.version>
    </properties>
    
    <modules>
        <module>scaffold-core</module>
        <module>scaffold-example</module>
        <module>system-pbc-bff</module>
    </modules>
    
    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Scaffold Core Module -->
            <dependency>
                <groupId>com.gwm.scaffold</groupId>
                <artifactId>scaffold-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Database -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- Tools -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.javafaker</groupId>
                <artifactId>javafaker</artifactId>
                <version>${javafaker.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>com.gwm</groupId>
                <artifactId>gwm-framework-encryption</artifactId>
                <version>${encryption.version}</version>
            </dependency>

            <dependency>
                <groupId>com.gwm</groupId>
                <artifactId>gwm-framework-oss</artifactId>
                <version>${gwm-framework-oss.version}</version>
            </dependency>

            <!--start cola-->
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-dto</artifactId>
                <version>${cola.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-extension-starter</artifactId>
                <version>${cola.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-statemachine</artifactId>
                <version>${cola.version}</version>
            </dependency>
            <!--end cola-->

            <!-- Documentation -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>${swagger-bootstrap-ui.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!-- Template Engine -->
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    
    <build>
        <pluginManagement>
            <plugins>
                <!-- Maven Compiler Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.10.1</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <compilerArgs>
                            <arg>-Xlint:deprecation</arg>
                            <arg>-Xlint:unchecked</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                
                <!-- Spring Boot Maven Plugin -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
