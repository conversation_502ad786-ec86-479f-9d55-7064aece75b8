package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserBindBatchDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserUnbindDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.domain.Result;
import io.swagger.annotations.ApiOperation;
import java.util.Map;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-角色Team管理服务")
public interface SystemPbcRoleTeamSvc {
    /**
     * 权限中心-查询角色用户列表
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-查询角色用户列表")
    @Method(desc = "权限中心-查询角色用户列表", name = "权限中心-查询角色用户列表 system/role/team/list")
    Map<String, Object> list(@Parameter(name = "request", required = true) RoleUserListDTO request);
    /**
     * 权限中心-角色下绑定用户
     * @param request
     */
    @ReturnValue(desc = "权限中心-角色下绑定用户")
    @Method(desc = "权限中心-角色下绑定用户", name = "权限中心-角色下绑定用户 system/role/team/bind")
    void bind(@Parameter(name = "request", required = true) RoleUserBindDTO request);
    /**
     * 权限中心-角色下解绑用户
     * @param request
     */
    @ReturnValue(desc = "权限中心-角色下解绑用户")
    @Method(desc = "权限中心-角色下解绑用户", name = "权限中心-角色下解绑用户 system/role/team/unbind")
    void unbind(@Parameter(name = "request", required = true) RoleUserUnbindDTO request);
}
