package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field.RuleFieldDropDownListForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field.RuleFieldListForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field.RuleFieldSettingsForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthCenterRequestInterceptor;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteFieldService", url = "EMPTY", configuration = {AuthCenterRequestInterceptor.class})
public interface RemoteFieldService {
    
    /**
     *  规则字段设置列表
     * @param RuleFieldListForm
     * @return CommonRlt
     */
    @PostMapping("/admin/rule/field/list")
    CommonRlt fieldList(@RequestBody RuleFieldListForm RuleFieldListForm);

    /**
     *  规则字段设置
     * @param ruleFieldSettingsForm
     * @return CommonRlt
     */
    @PostMapping("/admin/role/rule/field/Settings")
    CommonRlt fieldSettings(@RequestBody RuleFieldSettingsForm ruleFieldSettingsForm);

    /**
     *  规则字段设置详情
     * @param roleSetId
     * @return CommonRlt
     */
    @GetMapping("/admin/role/rule/field/detail")
     CommonRlt fieldDetail(
                          @ApiParam(name = "role_id", value = "角色ID（虚拟角色）", required = true)
                          @RequestParam(name = "role_id") String roleSetId);

    /**
     *  规则字段下拉列表
     * @param ruleFieldDropDownListForm
     * @return CommonRlt
     */
    @PostMapping("/admin/rule/field/dropDown/list")
     CommonRlt dropDownList(@RequestBody RuleFieldDropDownListForm ruleFieldDropDownListForm);

    /**
     *  规则字段设置临时
     * @param ruleFieldSettingsForm
     * @return CommonRlt<JSONObject>
     */
    @PostMapping("/admin/rule/field/fieldSettingsTemp")
     CommonRlt fieldSettingsTemp(@RequestBody RuleFieldSettingsForm ruleFieldSettingsForm);
}
