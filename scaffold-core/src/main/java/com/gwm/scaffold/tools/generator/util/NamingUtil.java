package com.gwm.scaffold.tools.generator.util;

/**
 * 命名转换工具类
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
public class NamingUtil {

    /**
     * 下划线转驼峰
     *
     * @param name 下划线命名
     * @param firstUpperCase 首字母是否大写
     * @return 驼峰命名
     */
    public static String underlineToCamel(String name, boolean firstUpperCase) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        
        // 去掉表前缀
        name = removeTablePrefix(name);
        
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = firstUpperCase;
        
        for (int i = 0; i < name.length(); i++) {
            char c = name.charAt(i);
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        
        return result.toString();
    }

    /**
     * 驼峰转下划线
     *
     * @param name 驼峰命名
     * @return 下划线命名
     */
    public static String camelToUnderline(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < name.length(); i++) {
            char c = name.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        
        return result.toString();
    }

    /**
     * 去掉表前缀
     */
    private static String removeTablePrefix(String tableName) {
        if (tableName == null || tableName.isEmpty()) {
            return tableName;
        }
        
        // 常见的表前缀
        String[] prefixes = {"sys_", "t_", "tb_", "tbl_"};
        
        for (String prefix : prefixes) {
            if (tableName.toLowerCase().startsWith(prefix)) {
                return tableName.substring(prefix.length());
            }
        }
        
        return tableName;
    }

    /**
     * 首字母大写
     */
    public static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * 首字母小写
     */
    public static String uncapitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toLowerCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * 获取包路径
     */
    public static String getPackagePath(String packageName) {
        if (packageName == null || packageName.isEmpty()) {
            return "";
        }
        return packageName.replace(".", "/");
    }

    /**
     * 获取类名（不含包名）
     */
    public static String getClassName(String fullClassName) {
        if (fullClassName == null || fullClassName.isEmpty()) {
            return fullClassName;
        }
        
        int lastDotIndex = fullClassName.lastIndexOf('.');
        if (lastDotIndex >= 0) {
            return fullClassName.substring(lastDotIndex + 1);
        }
        
        return fullClassName;
    }

    /**
     * 获取包名（不含类名）
     */
    public static String getPackageName(String fullClassName) {
        if (fullClassName == null || fullClassName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fullClassName.lastIndexOf('.');
        if (lastDotIndex >= 0) {
            return fullClassName.substring(0, lastDotIndex);
        }
        
        return "";
    }
}
