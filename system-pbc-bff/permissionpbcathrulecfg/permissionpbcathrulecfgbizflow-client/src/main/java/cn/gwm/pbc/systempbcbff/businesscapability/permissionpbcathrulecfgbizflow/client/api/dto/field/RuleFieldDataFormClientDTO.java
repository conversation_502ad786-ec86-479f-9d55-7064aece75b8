package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleFieldDataFormClientDTO", desc = "null")
@Data
@ApiModel("规则字段数据请求参数对象")
public class RuleFieldDataFormClientDTO {
    /**
     * 字段名称
     */
    @Field(name = "字段名称", desc = "字段名称")
    @ApiModelProperty("字段名称")
    @JsonProperty(value = "field_name")
    private String fieldName;

    /**
     * 字段编码
     */
    @Field(name = "字段编码", desc = "字段编码")
    @ApiModelProperty("字段编码")
    @JsonProperty(value = "field_code")
    private String fieldCode;

    /**
     * 字段类型
     */
    @Field(name = "字段类型", desc = "字段类型")
    @ApiModelProperty("字段类型")
    @JsonProperty(value = "field_type")
    private Integer fieldType;
}
