server:
  port: 8021
  servlet:
    context-path: /ownedVehicle
project:
  name: system-pbc-bff
spring:
  swagger:
    enable: true
    basePackage: cn.gwm.pbc.systempbcbff
    version: 1.0.0
    title: 测试标题
    description: 测试描述
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  h2:
    console:
      settings:
        web-allow-others: true
        trace: true
      path: /h2-console
      enable: true

bizworks:
  appId: system-pbc-bff
  tenantId: replacedByBizworks
feign:
  httpclient:
    enabled: true
    connection-timeout: 3000
    max-connections: 500
    max-connections-per-route: 200

gwm:
  sso:
    url: http://sso.test.paas.gwm.cn
#    platform-code: 8951cded973c9a69c49011190613a8f2
    platform-code: 3a8b7319ae144294db37210c7c0c511c
    pbcId: system-pbc-bff
    appId: ********************************
  permission:
    platform-code: 3a8b7319ae144294db37210c7c0c511c
    platform-secret: 144919005e28f8ca06676959cb627b2e
  open:
    url: https://gwapi.gwm.cn
#    url: http://localhost:8080
#    url: http://dev-pbc.gwm.cn
    env: sandbox
    app-key: 7MX9779096B5
    app-secret: 158062b367a94a1e8e9be1ad12a2502a
auth:
  filter:
    enabled: true
  ignore:
    whites:
      - /system/sso/callback
      - /*/actuator
      - /app/get
      - /SystemPbcSsoCallbackSvc/ssoCallback/v1
center:
  url: http://platform-system-manage.test.paas.gwm.cn