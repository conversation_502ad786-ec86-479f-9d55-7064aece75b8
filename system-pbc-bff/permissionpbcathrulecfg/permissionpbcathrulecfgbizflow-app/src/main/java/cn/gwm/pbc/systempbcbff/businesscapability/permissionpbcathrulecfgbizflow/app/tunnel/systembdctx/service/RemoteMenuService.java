package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.menu.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback.RemoteMenuFallbackFactory;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthRequestInterceptor;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteMenuService", url = "EMPTY",  configuration = {AuthRequestInterceptor.class}, fallback = RemoteMenuFallbackFactory.class)
public interface RemoteMenuService {

    /**
     * 查询菜单列表
     *
     * @param menuListDTO
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/resource/list")
    CommonRlt<JSONObject> menuList(@SpringQueryMap RemoteMenuListDTO menuListDTO);

    /**
     * 编辑菜单
     *
     * @param menuEditDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/resource/edit")
    CommonRlt<JSONObject> menuEdit(@RequestBody RemoteMenuEditDTO menuEditDTO);

    /**
     * 平台下菜单树
     *
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/resource/tree")
    CommonRlt<JSONArray> menuTree();

    /**
     * 查询菜单详情
     *
     * @param menuDetailDTO
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/resource/get")
    CommonRlt<JSONObject> menuDetail(@SpringQueryMap RemoteMenuDetailDTO menuDetailDTO);

    /**
     * 删除菜单
     *
     * @param menuDeleteDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/resource/delete")
    CommonRlt<JSONObject> menuDelete(@RequestBody RemoteMenuDeleteDTO menuDeleteDTO);

    /**
     * 创建菜单
     *
     * @param menuCreateDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/resource/create")
    CommonRlt<JSONObject> menuCreate(@RequestBody RemoteMenuCreateDTO menuCreateDTO);

    /**
     * 用户菜单权限树
     *
     * @param menuUserTreeDTO
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/resource/user/tree")
    CommonRlt<JSONObject> menuUserTree(@SpringQueryMap RemoteMenuUserTreeDTO menuUserTreeDTO);

}
