package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcFieldSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcFieldSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDetailFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDropDownListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldSettingsFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.AdminRemoveUsersOfRoleFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleBindGroupFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.SearchAdminUsersOfRoleFormClientDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 权限PBC-规则字段服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@RestController("PermissionPBCAthFieldCfgBizFlowSystemPbcRuleSvcCON")
public class SystemPbcFieldSvcController implements SystemPbcFieldSvcI {

    @Autowired
    private SystemPbcFieldSvc systemPbcFieldSvc;


    /**
     * 权限中心-规则字段设置列表 v2-/manage/admin/rule/field/list
     *
     * @param request request
     * @return Response 权限中心-规则字段设置列表
     */
    @Override
    public Map<String, Object> fieldList(RuleFieldListFormClientDTO request) {
         return systemPbcFieldSvc.fieldList(request);
    }

    /**
     * 权限中心-规则字段设置 v2-/manage/admin/role/rule/field/Settings
     *
     * @param request request
     * @return Response 权限中心-规则字段设置
     */
    @Override
    public Map<String, Object> fieldSettings(RuleFieldSettingsFormClientDTO request) {
        return systemPbcFieldSvc.fieldSettings(request);
    }

    /**
     * 权限中心-规则字段设置详情 v2-/manage/admin/role/rule/field/detail
     *
     * @param request request
     * @return Response 权限中心-规则字段设置详情
     */
    @Override
    public Map<String, Object> fieldDetail(RuleFieldDetailFormClientDTO request) {
           return systemPbcFieldSvc.fieldDetail(request);
    }

    /**
     * 权限中心-规则字段下拉列表 v2-/manage/admin/rule/field/dropDown/list
     *
     * @param request request
     * @return Response 权限中心-规则字段下拉列表
     */
    @Override
    public Map<String, Object> dropDownList(RuleFieldDropDownListFormClientDTO request) {
        return systemPbcFieldSvc.dropDownList(request);
    }

    /**
     * 权限中心-规则字段设置临时 v2-/manage/admin/rule/field/fieldSettingsTemp
     *
     * @param request request
     * @return Response 权限中心-规则字段设置临时
     */
    @Override
    public Map<String, Object> fieldSettingsTemp(RuleFieldSettingsFormClientDTO request) {
                return systemPbcFieldSvc.fieldSettingsTemp(request);
    }
}
