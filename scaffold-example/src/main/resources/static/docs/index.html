<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GWM Scaffold 脚手架文档</title>
    <link rel="stylesheet" href="../css/docs.css">
</head>
<body>
    <div class="docs-container">
        <!-- 侧边栏导航 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2>🚀 GWM Scaffold</h2>
                <p>企业级脚手架文档</p>
            </div>
            <ul class="nav-menu">
                <li><a href="#overview" class="nav-link active">📋 概览</a></li>
                <li><a href="#quickstart" class="nav-link">🚀 快速开始</a></li>
                <li><a href="#code-generator" class="nav-link">⚡ 代码生成器</a></li>
                <li><a href="#auth-module" class="nav-link">🔐 认证鉴权</a></li>
                <li><a href="#tools" class="nav-link">🛠️ 工具类库</a></li>
                <li><a href="#architecture" class="nav-link">🏗️ 模块架构</a></li>
                <li><a href="#best-practices" class="nav-link">⭐ 最佳实践</a></li>
                <li><a href="#examples" class="nav-link">📚 代码示例</a></li>
                <li><a href="#deployment" class="nav-link">🚀 部署指南</a></li>
            </ul>
            <div class="sidebar-footer">
                <a href="/swagger-ui.html" class="btn btn-outline">API文档</a>
                <a href="/actuator" class="btn btn-outline">监控面板</a>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 概览部分 -->
            <section id="overview" class="content-section active">
                <div class="section-header">
                    <h1>📋 脚手架概览</h1>
                    <p class="section-desc">GWM Spring Boot 脚手架是一个企业级的项目模板，提供完整的基础设施和最佳实践</p>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🔧</div>
                        <h3>开箱即用</h3>
                        <p>引入依赖即可使用，无需复杂配置</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🛠️</div>
                        <h3>工具丰富</h3>
                        <p>17个企业级工具类，覆盖各种开发场景</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔐</div>
                        <h3>认证鉴权</h3>
                        <p>完整的权限控制体系</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🛡️</div>
                        <h3>安全加密</h3>
                        <p>文件加密、HMAC签名等安全工具</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🌐</div>
                        <h3>网络请求</h3>
                        <p>HTTP工具、OkHttp封装</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3>数据处理</h3>
                        <p>字符串、集合、分页等数据处理工具</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>代码生成</h3>
                        <p>一键生成完整的CRUD代码</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>模块化</h3>
                        <p>按需引入功能模块，避免冗余</p>
                    </div>
                </div>

                <div class="tech-stack">
                    <h2>🏗️ 技术栈</h2>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <strong>Spring Boot 2.7.18</strong>
                            <span>企业级应用框架</span>
                        </div>
                        <div class="tech-item">
                            <strong>MyBatis Plus 3.5.6</strong>
                            <span>ORM框架增强</span>
                        </div>
                        <div class="tech-item">
                            <strong>MySQL 8.0+</strong>
                            <span>主数据库</span>
                        </div>
                        <div class="tech-item">
                            <strong>HikariCP + Druid</strong>
                            <span>高性能连接池</span>
                        </div>
                        <div class="tech-item">
                            <strong>Swagger 2.9.2</strong>
                            <span>API文档生成</span>
                        </div>
                        <div class="tech-item">
                            <strong>17个自研工具类</strong>
                            <span>企业级专用工具</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 快速开始部分 -->
            <section id="quickstart" class="content-section">
                <div class="section-header">
                    <h1>🚀 快速开始</h1>
                    <p class="section-desc">几分钟内快速搭建企业级Spring Boot应用</p>
                </div>

                <div class="requirements">
                    <h2>📋 环境要求</h2>
                    <ul>
                        <li>JDK 1.8+</li>
                        <li>Maven 3.6+</li>
                        <li>MySQL 8.0+ (可选，默认使用H2)</li>
                    </ul>
                </div>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>克隆项目</h3>
                            <p>获取脚手架代码到本地</p>
                            <div class="code-block">
                                <code>git clone &lt;repository-url&gt; my-project
cd my-project</code>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>引入依赖</h3>
                            <p>在pom.xml中添加脚手架核心依赖</p>
                            <div class="code-block">
                                <code>&lt;dependency&gt;
  &lt;groupId&gt;com.gwm.scaffold&lt;/groupId&gt;
  &lt;artifactId&gt;scaffold-core&lt;/artifactId&gt;
  &lt;version&gt;1.0.0-SNAPSHOT&lt;/version&gt;
&lt;/dependency&gt;</code>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>配置应用</h3>
                            <p>在application.yml中配置脚手架功能模块</p>
                            <div class="code-block">
                                <code>scaffold:
  enabled: true
  modules:
    auth: true
    web: true
    data: true</code>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h3>启动应用</h3>
                            <p>使用Maven启动Spring Boot应用</p>
                            <div class="code-block">
                                <code>mvn spring-boot:run</code>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="quick-links">
                    <h2>🔗 重要链接</h2>
                    <div class="link-grid">
                        <a href="http://localhost:8080" class="link-card">
                            <span class="link-icon">🏠</span>
                            <div>
                                <strong>应用首页</strong>
                                <small>http://localhost:8080</small>
                            </div>
                        </a>
                        <a href="/swagger-ui.html" class="link-card">
                            <span class="link-icon">📚</span>
                            <div>
                                <strong>API文档</strong>
                                <small>/swagger-ui.html</small>
                            </div>
                        </a>
                        <a href="/actuator" class="link-card">
                            <span class="link-icon">📊</span>
                            <div>
                                <strong>监控面板</strong>
                                <small>/actuator</small>
                            </div>
                        </a>
                        <a href="/h2-console" class="link-card">
                            <span class="link-icon">🗄️</span>
                            <div>
                                <strong>数据库控制台</strong>
                                <small>/h2-console</small>
                            </div>
                        </a>
                    </div>
                </div>
            </section>

            <!-- 代码生成器部分 -->
            <section id="code-generator" class="content-section">
                <div class="section-header">
                    <h1>⚡ 代码生成器</h1>
                    <p class="section-desc">根据数据库表结构自动生成符合脚手架规范的完整CRUD代码</p>
                </div>

                <div class="generator-intro">
                    <div class="intro-card">
                        <h2>🎯 功能特点</h2>
                        <div class="feature-grid">
                            <div class="feature-item">
                                <span class="feature-icon">🚀</span>
                                <h3>一键生成</h3>
                                <p>根据数据库表一键生成完整的CRUD代码</p>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">📋</span>
                                <h3>多层架构</h3>
                                <p>自动生成Entity、Mapper、Service、Controller等各层代码</p>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">🎨</span>
                                <h3>模板定制</h3>
                                <p>基于FreeMarker模板，支持自定义代码模板</p>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">📖</span>
                                <h3>文档集成</h3>
                                <p>自动生成Swagger API文档注解</p>
                            </div>
                        </div>
                    </div>

                    <div class="intro-card">
                        <h2>📁 生成文件类型</h2>
                        <div class="file-types">
                            <div class="file-type">
                                <strong>Entity</strong> - 数据库表对应的Java实体类
                            </div>
                            <div class="file-type">
                                <strong>Mapper</strong> - MyBatis数据访问层接口
                            </div>
                            <div class="file-type">
                                <strong>MapperXml</strong> - MyBatis SQL映射文件
                            </div>
                            <div class="file-type">
                                <strong>Service</strong> - 业务逻辑服务接口
                            </div>
                            <div class="file-type">
                                <strong>ServiceImpl</strong> - 业务逻辑服务实现
                            </div>
                            <div class="file-type">
                                <strong>Controller</strong> - REST API控制器
                            </div>
                            <div class="file-type">
                                <strong>DTO/VO</strong> - 数据传输和视图对象
                            </div>
                        </div>
                    </div>

                    <div class="intro-card">
                        <h2>🔧 使用步骤</h2>
                        <div class="generator-steps">
                            <div class="step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h3>选择数据表</h3>
                                    <p>从数据库表列表中选择要生成代码的表</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h3>配置参数</h3>
                                    <p>设置模块名、包名、作者等基本信息</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h3>预览代码</h3>
                                    <p>可选择预览将要生成的代码内容</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h3>生成代码</h3>
                                    <p>一键生成完整的业务代码</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="generator-action">
                        <a href="/generator.html" class="btn btn-primary btn-large">
                            ⚡ 打开代码生成器
                        </a>
                        <p class="action-desc">立即体验强大的代码生成功能</p>
                    </div>
                </div>
            </section>

            <!-- 认证鉴权部分 -->
            <section id="auth-module" class="content-section">
                <div class="section-header">
                    <h1>🔐 认证鉴权模块</h1>
                    <p class="section-desc">提供完整的用户认证、权限控制功能</p>
                </div>

                <div class="auth-intro">
                    <div class="intro-card">
                        <h2>🎯 核心特性</h2>
                        <div class="feature-grid">
                            <div class="feature-item">
                                <span class="feature-icon">🔐</span>
                                <h3>Token认证</h3>
                                <p>基于SSO登录的用户身份认证机制</p>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">🛡️</span>
                                <h3>权限控制</h3>
                                <p>基于注解的细粒度权限控制</p>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">👥</span>
                                <h3>角色管理</h3>
                                <p>支持角色级别的访问控制</p>
                            </div>
                        </div>
                    </div>

                    <div class="intro-card">
                        <h2>🔧 核心组件</h2>
                        <div class="component-list">
                            <div class="component-item">
                                <strong>LoginInterceptor</strong> - 登录拦截器，拦截请求进行Token验证
                            </div>
                            <div class="component-item">
                                <strong>AuthAspect</strong> - 权限切面，处理权限和角色注解
                            </div>
                            <div class="component-item">
                                <strong>LoginUserUtil</strong> - 用户工具类，基于ThreadLocal的用户信息存储
                            </div>
                            <div class="component-item">
                                <strong>AuthenticateService</strong> - 认证服务，提供Token验证和权限检查
                            </div>
                        </div>
                    </div>

                    <div class="intro-card">
                        <h2>📝 注解说明</h2>
                        <div class="annotation-list">
                            <div class="annotation-item">
                                <h3>@RequirePermission</h3>
                                <p>用于方法级别的权限控制，支持单个或多个权限验证</p>
                                <div class="code-example">
                                    <code>@RequirePermission("user:read")</code>
                                </div>
                            </div>
                            <div class="annotation-item">
                                <h3>@RequireRole</h3>
                                <p>用于方法级别的角色控制，支持单个或多个角色验证</p>
                                <div class="code-example">
                                    <code>@RequireRole("admin")</code>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="intro-card">
                        <h2>⚙️ 配置说明</h2>
                        <div class="config-list">
                            <div class="config-item">
                                <strong>scaffold.auth.enabled</strong>
                                <span>是否启用认证模块（默认：true）</span>
                            </div>
                            <div class="config-item">
                                <strong>scaffold.auth.white-list</strong>
                                <span>白名单路径列表，无需认证即可访问</span>
                            </div>
                            <div class="config-item">
                                <strong>scaffold.auth.sso.platform-code</strong>
                                <span>SSO平台编码</span>
                            </div>
                        </div>
                    </div>

                    <div class="auth-action">
                        <a href="/test/auth/user-info" class="btn btn-primary btn-large">
                            🔐 测试认证功能
                        </a>
                        <p class="action-desc">体验完整的认证鉴权功能</p>
                    </div>
                </div>
            </section>

            <!-- 工具类库部分 -->
            <section id="tools" class="content-section">
                <div class="section-header">
                    <h1>🛠️ 工具类库</h1>
                    <p class="section-desc">17个企业级工具类，覆盖开发中的各种常用功能</p>
                </div>

                <div class="tool-categories">
                    <div class="tool-category">
                        <h2>🔐 安全与加密工具</h2>
                        <div class="tool-list">
                            <div class="tool-item">
                                <h3>EncryptUtil - 文件加密工具</h3>
                                <p>提供文件加密解密、压缩包加密等功能</p>
                                <div class="tool-features">
                                    <span class="feature-tag">文件加密解密</span>
                                    <span class="feature-tag">压缩包加密</span>
                                    <span class="feature-tag">安全存储</span>
                                </div>
                                <div class="code-example">
                                    <code>String encrypted = EncryptUtil.fileEncrypt(file);</code>
                                </div>
                            </div>
                            <div class="tool-item">
                                <h3>HmacSignUtil - HMAC签名工具</h3>
                                <p>API签名认证、安全请求头生成</p>
                                <div class="tool-features">
                                    <span class="feature-tag">API签名认证</span>
                                    <span class="feature-tag">安全请求头</span>
                                    <span class="feature-tag">企业级认证</span>
                                </div>
                                <div class="code-example">
                                    <code>String signature = HmacSignUtil.createSignHeader(appKey, appSecret, uri, method);</code>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tool-category">
                        <h2>🌐 网络请求工具</h2>
                        <div class="tool-list">
                            <div class="tool-item">
                                <h3>HttpRequestUtil - HTTP请求工具</h3>
                                <p>支持POST/PUT/GET请求、文件上传等</p>
                                <div class="tool-features">
                                    <span class="feature-tag">多种请求方式</span>
                                    <span class="feature-tag">文件上传</span>
                                    <span class="feature-tag">自定义头部</span>
                                </div>
                                <div class="code-example">
                                    <code>String response = HttpRequestUtil.post(jsonData, url, headers);</code>
                                </div>
                            </div>
                            <div class="tool-item">
                                <h3>OkHttpUtil - OkHttp封装工具</h3>
                                <p>链式调用、异步请求、文件下载</p>
                                <div class="tool-features">
                                    <span class="feature-tag">链式调用</span>
                                    <span class="feature-tag">异步请求</span>
                                    <span class="feature-tag">连接池管理</span>
                                </div>
                                <div class="code-example">
                                    <code>String response = OkHttpUtil.builder().url(url).post(data).execute();</code>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tool-category">
                        <h2>📊 数据处理工具</h2>
                        <div class="tool-list">
                            <div class="tool-item">
                                <h3>StringUtil - 字符串处理工具</h3>
                                <p>智能类型转换、唯一文件名生成、类型判断</p>
                                <div class="tool-features">
                                    <span class="feature-tag">智能转换</span>
                                    <span class="feature-tag">类型判断</span>
                                    <span class="feature-tag">格式化</span>
                                </div>
                                <div class="code-example">
                                    <code>Object value = StringUtil.convert("123"); // 自动转换为Integer</code>
                                </div>
                            </div>
                            <div class="tool-item">
                                <h3>UUIDUtil - 唯一标识生成工具</h3>
                                <p>UUID生成、唯一编号、随机字符串</p>
                                <div class="tool-features">
                                    <span class="feature-tag">UUID生成</span>
                                    <span class="feature-tag">唯一编号</span>
                                    <span class="feature-tag">随机字符串</span>
                                </div>
                                <div class="code-example">
                                    <code>String orderNo = UUIDUtil.getUniqueNoWithPrefix("ORDER_");</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 其他部分将在后续添加 -->
        </main>
    </div>

    <script src="../js/docs.js"></script>
</body>
</html>
