package com.gwm.scaffold.auth.util;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.gwm.scaffold.auth.domain.LoginUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.expression.ExpressionException;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 登录用户工具类
 * 
 * 基于ThreadLocal的用户信息存储，支持链路追踪和异步场景
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
public final class LoginUserUtil {

    private static final LoginUserInfo DEFAULT_USER =
            LoginUserInfo.builder().userCode("**********").userName("未知用户").build();

    private static final TransmittableThreadLocal<LoginUserInfo> USER = new TransmittableThreadLocal<LoginUserInfo>() {
        @Override
        protected void beforeExecute() {
            LoginUserInfo userInfo = get();
            if (userInfo != null && userInfo.getToken() != null) {
                MDC.put("traceId", userInfo.getToken() + "_" + ThreadLocalRandom.current().nextInt(1000, 10000));
                MDC.put("userId", userInfo.getUserCode());
                MDC.put("userName", userInfo.getUserName());
            }
        }

        @Override
        protected void afterExecute() {
            MDC.clear();
        }
    };

    /**
     * 设置当前登录用户信息
     *
     * @param user 用户信息
     */
    public static void setUser(LoginUserInfo user) {
        if (user == null) {
            throw new ExpressionException("未获取到当前登录人信息，请确认token是否有效!");
        }
        USER.set(user);
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    public static LoginUserInfo getUser() {
        return USER.get() != null ? USER.get() : DEFAULT_USER;
    }

    /**
     * 获取用户名和工号的组合字符串
     *
     * @return 用户名(工号)
     */
    public static String getUserNameCode() {
        LoginUserInfo user = USER.get();
        if (user == null || StringUtils.isBlank(user.getUserCode())) {
            return "";
        }
        return user.getUserName() + "(" + user.getUserCode() + ")";
    }

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    public static Long getUserId() {
        return getUser().getUserId();
    }

    /**
     * 获取用户工号
     *
     * @return 用户工号
     */
    public static String getUserCode() {
        return getUser().getUserCode();
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    public static String getUserName() {
        return getUser().getUserName();
    }

    /**
     * 获取用户Token
     *
     * @return Token
     */
    public static String getToken() {
        return getUser().getToken();
    }

    /**
     * 获取用户IP
     *
     * @return IP地址
     */
    public static String getIp() {
        return getUser().getIp();
    }

    /**
     * 获取请求URL
     *
     * @return 请求URL
     */
    public static String getUrl() {
        return getUser().getUrl();
    }

    /**
     * 判断是否为正式员工
     *
     * @return 是否正式员工
     */
    public static Integer getIsFormal() {
        return getUser().getIsFormal();
    }

    /**
     * 获取性别
     *
     * @return 性别
     */
    public static Integer getSex() {
        return getUser().getSex();
    }

    /**
     * 获取职务名称
     *
     * @return 职务名称
     */
    public static String getDutyName() {
        return getUser().getDutyName();
    }

    /**
     * 获取部门名称
     *
     * @return 部门名称
     */
    public static String getDepartmentName() {
        return getUser().getDepartmentName();
    }

    /**
     * 获取部门ID
     *
     * @return 部门ID
     */
    public static Long getDepartmentId() {
        return getUser().getDepartmentId();
    }

    /**
     * 获取单位名称
     *
     * @return 单位名称
     */
    public static String getUnitName() {
        return getUser().getUnitName();
    }

    /**
     * 获取单位ID
     *
     * @return 单位ID
     */
    public static Long getUnitId() {
        return getUser().getUnitId();
    }

    /**
     * 清除当前用户信息
     */
    public static void clear() {
        USER.remove();
        MDC.clear();
    }

    /**
     * 判断当前是否有登录用户
     *
     * @return 是否有登录用户
     */
    public static boolean hasUser() {
        return USER.get() != null && !DEFAULT_USER.equals(USER.get());
    }

    /**
     * 判断当前用户是否具有指定角色
     *
     * @param roleCode 角色编码
     * @return 是否具有角色
     */
    public static boolean hasRole(String roleCode) {
        LoginUserInfo user = getUser();
        return user.getRoles() != null && user.getRoles().containsKey(roleCode);
    }

    private LoginUserUtil() {
    }
}
