{
	"info": {
		"_postman_id": "brand-management-api",
		"name": "品牌管理API测试集合",
		"description": "品牌管理模块的完整API测试用例，包括CRUD操作、状态管理、统计查询等功能",
		"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
	},
	"item": [
		{
			"name": "品牌查询接口",
			"item": [
				{
					"name": "分页查询品牌列表",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/api/brands?current=1&size=10&name=&country=&status=",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands"],
							"query": [
								{
									"key": "current",
									"value": "1",
									"description": "当前页码"
								},
								{
									"key": "size",
									"value": "10",
									"description": "每页数量"
								},
								{
									"key": "name",
									"value": "",
									"description": "品牌名称（模糊查询）"
								},
								{
									"key": "country",
									"value": "",
									"description": "所属国家"
								},
								{
									"key": "status",
									"value": "",
									"description": "状态（ACTIVE/INACTIVE）"
								}
							]
						}
					},
					"response": []
				},
				{
					"name": "根据ID查询品牌详情",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/api/brands/{{brandId}}",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands", "{{brandId}}"]
						}
					},
					"response": []
				},
				{
					"name": "获取启用品牌列表",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/api/brands/active",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands", "active"]
						}
					},
					"response": []
				},
				{
					"name": "按国别统计品牌数量",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/api/brands/statistics/country",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands", "statistics", "country"]
						}
					},
					"response": []
				}
			]
		},
		{
			"name": "品牌创建接口",
			"item": [
				{
					"name": "创建品牌-完整信息",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"// 验证响应状态码",
									"pm.test(\"Status code is 200\", function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"// 验证响应结构",
									"pm.test(\"Response has correct structure\", function () {",
									"    var jsonData = pm.response.json();",
									"    pm.expect(jsonData).to.have.property('code');",
									"    pm.expect(jsonData).to.have.property('data');",
									"    pm.expect(jsonData.data).to.have.property('id');",
									"});",
									"",
									"// 保存品牌ID到环境变量",
									"if (pm.response.code === 200) {",
									"    var jsonData = pm.response.json();",
									"    if (jsonData.data && jsonData.data.id) {",
									"        pm.environment.set(\"brandId\", jsonData.data.id);",
									"        console.log(\"Brand ID saved: \" + jsonData.data.id);",
									"    }",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"name\": \"奔驰\",\n  \"englishName\": \"Mercedes-Benz\",\n  \"country\": \"德国\",\n  \"foundedYear\": 1926,\n  \"website\": \"https://www.mercedes-benz.com\",\n  \"logoUrl\": \"https://example.com/logo/benz.png\",\n  \"description\": \"德国豪华汽车品牌，以高品质、高性能和高技术水平著称\",\n  \"status\": \"ACTIVE\",\n  \"sortWeight\": 100\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/api/brands",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands"]
						}
					},
					"response": []
				},
				{
					"name": "创建品牌-最小信息",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"name\": \"特斯拉\",\n  \"country\": \"美国\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/api/brands",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands"]
						}
					},
					"response": []
				},
				{
					"name": "创建品牌-重复名称（应失败）",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"// 验证应该返回错误",
									"pm.test(\"Should return error for duplicate name\", function () {",
									"    pm.response.to.have.status(500);",
									"});",
									"",
									"pm.test(\"Error message contains duplicate info\", function () {",
									"    var jsonData = pm.response.json();",
									"    pm.expect(jsonData.message).to.include('已存在');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"name\": \"奔驰\",\n  \"country\": \"中国\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/api/brands",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands"]
						}
					},
					"response": []
				}
			]
		},
		{
			"name": "品牌更新接口",
			"item": [
				{
					"name": "更新品牌信息",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test(\"Status code is 200\", function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test(\"Brand updated successfully\", function () {",
									"    var jsonData = pm.response.json();",
									"    pm.expect(jsonData.data.description).to.include('更新后');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"description\": \"更新后的品牌描述信息\",\n  \"website\": \"https://www.mercedes-benz.com.cn\",\n  \"sortWeight\": 200\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/api/brands/{{brandId}}",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands", "{{brandId}}"]
						}
					},
					"response": []
				},
				{
					"name": "更新品牌状态",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/api/brands/{{brandId}}/status?status=INACTIVE",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands", "{{brandId}}", "status"],
							"query": [
								{
									"key": "status",
									"value": "INACTIVE",
									"description": "新状态：ACTIVE或INACTIVE"
								}
							]
						}
					},
					"response": []
				}
			]
		},
		{
			"name": "品牌删除接口",
			"item": [
				{
					"name": "删除单个品牌",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test(\"Status code is 200\", function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test(\"Delete successful\", function () {",
									"    var jsonData = pm.response.json();",
									"    pm.expect(jsonData.message).to.include('成功');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"url": {
							"raw": "{{baseUrl}}/api/brands/{{brandId}}",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands", "{{brandId}}"]
						}
					},
					"response": []
				},
				{
					"name": "批量删除品牌",
					"request": {
						"method": "DELETE",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "[1, 2, 3]"
						},
						"url": {
							"raw": "{{baseUrl}}/api/brands/batch",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands", "batch"]
						}
					},
					"response": []
				}
			]
		}
		},
		{
			"name": "数据校验测试",
			"item": [
				{
					"name": "创建品牌-缺少必填字段",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test(\"Should return validation error\", function () {",
									"    pm.response.to.have.status(400);",
									"});",
									"",
									"pm.test(\"Error message contains validation info\", function () {",
									"    var jsonData = pm.response.json();",
									"    pm.expect(jsonData.message).to.include('不能为空');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"englishName\": \"Test Brand\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/api/brands",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands"]
						}
					},
					"response": []
				},
				{
					"name": "创建品牌-无效年份",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test(\"Should return validation error\", function () {",
									"    pm.response.to.have.status(500);",
									"});",
									"",
									"pm.test(\"Error message contains year validation\", function () {",
									"    var jsonData = pm.response.json();",
									"    pm.expect(jsonData.message).to.include('年份');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"name\": \"未来品牌\",\n  \"country\": \"中国\",\n  \"foundedYear\": 2030\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/api/brands",
							"host": ["{{baseUrl}}"],
							"path": ["api", "brands"]
						}
					},
					"response": []
				}
			]
		}
	],
	"event": [
		{
			"listen": "prerequest",
			"script": {
				"type": "text/javascript",
				"exec": [
					"// 设置默认环境变量",
					"if (!pm.environment.get(\"baseUrl\")) {",
					"    pm.environment.set(\"baseUrl\", \"http://localhost:8080\");",
					"}"
				]
			}
		}
	],
	"variable": [
		{
			"key": "baseUrl",
			"value": "http://localhost:8080",
			"type": "string"
		},
		{
			"key": "brandId",
			"value": "1",
			"type": "string"
		},
		{
			"key": "testBrandId",
			"value": "",
			"type": "string"
		}
	]
}
