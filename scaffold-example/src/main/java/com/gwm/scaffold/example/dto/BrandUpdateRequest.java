package com.gwm.scaffold.example.dto;

import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 品牌更新请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "品牌更新请求")
public class BrandUpdateRequest {

    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "品牌ID不能为空")
    @Min(value = 1, message = "品牌ID必须大于0")
    private Long id;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    @Size(min = 2, max = 50, message = "品牌名称长度必须在2-50个字符之间")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-&()（）]+$", message = "品牌名称只能包含中英文、数字、空格和常用符号")
    private String name;

    /**
     * 品牌英文名称
     */
    @Schema(description = "品牌英文名称")
    @Size(min = 2, max = 100, message = "品牌英文名称长度必须在2-100个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9\\s\\-&()]+$", message = "品牌英文名称只能包含英文、数字、空格和常用符号")
    private String englishName;

    /**
     * 所属国家
     */
    @Schema(description = "所属国家")
    @Size(max = 50, message = "所属国家长度不能超过50个字符")
    private String country;

    /**
     * 成立年份
     */
    @Schema(description = "成立年份")
    @Min(value = 1800, message = "成立年份不能早于1800年")
    @Max(value = 2025, message = "成立年份不能晚于当前年份")
    private Integer foundedYear;

    /**
     * 官网地址
     */
    @Schema(description = "官网地址")
    @Size(max = 255, message = "官网地址长度不能超过255个字符")
    @Pattern(regexp = "^(https?://).*", message = "官网地址必须以http://或https://开头")
    private String website;

    /**
     * LOGO图片URL
     */
    @Schema(description = "LOGO图片URL")
    @Size(max = 500, message = "LOGO图片URL长度不能超过500个字符")
    private String logoUrl;

    /**
     * 品牌简介
     */
    @Schema(description = "品牌简介")
    @Size(max = 1000, message = "品牌简介长度不能超过1000个字符")
    private String description;

    /**
     * 状态（ACTIVE-启用，INACTIVE-禁用）
     */
    @Schema(description = "状态（ACTIVE-启用，INACTIVE-禁用）")
    @Pattern(regexp = "^(ACTIVE|INACTIVE)$", message = "状态只能是ACTIVE或INACTIVE")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    @Min(value = 1, message = "sortWeight必须大于0")
    private Integer sortWeight;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

}
