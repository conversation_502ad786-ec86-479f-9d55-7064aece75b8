package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("平台用户查询参数对象")
public class SearchUserListForm implements Serializable {

    @JsonIgnore
    private String platformCode;

    /**
     * 登录账号
     */
    @ApiModelProperty("登录账号")
    @JsonProperty(value = "user_code")
    private String userCode;

    /**
     * 别名
     */
    @ApiModelProperty("别名")
    @JsonProperty(value = "alias")
    private String alias;

    /**
     * 是否启用（0否，1是）
     */
    @ApiModelProperty("是否启用（0否，1是）")
    private Integer enable;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    @JsonProperty(value = "mobile")
    private String mobile;

    /**
     * 用户类型（1正式用户，2自定义用户）
     */
    @ApiModelProperty("用户类型（1正式用户，2自定义用户）")
    @JsonProperty(value = "user_type")
    private Integer userType;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    @JsonProperty(value = "page")
    private Integer page = 1;

    /**
     * 条数
     */
    @ApiModelProperty("条数")
    @JsonProperty(value = "size")
    private Integer size = 10;

    /**
     * 用户创建时间，yyyy-MM-dd
     */
    @ApiModelProperty("用户创建时间，yyyy-MM-dd")
    @JsonProperty(value = "create_time")
    private String createTime;

    /**
     * 用户信息更新时间，yyyy-MM-dd
     */
    @ApiModelProperty("用户信息更新时间，yyyy-MM-dd")
    @JsonProperty(value = "update_time")
    private String updateTime;

    /**
     * 用户姓名
     */
    @ApiModelProperty("用户姓名")
    @JsonProperty(value = "user_name")
    private String userName;

    public void setAlias(String alias) {
        if (StringUtils.isNotEmpty(alias) && !Objects.equals(UniversalConstant.NULL_VALUE, alias) && alias.contains(UniversalConstant.PERCENT)) {
            this.alias = alias.replaceAll("%", "\\\\%")
                    .replaceAll("_", "\\\\_");
        } else {
            this.alias = alias;
        }
    }
}
