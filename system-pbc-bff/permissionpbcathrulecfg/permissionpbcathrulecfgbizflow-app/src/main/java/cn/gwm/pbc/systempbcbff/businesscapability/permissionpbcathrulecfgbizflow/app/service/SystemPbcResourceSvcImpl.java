package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcResourceSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource.AddAdminResourceForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource.DelAdminResourceForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource.EditAdminResourceForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule.RuleListFormDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteAdminRuleService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteResourceService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule.RuleListFormClientDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
/**
 * <AUTHOR>
 */
@Component("systemPbcResourceSvcImpl")
public class SystemPbcResourceSvcImpl implements SystemPbcResourceSvc {

    @Resource
    private RemoteResourceService remoteResourceService;

    @Value("${gwm.sso.platform-code}")
    private String platformCode;


    @Override
    public Map<String, Object> asyncLoadingResourceTree(AsyncClientDTO request) {
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteResourceService.asyncLoadingResourceTree(
                platformCode,
                request.getCategory(),
                request.getId()
        );
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }

        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> searchResourceTree(SearchClientDTO request) {
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteResourceService.searchResourceTree(
                platformCode,
                request.getCategory(),
                request.getName()
        );
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> tree(TreeClientDTO request) {
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteResourceService.searchTree(
                platformCode,
                request.getCategory()
        );
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> create(AddAdminResourceFormClientDTO request) {

        AddAdminResourceForm remote = new AddAdminResourceForm();
        BeanUtil.copyProperties(request, remote);
        remote.setPlatformCode(platformCode);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteResourceService.add(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> editResource(EditAdminResourceFormClientDTO request) {

        EditAdminResourceForm remote = new EditAdminResourceForm();
        BeanUtil.copyProperties(request, remote);
        remote.setPlatformCode(platformCode);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteResourceService.editResource(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> delResource(DelAdminResourceFormClientDTO request) {
        DelAdminResourceForm remote = new DelAdminResourceForm();
        BeanUtil.copyProperties(request, remote);
        remote.setPlatformCode(platformCode);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteResourceService.delResource(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> searchResourceById(DetailResourceFormClientDTO request) {
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteResourceService.searchResourceById(
                request.getId(),
                platformCode
        );
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }
}
