package com.gwm.scaffold.example.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.example.service.OpenClientService;
import com.gwm.scaffold.tools.util.HmacSignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.springframework.http.HttpMethod.GET;
import static org.springframework.http.HttpMethod.POST;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * openAPI接口工
 *
 * <AUTHOR> 王明莹
 * @date 2025-02-28
 */
@Slf4j
@Service
public class OpenClientServiceImpl implements OpenClientService {

//    @Value("${openPlatform.mdm.appKey}")
//    private String appKey;
//
//    @Value("${openPlatform.mdm.appSecret}")
//    private String appSecret;

    private String appKey = "6B9SXX57STXX";
    //    @Value("${openPlatform.mdm.appSecret}")
    private String appSecret = "61f535c2f29c40178f1502bdde9d69ea";

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 发送get请求
     *
     * @param url      url地址
     * @param paramMap 参数
     * @return 响应结果
     */
    public JSONObject getOpenClientResult(String url, Map<String, Object> paramMap) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            builder.queryParam(entry.getKey(), entry.getValue());
        }
        url = builder.build().toUriString();
        HttpHeaders multiValueMap = new HttpHeaders();
        multiValueMap.setAll(HmacSignUtil.createSignHeader(appKey, appSecret, url, GET.name(), Collections.emptyMap()));
        HttpEntity<Map<String, String>> formEntity = new HttpEntity<>(null, multiValueMap);
        ResponseEntity<JSONObject> response = null;
        long l1 = System.currentTimeMillis();
        try {
            response = restTemplate.exchange(url, GET, formEntity, JSONObject.class);
            log.info("请求：{} 入参：{}，响应：{}，时间：{} 毫秒 ", url, JSON.toJSONString(paramMap), JSON.toJSONString(response), (System.currentTimeMillis() - l1));
            return response.getBody();
        } catch (Exception e) {
            log.error("请求：{} 异常，入参：{}，响应：{}，时间：{} 毫秒", url, JSON.toJSONString(paramMap), JSON.toJSONString(response), (System.currentTimeMillis() - l1), e);
            throw new ServiceException("请求：{} 异常");
        }
    }

    /**
     * 发送post请求
     *
     * @param url      url地址
     * @param paramMap 参数
     * @return 响应结果
     */
    public JSONObject postOpenClientResult(String url, Map<String, Object> paramMap) {
        HttpHeaders multiValueMap = new HttpHeaders();
        multiValueMap.setAll(HmacSignUtil.createSignHeader(appKey, appSecret, url, POST.name(), Collections.emptyMap()));
        multiValueMap.setContentType(APPLICATION_JSON);
        JSONObject result = null;
        long l1 = System.currentTimeMillis();
        try {
            result = restTemplate.exchange(url, POST, new HttpEntity<>(paramMap, multiValueMap), JSONObject.class).getBody();
            log.info("请求：{} 入参：{}，响应：{}，时间：{} 毫秒", url, JSON.toJSONString(paramMap), JSON.toJSONString(result), (System.currentTimeMillis() - l1));
            return result;
        } catch (Exception e) {
            log.error("请求：{} 失败 入参：{}，响应：{}，时间：{} 毫秒， 异常", url, JSON.toJSONString(paramMap), JSON.toJSONString(result), (System.currentTimeMillis() - l1), e);
            throw new ServiceException("访问第三方接口响应异常：" + e.getMessage());
        }
    }




    public static void main(String[] args) {
        System.out.println(1687300800000L - System.currentTimeMillis());
        OpenClientServiceImpl openClientService = new OpenClientServiceImpl();

        // 手动初始化 RestTemplate，因为在 main 方法中 Spring 依赖注入不生效
        openClientService.initRestTemplate();

        Map<String, Object> paramMap = new HashMap<>(1, 1);
        paramMap.put("group_id", "10000520");

        JSONObject result = openClientService.getOpenClientResult("https://gwapi.gwm.cn/rest/hr/staff/domain/org/get", paramMap);
        System.out.println(result);
    }
    /**
     * 手动初始化 RestTemplate（用于 main 方法测试）
     */
    private void initRestTemplate() {
        if (this.restTemplate == null) {
            this.restTemplate = createRestTemplate();
        }
    }

    /**
     * 创建 RestTemplate 实例（与 BeanConfig 中的配置保持一致）
     */
    private RestTemplate createRestTemplate() {
        org.springframework.http.client.SimpleClientHttpRequestFactory factory =
                new org.springframework.http.client.SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(15000);
        factory.setReadTimeout(30000);
        RestTemplate restTemplate = new RestTemplate(factory);

        // 设置自定义的消息转换器为fastjson
        com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter fastJsonHttpMessageConverter =
                new com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter();
        com.alibaba.fastjson.support.config.FastJsonConfig fastJsonConfig =
                new com.alibaba.fastjson.support.config.FastJsonConfig();
        fastJsonConfig.setDateFormat("yyyy-MM-dd HH:mm:ss");
        fastJsonConfig.setCharset(java.nio.charset.StandardCharsets.UTF_8);
        fastJsonConfig.setSerializerFeatures(com.alibaba.fastjson.serializer.SerializerFeature.WriteMapNullValue);
        fastJsonConfig.setFeatures(com.alibaba.fastjson.parser.Feature.OrderedField);
        fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
        fastJsonHttpMessageConverter.setSupportedMediaTypes(
                java.util.Arrays.asList(APPLICATION_JSON,
                        org.springframework.http.MediaType.valueOf("application/json;charset=UTF-8")));
        restTemplate.getMessageConverters().add(0, fastJsonHttpMessageConverter);

        return restTemplate;
    }

}

