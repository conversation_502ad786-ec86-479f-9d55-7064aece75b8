package com.gwm.scaffold.tools.vo;

import com.alibaba.cola.dto.DTO;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 用户信息dto
 *
 * <AUTHOR>
 * @date 2025/6/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class UserInfoVO extends DTO {

    /**
     * 用户id
     */
    private Long id;

    /**
     * 用户工号
     */
    private String code;

    /**
     * 用户名称
     */
    private String alias;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 是否启用：0.否；1.是
     */
    private Integer enable;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 用户类型（1正式用户，2自定义用户）
     */
    @JSONField(name = "user_type")
    private Integer userType;

    /**
     * 所属组织id
     */
    @JSONField(name = "group_id")
    private Long groupId;

    /**
     * 创建人编码
     */
    @JSONField(name = "creator_code")
    private String creatorCode;

    /**
     * 创建人名称
     */
    @JSONField(name = "creator_name")
    private String creatorName;

    /**
     * 创建时间
     */
    @JSONField(name = "create_time")
    private Date createTime;

    /**
     * 更新人编码
     */
    @JSONField(name = "updator_code")
    private String updatorCode;

    /**
     * 更新人名称
     */
    @JSONField(name = "updator_name")
    private String updatorName;

    /**
     * 更新时间
     */
    @JSONField(name = "update_time")
    private Date updateTime;

    /**
     * 角色列表
     */
    private List<RoleVO> roles;
}
