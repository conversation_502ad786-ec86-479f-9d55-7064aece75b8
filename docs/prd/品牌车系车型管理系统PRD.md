# 品牌、车系、车型主数据管理系统产品需求文档（PRD）

## 1. 产品概述

### 1.1 产品背景
为了规范化管理汽车品牌、车系、车型信息，提供统一的数据管理平台，支持业务系统的数据查询和统计分析需求。系统需要确保数据的完整性、一致性和准确性，同时提供灵活的管理功能和严格的业务规则控制。

### 1.2 产品目标
- 建立完整的汽车品牌、车系、车型数据体系
- 提供便捷的数据管理界面和严格的业务规则控制
- 支持多维度数据查询和统计分析
- 为其他业务系统提供标准化数据接口
- 确保数据操作的安全性和可追溯性

### 1.3 目标用户
- **超级管理员**：拥有所有权限，负责系统配置和用户管理
- **数据管理员**：负责品牌、车系、车型信息的维护和审核
- **业务人员**：查询和统计相关数据，只读权限
- **系统集成方**：通过API接口获取数据

### 1.4 用户权限矩阵

| 功能模块 | 超级管理员 | 数据管理员 | 业务人员 | API用户 |
|---------|-----------|-----------|---------|---------|
| 品牌管理 | 增删改查 | 增改查 | 查 | 查 |
| 车系管理 | 增删改查 | 增改查 | 查 | 查 |
| 车型管理 | 增删改查 | 增改查 | 查 | 查 |
| 数据删除 | ✓ | 需审批 | ✗ | ✗ |
| 批量操作 | ✓ | ✓ | ✗ | ✗ |
| 系统配置 | ✓ | ✗ | ✗ | ✗ |
| 用户管理 | ✓ | ✗ | ✗ | ✗ |

## 2. 功能需求

### 2.1 品牌管理

#### 2.1.1 品牌信息维护
**功能描述**：管理汽车品牌基础信息

**字段定义**：
- 品牌ID：系统自动生成的唯一标识
- 品牌名称：品牌中文名称（必填，2-50字符）
- 品牌英文名称：品牌英文名称（可选，2-100字符）
- 品牌LOGO：品牌标识图片（支持jpg/png/gif，最大2MB）
- 品牌简介：品牌描述信息（可选，最大1000字符）
- 国别：品牌所属国家（必填，下拉选择）
- 成立时间：品牌成立年份（可选，1800-当前年份）
- 官网地址：品牌官方网站（可选，URL格式）
- 状态：启用/禁用（默认启用）
- 排序权重：用于列表排序（数字，默认100）
- 创建人：记录创建用户
- 创建时间：记录创建时间
- 更新人：记录最后更新用户
- 更新时间：记录最后更新时间

**业务规则**：

1. **新增品牌规则**：
   - 品牌名称不能重复（中文名称全局唯一）
   - 品牌英文名称不能重复（如果填写）
   - 必须选择国别
   - 成立时间不能晚于当前年份
   - 官网地址必须是有效的URL格式
   - 新增时默认状态为"启用"

2. **编辑品牌规则**：
   - 不能修改为已存在的品牌名称
   - 如果品牌下有车系，不能修改品牌的核心信息（名称、国别）
   - 只有创建人或管理员可以编辑
   - 编辑时需要记录操作日志

3. **删除品牌规则**：
   - 只有超级管理员可以删除品牌
   - 删除前必须检查关联关系：
     - 如果品牌下有车系，不允许删除
     - 如果品牌被其他业务系统引用，不允许删除
   - 删除操作需要二次确认
   - 删除后数据进入回收站，30天后物理删除
   - 删除操作记录详细日志

4. **状态管理规则**：
   - 禁用品牌时，其下所有车系和车型自动禁用
   - 启用品牌时，需要手动选择启用哪些车系
   - 状态变更需要记录操作原因

**校验规则**：

1. **品牌名称校验**：
   - 长度限制：2-50字符
   - 字符限制：只能包含中英文、数字、空格和常用符号（-、&、()、（））
   - 唯一性检查：品牌名称在系统中必须唯一
   - 错误提示：实时显示具体的错误信息

2. **成立时间校验**：
   - 年份范围：1800年至当前年份
   - 格式要求：四位数字年份
   - 可选字段：允许为空

3. **官网地址校验**：
   - 格式要求：必须是有效的URL格式（http://或https://开头）
   - 可选字段：允许为空
   - 可访问性：建议验证URL的可访问性

**功能点**：
- 新增品牌信息（带完整校验）
- 编辑品牌信息（权限控制）
- 删除品牌信息（级联检查）
- 批量导入品牌信息（Excel模板）
- 品牌状态管理（级联影响）
- 品牌排序管理
- 操作日志查看

#### 2.1.2 品牌查询
**功能描述**：支持多条件查询品牌信息

**查询条件**：
- 品牌名称（模糊查询）
- 国别
- 状态
- 创建时间范围

**查询结果**：
- 支持分页显示
- 支持排序（按创建时间、品牌名称等）
- 支持导出Excel

### 2.2 车系管理

#### 2.2.1 车系信息维护
**功能描述**：管理汽车车系基础信息

**字段定义**：
- 车系ID：系统自动生成的唯一标识
- 车系名称：车系中文名称（必填，2-50字符）
- 车系英文名称：车系英文名称（可选，2-100字符）
- 所属品牌：关联品牌信息（必填，下拉选择启用状态的品牌）
- 车系类型：轿车/SUV/MPV/跑车/皮卡等（必填）
- 级别：A级/B级/C级/D级等（必填）
- 上市时间：车系首次上市时间（必填，不能晚于当前日期）
- 停产时间：车系停产时间（可选，必须晚于上市时间）
- 车系图片：车系代表图片（支持jpg/png/gif，最大5MB）
- 车系简介：车系描述信息（可选，最大2000字符）
- 指导价区间：最低价-最高价（可选，用于参考）
- 状态：在售/停售/即将上市（默认即将上市）
- 排序权重：用于列表排序（数字，默认100）
- 创建人：记录创建用户
- 创建时间：记录创建时间
- 更新人：记录最后更新用户
- 更新时间：记录最后更新时间

**业务规则**：

1. **新增车系规则**：
   - 同一品牌下车系名称不能重复
   - 车系英文名称全局不能重复（如果填写）
   - 必须选择启用状态的品牌
   - 上市时间不能晚于当前日期
   - 停产时间必须晚于上市时间
   - 指导价区间：最低价不能大于最高价
   - 新增时默认状态为"即将上市"

2. **编辑车系规则**：
   - 不能修改为同品牌下已存在的车系名称
   - 如果车系下有车型，不能修改车系的核心信息（名称、品牌、类型）
   - 不能修改为已禁用的品牌
   - 只有创建人或管理员可以编辑
   - 编辑时需要记录操作日志

3. **删除车系规则**：
   - 只有超级管理员可以删除车系
   - 删除前必须检查关联关系：
     - 如果车系下有车型，不允许删除
     - 如果车系被其他业务系统引用，不允许删除
   - 删除操作需要二次确认
   - 删除后数据进入回收站，30天后物理删除
   - 删除操作记录详细日志

4. **状态管理规则**：
   - 停售车系时，其下所有车型自动停售
   - 启用车系时，需要手动选择启用哪些车型
   - 状态变更需要记录操作原因
   - 品牌禁用时，其下所有车系自动禁用

**校验规则**：

1. **车系名称校验**：
   - 长度限制：2-50字符
   - 字符限制：只能包含中英文、数字、空格和常用符号（-、&、()、（））
   - 唯一性检查：同一品牌下车系名称必须唯一
   - 错误提示：实时显示具体的错误信息

2. **时间校验**：
   - 上市时间：不能晚于当前日期
   - 停产时间：必须晚于上市时间（如果填写）
   - 日期格式：标准日期格式验证

3. **价格区间校验**：
   - 逻辑校验：最低价不能大于最高价
   - 数值校验：价格不能为负数
   - 可选字段：允许为空

4. **品牌关联校验**：
   - 状态检查：只能选择启用状态的品牌
   - 存在性检查：品牌必须在系统中存在
   - 实时验证：品牌状态变更时同步检查

**功能点**：
- 新增车系信息（带完整校验）
- 编辑车系信息（权限控制）
- 删除车系信息（级联检查）
- 批量导入车系信息（Excel模板）
- 车系状态管理（级联影响）
- 车系排序管理
- 操作日志查看
- 车系复制功能（复制基础信息到新车系）

#### 2.2.2 车系查询
**功能描述**：支持多条件查询车系信息

**查询条件**：
- 车系名称（模糊查询）
- 所属品牌
- 车系类型
- 级别
- 状态
- 上市时间范围

### 2.3 车型管理

#### 2.3.1 车型信息维护
**功能描述**：管理具体车型配置信息

**字段定义**：
- 车型ID：系统自动生成的唯一标识
- 车型名称：完整车型名称（必填，5-100字符）
- 所属车系：关联车系信息（必填，下拉选择启用状态的车系）
- 年款：车型年款（必填，2000-当前年份+2）
- 排量：发动机排量（可选，0.1-10.0L）
- 功率：最大功率（可选，单位kW）
- 扭矩：最大扭矩（可选，单位N·m）
- 变速箱：手动/自动/CVT等（必填）
- 档位数：变速箱档位数（可选，3-10档）
- 驱动方式：前驱/后驱/四驱（必填）
- 燃料类型：汽油/柴油/混动/纯电动等（必填）
- 车身结构：三厢/两厢/SUV/MPV等（必填）
- 座位数：座位数量（必填，2-9座）
- 车身尺寸：长×宽×高（可选，单位mm）
- 轴距：轴距长度（可选，单位mm）
- 整备质量：车辆重量（可选，单位kg）
- 油箱容积：油箱容量（可选，单位L）
- 综合油耗：百公里油耗（可选，单位L/100km）
- 指导价：厂商指导价格（必填，单位万元）
- 上市时间：具体上市时间（必填）
- 停售时间：停售时间（可选，必须晚于上市时间）
- 车型图片：车型图片（支持jpg/png/gif，最大10MB，最多5张）
- 配置详情：详细配置信息（JSON格式存储）
- 状态：在售/停售/即将上市（默认即将上市）
- 排序权重：用于列表排序（数字，默认100）
- 创建人：记录创建用户
- 创建时间：记录创建时间
- 更新人：记录最后更新用户
- 更新时间：记录最后更新时间

**业务规则**：

1. **新增车型规则**：
   - 同一车系下车型名称不能重复
   - 必须选择启用状态的车系
   - 年款不能超过当前年份+2年
   - 上市时间不能晚于当前日期+1年
   - 停售时间必须晚于上市时间
   - 指导价必须大于0
   - 排量、功率、扭矩等技术参数必须合理
   - 车身尺寸必须符合实际情况
   - 新增时默认状态为"即将上市"

2. **编辑车型规则**：
   - 不能修改为同车系下已存在的车型名称
   - 不能修改为已禁用的车系
   - 如果车型已上市，不能修改核心技术参数
   - 只有创建人或管理员可以编辑
   - 编辑时需要记录操作日志
   - 价格变动超过10%需要审批

3. **删除车型规则**：
   - 只有超级管理员可以删除车型
   - 删除前必须检查关联关系：
     - 如果车型被其他业务系统引用，不允许删除
     - 如果车型有销售记录，不允许删除
   - 删除操作需要二次确认
   - 删除后数据进入回收站，30天后物理删除
   - 删除操作记录详细日志

4. **状态管理规则**：
   - 车系停售时，其下所有车型自动停售
   - 车型状态变更需要记录操作原因
   - 即将上市→在售：需要确认上市时间
   - 在售→停售：需要填写停售原因

**校验规则**：

1. **车型名称校验**：
   - 长度限制：5-100字符
   - 字符限制：只能包含中英文、数字、空格和常用符号（-、&、()、（）、.）
   - 唯一性检查：同一车系下车型名称必须唯一
   - 错误提示：实时显示具体的错误信息

2. **年款校验**：
   - 年份范围：2000年至当前年份+2年
   - 格式要求：四位数字年份
   - 业务逻辑：不能设置过于超前的年款

3. **技术参数校验**：
   - 排量范围：0.1-10.0L（如果填写）
   - 功率范围：10-1000kW（如果填写）
   - 扭矩范围：50-2000N·m（如果填写）
   - 参数合理性：各参数之间应符合汽车工程常识

4. **车身尺寸校验**：
   - 车长范围：2000-8000mm
   - 车宽范围：1200-2500mm
   - 车高范围：1000-3000mm
   - 逻辑校验：轴距不能大于等于车长
   - 合理性检查：尺寸应符合实际车辆规格

5. **价格校验**：
   - 数值范围：必须大于0，不超过1000万元
   - 格式要求：支持小数点后两位
   - 业务逻辑：价格应在合理区间内

6. **车系关联校验**：
   - 状态检查：只能选择在售或即将上市的车系
   - 存在性检查：车系必须在系统中存在
   - 实时验证：车系状态变更时同步检查

7. **时间逻辑校验**：
   - 上市时间：不能晚于当前日期+1年
   - 停售时间：必须晚于上市时间（如果填写）
   - 日期格式：标准日期格式验证

**功能点**：
- 新增车型信息（带完整校验）
- 编辑车型信息（权限控制）
- 删除车型信息（级联检查）
- 批量导入车型信息（Excel模板）
- 车型配置对比（最多5个车型）
- 车型状态管理（级联影响）
- 车型复制功能（复制配置到新车型）
- 车型图片管理（上传、删除、排序）
- 价格变动审批流程
- 操作日志查看

#### 2.3.2 车型查询
**功能描述**：支持多条件查询车型信息

**查询条件**：
- 车型名称（模糊查询）
- 所属品牌
- 所属车系
- 年款
- 价格区间
- 排量区间
- 燃料类型
- 驱动方式
- 状态
- 上市时间范围

## 3. 接口需求

### 3.1 查询接口

#### 3.1.1 品牌查询接口
```
GET /api/brands
参数：
- name: 品牌名称（可选）
- country: 国别（可选）
- status: 状态（可选）
- page: 页码（默认1）
- size: 每页数量（默认10）

返回：品牌列表及分页信息
```

#### 3.1.2 车系查询接口
```
GET /api/series
参数：
- name: 车系名称（可选）
- brandId: 品牌ID（可选）
- type: 车系类型（可选）
- level: 级别（可选）
- status: 状态（可选）
- page: 页码（默认1）
- size: 每页数量（默认10）

返回：车系列表及分页信息
```

#### 3.1.3 车型查询接口
```
GET /api/models
参数：
- name: 车型名称（可选）
- brandId: 品牌ID（可选）
- seriesId: 车系ID（可选）
- yearModel: 年款（可选）
- minPrice: 最低价格（可选）
- maxPrice: 最高价格（可选）
- fuelType: 燃料类型（可选）
- driveType: 驱动方式（可选）
- status: 状态（可选）
- page: 页码（默认1）
- size: 每页数量（默认10）

返回：车型列表及分页信息
```

### 3.2 统计接口

#### 3.2.1 品牌统计接口
```
GET /api/statistics/brands
返回：
- 品牌总数
- 各国别品牌数量分布
- 品牌状态分布
```

#### 3.2.2 车系统计接口
```
GET /api/statistics/series
返回：
- 车系总数
- 各品牌车系数量分布
- 车系类型分布
- 车系级别分布
- 车系状态分布
```

#### 3.2.3 车型统计接口
```
GET /api/statistics/models
返回：
- 车型总数
- 各品牌车型数量分布
- 各车系车型数量分布
- 价格区间分布
- 燃料类型分布
- 驱动方式分布
- 车型状态分布
```

#### 3.2.4 综合统计接口
```
GET /api/statistics/overview
返回：
- 品牌、车系、车型总数概览
- 最新上市车型
- 热门品牌排行
- 价格分布统计
```

## 4. 页面需求与线框图原型

### 4.1 品牌管理页面

#### 4.1.1 品牌列表页
**线框图原型**：[品牌列表页线框图](wireframes/brand-list.html)

**页面功能**：
- 品牌信息列表展示，支持分页显示
- 多条件搜索：品牌名称、国别、状态、创建时间范围
- 批量操作：启用、禁用、删除选中的品牌
- 单个操作：编辑、删除单个品牌
- 支持按列排序（品牌名称、创建时间等）

**交互说明**：
- 支持多选批量操作
- 点击品牌名称进入详情页
- 删除操作需要二次确认弹窗
- 支持按列排序
- 分页加载，每页10/20/50条可选

#### 4.1.2 品牌新增/编辑页
**线框图原型**：[品牌新增/编辑页线框图](wireframes/brand-form.html)

**页面功能**：
- 品牌基础信息录入：名称、英文名称、国别、成立时间、官网
- 品牌LOGO上传和预览
- 品牌简介编辑
- 状态设置和排序权重配置
- 实时字段校验和错误提示

**校验提示**：
- 实时校验，错误信息显示在字段下方
- 必填字段标红星号
- 保存前进行完整性校验

#### 4.1.3 品牌详情页
**线框图原型**：[品牌详情页线框图](wireframes/brand-detail.html)

**页面功能**：
- 品牌完整信息展示：基础信息、LOGO、简介
- 关联车系列表展示和管理
- 操作日志查看
- 快速编辑和返回功能

**交互说明**：
- 展示品牌的所有详细信息
- 关联车系支持查看和编辑
- 操作日志按时间倒序显示
- 支持直接跳转到车系管理

### 4.2 车系管理页面

#### 4.2.1 车系列表页
**线框图原型**：[车系列表页线框图](wireframes/series-list.html)

**页面功能**：
- 车系信息列表展示，支持分页显示
- 多条件搜索：车系名称、品牌、类型、级别、状态
- 批量操作：上市、停售、删除选中的车系
- 单个操作：编辑、删除单个车系
- 支持按列排序（车系名称、创建时间等）

**交互说明**：
- 支持多选批量操作
- 点击车系名称进入详情页
- 删除操作需要二次确认弹窗
- 支持按列排序
- 分页加载，每页10/20/50条可选

#### 4.2.2 车系新增/编辑页
**线框图原型**：[车系新增/编辑页线框图](wireframes/series-form.html)

**页面功能**：
- 车系基础信息录入：名称、英文名称、品牌、类型、级别
- 时间信息设置：上市时间、停产时间
- 价格区间配置：最低价、最高价
- 车系图片上传和预览
- 车系简介编辑
- 状态设置和排序权重配置

**交互说明**：
- 品牌下拉框只显示启用状态的品牌
- 停产时间必须晚于上市时间
- 价格区间支持可选填写
- 实时字段校验和错误提示

### 4.3 车型管理页面

#### 4.3.1 车型列表页
**线框图原型**：[车型列表页线框图](wireframes/model-list.html)

**页面功能**：
- 车型信息列表展示，支持分页显示
- 多条件搜索：车型名称、品牌、车系、年款、价格区间、燃料类型、状态
- 车型对比功能：支持选择多个车型进行对比
- 单个操作：编辑单个车型
- 支持按列排序（车型名称、价格、创建时间等）

**交互说明**：
- 支持多选车型进行对比
- 点击车型名称进入详情页
- 对比功能最多支持5个车型
- 支持按列排序
- 分页加载，每页10/20/50条可选

#### 4.3.2 车型新增/编辑页
**线框图原型**：[车型新增/编辑页线框图](wireframes/model-form.html)

**页面功能**：
- 分标签页展示：基础信息、技术参数、配置详情、图片管理
- 车型基础信息录入：名称、车系、年款、指导价
- 动力系统参数：排量、功率、扭矩、变速箱、档位数
- 车身信息设置：驱动方式、燃料类型、车身结构、座位数
- 尺寸重量参数：车身尺寸、轴距、整备质量、油箱容积、综合油耗
- 时间状态管理：上市时间、停售时间、状态

**交互说明**：
- 标签页切换展示不同类别的信息
- 车系下拉框只显示启用状态的车系
- 停售时间必须晚于上市时间
- 实时字段校验和错误提示
- 支持技术参数的合理性检查

#### 4.3.3 车型对比页
**线框图原型**：[车型对比页线框图](wireframes/model-compare.html)

**页面功能**：
- 车型选择器：支持动态添加和移除对比车型
- 参数对比表格：按类别展示各项技术参数
- 优势标识：自动标识各项参数的最优值
- 导出功能：支持导出对比结果、打印、分享链接

**交互说明**：
- 最多支持5个车型同时对比
- 参数按类别分组显示
- 最优值用不同颜色标识
- 支持添加和移除对比车型
- 提供多种导出和分享方式

### 4.4 统计分析页面

#### 4.4.1 数据概览页
**线框图原型**：[数据概览页线框图](wireframes/dashboard.html)

**页面功能**：
- 核心指标展示：品牌总数、车系总数、车型总数、在售车型数量
- 品牌分布统计：按国别展示品牌分布情况（饼图）
- 车型分布统计：按燃料类型展示车型分布情况（柱状图）
- 价格分布统计：按价格区间展示车型分布情况（直方图）
- 最新上市车型：展示最近上市的车型列表

**交互说明**：
- 支持数据刷新和导出功能
- 图表支持交互式展示
- 最新车型列表支持点击查看详情
- 统计数据实时更新

#### 4.4.2 品牌分析页
**页面功能**：
- 品牌维度的详细数据分析
- 品牌车系数量分布
- 品牌市场占有率分析
- 品牌发展趋势图表

#### 4.4.3 车型分析页
**页面功能**：
- 车型维度的详细数据分析
- 车型价格分布统计
- 车型技术参数分析
- 车型市场趋势分析

## 5. 审批流程管理

### 5.1 价格变动审批流程

#### 5.1.1 触发条件
- 车型指导价变动超过10%
- 车系价格区间调整超过15%
- 批量价格调整操作

#### 5.1.2 审批流程
```
发起申请 → 数据管理员审核 → 超级管理员审批 → 执行变更 → 通知相关方
```

**流程详情**：
1. **发起申请**：
   - 填写价格变动申请表
   - 说明变动原因和依据
   - 上传相关证明文件

2. **数据管理员审核**：
   - 检查数据准确性
   - 验证变动合理性
   - 提出审核意见

3. **超级管理员审批**：
   - 最终审批决定
   - 可以驳回或要求修改
   - 审批通过后自动执行

4. **执行变更**：
   - 系统自动更新价格信息
   - 记录变更日志
   - 生成变更通知

5. **通知相关方**：
   - 邮件通知相关用户
   - 系统内消息提醒
   - API接口推送变更事件

#### 5.1.3 审批相关页面

**审批列表页**
**线框图原型**：[审批列表页线框图](wireframes/approval-list.html)

**页面功能**：
- 分标签页展示：待审批、已审批、我的申请
- 多条件搜索：申请类型、申请人、申请时间范围
- 审批申请列表：申请编号、类型、申请人、时间、变更内容、状态
- 快速操作：审批、查看申请详情
- 状态标识：待审批、已通过、已驳回等不同颜色标识

**审批详情页**
**线框图原型**：[审批详情页线框图](wireframes/approval-detail.html)

**页面功能**：
- 申请信息展示：申请编号、申请人、申请时间、变更类型、紧急程度
- 变更详情对比：变更项目、原值、新值、变动幅度对比表格
- 变更原因说明：详细的变更原因和背景描述
- 相关附件：支持查看和下载相关证明文件
- 审批历史时间线：完整的审批流程记录和审批意见
- 审批操作区域：通过、驳回、要求修改等操作按钮
- 审批意见录入：支持审批意见的文本输入和保存

**审批申请页**
**线框图原型**：[审批申请页线框图](wireframes/approval-form.html)

**页面功能**：
- 申请基本信息：申请类型、标题、紧急程度、期望完成时间
- 变更详情表格：支持添加多个变更项，包含变更对象、字段、原值、新值
- 申请原因说明：详细说明和影响评估
- 附件上传：支持多种格式文件上传，文件列表管理
- 审批流程预览：显示当前申请的审批流程和预计时间
- 智能提醒：根据变更内容自动提示审批要求和注意事项

### 5.2 数据删除审批流程

#### 5.2.1 触发条件
- 数据管理员申请删除品牌/车系/车型
- 批量删除操作
- 级联删除影响范围较大

#### 5.2.2 审批流程
```
删除申请 → 影响评估 → 超级管理员审批 → 执行删除 → 备份归档
```

## 6. 数据导入导出管理

### 6.1 批量导入功能

#### 6.1.1 导入模板设计

**品牌导入模板**：
```
品牌名称* | 英文名称 | 国别* | 成立时间 | 官网地址 | 品牌简介 | 状态
奔驰     | Mercedes-Benz | 德国 | 1926 | https://... | ... | 启用
宝马     | BMW | 德国 | 1916 | https://... | ... | 启用
```

**车系导入模板**：
```
车系名称* | 英文名称 | 品牌名称* | 车系类型* | 级别* | 上市时间* | 停产时间 | 最低价 | 最高价 | 状态
C级      | C-Class | 奔驰 | 轿车 | B级 | 2020-01-01 |  | 30 | 50 | 在售
3系      | 3 Series | 宝马 | 轿车 | B级 | 2019-05-15 |  | 28 | 45 | 在售
```

**车型导入模板**：
```
车型名称* | 车系名称* | 年款* | 排量 | 功率 | 扭矩 | 变速箱* | 驱动方式* | 燃料类型* | 指导价* | 状态
C 200 L运动版 | C级 | 2024 | 1.5 | 150 | 280 | 9AT | 后驱 | 汽油 | 32.52 | 在售
```

#### 6.1.2 导入校验规则

**数据格式校验**：
- 必填字段不能为空
- 数据类型必须正确
- 数值范围必须合理
- 日期格式必须正确

**业务逻辑校验**：
- 品牌名称不能重复
- 车系必须属于存在的品牌
- 车型必须属于存在的车系
- 关联数据状态必须有效

**导入流程**：
```
上传文件 → 格式校验 → 数据预览 → 业务校验 → 确认导入 → 执行导入 → 结果反馈
```

#### 6.1.3 导入结果页面功能
**页面功能**：
- 导入文件信息展示：文件名、导入时间、导入用户
- 导入统计信息：总记录数、成功数量、失败数量、成功率
- 失败记录详情：行号、数据内容、失败原因列表
- 操作功能：下载失败记录、重新导入失败记录、导出成功记录

### 6.2 数据导出功能

#### 6.2.1 导出格式支持
- Excel格式（.xlsx）
- CSV格式（.csv）
- JSON格式（.json）
- PDF格式（.pdf，用于报表）

#### 6.2.2 导出配置功能
**页面功能**：
- 导出范围选择：当前页数据、当前查询结果、全部数据、自定义范围
- 导出字段配置：基础信息、详细信息、时间信息、关联信息（可多选）
- 导出格式选择：Excel (.xlsx)、CSV (.csv)、JSON (.json)
- 其他选项设置：包含表头、包含统计信息、压缩文件
- 文件名自定义：支持自定义导出文件名

## 7. 系统配置管理

### 7.1 基础配置

#### 7.1.1 系统参数配置功能
**页面功能**：
- 基础设置：系统名称、系统版本、公司名称、联系邮箱
- 分页设置：默认每页条数、最大每页条数配置
- 文件上传设置：各类图片最大尺寸限制、允许的文件格式
- 审批设置：价格变动审批阈值、自动审批金额上限、审批超时时间
- 数据保留设置：回收站保留天数、操作日志保留天数、导出文件保留天数

#### 7.1.2 枚举值管理功能
**页面功能**：
- 枚举类型选择：支持切换不同的枚举类型（车系类型、级别、燃料类型等）
- 枚举值列表：展示代码、名称、排序、状态等信息
- 枚举值操作：新增、编辑、删除、批量编辑功能
- 排序管理：支持拖拽或数字输入调整排序
- 状态管理：启用/禁用枚举值
- 批量操作：保存更改、恢复默认设置

### 7.2 用户权限管理

#### 7.2.1 角色管理
```
┌─────────────────────────────────────────────────────────────────┐
│ 角色管理                                        [新增角色]       │
├─────────────────────────────────────────────────────────────────┤
│ 角色名称        | 用户数 | 状态 | 创建时间    | 操作            │
│ 超级管理员      | 2     | 启用 | 2024-01-01 | [编辑] [权限]    │
│ 数据管理员      | 5     | 启用 | 2024-01-01 | [编辑] [权限]    │
│ 业务人员        | 20    | 启用 | 2024-01-01 | [编辑] [权限]    │
│ API用户         | 3     | 启用 | 2024-01-01 | [编辑] [权限]    │
├─────────────────────────────────────────────────────────────────┤
│ 权限配置 - 数据管理员                                           │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 功能模块      | 查看 | 新增 | 编辑 | 删除 | 审批 | 导入导出  │ │
│ │ 品牌管理      | ☑   | ☑   | ☑   | ☐   | ☐   | ☑        │ │
│ │ 车系管理      | ☑   | ☑   | ☑   | ☐   | ☐   | ☑        │ │
│ │ 车型管理      | ☑   | ☑   | ☑   | ☐   | ☐   | ☑        │ │
│ │ 统计分析      | ☑   | ☐   | ☐   | ☐   | ☐   | ☑        │ │
│ │ 系统配置      | ☐   | ☐   | ☐   | ☐   | ☐   | ☐        │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 8. 操作日志与审计

### 8.1 操作日志记录

#### 8.1.1 日志记录范围
- 所有数据的增删改操作
- 用户登录登出记录
- 权限变更记录
- 系统配置修改记录
- 文件上传下载记录
- API调用记录

#### 8.1.2 日志字段定义
- 日志ID：唯一标识
- 操作时间：精确到秒
- 操作用户：用户ID和用户名
- 操作类型：CREATE/UPDATE/DELETE/LOGIN等
- 操作模块：品牌/车系/车型/系统配置等
- 操作对象：具体的数据ID和名称
- 操作前数据：JSON格式存储
- 操作后数据：JSON格式存储
- 操作结果：成功/失败
- 失败原因：错误信息
- IP地址：操作来源IP
- 用户代理：浏览器信息

#### 8.1.3 日志查询页面
```
┌─────────────────────────────────────────────────────────────────┐
│ 操作日志                                        [导出] [清理]    │
├─────────────────────────────────────────────────────────────────┤
│ 查询条件：                                                      │
│ 操作时间: [2024-01-01] 至 [2024-01-31]                         │
│ 操作用户: [下拉选择_______] 操作类型: [下拉选择_______]          │
│ 操作模块: [下拉选择_______] 关键词: [_____________] [搜索]       │
├─────────────────────────────────────────────────────────────────┤
│ 操作时间        | 用户  | 类型 | 模块   | 对象     | 结果 | 详情 │
│ 2024-01-15 10:30| 张三  | 编辑 | 品牌管理| 奔驰     | 成功 | [查看]│
│ 2024-01-15 10:25| 李四  | 新增 | 车型管理| C200L    | 成功 | [查看]│
│ 2024-01-15 10:20| 王五  | 删除 | 车系管理| A4       | 失败 | [查看]│
├─────────────────────────────────────────────────────────────────┤
│ 共1,234条记录                               第1/62页 [上一页][下一页]│
└─────────────────────────────────────────────────────────────────┘
```

### 8.2 数据变更追踪

#### 8.2.1 变更历史查看
```
┌─────────────────────────────────────────────────────────────────┐
│ 数据变更历史 - 奔驰 C级                         [返回]           │
├─────────────────────────────────────────────────────────────────┤
│ 变更时间        | 操作人 | 变更字段    | 变更前值  | 变更后值     │
│ 2024-01-15 10:30| 张三  | 指导价区间  | 30-50万  | 28-48万      │
│ 2024-01-10 14:20| 李四  | 车系简介    | 原简介... | 新简介...    │
│ 2024-01-05 09:15| 王五  | 状态       | 即将上市  | 在售         │
│ 2024-01-01 16:30| 张三  | 创建记录    | -        | 初始数据     │
├─────────────────────────────────────────────────────────────────┤
│ [导出变更历史] [对比版本]                                       │
└─────────────────────────────────────────────────────────────────┘
```

## 9. 非功能需求

### 9.1 性能要求
- 页面响应时间不超过3秒
- 接口响应时间不超过1秒
- 支持并发用户数不少于100
- 数据库查询优化，复杂查询不超过5秒
- 文件上传速度不低于1MB/s
- 批量导入1000条记录不超过30秒

### 9.2 安全要求
- 用户身份认证和权限控制
- 数据操作日志记录
- 敏感数据加密存储
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护
- 文件上传安全检查
- API接口访问频率限制

### 9.3 可用性要求
- 系统可用性不低于99.5%
- 支持数据备份和恢复
- 提供操作手册和帮助文档
- 支持7×24小时监控
- 故障恢复时间不超过4小时
- 数据备份每日自动执行

### 9.4 兼容性要求
- 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- 支持移动端响应式设计
- 提供RESTful API接口
- 支持IE11及以上版本（如有需要）
- 支持主流操作系统（Windows、macOS、Linux）

## 6. 数据字典

### 6.1 品牌状态枚举
- ACTIVE：启用
- INACTIVE：禁用

### 6.2 车系类型枚举
- SEDAN：轿车
- SUV：SUV
- MPV：MPV
- COUPE：跑车
- PICKUP：皮卡
- HATCHBACK：两厢车

### 6.3 车系级别枚举
- A：A级车
- B：B级车
- C：C级车
- D：D级车

### 6.4 车型状态枚举
- ON_SALE：在售
- DISCONTINUED：停售
- COMING_SOON：即将上市

### 6.5 燃料类型枚举
- GASOLINE：汽油
- DIESEL：柴油
- HYBRID：混合动力
- ELECTRIC：纯电动
- PLUG_IN_HYBRID：插电混动

### 6.6 驱动方式枚举
- FWD：前驱
- RWD：后驱
- AWD：四驱

## 7. 验收标准

### 7.1 功能验收
- 所有管理功能正常运行
- 查询功能支持多条件组合
- 统计接口数据准确
- 页面交互友好

### 7.2 性能验收
- 满足性能要求指标
- 大数据量下系统稳定运行

### 7.3 安全验收
- 通过安全测试
- 权限控制有效

## 8. 项目计划

### 8.1 开发阶段
- 第1周：数据库设计和基础框架搭建
- 第2-3周：品牌管理功能开发
- 第4-5周：车系管理功能开发
- 第6-7周：车型管理功能开发
- 第8周：统计分析功能开发
- 第9周：接口开发和联调
- 第10周：测试和优化

### 8.2 测试阶段
- 第11周：功能测试
- 第12周：性能测试和安全测试

### 8.3 上线阶段
- 第13周：部署和上线

## 9. 风险评估

### 9.1 技术风险
- 大数据量查询性能问题
- 图片存储和访问效率

### 9.2 业务风险
- 数据标准化程度不一致
- 历史数据迁移复杂度

### 9.3 风险应对
- 采用数据库索引优化查询性能
- 使用CDN加速图片访问
- 制定数据标准化规范
- 分阶段进行数据迁移