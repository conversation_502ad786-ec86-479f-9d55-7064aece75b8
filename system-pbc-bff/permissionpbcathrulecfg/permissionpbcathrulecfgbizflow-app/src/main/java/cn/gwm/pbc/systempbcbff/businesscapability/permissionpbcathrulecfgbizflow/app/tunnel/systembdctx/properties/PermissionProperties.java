package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "gwm.permission")
@Data
public class PermissionProperties {

    private String platformCode;

    private String platformSecret;

}
