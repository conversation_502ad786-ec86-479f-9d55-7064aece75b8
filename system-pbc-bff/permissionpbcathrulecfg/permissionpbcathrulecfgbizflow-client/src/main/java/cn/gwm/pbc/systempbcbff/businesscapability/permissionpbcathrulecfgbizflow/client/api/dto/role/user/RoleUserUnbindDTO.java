package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleUserUnbindDTO", desc = "")
@Data
public class RoleUserUnbindDTO implements Serializable {
    /**
     * 角色id
     */
    @Field(name = "角色id", desc = "角色id")
    private Long id;

    /**
     * 用户工号集合
     */
    @Field(name = "用户工号集合", desc = "用户工号集合")
    @Alias("user_codes")
    private String[] userCodes;

}
