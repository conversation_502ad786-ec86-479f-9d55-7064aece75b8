package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleFieldDropDownListFormClientDTO", desc = "null")
@Data
@ApiModel("规则字段下拉列表请求参数对象")
public class RuleFieldDropDownListFormClientDTO implements Serializable {

    /**
     * 当前页
     */
    @Field(name = "page", desc = "")
    @ApiModelProperty(value = "当前页")
    private Integer page;

    /**
     * 每页显示几条
     */
    @Field(name = "size", desc = "")
    @ApiModelProperty(value = "每页显示几条")
    private Integer size;

    /**
     * 字段编码
     */
    @Field(name = "字段编码", desc = "字段编码")
    @ApiModelProperty("字段编码")
    @JsonProperty(value = "field_code")
    private String fieldCode;

    /**
     * 组织ID
     */
    @Field(name = "组织ID", desc = "组织ID")
    @ApiModelProperty("组织ID")
    @JsonProperty(value = "group_id")
    private Integer groupId;

    /**
     * 岗位ID集合
     */
    @Field(name = "岗位ID集合", desc = "岗位ID集合")
    @ApiModelProperty("岗位ID集合")
    @JsonProperty(value = "position_ids")
    private List<Long> positionIds;
}