# 04 领域模型设计规范

## 领域实体设计原则

### 核心原则
- 实体应反映业务领域概念，而非数据库表结构
- 每个实体类应代表一个明确的业务对象
- 实体类应封装其内部状态和行为
- 遵循单一职责原则，一个实体类只负责一个领域概念
- 实体关系应反映业务领域中的真实关系

### 实体类结构
- 使用`@Table`注解映射数据库表
- 必须有唯一标识符（ID）
- 包含必要的业务属性
- 包含审计字段（创建时间、更新时间等）
- 提供适当的构造函数、getter和setter方法
- 考虑实现`equals()`和`hashCode()`方法

## 注解使用规范

### MyBatis-Plus注解
- `@TableName`: 指定表名
- `@TableId`: 指定主键字段
- `@TableField`: 指定字段映射
- `@TableLogic`: 标记逻辑删除字段
- `@Version`: 标记乐观锁版本字段
- `@TableField(fill = FieldFill.INSERT)`: 自动填充字段

### 验证注解
- `@NotNull`: 字段不能为null
- `@NotBlank`: 字符串不能为空白
- `@NotEmpty`: 集合不能为空
- `@Size`: 指定字符串长度或集合大小范围
- `@Min`/`@Max`: 指定数值范围
- `@Pattern`: 指定正则表达式模式
- `@Email`: 验证电子邮件格式
- `@Past`/`@Future`: 验证日期

## 数据库映射规范

### 表名映射
- 使用下划线命名法（snake_case）
- 使用模块前缀（如`cmp_`表示主数据管理模块）
- 表名应为英文单数形式
- 示例：`cmp_brand`, `cmp_manufacturer`

### 字段映射
- 使用下划线命名法
- 主键统一命名为`id`
- 外键命名为`关联表名_id`（如`brand_id`）
- 字段名应清晰表达其业务含义

### 字段类型映射
- 使用合适的数据类型
- 字符串长度应根据业务需求设定
- 使用精确的数值类型（如`DECIMAL`而非`FLOAT`）
- 日期时间使用合适的类型（如`DATETIME`, `DATE`）

## 审计字段规范

### 基本审计字段
- `created_by`: 创建人ID或用户名
- `created_at`: 创建时间
- `updated_by`: 更新人ID或用户名
- `updated_at`: 更新时间
- `deleted_by`: 删除人ID或用户名（可选）
- `deleted_at`: 删除时间（可选，用于逻辑删除）
- `version`: 版本号（用于乐观锁）

### 审计字段实现
- 使用MyBatis-Plus的`@TableField(fill = FieldFill.INSERT)`等注解
- 结合`MetaObjectHandler`实现自动填充

## 关联关系处理

### 关联类型
- 一对一关联: `@OneToOne`
- 一对多关联: `@OneToMany`
- 多对一关联: `@ManyToOne`
- 多对多关联: `@ManyToMany`

### 关联配置
- 明确指定关联的`fetch`类型（建议使用`LAZY`）
- 明确指定`cascade`类型
- 使用`@JoinColumn`指定外键列
- 双向关联时，明确指定`mappedBy`

### 关联注意事项
- 避免循环引用
- 考虑使用单向关联降低耦合
- 大型集合考虑使用批量查询而非直接关联
- 处理JSON序列化时避免无限递归

## 值对象设计

### 值对象特性
- 不变性
- 基于属性的相等性
- 无唯一标识
- 可替换性

### 值对象实现
- 使用`@Embeddable`和`@Embedded`嵌入值对象
- 所有字段设为`final`确保不变性
- 实现`equals()`和`hashCode()`
- 不提供修改方法，修改返回新实例

## 领域模型示例

### 品牌实体示例
```java
package com.gwm.globalvehicle.masterdata.brand.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 品牌实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cmp_brand")
@ApiModel(description = "品牌实体")
public class Brand {
    
    /**
     * 品牌ID - 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "品牌ID", example = "1")
    private Long id;
    
    /**
     * 品牌代码 - 业务主键，唯一标识
     */
    @TableField("brand_code")
    @NotBlank(message = "品牌代码不能为空")
    @Size(max = 50, message = "品牌代码长度不能超过50个字符")
    @ApiModelProperty(value = "品牌代码", required = true, example = "BMW")
    private String brandCode;
    
    /**
     * 品牌中文名称
     */
    @TableField("brand_name_cn")
    @NotBlank(message = "品牌中文名称不能为空")
    @Size(max = 100, message = "品牌中文名称长度不能超过100个字符")
    @ApiModelProperty(value = "品牌中文名称", required = true, example = "宝马")
    private String brandNameCn;
    
    /**
     * 品牌英文名称
     */
    @TableField("brand_name_en")
    @Size(max = 100, message = "品牌英文名称长度不能超过100个字符")
    @ApiModelProperty(value = "品牌英文名称", example = "BMW")
    private String brandNameEn;
    
    /**
     * 品牌Logo URL
     */
    @TableField("brand_logo_url")
    @Size(max = 500, message = "品牌Logo URL长度不能超过500个字符")
    @ApiModelProperty(value = "品牌Logo URL", example = "https://example.com/logo/bmw.png")
    private String brandLogoUrl;
    
    /**
     * 品牌所属国家/地区
     */
    @TableField("brand_country")
    @Size(max = 50, message = "品牌所属国家/地区长度不能超过50个字符")
    @ApiModelProperty(value = "品牌所属国家/地区", example = "德国")
    private String brandCountry;
    
    /**
     * 品牌定位
     */
    @TableField("brand_positioning")
    @Size(max = 20, message = "品牌定位长度不能超过20个字符")
    @ApiModelProperty(value = "品牌定位", example = "LUXURY")
    private String brandPositioning;
    
    /**
     * 状态(1:启用,0:停用)
     */
    @TableField("status")
    @ApiModelProperty(value = "状态", example = "1")
    private Integer status;
    
    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人ID", hidden = true)
    private Long createUserId;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", hidden = true)
    private LocalDateTime createTime;
    
    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人ID", hidden = true)
    private Long updateUserId;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间", hidden = true)
    private LocalDateTime updateTime;
}