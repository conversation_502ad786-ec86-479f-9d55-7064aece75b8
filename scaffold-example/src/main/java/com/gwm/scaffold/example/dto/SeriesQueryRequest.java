package com.gwm.scaffold.example.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import com.gwm.scaffold.web.cmd.AbstractPageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "查询请求")
public class SeriesQueryRequest extends AbstractPageRequest {

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * name
     */
    @Schema(description = "name")
    private String name;

    /**
     * englishName
     */
    @Schema(description = "englishName")
    private String englishName;

    /**
     * brandId
     */
    @Schema(description = "brandId")
    private Long brandId;

    /**
     * type
     */
    @Schema(description = "type")
    private String type;

    /**
     * level
     */
    @Schema(description = "level")
    private String level;

    /**
     * launchTime
     */
    @Schema(description = "launchTime")
    private Date launchTime;

    /**
     * launchTime开始时间
     */
    @Schema(description = "launchTime开始时间")
    private Date launchTimeStart;

    /**
     * launchTime结束时间
     */
    @Schema(description = "launchTime结束时间")
    private Date launchTimeEnd;

    /**
     * discontinueTime
     */
    @Schema(description = "discontinueTime")
    private Date discontinueTime;

    /**
     * discontinueTime开始时间
     */
    @Schema(description = "discontinueTime开始时间")
    private Date discontinueTimeStart;

    /**
     * discontinueTime结束时间
     */
    @Schema(description = "discontinueTime结束时间")
    private Date discontinueTimeEnd;

    /**
     * minPrice
     */
    @Schema(description = "minPrice")
    private BigDecimal minPrice;

    /**
     * maxPrice
     */
    @Schema(description = "maxPrice")
    private BigDecimal maxPrice;

    /**
     * imageUrl
     */
    @Schema(description = "imageUrl")
    private String imageUrl;

    /**
     * description
     */
    @Schema(description = "description")
    private String description;

    /**
     * status
     */
    @Schema(description = "status")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    private Integer sortWeight;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

    /**
     * deletedTime开始时间
     */
    @Schema(description = "deletedTime开始时间")
    private LocalDateTime deletedTimeStart;

    /**
     * deletedTime结束时间
     */
    @Schema(description = "deletedTime结束时间")
    private LocalDateTime deletedTimeEnd;

}
