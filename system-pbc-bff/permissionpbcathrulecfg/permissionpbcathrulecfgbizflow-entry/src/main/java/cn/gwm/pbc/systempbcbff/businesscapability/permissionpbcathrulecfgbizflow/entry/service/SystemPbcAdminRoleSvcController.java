package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcAdminRoleSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcGroupSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcAdminRoleSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcGroupSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.group.SearchGroupClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.AdminRemoveUsersOfRoleFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleBindGroupFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.SearchAdminUsersOfRoleFormClientDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 权限PBC-admin角色服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@RestController("PermissionPBCAthAdminRoleCfgBizFlowSystemPbcRuleSvcCON")
public class SystemPbcAdminRoleSvcController implements SystemPbcAdminRoleSvcI {

    @Autowired
    private SystemPbcAdminRoleSvc systemPbcAdminRoleSvc;

    /**
     * 权限中心-角色绑定组织 v2-/manage/admin/role/bind/group
     *
     * @param request request
     * @return Response 权限中心-角色绑定组织
     */
    @Override
    public Map<String, Object> roleBindGroup(RoleBindGroupFormClientDTO request) {
        return systemPbcAdminRoleSvc.roleBindGroup(request);
    }

    /**
     * 权限中心-查询角色用户和组织列表 v2-/manage/admin/role/userAndGroup/list
     *
     * @param request request
     * @return Response 权限中心-查询角色用户和组织列表
     */
    @Override
    public Map<String, Object> searchUserAndGroupOfRole(SearchAdminUsersOfRoleFormClientDTO request) {
        return systemPbcAdminRoleSvc.searchUserAndGroupOfRole(request);
    }

    /**
     * 权限中心-解除绑定用户 v2-/manage/admin/role/user/unbind
     *
     * @param request request
     * @return Response 权限中心-解除绑定用户
     */
    @Override
    public Map<String, Object> removeUsersOfRole(AdminRemoveUsersOfRoleFormClientDTO request) {
        return systemPbcAdminRoleSvc.removeUsersOfRole(request);
    }

}
