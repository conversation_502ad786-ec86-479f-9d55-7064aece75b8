package com.gwm.scaffold.docs.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI 配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "scaffold.docs", name = "enabled", havingValue = "true", matchIfMissing = true)
public class OpenApiConfig {

    /**
     * 创建 OpenAPI 配置
     */
    @Bean
    public OpenAPI createOpenAPI() {
        log.info("正在初始化 OpenAPI 配置...");
        
        return new OpenAPI()
                .info(createApiInfo());
    }

    /**
     * 创建 API 信息
     */
    private Info createApiInfo() {
        return new Info()
                .title("GWM Scaffold 示例应用 API")
                .description("展示如何使用 GWM Scaffold 快速构建企业级应用的 API 文档")
                .version("1.0.0")
                .contact(new Contact()
                        .name("GWM开发团队")
                        .email("<EMAIL>")
                        .url("https://github.com/gwm/scaffold"))
                .license(new License()
                        .name("Apache 2.0")
                        .url("http://www.apache.org/licenses/LICENSE-2.0"));
    }


}
