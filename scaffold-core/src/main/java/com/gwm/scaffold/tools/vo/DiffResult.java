package com.gwm.scaffold.tools.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> 王明莹
 * @Description 自定义差异结果类
 * @Date
 **/
// 替代方案：自定义差异结果包装类
@Data
@AllArgsConstructor
public class DiffResult<T> {
    private List<T> toInsert;
    private List<T> toUpdate;
    private List<T> toDelete;

    // 空结果工厂方法
    public static <T> DiffResult<T> empty() {
        return new DiffResult<>(Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList());
    }
}