package com.gwm.scaffold.example.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gwm.scaffold.example.dto.OrderCreateRequest;
import com.gwm.scaffold.example.dto.OrderQueryRequest;
import com.gwm.scaffold.example.dto.OrderUpdateRequest;
import com.gwm.scaffold.example.entity.Order;
import com.gwm.scaffold.example.vo.OrderVO;

import java.util.List;
import java.util.Map;

/**
 * 订单服务接口
 * 
 * 提供订单相关的业务功能
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public interface OrderService extends IService<Order> {

    /**
     * 分页查询订单列表
     *
     * @param request 查询条件
     * @return 订单分页数据
     */
    IPage<OrderVO> getOrderPage(OrderQueryRequest request);

    /**
     * 根据ID获取订单详情
     *
     * @param id 订单ID
     * @return 订单详情
     */
    OrderVO getOrderById(Long id);

    /**
     * 创建订单
     *
     * @param request 创建请求
     * @return 订单详情
     */
    OrderVO createOrder(OrderCreateRequest request);

    /**
     * 更新订单
     *
     * @param request 更新请求
     * @return 订单详情
     */
    OrderVO updateOrder(OrderUpdateRequest request);

    /**
     * 删除订单
     *
     * @param id 订单ID
     * @return 是否成功
     */
    boolean deleteOrder(Long id);

    /**
     * 批量删除订单
     *
     * @param ids 订单ID列表
     * @return 删除数量
     */
    int batchDeleteOrders(List<Long> ids);

    /**
     * 更新订单状态
     *
     * @param id     订单ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateOrderStatus(Long id, Integer status);

    /**
     * 批量更新订单状态
     *
     * @param ids    订单ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdateOrderStatus(List<Long> ids, Integer status);

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    OrderVO getOrderByOrderNo(String orderNo);

    /**
     * 根据客户ID查询订单列表
     *
     * @param customerId 客户ID
     * @return 订单列表
     */
    List<OrderVO> getOrdersByCustomerId(Long customerId);

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    String generateOrderNo();

    /**
     * 获取订单状态统计
     *
     * @return 状态统计结果
     */
    Map<String, Long> getOrderStatusStatistics();
}
