package com.gwm.scaffold.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.monitor.annotation.Monitor;
import com.gwm.scaffold.example.entity.Brand;
import com.gwm.scaffold.example.mapper.BrandMapper;
import com.gwm.scaffold.example.service.BrandService;
import com.gwm.scaffold.example.dto.BrandCreateRequest;
import com.gwm.scaffold.example.dto.BrandUpdateRequest;
import com.gwm.scaffold.example.dto.BrandQueryRequest;
import com.gwm.scaffold.example.vo.BrandVO;
import com.gwm.scaffold.example.enums.BrandStatus;
import com.gwm.scaffold.tools.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 品牌服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Service
public class BrandServiceImpl extends ServiceImpl<BrandMapper, Brand> implements BrandService {

    @Override
    @Monitor(value = "分页查询", threshold = 1000)
    public IPage<BrandVO> getBrandPage(BrandQueryRequest request) {
        Page<Brand> page = new Page<>(request.getCurrent(), request.getSize());
        IPage<Brand> entityPage = baseMapper.selectBrandPage(page, request);

        // 转换为VO
        Page<BrandVO> voPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<BrandVO> voList = entityPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    @Monitor(value = "获取详情")
    public BrandVO getBrandById(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("ID不能为空");
        }

        Brand brand = getById(id);
        if (brand == null) {
            throw new ServiceException("不存在");
        }

        return convertToVO(brand);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "创建品牌")
    public BrandVO createBrand(BrandCreateRequest request) {
        if (request == null) {
            throw new ServiceException("创建请求不能为空");
        }

        // 业务规则校验
        validateBrandForCreate(request);

        // 创建品牌实体
        Brand brand = new Brand();
        BeanUtils.copyProperties(request, brand);

        // 设置默认值
        if (brand.getStatus() == null) {
            brand.setStatus(BrandStatus.ACTIVE.getCode());
        }
        if (brand.getSortWeight() == null) {
            brand.setSortWeight(100);
        }

        boolean success = save(brand);
        if (!success) {
            throw new ServiceException("品牌创建失败");
        }

        log.info("品牌创建成功，ID：{}，名称：{}", brand.getId(), brand.getName());
        return getBrandById(brand.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新品牌")
    public BrandVO updateBrand(BrandUpdateRequest request) {
        if (request == null) {
            throw new ServiceException("更新请求不能为空");
        }
        if (request.getId() == null || request.getId() <= 0) {
            throw new ServiceException("品牌ID不能为空");
        }

        // 检查品牌是否存在
        Brand existingBrand = getById(request.getId());
        if (existingBrand == null) {
            throw new ServiceException("品牌不存在");
        }

        // 业务规则校验
        validateBrandForUpdate(request, existingBrand);

        // 更新品牌信息（只更新非空字段）
        updateBrandFields(existingBrand, request);

        boolean success = updateById(existingBrand);
        if (!success) {
            throw new ServiceException("品牌更新失败");
        }

        log.info("品牌更新成功，ID：{}，名称：{}", request.getId(), existingBrand.getName());
        return getBrandById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "删除品牌")
    public boolean deleteBrand(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("品牌ID不能为空");
        }

        Brand brand = getById(id);
        if (brand == null) {
            throw new ServiceException("品牌不存在");
        }

        // 检查关联关系（这里暂时跳过，等车系模块实现后再添加）
        // checkBrandAssociations(id);

        // 执行软删除
        brand.setDeleted(1);
        brand.setDeletedTime(LocalDateTime.now());
        boolean success = updateById(brand);

        if (success) {
            log.info("品牌删除成功，ID：{}，名称：{}", id, brand.getName());
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "批量删除")
    public int batchDeleteBrands(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("ID列表不能为空");
        }

        boolean success = removeByIds(ids);
        int count = success ? ids.size() : 0;

        log.info("批量删除完成，删除数量：{}", count);
        return count;
    }

    /**
     * 将实体转换为VO
     */
    private BrandVO convertToVO(Brand entity) {
        if (entity == null) {
            return null;
        }

        BrandVO vo = new BrandVO();
        BeanUtils.copyProperties(entity, vo);

        // 设置状态名称
        if (StringUtils.hasText(entity.getStatus())) {
            BrandStatus status = BrandStatus.fromCode(entity.getStatus());
            if (status != null) {
                vo.setStatusName(status.getName());
            }
        }

        return vo;
    }

    /**
     * 创建品牌时的业务规则校验
     *
     * @param request 创建请求
     */
    private void validateBrandForCreate(BrandCreateRequest request) {
        // 校验品牌名称唯一性
        if (StringUtils.hasText(request.getName())) {
            LambdaQueryWrapper<Brand> nameWrapper = new LambdaQueryWrapper<>();
            nameWrapper.eq(Brand::getName, request.getName());
            nameWrapper.eq(Brand::getDeleted, 0);
            if (count(nameWrapper) > 0) {
                throw new ServiceException("品牌名称已存在：" + request.getName());
            }
        }

        // 校验英文名称唯一性（如果填写）
        if (StringUtils.hasText(request.getEnglishName())) {
            LambdaQueryWrapper<Brand> englishNameWrapper = new LambdaQueryWrapper<>();
            englishNameWrapper.eq(Brand::getEnglishName, request.getEnglishName());
            englishNameWrapper.eq(Brand::getDeleted, 0);
            if (count(englishNameWrapper) > 0) {
                throw new ServiceException("品牌英文名称已存在：" + request.getEnglishName());
            }
        }

        // 校验成立年份合理性
        if (request.getFoundedYear() != null) {
            int currentYear = java.time.LocalDate.now().getYear();
            if (request.getFoundedYear() > currentYear) {
                throw new ServiceException("成立年份不能晚于当前年份");
            }
        }

        // 校验状态值
        if (StringUtils.hasText(request.getStatus()) && !BrandStatus.isValidCode(request.getStatus())) {
            throw new ServiceException("无效的品牌状态：" + request.getStatus());
        }
    }

    /**
     * 更新品牌时的业务规则校验
     *
     * @param request 更新请求
     * @param existingBrand 现有品牌
     */
    private void validateBrandForUpdate(BrandUpdateRequest request, Brand existingBrand) {
        // 校验品牌名称唯一性（排除自己）
        if (StringUtils.hasText(request.getName()) && !request.getName().equals(existingBrand.getName())) {
            LambdaQueryWrapper<Brand> nameWrapper = new LambdaQueryWrapper<>();
            nameWrapper.eq(Brand::getName, request.getName());
            nameWrapper.ne(Brand::getId, request.getId());
            nameWrapper.eq(Brand::getDeleted, 0);
            if (count(nameWrapper) > 0) {
                throw new ServiceException("品牌名称已存在：" + request.getName());
            }
        }

        // 校验英文名称唯一性（如果填写且与现有不同）
        if (StringUtils.hasText(request.getEnglishName()) && !request.getEnglishName().equals(existingBrand.getEnglishName())) {
            LambdaQueryWrapper<Brand> englishNameWrapper = new LambdaQueryWrapper<>();
            englishNameWrapper.eq(Brand::getEnglishName, request.getEnglishName());
            englishNameWrapper.ne(Brand::getId, request.getId());
            englishNameWrapper.eq(Brand::getDeleted, 0);
            if (count(englishNameWrapper) > 0) {
                throw new ServiceException("品牌英文名称已存在：" + request.getEnglishName());
            }
        }

        // 校验成立年份合理性
        if (request.getFoundedYear() != null) {
            int currentYear = java.time.LocalDate.now().getYear();
            if (request.getFoundedYear() > currentYear) {
                throw new ServiceException("成立年份不能晚于当前年份");
            }
        }

        // 校验状态值
        if (StringUtils.hasText(request.getStatus()) && !BrandStatus.isValidCode(request.getStatus())) {
            throw new ServiceException("无效的品牌状态：" + request.getStatus());
        }
    }

    /**
     * 更新品牌字段（只更新非空字段）
     *
     * @param existingBrand 现有品牌
     * @param request 更新请求
     */
    private void updateBrandFields(Brand existingBrand, BrandUpdateRequest request) {
        if (StringUtils.hasText(request.getName())) {
            existingBrand.setName(request.getName());
        }
        if (StringUtils.hasText(request.getEnglishName())) {
            existingBrand.setEnglishName(request.getEnglishName());
        }
        if (StringUtils.hasText(request.getCountry())) {
            existingBrand.setCountry(request.getCountry());
        }
        if (request.getFoundedYear() != null) {
            existingBrand.setFoundedYear(request.getFoundedYear());
        }
        if (StringUtils.hasText(request.getWebsite())) {
            existingBrand.setWebsite(request.getWebsite());
        }
        if (StringUtils.hasText(request.getLogoUrl())) {
            existingBrand.setLogoUrl(request.getLogoUrl());
        }
        if (StringUtils.hasText(request.getDescription())) {
            existingBrand.setDescription(request.getDescription());
        }
        if (StringUtils.hasText(request.getStatus())) {
            existingBrand.setStatus(request.getStatus());
        }
        if (request.getSortWeight() != null) {
            existingBrand.setSortWeight(request.getSortWeight());
        }
    }

    @Override
    @Monitor(value = "获取启用品牌列表")
    public List<BrandVO> getActiveBrandList() {
        LambdaQueryWrapper<Brand> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Brand::getStatus, BrandStatus.ACTIVE.getCode());
        wrapper.eq(Brand::getDeleted, 0);
        wrapper.orderByAsc(Brand::getSortWeight, Brand::getName);

        List<Brand> brands = list(wrapper);
        return brands.stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新品牌状态")
    public boolean updateBrandStatus(Long id, String status) {
        if (id == null || id <= 0) {
            throw new ServiceException("品牌ID不能为空");
        }
        if (!BrandStatus.isValidCode(status)) {
            throw new ServiceException("无效的品牌状态：" + status);
        }

        Brand brand = getById(id);
        if (brand == null) {
            throw new ServiceException("品牌不存在");
        }

        brand.setStatus(status);
        boolean success = updateById(brand);

        if (success) {
            log.info("品牌状态更新成功，ID：{}，状态：{}", id, status);
        }

        return success;
    }

    @Override
    @Monitor(value = "按国别统计品牌数量")
    public List<BrandVO> getBrandCountByCountry() {
        // 这里返回一个简单的统计，实际项目中可能需要专门的统计VO
        LambdaQueryWrapper<Brand> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Brand::getDeleted, 0);
        wrapper.groupBy(Brand::getCountry);
        wrapper.orderByDesc(Brand::getCountry);

        List<Brand> brands = list(wrapper);
        return brands.stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }
}
