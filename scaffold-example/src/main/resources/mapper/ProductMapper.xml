<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwm.scaffold.example.mapper.ProductMapper">

    <!-- 产品信息表结果映射 -->
    <resultMap id="ProductResultMap" type="com.gwm.scaffold.example.entity.Product">
        <id column="ID" property="id"/>
        <result column="PRODUCT_CODE" property="productCode"/>
        <result column="PRODUCT_NAME" property="productName"/>
        <result column="PRODUCT_NAME_EN" property="productNameEn"/>
        <result column="CATEGORY_ID" property="categoryId"/>
        <result column="CATEGORY_NAME" property="categoryName"/>
        <result column="BRAND_ID" property="brandId"/>
        <result column="BRAND_NAME" property="brandName"/>
        <result column="PRODUCT_TYPE" property="productType"/>
        <result column="UNIT" property="unit"/>
        <result column="WEIGHT" property="weight"/>
        <result column="VOLUME" property="volume"/>
        <result column="LENGTH" property="length"/>
        <result column="WIDTH" property="width"/>
        <result column="HEIGHT" property="height"/>
        <result column="COST_PRICE" property="costPrice"/>
        <result column="SALE_PRICE" property="salePrice"/>
        <result column="MARKET_PRICE" property="marketPrice"/>
        <result column="MIN_STOCK" property="minStock"/>
        <result column="MAX_STOCK" property="maxStock"/>
        <result column="CURRENT_STOCK" property="currentStock"/>
        <result column="SALES_COUNT" property="salesCount"/>
        <result column="VIEW_COUNT" property="viewCount"/>
        <result column="STATUS" property="status"/>
        <result column="IS_HOT" property="isHot"/>
        <result column="IS_NEW" property="isNew"/>
        <result column="IS_RECOMMEND" property="isRecommend"/>
        <result column="SORT_ORDER" property="sortOrder"/>
        <result column="MAIN_IMAGE" property="mainImage"/>
        <result column="IMAGE_LIST" property="imageList"/>
        <result column="VIDEO_URL" property="videoUrl"/>
        <result column="DESCRIPTION" property="description"/>
        <result column="SPECIFICATION" property="specification"/>
        <result column="FEATURES" property="features"/>
        <result column="SERVICE_INFO" property="serviceInfo"/>
        <result column="KEYWORDS" property="keywords"/>
        <result column="META_TITLE" property="metaTitle"/>
        <result column="META_DESCRIPTION" property="metaDescription"/>
        <result column="SUPPLIER_ID" property="supplierId"/>
        <result column="SUPPLIER_NAME" property="supplierName"/>
        <result column="WARRANTY_PERIOD" property="warrantyPeriod"/>
        <result column="SHELF_LIFE" property="shelfLife"/>
        <result column="PRODUCTION_DATE" property="productionDate"/>
        <result column="EXPIRY_DATE" property="expiryDate"/>
        <result column="BARCODE" property="barcode"/>
        <result column="QR_CODE" property="qrCode"/>
        <result column="REMARK" property="remark"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_USER_CODE" property="createUserCode"/>
        <result column="CREATE_USER_NAME" property="createUserName"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="UPDATE_USER_CODE" property="updateUserCode"/>
        <result column="UPDATE_USER_NAME" property="updateUserName"/>
        <result column="VERSION" property="version"/>
        <result column="DELETED" property="deleted"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        ID, PRODUCT_CODE, PRODUCT_NAME, PRODUCT_NAME_EN, CATEGORY_ID, CATEGORY_NAME, BRAND_ID, BRAND_NAME, PRODUCT_TYPE, UNIT, WEIGHT, VOLUME, LENGTH, WIDTH, HEIGHT, COST_PRICE, SALE_PRICE, MARKET_PRICE, MIN_STOCK, MAX_STOCK, CURRENT_STOCK, SALES_COUNT, VIEW_COUNT, STATUS, IS_HOT, IS_NEW, IS_RECOMMEND, SORT_ORDER, MAIN_IMAGE, IMAGE_LIST, VIDEO_URL, DESCRIPTION, SPECIFICATION, FEATURES, SERVICE_INFO, KEYWORDS, META_TITLE, META_DESCRIPTION, SUPPLIER_ID, SUPPLIER_NAME, WARRANTY_PERIOD, SHELF_LIFE, PRODUCTION_DATE, EXPIRY_DATE, BARCODE, QR_CODE, REMARK, CREATE_TIME, CREATE_USER_CODE, CREATE_USER_NAME, UPDATE_TIME, UPDATE_USER_CODE, UPDATE_USER_NAME, VERSION, DELETED
    </sql>

    <!-- 分页查询产品信息表列表 -->
    <select id="selectProductPage" resultMap="ProductResultMap">
        SELECT
        <include refid="BaseColumns"/>
        FROM SYS_PRODUCT
        <where>
            deleted = 0
            <if test="request.id != null">
                AND ID = #{request.id}
            </if>
            <if test="request.productCode != null and request.productCode != ''">
                AND PRODUCT_CODE LIKE CONCAT('%', #{request.productCode}, '%')
            </if>
            <if test="request.productName != null and request.productName != ''">
                AND PRODUCT_NAME LIKE CONCAT('%', #{request.productName}, '%')
            </if>
            <if test="request.productNameEn != null and request.productNameEn != ''">
                AND PRODUCT_NAME_EN LIKE CONCAT('%', #{request.productNameEn}, '%')
            </if>
            <if test="request.categoryId != null">
                AND CATEGORY_ID = #{request.categoryId}
            </if>
            <if test="request.categoryName != null and request.categoryName != ''">
                AND CATEGORY_NAME LIKE CONCAT('%', #{request.categoryName}, '%')
            </if>
            <if test="request.brandId != null">
                AND BRAND_ID = #{request.brandId}
            </if>
            <if test="request.brandName != null and request.brandName != ''">
                AND BRAND_NAME LIKE CONCAT('%', #{request.brandName}, '%')
            </if>
            <if test="request.productType != null">
                AND PRODUCT_TYPE = #{request.productType}
            </if>
            <if test="request.unit != null and request.unit != ''">
                AND UNIT LIKE CONCAT('%', #{request.unit}, '%')
            </if>
            <if test="request.weight != null">
                AND WEIGHT = #{request.weight}
            </if>
            <if test="request.volume != null">
                AND VOLUME = #{request.volume}
            </if>
            <if test="request.length != null">
                AND LENGTH = #{request.length}
            </if>
            <if test="request.width != null">
                AND WIDTH = #{request.width}
            </if>
            <if test="request.height != null">
                AND HEIGHT = #{request.height}
            </if>
            <if test="request.costPrice != null">
                AND COST_PRICE = #{request.costPrice}
            </if>
            <if test="request.salePrice != null">
                AND SALE_PRICE = #{request.salePrice}
            </if>
            <if test="request.marketPrice != null">
                AND MARKET_PRICE = #{request.marketPrice}
            </if>
            <if test="request.minStock != null">
                AND MIN_STOCK = #{request.minStock}
            </if>
            <if test="request.maxStock != null">
                AND MAX_STOCK = #{request.maxStock}
            </if>
            <if test="request.currentStock != null">
                AND CURRENT_STOCK = #{request.currentStock}
            </if>
            <if test="request.salesCount != null">
                AND SALES_COUNT = #{request.salesCount}
            </if>
            <if test="request.viewCount != null">
                AND VIEW_COUNT = #{request.viewCount}
            </if>
            <if test="request.status != null">
                AND STATUS = #{request.status}
            </if>
            <if test="request.isHot != null">
                AND IS_HOT = #{request.isHot}
            </if>
            <if test="request.isNew != null">
                AND IS_NEW = #{request.isNew}
            </if>
            <if test="request.isRecommend != null">
                AND IS_RECOMMEND = #{request.isRecommend}
            </if>
            <if test="request.sortOrder != null">
                AND SORT_ORDER = #{request.sortOrder}
            </if>
            <if test="request.mainImage != null and request.mainImage != ''">
                AND MAIN_IMAGE LIKE CONCAT('%', #{request.mainImage}, '%')
            </if>
            <if test="request.imageList != null and request.imageList != ''">
                AND IMAGE_LIST LIKE CONCAT('%', #{request.imageList}, '%')
            </if>
            <if test="request.videoUrl != null and request.videoUrl != ''">
                AND VIDEO_URL LIKE CONCAT('%', #{request.videoUrl}, '%')
            </if>
            <if test="request.description != null and request.description != ''">
                AND DESCRIPTION LIKE CONCAT('%', #{request.description}, '%')
            </if>
            <if test="request.specification != null and request.specification != ''">
                AND SPECIFICATION LIKE CONCAT('%', #{request.specification}, '%')
            </if>
            <if test="request.features != null and request.features != ''">
                AND FEATURES LIKE CONCAT('%', #{request.features}, '%')
            </if>
            <if test="request.serviceInfo != null and request.serviceInfo != ''">
                AND SERVICE_INFO LIKE CONCAT('%', #{request.serviceInfo}, '%')
            </if>
            <if test="request.keywords != null and request.keywords != ''">
                AND KEYWORDS LIKE CONCAT('%', #{request.keywords}, '%')
            </if>
            <if test="request.metaTitle != null and request.metaTitle != ''">
                AND META_TITLE LIKE CONCAT('%', #{request.metaTitle}, '%')
            </if>
            <if test="request.metaDescription != null and request.metaDescription != ''">
                AND META_DESCRIPTION LIKE CONCAT('%', #{request.metaDescription}, '%')
            </if>
            <if test="request.supplierId != null">
                AND SUPPLIER_ID = #{request.supplierId}
            </if>
            <if test="request.supplierName != null and request.supplierName != ''">
                AND SUPPLIER_NAME LIKE CONCAT('%', #{request.supplierName}, '%')
            </if>
            <if test="request.warrantyPeriod != null">
                AND WARRANTY_PERIOD = #{request.warrantyPeriod}
            </if>
            <if test="request.shelfLife != null">
                AND SHELF_LIFE = #{request.shelfLife}
            </if>
            <if test="request.productionDateStart != null">
                AND PRODUCTION_DATE >= #{request.productionDateStart}
            </if>
            <if test="request.productionDateEnd != null">
                AND PRODUCTION_DATE &lt;= #{request.productionDateEnd}
            </if>
            <if test="request.expiryDateStart != null">
                AND EXPIRY_DATE >= #{request.expiryDateStart}
            </if>
            <if test="request.expiryDateEnd != null">
                AND EXPIRY_DATE &lt;= #{request.expiryDateEnd}
            </if>
            <if test="request.barcode != null and request.barcode != ''">
                AND BARCODE LIKE CONCAT('%', #{request.barcode}, '%')
            </if>
            <if test="request.qrCode != null and request.qrCode != ''">
                AND QR_CODE LIKE CONCAT('%', #{request.qrCode}, '%')
            </if>
            <if test="request.remark != null and request.remark != ''">
                AND REMARK LIKE CONCAT('%', #{request.remark}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>



</mapper>
