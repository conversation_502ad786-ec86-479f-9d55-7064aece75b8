package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteAuthClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 登录降级处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteAuthFallbackFactory implements FallbackFactory<RemoteAuthClientService> {

    @Override
    public RemoteAuthClientService create(Throwable throwable) {
        return (uri, platformCode, accessToken) -> {
            log.error("单点Token交换用户信息异常：" + throwable.getMessage());
            return null;
        };

    }
}
