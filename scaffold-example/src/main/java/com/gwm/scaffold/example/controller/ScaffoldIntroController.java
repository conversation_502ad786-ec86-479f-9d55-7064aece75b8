package com.gwm.scaffold.example.controller;

import com.gwm.scaffold.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 脚手架介绍控制器
 * 
 * 提供脚手架功能介绍和使用指南
 *
 * <AUTHOR>
 * @date 2025/7/17
 */
@Slf4j
@Api(tags = "脚手架介绍", description = "脚手架功能介绍和使用指南")
@RestController
@RequestMapping("/api/scaffold")
public class ScaffoldIntroController {

    @ApiOperation(value = "脚手架介绍页面", notes = "显示脚手架介绍页面")
    @GetMapping(value = "/intro", produces = MediaType.TEXT_HTML_VALUE)
    public void scaffoldIntroPage(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");

        try {
            ClassPathResource resource = new ClassPathResource("static/scaffold-intro.html");
            String html = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            response.getWriter().write(html);
        } catch (Exception e) {
            log.error("加载脚手架介绍页面失败", e);
            response.getWriter().write("<h1>脚手架介绍页面加载失败</h1><p>请检查静态资源文件</p>");
        }
    }

    @ApiOperation(value = "脚手架文档页面", notes = "显示完整的脚手架文档")
    @GetMapping(value = "/docs", produces = MediaType.TEXT_HTML_VALUE)
    public void scaffoldDocsPage(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");

        try {
            ClassPathResource resource = new ClassPathResource("static/docs/index.html");
            String html = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            response.getWriter().write(html);
        } catch (Exception e) {
            log.error("加载脚手架文档页面失败", e);
            response.getWriter().write("<h1>脚手架文档页面加载失败</h1><p>请检查静态资源文件</p>");
        }
    }

    @ApiOperation(value = "Markdown文档查看器", notes = "显示Markdown文档查看器页面")
    @GetMapping(value = "/markdown-viewer", produces = MediaType.TEXT_HTML_VALUE)
    public void markdownViewer(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");

        try {
            ClassPathResource resource = new ClassPathResource("static/markdown-viewer.html");
            String html = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            response.getWriter().write(html);
        } catch (Exception e) {
            log.error("加载Markdown查看器失败", e);
            response.getWriter().write("<h1>Markdown查看器加载失败</h1><p>请检查静态资源文件</p>");
        }
    }

    @ApiOperation(value = "代码生成器介绍页面", notes = "显示代码生成器功能介绍页面")
    @GetMapping(value = "/code-generator-intro", produces = MediaType.TEXT_HTML_VALUE)
    public void codeGeneratorIntroPage(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");

        try {
            ClassPathResource resource = new ClassPathResource("static/code-generator-intro.html");
            String html = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            response.getWriter().write(html);
        } catch (Exception e) {
            log.error("加载代码生成器介绍页面失败", e);
            response.getWriter().write("<h1>代码生成器介绍页面加载失败</h1><p>请检查静态资源文件</p>");
        }
    }

    @ApiOperation(value = "认证鉴权介绍页面", notes = "显示认证鉴权模块功能介绍页面")
    @GetMapping(value = "/auth-module-intro", produces = MediaType.TEXT_HTML_VALUE)
    public void authModuleIntroPage(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");

        try {
            ClassPathResource resource = new ClassPathResource("static/auth-module-intro.html");
            String html = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            response.getWriter().write(html);
        } catch (Exception e) {
            log.error("加载认证鉴权介绍页面失败", e);
            response.getWriter().write("<h1>认证鉴权介绍页面加载失败</h1><p>请检查静态资源文件</p>");
        }
    }

    @ApiOperation(value = "Markdown文档", notes = "获取Markdown格式的脚手架文档")
    @GetMapping(value = "/markdown", produces = MediaType.TEXT_PLAIN_VALUE)
    public void scaffoldMarkdown(HttpServletResponse response) throws IOException {
        response.setContentType("text/plain;charset=UTF-8");

        try {
            ClassPathResource resource = new ClassPathResource("../docs/SCAFFOLD_GUIDE.md");
            String markdown = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            response.getWriter().write(markdown);
        } catch (Exception e) {
            log.error("加载Markdown文档失败", e);
            response.getWriter().write("# 脚手架文档加载失败\n\n请检查文档文件是否存在。");
        }
    }

    @ApiOperation(value = "脚手架概览", notes = "获取脚手架基本信息和功能概览")
    @GetMapping("/overview")
    public Result<Map<String, Object>> getScaffoldOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        // 基本信息
        Map<String, Object> basicInfo = new HashMap<>();
        basicInfo.put("name", "GWM Spring Boot 企业级脚手架");
        basicInfo.put("version", "1.1.0");
        basicInfo.put("description", "企业级Spring Boot项目模板，提供完整的基础设施和最佳实践");
        basicInfo.put("author", "GWM开发团队");
        basicInfo.put("license", "MIT");
        basicInfo.put("javaVersion", "1.8+");
        basicInfo.put("springBootVersion", "2.7.18");
        overview.put("basicInfo", basicInfo);
        
        // 技术栈
        Map<String, String> techStack = new LinkedHashMap<>();
        techStack.put("Spring Boot", "2.7.18 - 企业级应用框架");
        techStack.put("MyBatis Plus", "3.5.6 - ORM框架增强");
        techStack.put("MySQL", "8.0+ - 主数据库");
        techStack.put("HikariCP + Druid", "高性能连接池");
        techStack.put("Caffeine", "本地缓存");
        techStack.put("Swagger", "2.9.2 - API文档生成");
        techStack.put("Hutool", "通用工具库");
        techStack.put("OkHttp3", "HTTP客户端");
        techStack.put("Jasypt", "配置加密");
        techStack.put("JavaFaker", "测试数据生成");
        overview.put("techStack", techStack);
        
        // 核心特性
        List<Map<String, String>> features = new ArrayList<>();
        features.add(createFeature("🔧", "开箱即用", "引入依赖即可使用，无需复杂配置"));
        features.add(createFeature("🛡️", "权限控制", "完整的认证权限体系"));
        features.add(createFeature("📈", "性能监控", "实时监控应用状态"));
        features.add(createFeature("📖", "API文档", "自动生成API文档"));
        features.add(createFeature("🔄", "数据增强", "MyBatis Plus增强"));
        features.add(createFeature("🎯", "模块化", "按需引入功能模块"));
        features.add(createFeature("🛠️", "工具丰富", "17个企业级工具类"));
        features.add(createFeature("🔐", "安全加密", "文件加密、HMAC签名"));
        overview.put("features", features);
        
        return Result.success(overview);
    }

    @ApiOperation(value = "工具类库", notes = "获取脚手架提供的工具类库信息")
    @GetMapping("/tools")
    public Result<Map<String, Object>> getToolsInfo() {
        Map<String, Object> tools = new HashMap<>();
        
        // 安全与加密工具
        List<Map<String, Object>> securityTools = new ArrayList<>();
        securityTools.add(createToolInfo("EncryptUtil", "文件加密工具", 
            Arrays.asList("文件加密解密", "压缩包加密", "安全存储"), 
            "EncryptUtil.fileEncrypt(file)"));
        securityTools.add(createToolInfo("HmacSignUtil", "HMAC签名工具", 
            Arrays.asList("API签名认证", "安全请求头", "企业级认证"), 
            "HmacSignUtil.createSignHeader(appKey, appSecret, uri, method)"));
        tools.put("securityTools", securityTools);
        
        // 网络请求工具
        List<Map<String, Object>> networkTools = new ArrayList<>();
        networkTools.add(createToolInfo("HttpRequestUtil", "HTTP请求工具", 
            Arrays.asList("POST/PUT/GET请求", "文件上传", "多种数据格式"), 
            "HttpRequestUtil.post(jsonData, url, headers)"));
        networkTools.add(createToolInfo("OkHttpUtil", "OkHttp封装工具", 
            Arrays.asList("链式调用", "异步请求", "文件下载", "连接池管理"), 
            "OkHttpUtil.builder().url(url).post(data).execute()"));
        tools.put("networkTools", networkTools);
        
        // 数据处理工具
        List<Map<String, Object>> dataTools = new ArrayList<>();
        dataTools.add(createToolInfo("StringUtil", "字符串处理工具", 
            Arrays.asList("智能类型转换", "唯一文件名", "类型判断", "格式化"), 
            "StringUtil.convert(\"123\") // 自动转换为Integer"));
        dataTools.add(createToolInfo("UUIDUtil", "唯一标识生成工具", 
            Arrays.asList("UUID生成", "唯一编号", "随机字符串", "带前缀编号"), 
            "UUIDUtil.getUniqueNoWithPrefix(\"ORDER_\")"));
        dataTools.add(createToolInfo("CollectionDiffUtil", "集合差异计算工具", 
            Arrays.asList("差异分析", "增删改识别", "批量操作优化"), 
            "CollectionDiffUtil.diff(oldList, newList, User::getId)"));
        tools.put("dataTools", dataTools);
        
        // 日期时间工具
        List<Map<String, Object>> dateTools = new ArrayList<>();
        dateTools.add(createToolInfo("DateUtil", "日期处理工具", 
            Arrays.asList("天数计算", "类型转换", "日期操作"), 
            "DateUtil.calculateUsageDays(startDate, endDate)"));
        dateTools.add(createToolInfo("ObjectUtil", "对象处理工具", 
            Arrays.asList("目标日期", "区间检测", "对象转Map"), 
            "ObjectUtil.getTargetDate(7) // 7天后"));
        tools.put("dateTools", dateTools);
        
        // 前端支持工具
        List<Map<String, Object>> frontendTools = new ArrayList<>();
        frontendTools.add(createToolInfo("FrontUtil", "前端类型转换工具", 
            Arrays.asList("Java类型转前端类型", "前后端协作"), 
            "FrontUtil.convertFrontType(String.class) // \"string\""));
        frontendTools.add(createToolInfo("EnumUtil", "枚举处理工具", 
            Arrays.asList("枚举转换", "反射解析", "动态加载"), 
            "EnumUtil.convertEnum(\"UserStatus\")"));
        tools.put("frontendTools", frontendTools);
        
        // 系统工具
        List<Map<String, Object>> systemTools = new ArrayList<>();
        systemTools.add(createToolInfo("SpringContextUtil", "Spring上下文工具", 
            Arrays.asList("Bean获取", "上下文访问", "依赖注入"), 
            "SpringContextUtil.getBean(UserService.class)"));
        systemTools.add(createToolInfo("LoginUserUtil", "登录用户工具", 
            Arrays.asList("用户信息管理", "链路追踪", "异步支持"), 
            "LoginUserUtil.getCurrentUser()"));
        systemTools.add(createToolInfo("FakeUtil", "测试数据生成工具", 
            Arrays.asList("对象填充", "深度控制", "类型支持"), 
            "FakeUtil.createFake(User.class)"));
        tools.put("systemTools", systemTools);
        
        return Result.success(tools);
    }

    @ApiOperation(value = "快速开始", notes = "获取快速开始指南")
    @GetMapping("/quickstart")
    public Result<Map<String, Object>> getQuickStart() {
        Map<String, Object> quickStart = new HashMap<>();
        
        // 环境要求
        Map<String, String> requirements = new LinkedHashMap<>();
        requirements.put("JDK", "1.8+");
        requirements.put("Maven", "3.6+");
        requirements.put("MySQL", "8.0+ (可选，默认使用H2)");
        quickStart.put("requirements", requirements);
        
        // 快速开始步骤
        List<Map<String, Object>> steps = new ArrayList<>();
        steps.add(createStep(1, "克隆项目", 
            "git clone <repository-url> my-project\ncd my-project", 
            "获取脚手架代码"));
        steps.add(createStep(2, "修改项目信息", 
            "编辑 pom.xml 中的 groupId、artifactId、name 等信息", 
            "自定义项目基本信息"));
        steps.add(createStep(3, "引入依赖", 
            "<dependency>\n  <groupId>com.gwm.scaffold</groupId>\n  <artifactId>scaffold-core</artifactId>\n  <version>1.0.0-SNAPSHOT</version>\n</dependency>", 
            "添加脚手架核心依赖"));
        steps.add(createStep(4, "配置应用", 
            "scaffold:\n  enabled: true\n  modules:\n    auth: true\n    web: true", 
            "配置脚手架功能模块"));
        steps.add(createStep(5, "启动应用", 
            "mvn spring-boot:run", 
            "启动Spring Boot应用"));
        steps.add(createStep(6, "访问应用", 
            "应用首页: http://localhost:8080\nAPI文档: http://localhost:8080/swagger-ui.html", 
            "验证应用是否正常运行"));
        quickStart.put("steps", steps);
        
        // 重要链接
        Map<String, String> links = new LinkedHashMap<>();
        links.put("应用首页", "http://localhost:8080");
        links.put("API文档", "http://localhost:8080/swagger-ui.html");
        links.put("Bootstrap UI", "http://localhost:8080/doc.html");
        links.put("监控面板", "http://localhost:8080/actuator");
        links.put("数据库控制台", "http://localhost:8080/h2-console");
        links.put("Druid监控", "http://localhost:8080/druid");
        quickStart.put("links", links);
        
        return Result.success(quickStart);
    }

    @ApiOperation(value = "模块架构", notes = "获取脚手架模块架构信息")
    @GetMapping("/architecture")
    public Result<Map<String, Object>> getArchitecture() {
        Map<String, Object> architecture = new HashMap<>();
        
        // 模块列表
        List<Map<String, Object>> modules = new ArrayList<>();
        modules.add(createModuleInfo("scaffold-core", "核心模块", "必需", 
            Arrays.asList("统一返回结果封装", "基础实体类和异常体系", "核心工具类库", "自动配置支持"),
            "提供脚手架的核心基础功能"));
        modules.add(createModuleInfo("scaffold-example", "示例模块", "推荐", 
            Arrays.asList("用户管理CRUD示例", "订单管理业务示例", "认证权限使用示例", "API文档集成示例"),
            "完整的示例应用，展示脚手架的使用方法"));
        modules.add(createModuleInfo("system-pbc-bff", "业务模块示例", "可选", 
            Arrays.asList("业务模块创建示例", "模块间通信示例", "业务逻辑组织方式"),
            "独立的业务模块示例"));
        architecture.put("modules", modules);
        
        // 依赖关系
        Map<String, Object> dependencies = new HashMap<>();
        dependencies.put("scaffold-core", Arrays.asList("无依赖"));
        dependencies.put("scaffold-example", Arrays.asList("scaffold-core"));
        dependencies.put("system-pbc-bff", Arrays.asList("scaffold-core"));
        architecture.put("dependencies", dependencies);
        
        return Result.success(architecture);
    }

    @ApiOperation(value = "代码生成器", notes = "获取代码生成器功能介绍")
    @GetMapping("/code-generator")
    public Result<Map<String, Object>> getCodeGenerator() {
        Map<String, Object> generator = new HashMap<>();

        // 基本信息
        Map<String, Object> basicInfo = new HashMap<>();
        basicInfo.put("name", "GWM Scaffold 代码生成器");
        basicInfo.put("description", "根据数据库表结构自动生成符合脚手架规范的代码");
        basicInfo.put("version", "1.0.0");
        basicInfo.put("author", "GWM开发团队");
        generator.put("basicInfo", basicInfo);

        // 核心特性
        List<Map<String, String>> features = new ArrayList<>();
        features.add(createFeature("🚀", "一键生成", "根据数据库表一键生成完整的CRUD代码"));
        features.add(createFeature("📋", "多层架构", "自动生成Entity、Mapper、Service、Controller等各层代码"));
        features.add(createFeature("🎨", "模板定制", "基于FreeMarker模板，支持自定义代码模板"));
        features.add(createFeature("📖", "文档集成", "自动生成Swagger API文档注解"));
        features.add(createFeature("🔧", "配置灵活", "支持包名、输出路径、作者等配置"));
        features.add(createFeature("👁️", "代码预览", "生成前可预览代码内容"));
        features.add(createFeature("📦", "批量生成", "支持批量生成多个表的代码"));
        features.add(createFeature("🔄", "覆盖控制", "可选择是否覆盖已存在的文件"));
        generator.put("features", features);

        // 生成的文件类型
        List<Map<String, String>> fileTypes = new ArrayList<>();
        fileTypes.add(createFileType("Entity", "实体类", "数据库表对应的Java实体类，包含字段映射和注解"));
        fileTypes.add(createFileType("Mapper", "数据访问层", "MyBatis Mapper接口，继承BaseMapper提供基础CRUD"));
        fileTypes.add(createFileType("MapperXml", "SQL映射文件", "MyBatis XML映射文件，包含自定义SQL"));
        fileTypes.add(createFileType("Service", "服务接口", "业务逻辑服务接口定义"));
        fileTypes.add(createFileType("ServiceImpl", "服务实现", "业务逻辑服务实现类"));
        fileTypes.add(createFileType("Controller", "控制器", "REST API控制器，包含完整的CRUD接口"));
        fileTypes.add(createFileType("DTO", "数据传输对象", "用于API请求和响应的数据传输对象"));
        fileTypes.add(createFileType("VO", "视图对象", "用于前端展示的视图对象"));
        generator.put("fileTypes", fileTypes);

        // 使用步骤
        List<Map<String, Object>> steps = new ArrayList<>();
        steps.add(createStep(1, "选择数据表", "从数据库表列表中选择要生成代码的表", "系统会自动加载数据库中的所有表"));
        steps.add(createStep(2, "配置参数", "设置模块名、包名、作者等基本信息", "支持自动填充和智能推荐"));
        steps.add(createStep(3, "预览代码", "可选择预览将要生成的代码内容", "确保生成的代码符合预期"));
        steps.add(createStep(4, "生成代码", "一键生成完整的业务代码", "自动创建目录结构和文件"));
        generator.put("steps", steps);

        // 配置选项
        Map<String, Object> configOptions = new HashMap<>();
        configOptions.put("tableName", "数据库表名（必填）");
        configOptions.put("moduleName", "模块名称（必填）");
        configOptions.put("packageName", "Java包名（默认：com.gwm.scaffold.example）");
        configOptions.put("author", "代码作者（默认：scaffold）");
        configOptions.put("businessName", "业务名称（用于注释）");
        configOptions.put("functionName", "功能描述（用于注释）");
        configOptions.put("outputPath", "输出路径（默认：scaffold-example模块）");
        configOptions.put("overwrite", "是否覆盖已存在文件（默认：false）");
        configOptions.put("generatePermission", "是否生成权限注解（默认：true）");
        generator.put("configOptions", configOptions);

        // 访问地址
        Map<String, String> urls = new HashMap<>();
        urls.put("代码生成器页面", "/generator.html");
        urls.put("获取表列表", "/api/generator/tables");
        urls.put("生成代码", "/api/generator/generate");
        urls.put("预览代码", "/api/generator/preview");
        urls.put("获取表信息", "/api/generator/table/{tableName}");
        urls.put("批量生成", "/api/generator/batch-generate");
        generator.put("urls", urls);

        return Result.success(generator);
    }

    @ApiOperation(value = "认证鉴权模块", notes = "获取认证鉴权模块功能介绍")
    @GetMapping("/auth-module")
    public Result<Map<String, Object>> getAuthModule() {
        Map<String, Object> authModule = new HashMap<>();

        // 基本信息
        Map<String, Object> basicInfo = new HashMap<>();
        basicInfo.put("name", "GWM Scaffold 认证鉴权模块");
        basicInfo.put("description", "提供完整的用户认证、权限控制和数据权限管理功能");
        basicInfo.put("version", "1.0.0");
        basicInfo.put("author", "GWM开发团队");
        authModule.put("basicInfo", basicInfo);

        // 核心特性
        List<Map<String, String>> features = new ArrayList<>();
        features.add(createFeature("🔐", "Token认证", "基于Token的用户身份认证机制"));
        features.add(createFeature("🛡️", "权限控制", "基于注解的细粒度权限控制"));
        features.add(createFeature("👥", "角色管理", "支持角色级别的访问控制"));
        features.add(createFeature("📊", "数据权限", "行级数据权限控制"));
        features.add(createFeature("🔄", "SSO集成", "支持单点登录系统集成"));
        features.add(createFeature("🚫", "白名单", "灵活的路径白名单配置"));
        features.add(createFeature("📝", "链路追踪", "完整的用户操作链路追踪"));
        features.add(createFeature("⚡", "异步支持", "支持异步场景的用户信息传递"));
        authModule.put("features", features);

        // 核心组件
        List<Map<String, String>> components = new ArrayList<>();
        components.add(createComponent("LoginInterceptor", "登录拦截器", "拦截请求进行Token验证和用户信息设置"));
        components.add(createComponent("AuthAspect", "权限切面", "处理@RequirePermission和@RequireRole注解"));
        components.add(createComponent("LoginUserUtil", "用户工具类", "基于ThreadLocal的用户信息存储和获取"));
        components.add(createComponent("AuthenticateService", "认证服务", "提供Token验证、权限检查等核心功能"));
        components.add(createComponent("DataPermissionAspect", "数据权限切面", "处理@DataPermission注解的数据权限控制"));
        authModule.put("components", components);

        // 注解说明
        List<Map<String, String>> annotations = new ArrayList<>();
        annotations.add(createAnnotation("@RequirePermission", "权限校验注解", "用于方法级别的权限控制"));
        annotations.add(createAnnotation("@RequireRole", "角色校验注解", "用于方法级别的角色控制"));
        annotations.add(createAnnotation("@DataPermission", "数据权限注解", "用于行级数据权限控制"));
        authModule.put("annotations", annotations);

        // 配置选项
        Map<String, String> configOptions = new LinkedHashMap<>();
        configOptions.put("scaffold.auth.enabled", "是否启用认证模块（默认：true）");
        configOptions.put("scaffold.auth.white-list", "白名单路径列表");
        configOptions.put("scaffold.auth.sso.platform-code", "SSO平台编码");
        configOptions.put("scaffold.auth.sso.check-token-url", "Token验证URL");
        configOptions.put("scaffold.auth.sso.domain", "SSO域名");
        configOptions.put("scaffold.data.permission.enabled", "是否启用数据权限（默认：false）");
        authModule.put("configOptions", configOptions);

        // 使用示例
        Map<String, String> examples = new LinkedHashMap<>();
        examples.put("权限控制", "@RequirePermission(\"user:read\")");
        examples.put("角色控制", "@RequireRole(\"admin\")");
        examples.put("多权限AND", "@RequirePermission(value = {\"user:read\", \"user:write\"}, logical = RequirePermission.Logical.AND)");
        examples.put("多权限OR", "@RequirePermission(value = {\"user:read\", \"order:read\"}, logical = RequirePermission.Logical.OR)");
        examples.put("数据权限", "@DataPermission(Type.USER)");
        examples.put("获取用户信息", "LoginUserUtil.getUserCode()");
        authModule.put("examples", examples);

        return Result.success(authModule);
    }

    @ApiOperation(value = "最佳实践", notes = "获取开发最佳实践指南")
    @GetMapping("/best-practices")
    public Result<Map<String, Object>> getBestPractices() {
        Map<String, Object> practices = new HashMap<>();
        
        // 项目结构规范
        List<String> projectStructure = Arrays.asList(
            "src/main/java/com/company/project/",
            "├── Application.java           # 启动类",
            "├── config/                   # 配置类",
            "├── controller/               # 控制器层",
            "├── service/                  # 服务层",
            "├── mapper/                   # 数据访问层",
            "├── entity/                   # 实体类",
            "├── dto/                      # 数据传输对象",
            "├── vo/                       # 视图对象",
            "├── enums/                    # 枚举类",
            "└── common/                   # 公共组件"
        );
        practices.put("projectStructure", projectStructure);
        
        // 编码规范
        List<Map<String, String>> codingStandards = new ArrayList<>();
        codingStandards.add(createStandard("命名规范", "类名：大驼峰；方法名：小驼峰；常量：全大写下划线分隔"));
        codingStandards.add(createStandard("注释规范", "类和方法必须添加JavaDoc注释；复杂逻辑添加行内注释"));
        codingStandards.add(createStandard("异常处理", "使用自定义异常类；避免捕获Exception；记录详细错误日志"));
        codingStandards.add(createStandard("返回结果", "统一使用Result<T>封装返回结果"));
        practices.put("codingStandards", codingStandards);
        
        // 代码示例
        Map<String, String> codeExamples = new HashMap<>();
        codeExamples.put("实体类", "@Data\n@TableName(\"sys_user\")\npublic class User extends BaseSuperEntity implements BaseEntity {\n    @TableId(type = IdType.AUTO)\n    private Long id;\n    private String username;\n}");
        codeExamples.put("Service", "@Service\npublic class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {\n    @Override\n    public Result<User> createUser(User user) {\n        save(user);\n        return Result.success(\"用户创建成功\", user);\n    }\n}");
        codeExamples.put("Controller", "@RestController\n@RequestMapping(\"/api/users\")\n@Api(tags = \"用户管理\")\npublic class UserController {\n    @PostMapping\n    @ApiOperation(\"创建用户\")\n    public Result<User> createUser(@RequestBody @Valid User user) {\n        return userService.createUser(user);\n    }\n}");
        practices.put("codeExamples", codeExamples);
        
        return Result.success(practices);
    }

    // 辅助方法
    private Map<String, String> createFeature(String icon, String title, String description) {
        Map<String, String> feature = new HashMap<>();
        feature.put("icon", icon);
        feature.put("title", title);
        feature.put("description", description);
        return feature;
    }

    private Map<String, Object> createToolInfo(String name, String title, List<String> features, String example) {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", name);
        tool.put("title", title);
        tool.put("features", features);
        tool.put("example", example);
        return tool;
    }

    private Map<String, Object> createStep(int step, String title, String content, String description) {
        Map<String, Object> stepInfo = new HashMap<>();
        stepInfo.put("step", step);
        stepInfo.put("title", title);
        stepInfo.put("content", content);
        stepInfo.put("description", description);
        return stepInfo;
    }

    private Map<String, Object> createModuleInfo(String name, String title, String required, List<String> features, String description) {
        Map<String, Object> module = new HashMap<>();
        module.put("name", name);
        module.put("title", title);
        module.put("required", required);
        module.put("features", features);
        module.put("description", description);
        return module;
    }

    private Map<String, String> createStandard(String title, String description) {
        Map<String, String> standard = new HashMap<>();
        standard.put("title", title);
        standard.put("description", description);
        return standard;
    }

    private Map<String, String> createFileType(String name, String title, String description) {
        Map<String, String> fileType = new HashMap<>();
        fileType.put("name", name);
        fileType.put("title", title);
        fileType.put("description", description);
        return fileType;
    }

    private Map<String, String> createComponent(String name, String title, String description) {
        Map<String, String> component = new HashMap<>();
        component.put("name", name);
        component.put("title", title);
        component.put("description", description);
        return component;
    }

    private Map<String, String> createAnnotation(String name, String title, String description) {
        Map<String, String> annotation = new HashMap<>();
        annotation.put("name", name);
        annotation.put("title", title);
        annotation.put("description", description);
        return annotation;
    }
}
