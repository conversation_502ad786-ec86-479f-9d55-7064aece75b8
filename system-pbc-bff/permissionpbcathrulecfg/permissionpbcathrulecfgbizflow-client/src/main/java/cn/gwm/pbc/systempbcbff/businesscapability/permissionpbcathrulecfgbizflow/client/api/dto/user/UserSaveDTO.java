package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "UserSaveDTO", desc = "")
@Data
public class UserSaveDTO implements Serializable {

    /**
     * 是否启用 0否 1是
     */
    @Alias("enable")
    private static final Integer ENABLE = 1;

    /**
     * id
     */
    @Field(name = "id", desc = "")
    @Alias("user_ids")
    private String id;
    /**
     * 别名
     */
    @Field(name = "别名", desc = "别名")
    private String alias;

    /**
     * 账号
     */
    @Field(name = "账号", desc = "账号")
    @Alias("user_code")
    private String code;

    /**
     * 头像
     */
    @Field(name = "头像", desc = "头像")
    @Alias("head_pic")
    private String headPic;

    /**
     * 手机号码
     */

    /**
     * 手机号码
     */
    @Field(name = "手机号码", desc = "手机号码")
    private String mobile;
    /**
     * 密码
     */
    @Field(name = "密码", desc = "密码")
    private String paswd;
    /**
     * ext2
     */
    @Field(name = "ext2", desc = "")
    private String ext2;
    /**
     * ext1
     */
    @Field(name = "ext1", desc = "")
    private String ext1;
    /**
     * ext3
     */
    @Field(name = "ext3", desc = "")
    private String ext3;
    /**
     * roleIds
     */
    @Field(name = "roleIds", desc = "")
    @Alias("role_ids")
    private String[] roleIds;

}
