package com.gwm.scaffold.auth.domain;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 登录用户信息
 * 
 * 存储当前登录用户的详细信息
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@Accessors(chain = true)
@Builder
public class LoginUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户工号
     */
    private String userCode;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 是否正式员工 (1:正式 0:非正式)
     */
    private Integer isFormal;

    /**
     * 性别 (1:男 2:女)
     */
    private Integer sex;

    /**
     * 职务名称
     */
    private String dutyName;

    /**
     * 职级
     */
    private Integer grade;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 单位ID
     */
    private Long unitId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 上级组名称
     */
    private String parentGroupName;

    /**
     * 上级组ID
     */
    private Long parentGroupId;

    /**
     * 组名称
     */
    private String groupName;

    /**
     * 组ID
     */
    private Long groupId;

    /**
     * 直接领导工号
     */
    private String directLeaderCode;

    /**
     * 直接领导姓名
     */
    private String directLeaderName;

    /**
     * 组织架构ID列表
     */
    private List<Long> organizationIds;

    /**
     * 组织架构名称列表
     */
    private List<String> organizationNames;

    /**
     * 用户角色信息
     * Key: 角色编码, Value: 角色信息
     */
    private Map<String, Object> roles;

    /**
     * 品牌权限信息
     */
    private List<String> brandPermissions;

    /**
     * 访问Token
     */
    private String token;

    /**
     * 用户IP地址
     */
    private String ip;

    /**
     * 请求URL
     */
    private String url;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
}
