package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user;


import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DepartmentCode extends BaseRltCode {

    static {
        MultiLangData.put(DepartmentCode.DE_0001, "组织信息不存在", "Current group is not in the com.api.data base", "th", "ru");
        MultiLangData.put(DepartmentCode.DE_0002, "组织ID不能为空", "Organization ID cannot be empty", "th", "ru");
        MultiLangData.put(DepartmentCode.DE_0003, "组织ID最多50", "Organization ID up to 50", "th", "ru");
        MultiLangData.put(DepartmentCode.DE_0004, "部分组织信息不存在", "Some organization information does not exist", "th", "ru");
        MultiLangData.put(DepartmentCode.DE_0005, "不允许选择根节点", "Root node selection not allowed", "th", "ru");
        MultiLangData.put(DepartmentCode.DE_0006, "当前组织直接领导工号不能为空", "The current organization's direct leader job number cannot be empty", "th", "ru");
    }

    public final static String DE_0001 = "DE_0001";
    public final static String DE_0002 = "DE_0002";
    public final static String DE_0003 = "DE_0003";
    public final static String DE_0004 = "DE_0004";
    public final static String DE_0005 = "DE_0005";
    public final static String DE_0006 = "DE_0006";
}
