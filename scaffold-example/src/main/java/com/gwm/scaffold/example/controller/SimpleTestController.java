package com.gwm.scaffold.example.controller;

import com.gwm.scaffold.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 简单测试控制器
 * 
 * 用于测试认证鉴权分离功能
 *
 * <AUTHOR>
 * @date 2025/1/16
 */
@Api(tags = "简单测试", description = "测试认证鉴权分离功能")
@RestController
@RequestMapping("/api/simple-test")
@Slf4j
public class SimpleTestController {

    @ApiOperation(value = "公开接口测试", notes = "测试公开接口访问")
    @GetMapping("/public")
    public Result<String> publicTest() {
        log.info("访问公开接口测试");
        return Result.success("公开接口访问成功，无需认证");
    }

    @ApiOperation(value = "普通接口测试", notes = "测试普通接口访问")
    @GetMapping("/normal")
    public Result<String> normalTest() {
        log.info("访问普通接口测试");
        return Result.success("普通接口访问成功");
    }
}
