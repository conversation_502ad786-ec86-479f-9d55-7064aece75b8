package com.gwm.scaffold.example.dto;

import com.gwm.scaffold.web.cmd.AbstractPageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单SPI请求DTO
 *
 * 用于接收SPI接口的请求参数
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "订单SPI请求")
public class OrderSpiRequest extends AbstractPageRequest {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", example = "ORD202508115000")
    private String orderNo;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID", example = "1")
    private Long customerId;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名", example = "张三")
    private String customerName;

    /**
     * 客户电话
     */
    @ApiModelProperty(value = "客户电话", example = "13800138001")
    private String customerPhone;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", example = "iPhone")
    private String productName;

    /**
     * 订单状态 (1:待支付 2:已支付 3:已发货 4:已完成 5:已取消)
     */
    @ApiModelProperty(value = "订单状态", example = "1", notes = "1:待支付 2:已支付 3:已发货 4:已完成 5:已取消")
    private Integer orderStatus;

    /**
     * 支付方式 (1:支付宝 2:微信 3:银行卡 4:现金)
     */
    @ApiModelProperty(value = "支付方式", example = "1", notes = "1:支付宝 2:微信 3:银行卡 4:现金")
    private Integer paymentMethod;


}
