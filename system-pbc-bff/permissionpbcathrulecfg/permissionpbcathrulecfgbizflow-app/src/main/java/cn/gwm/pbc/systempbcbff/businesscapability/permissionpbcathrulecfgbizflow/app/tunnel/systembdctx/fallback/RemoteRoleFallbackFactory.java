package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteRoleService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.RemoteUserBindRoleDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.*;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 登录降级处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteRoleFallbackFactory implements FallbackFactory<RemoteRoleService> {

    @Override
    public RemoteRoleService create(Throwable throwable) {
        return new RemoteRoleService() {
            @Override
            public CommonRlt<JSONObject> roleUserListAll(RemoteRoleUserListAllDTO roleUserListAllDTO) {
                log.error("调用查看角色下已绑定的用户（非分页）异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleUnbindMenu(RemoteRoleUnbindMenuDTO roleUnbindMenuDTO) {
                log.error("调用解绑菜单异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleCreate(RemoteRoleCreateDTO roleCreateDTO) {
                log.error("调用创建角色异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleEdit(RemoteRoleEditDTO roleEditDTO) {
                log.error("调用编辑角色异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONArray> roleDelete(RemoteRoleDeleteDTO roleDeleteDTO) {
                log.error("调用删除角色异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleDetail(RemoteRoleDetailDTO roleDetailDTO) {
                log.error("调用查看角色详情异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleList(RemoteRoleListDTO roleListDTO) {
                log.error("调用查询角色列表异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleState(RemoteRoleStateDTO roleStateDTO) {
                log.error("调用启用禁用角色异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONArray> roleBindUser(RemoteRoleBindUserDTO roleBindUserDTO) {
                log.error("调用角色绑定多用户异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleBindUsers(RemoteRoleBindUsersDTO roleBindUsersDTO) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleBindMenu(RemoteRoleBindMenuDTO roleBindMenuDTO) {
                log.error("调用角色绑定菜单异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> userBindRole(RemoteUserBindRoleDTO userBindRoleDTO) {
                log.error("调用用户绑定多角色异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONArray> roleMenuTree(RemoteRoleMenuTreeDTO roleMenuTreeDTO) {
                log.error("调用查询角色绑定菜单树异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleUnbindUser(RemoteRoleUnbindUserDTO roleUnbindUserDTO) {
                log.error("调用解除角色下已绑定用户异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> roleUserList(RemoteRoleUserListDTO roleUserListDTO) {
                log.error("调用查询角色已绑定用户异常：" + throwable.getMessage());
                return null;
            }
        };

    }
}
