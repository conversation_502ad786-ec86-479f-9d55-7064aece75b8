package com.gwm.scaffold.monitor.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 监控配置属性
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@ConfigurationProperties(prefix = "scaffold.monitor")
public class MonitorProperties {

    /**
     * 是否启用监控
     */
    private boolean enabled = true;

    /**
     * 内置监控面板配置
     */
    private Dashboard dashboard = new Dashboard();

    /**
     * 指标收集配置
     */
    private Metrics metrics = new Metrics();

    /**
     * 外部集成配置
     */
    private External external = new External();

    /**
     * 告警配置
     */
    private Alert alert = new Alert();

    /**
     * 内置监控面板配置
     */
    @Data
    public static class Dashboard {
        /**
         * 是否启用内置监控面板
         */
        private boolean enabled = true;

        /**
         * 监控面板访问路径
         */
        private String path = "/scaffold/monitor";

        /**
         * 数据刷新间隔（毫秒）
         */
        private long refreshInterval = 5000;

        /**
         * 是否需要认证
         */
        private boolean requireAuth = false;
    }

    /**
     * 指标收集配置
     */
    @Data
    public static class Metrics {
        /**
         * 是否收集JVM指标
         */
        private boolean jvm = true;

        /**
         * 是否收集数据库指标
         */
        private boolean database = true;

        /**
         * 是否收集业务指标
         */
        private boolean business = true;

        /**
         * 是否收集HTTP请求指标
         */
        private boolean http = true;

        /**
         * 指标保留时间（秒）
         */
        private long retentionSeconds = 3600;
    }

    /**
     * 外部集成配置
     */
    @Data
    public static class External {
        /**
         * Prometheus配置
         */
        private Prometheus prometheus = new Prometheus();

        /**
         * Grafana配置
         */
        private Grafana grafana = new Grafana();

        @Data
        public static class Prometheus {
            /**
             * 是否启用Prometheus集成
             */
            private boolean enabled = false;

            /**
             * Prometheus端点路径
             */
            private String path = "/actuator/prometheus";
        }

        @Data
        public static class Grafana {
            /**
             * 是否启用Grafana集成
             */
            private boolean enabled = false;

            /**
             * Grafana服务器URL
             */
            private String url = "http://localhost:3000";

            /**
             * 仪表板ID
             */
            private String dashboardId = "scaffold-monitor";
        }
    }

    /**
     * 告警配置
     */
    @Data
    public static class Alert {
        /**
         * 是否启用告警
         */
        private boolean enabled = false;

        /**
         * 内存使用率告警阈值（百分比）
         */
        private double memoryThreshold = 80.0;

        /**
         * CPU使用率告警阈值（百分比）
         */
        private double cpuThreshold = 80.0;

        /**
         * 错误率告警阈值（百分比）
         */
        private double errorRateThreshold = 5.0;

        /**
         * 响应时间告警阈值（毫秒）
         */
        private long responseTimeThreshold = 5000;
    }
}
