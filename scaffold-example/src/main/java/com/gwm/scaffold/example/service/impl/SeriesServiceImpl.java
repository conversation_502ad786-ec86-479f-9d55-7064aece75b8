package com.gwm.scaffold.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.monitor.annotation.Monitor;
import com.gwm.scaffold.example.entity.Series;
import com.gwm.scaffold.example.mapper.SeriesMapper;
import com.gwm.scaffold.example.service.SeriesService;
import com.gwm.scaffold.example.dto.SeriesCreateRequest;
import com.gwm.scaffold.example.dto.SeriesUpdateRequest;
import com.gwm.scaffold.example.dto.SeriesQueryRequest;
import com.gwm.scaffold.example.vo.SeriesVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Service
public class SeriesServiceImpl extends ServiceImpl<SeriesMapper, Series> implements SeriesService {

    @Override
    @Monitor(value = "分页查询", threshold = 1000)
    public IPage<SeriesVO> getSeriesPage(SeriesQueryRequest request) {
        Page<Series> page = new Page<>(request.getCurrent(), request.getSize());
        IPage<Series> entityPage = baseMapper.selectSeriesPage(page, request);

        // 转换为VO
        Page<SeriesVO> voPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<SeriesVO> voList = entityPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    @Monitor(value = "获取详情")
    public SeriesVO getSeriesById(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("ID不能为空");
        }

        Series series = getById(id);
        if (series == null) {
            throw new ServiceException("不存在");
        }

        return convertToVO(series);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "创建")
    public SeriesVO createSeries(SeriesCreateRequest request) {
        if (request == null) {
            throw new ServiceException("创建请求不能为空");
        }

        // 创建
        Series series = new Series();
        BeanUtils.copyProperties(request, series);

        boolean success = save(series);
        if (!success) {
            throw new ServiceException("创建失败");
        }

        log.info("创建成功，ID：{}", series.getId());
        return getSeriesById(series.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新")
    public SeriesVO updateSeries(SeriesUpdateRequest request) {
        if (request == null) {
            throw new ServiceException("更新请求不能为空");
        }
        if (request.getId() == null || request.getId() <= 0) {
            throw new ServiceException("ID不能为空");
        }

        // 检查是否存在
        Series existingSeries = getById(request.getId());
        if (existingSeries == null) {
            throw new ServiceException("不存在");
        }

        // 更新
        BeanUtils.copyProperties(request, existingSeries);

        boolean success = updateById(existingSeries);
        if (!success) {
            throw new ServiceException("更新失败");
        }

        log.info("更新成功，ID：{}", request.getId());
        return getSeriesById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "删除")
    public boolean deleteSeries(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("ID不能为空");
        }

        Series series = getById(id);
        if (series == null) {
            throw new ServiceException("不存在");
        }

        boolean success = removeById(id);
        if (success) {
            log.info("删除成功，ID：{}", id);
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "批量删除")
    public int batchDeleteSeriess(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("ID列表不能为空");
        }

        boolean success = removeByIds(ids);
        int count = success ? ids.size() : 0;

        log.info("批量删除完成，删除数量：{}", count);
        return count;
    }

    /**
     * 将实体转换为VO
     */
    private SeriesVO convertToVO(Series entity) {
        if (entity == null) {
            return null;
        }

        SeriesVO vo = new SeriesVO();
        BeanUtils.copyProperties(entity, vo);

        // 可以在这里添加额外的转换逻辑，例如状态名称等

        return vo;
    }
}
