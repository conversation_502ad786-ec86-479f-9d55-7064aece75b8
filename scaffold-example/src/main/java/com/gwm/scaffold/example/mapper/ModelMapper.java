package com.gwm.scaffold.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwm.scaffold.example.entity.Model;
import com.gwm.scaffold.example.dto.ModelQueryRequest;
import com.gwm.scaffold.example.vo.ModelVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface ModelMapper extends BaseMapper<Model> {

    /**
     * 分页查询列表
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 分页数据
     */
    IPage<Model> selectModelPage(Page<Model> page, @Param("request") ModelQueryRequest request);


}
