package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.role.RoleInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleDeleteDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleSaveDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;

import java.util.Map;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-角色管理服务")
public interface SystemPbcRoleSvc {
    /**
     * 权限中心-编辑角色
     * @param request
     */
    @ReturnValue(desc = "权限中心-编辑角色")
    @Method(desc = "权限中心-编辑角色", name = "权限中心-编辑角色 system/role/save")
    void save(@Parameter(name = "request", required = true) RoleSaveDTO request);
    /**
     * 权限中心-删除角色
     * @param request
     */
    @ReturnValue(desc = "权限中心-删除角色")
    @Method(desc = "权限中心-删除角色", name = "权限中心-删除角色 system/role/delete")
    void delete(@Parameter(name = "request", required = true) RoleDeleteDTO request);
    /**
     * 权限中心-获取角色详情
     * @param request
     * @return RoleInfoItemVO
     */
    @ReturnValue(desc = "权限中心-获取角色详情")
    @Method(desc = "权限中心-获取角色详情", name = "权限中心-获取角色详情 system/role/detail")
    RoleInfoItemVO detail(@Parameter(name = "request", required = true) RoleDetailDTO request);
    /**
     * 权限中心-查询角色列表
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-查询角色列表")
    @Method(desc = "权限中心-查询角色列表", name = "权限中心-查询角色列表 system/role/list")
    Map<String, Object> list(@Parameter(name = "request", required = true) RoleListDTO request);
}
