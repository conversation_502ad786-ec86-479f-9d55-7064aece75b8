package com.gwm.scaffold.auth.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 登录结果信息
 * 
 * 封装用户登录认证的结果数据
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@Accessors(chain = true)
public class LoginResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 过期时间（时间戳）
     */
    private Long expireTime;

    /**
     * 用户信息
     */
    private LoginUserInfo userInfo;

    /**
     * 是否成功
     */
    private boolean success = true;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建成功的登录结果
     *
     * @param accessToken 访问令牌
     * @param userInfo    用户信息
     * @return 登录结果
     */
    public static LoginResult success(String accessToken, LoginUserInfo userInfo) {
        return new LoginResult()
                .setAccessToken(accessToken)
                .setUserInfo(userInfo)
                .setSuccess(true);
    }

    /**
     * 创建成功的登录结果（带过期时间）
     *
     * @param accessToken 访问令牌
     * @param expireTime  过期时间
     * @param userInfo    用户信息
     * @return 登录结果
     */
    public static LoginResult success(String accessToken, Long expireTime, LoginUserInfo userInfo) {
        return new LoginResult()
                .setAccessToken(accessToken)
                .setExpireTime(expireTime)
                .setUserInfo(userInfo)
                .setSuccess(true);
    }

    /**
     * 创建失败的登录结果
     *
     * @param errorMessage 错误信息
     * @return 登录结果
     */
    public static LoginResult failure(String errorMessage) {
        return new LoginResult()
                .setSuccess(false)
                .setErrorMessage(errorMessage);
    }
}
