package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule.*;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * 权限PBC-资源服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcResourceSvcI-1702533187760")
@RequestMapping(path = "/PermissionPBCAthResourceCfgBizFlow")
public interface SystemPbcResourceSvcI {

    /**
     * 权限中心-异步加载资源树 v2-/admin/resource/asyncLoading/resourceTree
     *
     * @param request request
     * @return Response 权限中心-异步加载资源树
     */
    @PostMapping(path = "/SystemPbcResourceSvc/asyncLoadingResourceTree/v1")
    Map<String, Object> asyncLoadingResourceTree(@RequestBody(required = true) AsyncClientDTO request);

    /**
     * 权限中心-通过名称模糊查询资源树 v2-/admin/resource/searchResourceTree
     *
     * @param request request
     * @return Response 权限中心-通过名称模糊查询资源树
     */
    @PostMapping(path = "/SystemPbcResourceSvc/searchResourceTree/v1")
    Map<String, Object> searchResourceTree(@RequestBody(required = true) SearchClientDTO request);

    /**
     * 权限中心-查询菜单树形结构 v2-/admin/resource/tree
     *
     * @param request request
     * @return Response 权限中心-查询菜单树形结构
     */
    @PostMapping(path = "/SystemPbcResourceSvc/searchTree/v1")
    Map<String, Object> searchTree(@RequestBody(required = true) TreeClientDTO request);

    /**
     * 权限中心-创建菜单 v2-/admin/resource/create
     *
     * @param request request
     * @return Response 权限中心-创建菜单
     */
    @PostMapping(path = "/SystemPbcResourceSvc/create/v1")
    Map<String, Object> create(@RequestBody(required = true) AddAdminResourceFormClientDTO request);

    /**
     * 权限中心-编辑菜单 v2-/admin/resource/edit
     *
     * @param request request
     * @return Response 权限中心-编辑菜单
     */
    @PostMapping(path = "/SystemPbcResourceSvc/editResource/v1")
    Map<String, Object> editResource(@RequestBody(required = true) EditAdminResourceFormClientDTO request);

    /**
     * 权限中心-删除菜单 v2-/admin/resource/delete
     *
     * @param request request
     * @return Response 权限中心-删除菜单
     */
    @PostMapping(path = "/SystemPbcResourceSvc/delResource/v1")
    Map<String, Object> delResource(@RequestBody(required = true) DelAdminResourceFormClientDTO request);

    /**
     * 权限中心-根据菜单ID获取详情 v2-/admin/resource/get
     *
     * @param request request
     * @return Response 权限中心-根据菜单ID获取详情
     */
    @PostMapping(path = "/SystemPbcResourceSvc/searchResourceById/v1")
    Map<String, Object> searchResourceById(@RequestBody(required = true) DetailResourceFormClientDTO request);

}
