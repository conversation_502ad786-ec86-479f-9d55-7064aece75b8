package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleCreateDTO", desc = "")
@Data
public class RoleCreateDTO implements Serializable {

    /**
     * 角色编码（应用下唯一）
     */
    @Field(name = "角色编码（应用下唯一）", desc = "角色编码（应用下唯一）")
    private String code;
    /**
     * 扩展字段1
     */
    @Field(name = "扩展字段1", desc = "扩展字段1")
    private String ext1;
    /**
     * 扩展字段2
     */
    @Field(name = "扩展字段2", desc = "扩展字段2")
    private String ext2;
    /**
     * 扩展字段3
     */
    @Field(name = "扩展字段3", desc = "扩展字段3")
    private String ext3;
    /**
     * 角色名称
     */
    @Field(name = "角色名称", desc = "角色名称")
    private String name;
    /**
     * 描述
     */
    @Field(name = "描述", desc = "描述")
    private String remark;
    /**
     * 顺序
     */
    @Field(name = "顺序", desc = "顺序")
    @Alias("sort_no")
    private Integer sortNo;
    /**
     * 角色状态，0禁用1启用
     */
    @Field(name = "角色状态，0禁用1启用", desc = "角色状态，0禁用1启用")
    private String state;


}
