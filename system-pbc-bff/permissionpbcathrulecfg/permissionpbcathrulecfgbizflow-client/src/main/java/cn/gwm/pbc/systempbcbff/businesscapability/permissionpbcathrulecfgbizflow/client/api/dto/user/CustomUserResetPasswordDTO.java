package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Base64;
/**
 * <AUTHOR>
 */
@StructureObject(name = "CustomUserResetPasswordDTO", desc = "")
@Data
public class CustomUserResetPasswordDTO implements Serializable {

    /**
     * code
     */
    @Field(name = "code", desc = "")
    @Alias("user_code")
    private String code;

    /**
     * defaultPassword
     */
    @Field(name = "defaultPassword", desc = "")
    @Alias("new_passwd")
    private String defaultPassword;

    public CustomUserResetPasswordDTO() {
        this.defaultPassword = Base64.getEncoder().encodeToString("123.Com".getBytes());
    }

}
