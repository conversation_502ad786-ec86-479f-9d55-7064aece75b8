package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource.SearchAdminResourceOfRoleForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthCenterRequestInterceptor;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteRoleResourceService", url = "EMPTY", configuration = {AuthCenterRequestInterceptor.class})
public interface RemoteRoleResourceService {

    /**
     *  查询角色菜单树（返回所有树和选择节点）
     * @param searchAdminResourceOfRoleForm
     * @return CommonRlt<JSONObject>
     */
    @PostMapping("/admin/role/resource/Info")
    CommonRlt<JSONObject> searchResourceOfRoleInfo(@Validated @RequestBody SearchAdminResourceOfRoleForm searchAdminResourceOfRoleForm);


}
