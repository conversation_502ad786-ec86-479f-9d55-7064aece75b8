package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user;

import org.springframework.stereotype.Repository;
/**
 * <AUTHOR>
 */
@Repository
public class BaseRltCode {

    static {
        //base部分
		/*MultiLangData.CN.put(BaseRltCode.S_0000, "成功");
		MultiLangData.CN.put(BaseRltCode.E_9999, "失败");
		MultiLangData.CN.put(BaseRltCode.E_0002, "缺少token参数");
		MultiLangData.CN.put(BaseRltCode.E_0003, "没有访问权限，请重新登录");
		MultiLangData.CN.put(BaseRltCode.E_0004, "请求不是post方法");
		MultiLangData.CN.put(BaseRltCode.E_0005, "非法的请求路径");
		MultiLangData.CN.put(BaseRltCode.E_0011, "参数输入错误");*/
        MultiLangData.put(BaseRltCode.S_0000, "请求成功", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_0001, "登录超时", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_0002, "未找到请求中的Authorization参数,请检查传递参数是否有误", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_0003, "没有访问权限，请重新登录", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_0004, "请求缺少正确的[{0}]参数", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_0005, "用户[{0}]没有该接口[{1}]的访问权限", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_0006, "用户[{0}]未绑定该平台，没有该接口[{1}]的访问权限", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_0007, "用户[{0}]已被禁用，没有该接口[{1}]的访问权限", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_9998, "查询多语言失败", "query muti-lang error.", "th", "ru");
        MultiLangData.put(BaseRltCode.E_9999, "操作失败", "operate failed", "th", "ru");

        MultiLangData.put(BaseRltCode.D_0001, "请检查header是否传递platform_code参数", "", "th", "ru");
//        MultiLangData.put(BaseRltCode.D_0002, "请检查header是否传递token参数", "", "th", "ru");
        MultiLangData.put(BaseRltCode.D_0003, "凭证校验失败,请核对秘钥与参数是否匹配", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_5001, "服务器执行异常", "", "th", "ru");
        MultiLangData.put(BaseRltCode.E_5002, "服务异常", "", "th", "ru");
    }

    public final static String S_0000 = "S_0000";
    public final static String E_9998 = "E_9998";
    public final static String E_9999 = "E_9999";
    public final static String E_5001 = "E_5001";
    public final static String E_5002 = "E_5002";

    public final static String E_0001 = "E_0001";
    public final static String E_0002 = "E_0002";
    public final static String E_0003 = "E_0003";
    public final static String E_0004 = "E_0004";
    public final static String E_0005 = "E_0005";
    public final static String E_0006 = "E_0006";
    public final static String E_0007 = "E_0007";
    public final static String E_0011 = "E_0011";

    public final static String D_0001 = "D_0001";
//  public final static String D_0002 = "D_0002";

    public final static String D_0003 = "D_0003";


}