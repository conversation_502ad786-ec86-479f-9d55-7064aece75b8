package com.gwm.scaffold.example.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwm.scaffold.auth.annotation.RequirePermission;
import com.gwm.scaffold.core.domain.Result;
import com.gwm.scaffold.example.dto.OrderQueryRequest;
import com.gwm.scaffold.example.dto.OrderSpiRequest;
import com.gwm.scaffold.example.service.OrderService;
import com.gwm.scaffold.example.vo.OrderVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 订单管理SPI控制器
 * 
 * 提供SPI接口，支持通过JSON body传递参数
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Tag(name = "订单管理SPI", description = "订单相关SPI接口")
@RestController
@RequestMapping("/spi/orders")
public class OrderSpiController {

    @Autowired
    private OrderService orderService;

    /**
     * 根据订单号查询订单
     */
    @Operation(summary = "根据订单号查询", description = "通过JSON body传递订单号查询订单信息")
    @GetMapping
    @RequirePermission("order:read")
    public Result<OrderVO> getByOrderNo(@RequestBody(required = false) Map<String, String> params) {
        String orderNo = params != null ? params.get("orderNo") : null;
        log.info("SPI接口 - 根据订单号查询订单，订单号：{}", orderNo);
        
//        ValidationUtil.validateNotBlank(orderNo, "订单号");
        
        OrderVO result = orderService.getOrderByOrderNo(orderNo);
        return Result.success(result);
    }

    /**
     * 分页查询订单列表
     */
    @Operation(summary = "分页查询订单列表", description = "支持按订单号、客户信息、商品名称等条件查询")
    @PostMapping("/list")
    @RequirePermission("order:list")
    public Result<IPage<OrderVO>> list(@RequestBody OrderQueryRequest request) {
        log.info("SPI接口 - 分页查询订单列表，参数：{}", request);
        
        // 参数校验
//        ValidationUtil.validatePageParams(request.getCurrent(), request.getSize());
        
        IPage<OrderVO> result = orderService.getOrderPage(request);
        return Result.success(result);
    }
}
