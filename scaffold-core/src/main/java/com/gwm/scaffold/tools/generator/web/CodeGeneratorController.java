package com.gwm.scaffold.tools.generator.web;

import com.gwm.scaffold.core.domain.Result;
import com.gwm.scaffold.tools.generator.CodeGenerator;
import com.gwm.scaffold.tools.generator.config.GeneratorConfig;
import com.gwm.scaffold.tools.generator.domain.TableInfo;
import com.gwm.scaffold.tools.generator.util.DatabaseMetaUtil;
import com.gwm.scaffold.tools.generator.util.TemplateUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import javax.validation.Valid;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代码生成器控制器
 *
 * 提供代码生成相关的Web接口
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
@Tag(name = "代码生成器", description = "代码生成相关接口")
@RestController
@RequestMapping("/api/generator")
public class CodeGeneratorController {

    @Autowired
    private CodeGenerator codeGenerator;

    @Autowired
    private DatabaseMetaUtil databaseMetaUtil;

    @Autowired
    private TemplateUtil templateUtil;

    @Autowired
    private DataSource dataSource;

    /**
     * 生成代码
     */
    @Operation(summary = "生成代码", description = "根据表名生成完整的业务代码")
    @PostMapping("/generate")
    public Result<Map<String, Object>> generateCode(@Valid @RequestBody GeneratorConfig config) {
        try {
            // 设置默认输出路径
            if (config.getOutputPath() == null || config.getOutputPath().isEmpty()) {
                config.setOutputPath("scaffold-example/src/main/java/" + config.getPackageName().replace(".", "/"));
            }
            
            Map<String, Object> result = codeGenerator.generateCode(config);
            
            if ((Boolean) result.get("success")) {
                return Result.success("代码生成成功", result);
            } else {
                return Result.error((String) result.get("message"));
            }
        } catch (Exception e) {
            log.error("代码生成失败", e);
            return Result.error("代码生成失败：" + e.getMessage());
        }
    }

    /**
     * 预览代码
     */
    @Operation(summary = "预览代码", description = "预览将要生成的代码内容")
    @PostMapping("/preview")
    public Result<Map<String, Object>> previewCode(@Valid @RequestBody GeneratorConfig config) {
        try {
            TableInfo tableInfo = databaseMetaUtil.getTableInfo(config.getTableName());
            if (tableInfo == null) {
                return Result.error("表不存在：" + config.getTableName());
            }

            Map<String, Object> templateData = buildTemplateData(tableInfo, config);
            Map<String, Object> preview = new HashMap<>();

            // 预览各层代码
            preview.put("entity", templateUtil.processTemplate("entity.ftl", templateData));
            preview.put("mapper", templateUtil.processTemplate("mapper.ftl", templateData));
            preview.put("service", templateUtil.processTemplate("service.ftl", templateData));
            preview.put("controller", templateUtil.processTemplate("controller.ftl", templateData));

            Map<String, Object> result = new HashMap<>();
            result.put("tableInfo", tableInfo);
            result.put("preview", preview);

            return Result.success("代码预览成功", result);
        } catch (Exception e) {
            log.error("代码预览失败", e);
            return Result.error("代码预览失败：" + e.getMessage());
        }
    }

    /**
     * 获取数据库表列表
     */
    @Operation(summary = "获取表列表", description = "获取数据库中的所有表")
    @GetMapping("/tables")
    public Result<List<Map<String, String>>> getTables() {
        List<Map<String, String>> tables = new ArrayList<>();
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            try (ResultSet rs = metaData.getTables(null, null, "%", new String[]{"TABLE"})) {
                while (rs.next()) {
                    Map<String, String> table = new HashMap<>();
                    table.put("tableName", rs.getString("TABLE_NAME"));
                    table.put("tableComment", rs.getString("REMARKS"));
                    tables.add(table);
                }
            }
        } catch (SQLException e) {
            log.error("获取表列表失败", e);
            return Result.error("获取表列表失败：" + e.getMessage());
        }
        
        return Result.success("获取表列表成功", tables);
    }

    /**
     * 获取表信息
     */
    @Operation(summary = "获取表信息", description = "获取指定表的详细信息")
    @GetMapping("/table/{tableName}")
    public Result<TableInfo> getTableInfo(@Parameter(description = "表名") @PathVariable String tableName) {
        try {
            TableInfo tableInfo = databaseMetaUtil.getTableInfo(tableName);
            if (tableInfo == null) {
                return Result.error("表不存在：" + tableName);
            }
            return Result.success( "获取表信息成功", tableInfo);
        } catch (Exception e) {
            log.error("获取表信息失败", e);
            return Result.error("获取表信息失败：" + e.getMessage());
        }
    }

    /**
     * 批量生成代码
     */
    @Operation(summary = "批量生成代码", description = "批量生成多个表的代码")
    @PostMapping("/batch-generate")
    public Result<List<Map<String, Object>>> batchGenerate(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> tableNames = (List<String>) request.get("tableNames");
            String moduleName = (String) request.get("moduleName");
            String packageName = (String) request.get("packageName");
            
            List<Map<String, Object>> results = new ArrayList<>();
            
            for (String tableName : tableNames) {
                GeneratorConfig config = new GeneratorConfig(tableName, moduleName);
                if (packageName != null && !packageName.isEmpty()) {
                    config.setPackageName(packageName);
                }
                config.setOutputPath("scaffold-example/src/main/java/" + config.getPackageName().replace(".", "/"));
                
                Map<String, Object> result = codeGenerator.generateCode(config);
                result.put("tableName", tableName);
                results.add(result);
            }
            
            return Result.success("批量生成完成", results);
        } catch (Exception e) {
            log.error("批量生成失败", e);
            return Result.error("批量生成失败：" + e.getMessage());
        }
    }

    /**
     * 构建模板数据
     */
    private Map<String, Object> buildTemplateData(TableInfo tableInfo, GeneratorConfig config) {
        Map<String, Object> data = new HashMap<>();
        
        // 基础信息
        data.put("tableInfo", tableInfo);
        data.put("config", config);
        data.put("packageName", config.getPackageName());
        data.put("moduleName", config.getModuleName());
        data.put("author", config.getAuthor());
        data.put("date", java.time.LocalDate.now().toString());
        
        // 类名信息
        data.put("entityName", tableInfo.getEntityName());
        data.put("entityNameLower", tableInfo.getEntityNameLower());
        data.put("serviceName", tableInfo.getEntityName() + "Service");
        data.put("serviceImplName", tableInfo.getEntityName() + "ServiceImpl");
        data.put("mapperName", tableInfo.getEntityName() + "Mapper");
        data.put("controllerName", tableInfo.getEntityName() + "Controller");
        data.put("createRequestName", tableInfo.getEntityName() + "CreateRequest");
        data.put("updateRequestName", tableInfo.getEntityName() + "UpdateRequest");
        data.put("queryRequestName", tableInfo.getEntityName() + "QueryRequest");
        data.put("voName", tableInfo.getEntityName() + "VO");
        
        return data;
    }
}
