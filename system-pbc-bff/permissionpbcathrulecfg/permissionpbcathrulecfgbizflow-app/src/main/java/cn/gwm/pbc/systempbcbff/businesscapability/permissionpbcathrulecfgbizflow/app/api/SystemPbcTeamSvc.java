package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.department.TeamTreeDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;

import java.util.Map;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-用户组管理服务")
public interface SystemPbcTeamSvc {
    /**
     * 权限中心-查询应用下用户组树形列表
     * @param request
     * @return  Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-查询应用下用户组树形列表")
    @Method(desc = "权限中心-查询应用下用户组树形列表", name = "权限中心-查询应用下用户组树形列表 system/team/tree")
    Map<String, Object> tree(@Parameter(name = "request", required = true) TeamTreeDTO request);
}
