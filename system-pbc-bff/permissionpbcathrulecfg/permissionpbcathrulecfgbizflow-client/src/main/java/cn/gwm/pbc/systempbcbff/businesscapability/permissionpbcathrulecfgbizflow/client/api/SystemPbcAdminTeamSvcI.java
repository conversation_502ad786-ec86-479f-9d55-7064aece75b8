package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDetailFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDropDownListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldSettingsFormClientDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * 权限PBC-管理团队树服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcFieldSvcI-1702533187760")
@RequestMapping(path = "/PermissionPBCAthFieldCfgBizFlow")
public interface SystemPbcAdminTeamSvcI {

     /**
     * 权限中心-根据员工工号查询完整团队树 v2-/manage/admin/team/getTeamTree
     *
     * @return Response 权限中心-根据员工工号查询完整团队树
     */
    @PostMapping(path = "/SystemPbcAdminTeamSvc/getTeamTree/v1")
    Map<String, Object> getTeamTree();

}
