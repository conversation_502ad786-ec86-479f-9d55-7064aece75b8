package com.gwm.scaffold.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Scaffold Monitor 模块自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.modules.monitor", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(name = "com.gwm.scaffold.monitor.config.MonitorAutoConfiguration")
@Import({
    com.gwm.scaffold.monitor.config.MonitorAutoConfiguration.class
})
public class ScaffoldMonitorAutoConfiguration {

    public ScaffoldMonitorAutoConfiguration() {
        log.debug("GWM Scaffold Monitor 模块已启用");
    }
}
