package com.gwm.scaffold.data.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * MyBatis Plus 配置类
 * 
 * 配置分页插件、乐观锁插件、防全表更新删除插件等
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnClass(MybatisPlusInterceptor.class)
@EnableConfigurationProperties(MybatisPlusProperties.class)
public class MybatisPlusConfig {

    private final MybatisPlusProperties properties;

    public MybatisPlusConfig(MybatisPlusProperties properties) {
        this.properties = properties;
    }

    /**
     * MyBatis Plus 拦截器配置
     * 
     * @return MybatisPlusInterceptor
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件
        if (properties.getPagination().isEnabled()) {
            PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(
                    DbType.valueOf(properties.getPagination().getDbType().toUpperCase()));
            paginationInterceptor.setOverflow(properties.getPagination().isOverflow());
            paginationInterceptor.setMaxLimit(properties.getPagination().getMaxLimit());
            interceptor.addInnerInterceptor(paginationInterceptor);
            log.info("MyBatis Plus 分页插件已启用，数据库类型：{}，最大分页数：{}", 
                    properties.getPagination().getDbType(), properties.getPagination().getMaxLimit());
        }
        
        // 防止全表更新与删除插件
        if (properties.isBlockAttackEnabled()) {
            interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
            log.info("MyBatis Plus 防全表更新删除插件已启用");
        }
        
        // 乐观锁插件
        if (properties.isOptimisticLockerEnabled()) {
            interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
            log.info("MyBatis Plus 乐观锁插件已启用");
        }
        
        return interceptor;
    }

    /**
     * 自动填充处理器
     * 
     * 自动填充创建时间、创建人、更新时间、更新人等字段
     */
    @Component
    @ConditionalOnClass(MetaObjectHandler.class)
    public static class AutoFillMetaObjectHandler implements MetaObjectHandler {

        /**
         * 插入时自动填充
         */
        @Override
        public void insertFill(MetaObject metaObject) {
            Date now = new Date();
            
            // 填充创建时间
            this.strictInsertFill(metaObject, "createTime", Date.class, now);
            
            // 填充更新时间
            this.strictInsertFill(metaObject, "updateTime", Date.class, now);
            
            // 获取当前用户信息
            UserInfo currentUser = getCurrentUser();
            
            // 填充创建人信息
            this.strictInsertFill(metaObject, "createUserCode", String.class, currentUser.getUserCode());
            this.strictInsertFill(metaObject, "createUserName", String.class, currentUser.getUserName());
            
            // 填充更新人信息
            this.strictInsertFill(metaObject, "updateUserCode", String.class, currentUser.getUserCode());
            this.strictInsertFill(metaObject, "updateUserName", String.class, currentUser.getUserName());
            
            log.debug("插入时自动填充完成，用户：{}", currentUser.getUserCode());
        }

        /**
         * 更新时自动填充
         */
        @Override
        public void updateFill(MetaObject metaObject) {
            Date now = new Date();
            
            // 填充更新时间
            this.strictUpdateFill(metaObject, "updateTime", Date.class, now);
            
            // 获取当前用户信息
            UserInfo currentUser = getCurrentUser();
            
            // 填充更新人信息
            this.strictUpdateFill(metaObject, "updateUserCode", String.class, currentUser.getUserCode());
            this.strictUpdateFill(metaObject, "updateUserName", String.class, currentUser.getUserName());
            
            log.debug("更新时自动填充完成，用户：{}", currentUser.getUserCode());
        }

        /**
         * 获取当前用户信息
         * 
         * @return 用户信息
         */
        private UserInfo getCurrentUser() {
            try {
                // 尝试从认证模块获取用户信息
                Class<?> loginUserUtilClass = Class.forName("com.gwm.scaffold.auth.util.LoginUserUtil");
                Object userCode = loginUserUtilClass.getMethod("getUserCode").invoke(null);
                Object userName = loginUserUtilClass.getMethod("getUserName").invoke(null);
                
                return new UserInfo(
                        userCode != null ? userCode.toString() : "system",
                        userName != null ? userName.toString() : "系统"
                );
            } catch (Exception e) {
                // 如果没有认证模块或获取失败，使用默认值
                log.debug("无法获取当前用户信息，使用默认值");
                return new UserInfo("system", "系统");
            }
        }

        /**
         * 用户信息内部类
         */
        private static class UserInfo {
            private final String userCode;
            private final String userName;

            public UserInfo(String userCode, String userName) {
                this.userCode = userCode;
                this.userName = userName;
            }

            public String getUserCode() {
                return userCode;
            }

            public String getUserName() {
                return userName;
            }
        }
    }
}
