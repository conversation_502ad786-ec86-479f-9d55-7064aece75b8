package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleDetailClientDTO", desc = "null")
@Data
@ApiModel("下拉规则列表请求参数对象")
public class RuleDetailClientDTO implements Serializable {

    /**
     * 规则名称
     */
    @Field(name = "规则名称", desc = "规则名称")
    @ApiModelProperty("规则ID")
    @JsonProperty(value = "rule_id")
    private Long ruleId;

}