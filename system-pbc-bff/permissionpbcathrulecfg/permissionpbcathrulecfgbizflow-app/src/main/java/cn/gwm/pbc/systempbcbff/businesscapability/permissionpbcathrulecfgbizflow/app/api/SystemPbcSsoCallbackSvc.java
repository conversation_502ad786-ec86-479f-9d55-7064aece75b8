package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.LoginRltVoClientDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;
//import com.gwm.pbc.auth.model.LoginRltVO;

/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-单点认证服务管理服务")
public interface SystemPbcSsoCallbackSvc {

//    @ReturnValue(desc = "权限中心-单点登录转换")
//    @Method(desc = "权限中心-单点登录转换", name = "权限中心-单点登录转换 system/sso/callback")
//    LoginRltVO ssoCallback(@Parameter(name = "accessToken", required = true) String accessToken);
    /**
     * 权限中心-单点登录转换
     * @return LoginRltVOClientDTO
     */
    @ReturnValue(desc = "权限中心-单点登录转换")
    @Method(desc = "权限中心-单点登录转换", name = "权限中心-单点登录转换 system/sso/callback")
    LoginRltVoClientDTO ssoCallback();

}
