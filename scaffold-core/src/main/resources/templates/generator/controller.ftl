package ${packageName}.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwm.scaffold.auth.annotation.RequirePermission;
import com.gwm.scaffold.core.domain.Result;
import ${packageName}.dto.${createRequestName};
import ${packageName}.dto.${updateRequestName};
import ${packageName}.dto.${queryRequestName};
import ${packageName}.service.${serviceName};
import ${packageName}.vo.${voName};
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * ${tableInfo.tableComment!entityName}管理控制器
 * 
 * <AUTHOR>
 * @date ${date}
 */
@Slf4j
@Tag(name = "${tableInfo.tableComment!entityName}管理", description = "${tableInfo.tableComment!entityName}相关接口")
@RestController
@RequestMapping("${config.apiPrefix}/${entityNameLower}s")
public class ${controllerName} {

    @Autowired
    private ${serviceName} ${entityNameLower}Service;

    /**
     * 分页查询${tableInfo.tableComment!entityName}列表
     */
    @Operation(summary = "分页查询${tableInfo.tableComment!entityName}列表", description = "支持多条件查询")
    @GetMapping
    @RequirePermission("${entityNameLower}:list")
    public Result<IPage<${voName}>> list(${queryRequestName} request) {
        log.info("分页查询${tableInfo.tableComment!entityName}列表，参数：{}", request);
        
        IPage<${voName}> result = ${entityNameLower}Service.get${entityName}Page(request);
        return Result.success(result);
    }

    /**
     * 根据ID获取${tableInfo.tableComment!entityName}详情
     */
    @Operation(summary = "获取${tableInfo.tableComment!entityName}详情", description = "根据ID获取${tableInfo.tableComment!entityName}详细信息")
    @GetMapping("/{id}")
    @RequirePermission("${entityNameLower}:read")
    public Result<${voName}> getById(@Parameter(description = "${tableInfo.tableComment!entityName}ID") @PathVariable ${tableInfo.primaryKey.javaType} id) {
        log.info("获取${tableInfo.tableComment!entityName}详情，ID：{}", id);
        
        ${voName} result = ${entityNameLower}Service.get${entityName}ById(id);
        return Result.success(result);
    }

    /**
     * 创建${tableInfo.tableComment!entityName}
     */
    @Operation(summary = "创建${tableInfo.tableComment!entityName}", description = "创建新的${tableInfo.tableComment!entityName}")
    @PostMapping
    @RequirePermission("${entityNameLower}:create")
    public Result<${voName}> create(@Valid @RequestBody ${createRequestName} request) {
        log.info("创建${tableInfo.tableComment!entityName}，参数：{}", request);
        
        ${voName} result = ${entityNameLower}Service.create${entityName}(request);
        return Result.success(result);
    }

    /**
     * 更新${tableInfo.tableComment!entityName}
     */
    @Operation(summary = "更新${tableInfo.tableComment!entityName}", description = "更新${tableInfo.tableComment!entityName}信息")
    @PutMapping("/{id}")
    @RequirePermission("${entityNameLower}:update")
    public Result<${voName}> update(
            @Parameter(description = "${tableInfo.tableComment!entityName}ID") @PathVariable ${tableInfo.primaryKey.javaType} id,
            @Valid @RequestBody ${updateRequestName} request) {
        log.info("更新${tableInfo.tableComment!entityName}，ID：{}，参数：{}", id, request);
        
        request.setId(id);
        ${voName} result = ${entityNameLower}Service.update${entityName}(request);
        return Result.success(result);
    }

    /**
     * 删除${tableInfo.tableComment!entityName}
     */
    @Operation(summary = "删除${tableInfo.tableComment!entityName}", description = "根据ID删除${tableInfo.tableComment!entityName}")
    @DeleteMapping("/{id}")
    @RequirePermission("${entityNameLower}:delete")
    public Result<String> delete(@Parameter(description = "${tableInfo.tableComment!entityName}ID") @PathVariable ${tableInfo.primaryKey.javaType} id) {
        log.info("删除${tableInfo.tableComment!entityName}，ID：{}", id);
        
        boolean success = ${entityNameLower}Service.delete${entityName}(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * 批量删除${tableInfo.tableComment!entityName}
     */
    @Operation(summary = "批量删除${tableInfo.tableComment!entityName}", description = "根据ID列表批量删除${tableInfo.tableComment!entityName}")
    @DeleteMapping("/batch")
    @RequirePermission("${entityNameLower}:delete")
    public Result<String> batchDelete(@Parameter(description = "${tableInfo.tableComment!entityName}ID列表") @RequestBody List<${tableInfo.primaryKey.javaType}> ids) {
        log.info("批量删除${tableInfo.tableComment!entityName}，ID列表：{}", ids);
        
        int count = ${entityNameLower}Service.batchDelete${entityName}s(ids);
        return Result.success("成功删除 " + count + " 个${tableInfo.tableComment!entityName}");
    }


}
