package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;


import com.alibaba.bizworks.core.specification.*;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
/**
 * <AUTHOR>
 */
@StructureObject(name = "QueryResult", desc = "")
public class QueryResult<T> implements Serializable {
    /**
     * page
     */
    @Field(name = "page", desc = "")
    public final int page;
    /**
     * total
     */
    @Field(name = "total", desc = "")
    public final long total;
    /**
     * rows
     */
    @Field(name = "rows", desc = "")
    public final List<T> rows;

    public QueryResult(long totalRecords, List<T> data, int page) {
        this.total = totalRecords;
        this.rows = data;
        this.page = page;
    }

    public QueryResult() {
        this.page = 1;
        this.total = 0L;
        this.rows = Collections.emptyList();
    }

}
