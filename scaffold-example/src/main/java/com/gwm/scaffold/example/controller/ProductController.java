package com.gwm.scaffold.example.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwm.scaffold.auth.annotation.RequirePermission;
import com.gwm.scaffold.core.domain.Result;
import com.gwm.scaffold.example.dto.ProductCreateRequest;
import com.gwm.scaffold.example.dto.ProductUpdateRequest;
import com.gwm.scaffold.example.dto.ProductQueryRequest;
import com.gwm.scaffold.example.service.ProductService;
import com.gwm.scaffold.example.vo.ProductVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 产品信息表管理控制器
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Tag(name = "产品信息表管理", description = "产品信息表相关接口")
@RestController
@RequestMapping("/api/products")
public class ProductController {

    @Autowired
    private ProductService productService;

    /**
     * 分页查询产品信息表列表
     */
    @Operation(summary = "分页查询产品信息表列表", description = "支持多条件查询")
    @GetMapping
    @RequirePermission("product:list")
    public Result<IPage<ProductVO>> list(ProductQueryRequest request) {
        log.info("分页查询产品信息表列表，参数：{}", request);
        
        IPage<ProductVO> result = productService.getProductPage(request);
        return Result.success(result);
    }

    /**
     * 根据ID获取产品信息表详情
     */
    @Operation(summary = "获取产品信息表详情", description = "根据ID获取产品信息表详细信息")
    @GetMapping("/{id}")
    @RequirePermission("product:read")
    public Result<ProductVO> getById(@Parameter(description = "产品信息表ID") @PathVariable Long id) {
        log.info("获取产品信息表详情，ID：{}", id);
        
        ProductVO result = productService.getProductById(id);
        return Result.success(result);
    }

    /**
     * 创建产品信息表
     */
    @Operation(summary = "创建产品信息表", description = "创建新的产品信息表")
    @PostMapping
    @RequirePermission("product:create")
    public Result<ProductVO> create(@Valid @RequestBody ProductCreateRequest request) {
        log.info("创建产品信息表，参数：{}", request);
        
        ProductVO result = productService.createProduct(request);
        return Result.success(result);
    }

    /**
     * 更新产品信息表
     */
    @Operation(summary = "更新产品信息表", description = "更新产品信息表信息")
    @PutMapping("/{id}")
    @RequirePermission("product:update")
    public Result<ProductVO> update(
            @Parameter(description = "产品信息表ID") @PathVariable Long id,
            @Valid @RequestBody ProductUpdateRequest request) {
        log.info("更新产品信息表，ID：{}，参数：{}", id, request);
        
        request.setId(id);
        ProductVO result = productService.updateProduct(request);
        return Result.success(result);
    }

    /**
     * 删除产品信息表
     */
    @Operation(summary = "删除产品信息表", description = "根据ID删除产品信息表")
    @DeleteMapping("/{id}")
    @RequirePermission("product:delete")
    public Result<String> delete(@Parameter(description = "产品信息表ID") @PathVariable Long id) {
        log.info("删除产品信息表，ID：{}", id);
        
        boolean success = productService.deleteProduct(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * 批量删除产品信息表
     */
    @Operation(summary = "批量删除产品信息表", description = "根据ID列表批量删除产品信息表")
    @DeleteMapping("/batch")
    @RequirePermission("product:delete")
    public Result<String> batchDelete(@Parameter(description = "产品信息表ID列表") @RequestBody List<Long> ids) {
        log.info("批量删除产品信息表，ID列表：{}", ids);
        
        int count = productService.batchDeleteProducts(ids);
        return Result.success("成功删除 " + count + " 个产品信息表");
    }


}
