package com.gwm.scaffold.auth.annotation;

import java.lang.annotation.*;

/**
 * 权限校验注解
 * 
 * 用于方法级别的权限控制
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequirePermission {

    /**
     * 权限标识
     */
    String[] value() default {};

    /**
     * 权限标识（别名）
     */
    String[] permissions() default {};

    /**
     * 逻辑关系：AND-所有权限都必须满足，OR-满足任一权限即可
     */
    Logical logical() default Logical.AND;

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        /**
         * 且
         */
        AND,
        /**
         * 或
         */
        OR
    }
}
