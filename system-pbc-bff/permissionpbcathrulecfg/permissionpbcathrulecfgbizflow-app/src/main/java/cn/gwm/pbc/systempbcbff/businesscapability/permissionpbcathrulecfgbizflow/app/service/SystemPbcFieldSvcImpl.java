package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcFieldSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field.RuleFieldDropDownListForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field.RuleFieldListForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field.RuleFieldSettingsForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteFieldService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDetailFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldDropDownListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldListFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field.RuleFieldSettingsFormClientDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
/**
 * <AUTHOR>
 */
@Component("systemPbcFieldSvcImpl")
public class SystemPbcFieldSvcImpl implements SystemPbcFieldSvc {


    @Autowired
    private RemoteFieldService remoteFieldService;

    @Override
    public Map<String, Object> fieldList(RuleFieldListFormClientDTO request) {
        RuleFieldListForm remote = new RuleFieldListForm();
        BeanUtil.copyProperties(request, remote);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteFieldService.fieldList(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> fieldSettings(RuleFieldSettingsFormClientDTO request) {
        RuleFieldSettingsForm remote = new RuleFieldSettingsForm();
        BeanUtil.copyProperties(request, remote);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteFieldService.fieldSettings(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> fieldDetail(RuleFieldDetailFormClientDTO request) {
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteFieldService.fieldDetail(request.getRoleSetId());
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> dropDownList(RuleFieldDropDownListFormClientDTO request) {
        RuleFieldDropDownListForm remote = new RuleFieldDropDownListForm();
        BeanUtil.copyProperties(request, remote);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteFieldService.dropDownList(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> fieldSettingsTemp(RuleFieldSettingsFormClientDTO request) {
        RuleFieldSettingsForm remote = new RuleFieldSettingsForm();
        BeanUtil.copyProperties(request, remote);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteFieldService.fieldSettingsTemp(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }
}
