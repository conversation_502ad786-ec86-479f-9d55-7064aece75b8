package com.gwm.scaffold.tools.util;

import com.alibaba.fastjson.JSON;
import com.gwm.scaffold.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.Semaphore;

import static com.gwm.scaffold.tools.util.OkHttpUtil.BodyMode.*;
import static com.gwm.scaffold.tools.util.Constants.SUPPRESS_WARNINGS_VAL;
import static java.util.concurrent.TimeUnit.SECONDS;
import static okhttp3.MediaType.Companion;
import static okhttp3.MediaType.parse;
import static okhttp3.MultipartBody.FORM;
import static okhttp3.RequestBody.create;

/**
 * OkHttp工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class OkHttpUtil {

    private static final int SUCCESS_CODE = 200;
    private static final int CONNECT_TIMEOUT = 15;
    private static final int WRITE_TIMEOUT = 20;
    private static final int READ_TIMEOUT = 30;
    private static final String USER_AGENT_KEY = "User-Agent";
    private static final String USER_AGENT_VALUE = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36";
    private static final String JSON_FORMAT = "application/json;charset=utf-8";
    private static final String FILE_FORMAT = "multipart/form-data";
    private static final String STREAM_FORMAT = "application/octet-stream";
    private static volatile OkHttpClient okHttpClient = null;
    private static volatile Semaphore semaphore = null;
    private Request.Builder request;
    private String url;
    private final Map<String, String> headerMap = new LinkedHashMap<>(16);
    private final Map<String, String> paramMap = new LinkedHashMap<>(16);
    private Object body;
    private final Map<String, Object> bodyMap = new LinkedHashMap<>(16);
    private final Map<String, Object> formMap = new LinkedHashMap<>(16);
    private final Map<String, File> fileMap = new LinkedHashMap<>(16);

    /**
     * 请求方式
     */
    private RequestMethodEnum requestMethodEnum;

    /**
     * 初始化okHttpClient，并且允许https访问
     */
    private OkHttpUtil() {
        if (null == okHttpClient) {
            synchronized (OkHttpUtil.class) {
                if (null == okHttpClient) {
                    TrustManager[] trustManagers = buildTrustManagers();
                    okHttpClient = new OkHttpClient.Builder()
                            .connectTimeout(CONNECT_TIMEOUT, SECONDS)
                            .writeTimeout(WRITE_TIMEOUT, SECONDS)
                            .readTimeout(READ_TIMEOUT, SECONDS)
                            .sslSocketFactory(createSSLSocketFactory(trustManagers),
                                    (X509TrustManager) trustManagers[0])
                            .hostnameVerifier((hostName, session) -> true)
                            .retryOnConnectionFailure(true)
                            .build();
                    addHeader(USER_AGENT_KEY, USER_AGENT_VALUE);
                }
            }
        }
    }

    /**
     * 创建OkHttpUtil
     */
    public static OkHttpUtil builder() {
        return new OkHttpUtil();
    }

    /**
     * 添加请求地址
     *
     * @param url 请求地址
     */
    public OkHttpUtil url(String url) {
        this.url = url;
        return this;
    }

    /**
     * 添加请求头
     *
     * @param headerMap 请求头map
     */
    public OkHttpUtil addHeader(Map<String, String> headerMap) {
        this.headerMap.putAll(headerMap);
        return this;
    }

    /**
     * 添加请求头
     *
     * @param key   参数名
     * @param value 参数值
     */
    public OkHttpUtil addHeader(String key, String value) {
        headerMap.put(key, value);
        return this;
    }

    /**
     * 添加请求头
     *
     * @param condition 条件
     * @param key       参数名
     * @param value     参数值
     */
    public OkHttpUtil addHeader(boolean condition, String key, String value) {
        if (condition) {
            addHeader(key, value);
        }
        return this;
    }

    /**
     * 添加参数
     *
     * @param param 参数
     */
    @SuppressWarnings(SUPPRESS_WARNINGS_VAL)
    public OkHttpUtil param(Object param) {
        this.paramMap.clear();
        if (param instanceof Map) {
            this.paramMap.putAll((Map<? extends String, ? extends String>) param);
        } else {
            this.paramMap.putAll(ObjectUtil.objectToMap(paramMap));
        }
        return initGet();
    }

    /**
     * 添加参数
     *
     * @param key   参数名
     * @param value 参数值
     */
    public OkHttpUtil addParam(String key, String value) {
        addParam(true, key, value);
        return initGet();
    }

    /**
     * 添加参数
     *
     * @param param 参数
     */
    @SuppressWarnings(SUPPRESS_WARNINGS_VAL)
    public OkHttpUtil addParam(Object param) {
        if (param instanceof Map) {
            paramMap.putAll((Map<? extends String, ? extends String>) param);
        } else {
            paramMap.putAll(ObjectUtil.objectToMap(paramMap));
        }
        return this;
    }

    /**
     * 添加参数
     *
     * @param condition 条件
     * @param key       参数名
     * @param value     参数值
     */
    public OkHttpUtil addParam(boolean condition, String key, String value) {
        if (condition) {
            paramMap.put(key, value);
        }
        return initGet();
    }

    /**
     * 添加请求体
     *
     * @param body 请求体
     */
    public OkHttpUtil body(Object body) {
        return body(true, body);
    }

    /**
     * 添加请求体
     *
     * @param condition 条件
     * @param body      请求体
     */
    @SuppressWarnings(SUPPRESS_WARNINGS_VAL)
    public OkHttpUtil body(boolean condition, Object body) {
        if (condition) {
            this.bodyMap.clear();
            if (body instanceof Map) {
                this.bodyMap.putAll((Map<? extends String, ?>) body);
            } else {
                this.body = body;
            }
        }
        return initPost();
    }

    /**
     * 添加请求体
     *
     * @param key   参数名
     * @param value 参数值
     */
    public OkHttpUtil addBody(String key, Object value) {
        return addBody(true, key, value);
    }

    /**
     * 添加请求体
     *
     * @param condition 条件
     * @param key       参数名
     * @param value     参数值
     */
    public OkHttpUtil addBody(boolean condition, String key, Object value) {
        if (condition) {
            bodyMap.put(key, value);
        }
        initPost();
        return this;
    }

    /**
     * 添加form表单体
     *
     * @param key   参数名
     * @param value 参数值
     */
    public OkHttpUtil addForm(String key, Object value) {
        return addForm(true, key, value);
    }

    /**
     * 添加form表单体
     *
     * @param condition 条件
     * @param key       参数名
     * @param value     参数值
     */
    public OkHttpUtil addForm(boolean condition, String key, Object value) {
        if (condition) {
            formMap.put(key, value);
        }
        return this;
    }

    /**
     * 生成表单
     *
     * @param condition 条件
     * @param form      表单信息
     * @return 该对象
     */
    @SuppressWarnings(SUPPRESS_WARNINGS_VAL)
    public OkHttpUtil form(boolean condition, Object form) {
        if (condition) {
            this.formMap.clear();
            if (body instanceof Map) {
                this.formMap.putAll((Map<? extends String, ?>) form);
            } else {
                this.formMap.putAll(JSON.parseObject(JSON.toJSONString(form), Map.class));
            }
        }
        return this;
    }

    /**
     * 添加文件
     *
     * @param key  参数名
     * @param file 文件
     */
    public OkHttpUtil addFile(String key, File file) {
        fileMap.put(key, file);
        return this;
    }

    /**
     * 添加文件
     *
     * @param condition 条件
     * @param key       参数名
     * @param file      文件
     */
    public OkHttpUtil addFile(boolean condition, String key, File file) {
        if (condition) {
            addFile(key, file);
        }
        return this;
    }

    /**
     * get请求
     */
    public OkHttpUtil get() {
        initGet();
        return this;
    }

    /**
     * post请求
     */
    public OkHttpUtil post() {
        return post(JSON_MODE);
    }

    /**
     * post请求
     *
     * @param bodyMode body数据模式
     */
    public OkHttpUtil post(BodyMode bodyMode) {
        addUrlParam();
        requestMethodEnum = RequestMethodEnum.POST;
        request = new Request.Builder().url(url).post(buildRequestBody(null == bodyMode ? JSON_MODE : bodyMode));
        return this;
    }

    /**
     * put请求
     *
     * @param bodyMode body数据模式
     */
    public OkHttpUtil put(BodyMode bodyMode) {
        addUrlParam();
        requestMethodEnum = RequestMethodEnum.PUT;
        request = new Request.Builder().url(url).put(buildRequestBody(bodyMode));
        return this;
    }

    /**
     * delete请求
     *
     * @param bodyMode body数据模式
     */
    public OkHttpUtil delete(BodyMode bodyMode) {
        addUrlParam();
        requestMethodEnum = RequestMethodEnum.DELETE;
        request = new Request.Builder().url(url).delete(buildRequestBody(bodyMode));
        return this;
    }

    /**
     * 同步请求
     *
     * @return 请求结果
     */
    public ResponseBody sync() {
        setHeader(request);
        try {
            Response response = okHttpClient.newCall(request.build()).execute();
            if (null == response.body()) {
                log.error("同步请求({})：{}，失败，结果为空，param：{}，body：{}", requestMethodEnum, url, JSON.toJSONString(paramMap), JSON.toJSONString(body));
                throw new ServiceException("同步请求(" + requestMethodEnum + "):" + url + "失败！");
            }
            if (SUCCESS_CODE != response.code()) {
                log.error("同步请求({})：{}，失败，http状态码：{}，param：{}，body：{}", requestMethodEnum, url, response.code(), JSON.toJSONString(paramMap), JSON.toJSONString(body));
                throw new ServiceException("同步请求(" + requestMethodEnum + "):" + url + "失败！", response.code());
            }
            log.info("同步请求({})：{}，param：{}，body：{}，result:{}", requestMethodEnum, url, JSON.toJSONString(paramMap), JSON.toJSONString(body), JSON.toJSONString(response));
            return response.body();
        } catch (Exception e) {
            log.error("同步请求({})：{}，失败，param：{}，body：{}", requestMethodEnum, url, JSON.toJSONString(paramMap), JSON.toJSONString(body), e);
            throw new ServiceException("同步请求(" + requestMethodEnum + "):" + url + "失败！");
        } finally {
            initParam();
        }
    }

    /**
     * 异步请求，有返回值（限流的，同一时间只允许一个访问，其他等待）
     *
     * @return 请求结果
     */
    public String async() {
        StringBuilder sb = new StringBuilder();
        setHeader(request);
        okHttpClient.newCall(request.build()).enqueue(new Callback() {
            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                if (SUCCESS_CODE != response.code()) {
                    log.error("异步请求({})：{}，失败，http状态码：{}，param：{}，body：{}", requestMethodEnum, url, response.code(), JSON.toJSONString(paramMap), JSON.toJSONString(body));
                    throw new ServiceException("异步请求(" + requestMethodEnum + "):" + url + "失败！");
                }
                ResponseBody responseBody = response.body();
                if (null != responseBody) {
                    log.info("异步请求({})：{}，param：{}，body：{}", requestMethodEnum, url, JSON.toJSONString(paramMap), JSON.toJSONString(body));
                    sb.append(responseBody.string());
                    getSemaphoreInstance().release();
                }
                initParam();
            }

            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                log.error("异步请求({})：{}，失败，param：{}，body：{}", requestMethodEnum, url, JSON.toJSONString(paramMap), JSON.toJSONString(body), e);
                initParam();
                throw new ServiceException("异步请求(" + requestMethodEnum + "):" + url + "失败！");
            }
        });
        try {
            getSemaphoreInstance().acquire();
        } catch (InterruptedException e) {
            log.error("发送异步请求异常", e);
            throw new ServiceException("异步请求(" + requestMethodEnum + "):" + url + "失败！");
        }
        return sb.toString();
    }

    /**
     * 异步请求，带有接口回调
     *
     * @param callBack 回调接口
     */
    public void async(ICallBack callBack) {
        setHeader(request);
        okHttpClient.newCall(request.build()).enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                log.error("异步请求({})：{}，失败，param：{}，body：{}", requestMethodEnum, url, JSON.toJSONString(paramMap), JSON.toJSONString(body), e);
                callBack.onFailure(call, e.getMessage());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                if (null != response.body()) {
                    log.info("异步请求({})：{}，param：{}，body：{}", requestMethodEnum, url, JSON.toJSONString(paramMap), JSON.toJSONString(body));
                    callBack.onResponse(call, response.body().string());
                }
                initParam();
            }
        });
    }

    /**
     * 为request添加请求头
     *
     * @param request 请求
     */
    private void setHeader(Request.Builder request) {
        headerMap.forEach(request::header);
    }

    /**
     * 请求的url添加路径参数
     */
    private void addUrlParam() {
        StringBuilder sb = new StringBuilder(url);
        if (!CollectionUtils.isEmpty(paramMap)) {
            sb.append("?");
            try {
                for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                    sb.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8.toString()))
                            .append("=")
                            .append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.toString()))
                            .append("&");
                }
            } catch (Exception e) {
                log.error("设置请求路径异常", e);
            }
            sb.deleteCharAt(sb.length() - 1);
        }
        url = sb.toString();
    }

    /**
     * 初始化post请求map
     *
     * @return map
     */
    private RequestBody buildRequestBody(BodyMode bodyMode) {
        if (JSON_MODE.equals(bodyMode)) {
            return jsonPost();
        }
        if (FORM_MODE.equals(bodyMode)) {
            return formPost();
        }
        if (FILE_MODE.equals(bodyMode)) {
            return filePost();
        }
        throw new ServiceException("不支持的body数据模式");
    }

    /**
     * json post
     *
     * @return 请求体
     */
    private RequestBody jsonPost() {
        String json;
        if (null != body) {
            json = JSON.toJSONString(body);
        } else if (!CollectionUtils.isEmpty(bodyMap)) {
            json = JSON.toJSONString(bodyMap);
        } else {
            json = "";
        }
        return create(json, parse(JSON_FORMAT));
    }

    /**
     * form post
     *
     * @return 请求体
     */
    private RequestBody formPost() {
        FormBody.Builder formBody = new FormBody.Builder();
        if (!CollectionUtils.isEmpty(formMap)) {
            formMap.forEach((k, v) -> formBody.add(k, String.valueOf(v)));
        }
        return formBody.build();
    }

    /**
     * file post
     *
     * @return 请求体
     */
    private RequestBody filePost() {
        MultipartBody.Builder fileBody = new MultipartBody.Builder();
        if (!CollectionUtils.isEmpty(fileMap)) {
            fileMap.forEach((k, v) ->
                    fileBody.setType(FORM)
                            .addFormDataPart(k, v.getName(), create(v, Companion.parse(STREAM_FORMAT)))
                            .addFormDataPart("file_size", String.valueOf(v.length()))
            );
        }
        if (!CollectionUtils.isEmpty(formMap)) {
            formMap.forEach((k, v) -> fileBody.addFormDataPart(k, String.valueOf(v)));
        }
        return fileBody.build();
    }

    /**
     * 文件请求
     *
     * @return 请求结果
     */
    public InputStream file() {
        setHeader(request);
        try {
            Response response = okHttpClient.newCall(request.build()).execute();
            if (null == response.body()) {
                return null;
            }
            return response.body().byteStream();
        } catch (IOException e) {
            log.error("发送文件请求异常", e);
            throw new ServiceException("发送文件请求异常：" + e.getMessage());
        }
    }

    /**
     * 用于异步请求时，控制访问线程数
     *
     * @return Semaphore
     */
    private Semaphore getSemaphoreInstance() {
        // 只能1个线程同时访问
        if (null == semaphore) {
            synchronized (OkHttpUtil.class) {
                if (semaphore == null) {
                    semaphore = new Semaphore(0);
                }
            }
        }
        return semaphore;
    }

    private TrustManager[] buildTrustManagers() {
        return new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
        };
    }

    /**
     * 生成安全套接字工厂，用于https请求的证书跳过。
     *
     * @return SSLSocketFactory
     */
    private SSLSocketFactory createSSLSocketFactory(TrustManager[] trustAllCerts) {
        SSLSocketFactory sslFactory = null;
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            sslFactory = sc.getSocketFactory();
        } catch (Exception e) {
            log.error("创建SSLSocketFactory异常", e);
        }
        return sslFactory;
    }

    /**
     * 初始化post请求
     *
     * @return 当前请求对象
     */
    private OkHttpUtil initPost() {
        if (!StringUtils.hasText(url)) {
            addUrlParam();
        }
        if (null == requestMethodEnum) {
            requestMethodEnum = RequestMethodEnum.POST;
        }
        request = new Request.Builder().url(url).post(buildRequestBody(JSON_MODE));
        return this;
    }

    /**
     * 初始化get请求
     *
     * @return 当前请求对象
     */
    private OkHttpUtil initGet() {
        addUrlParam();
        if (null == requestMethodEnum) {
            requestMethodEnum = RequestMethodEnum.GET;
        }
        if (null == request) {
            request = new Request.Builder().url(url).get();
        }
        return this;
    }

    /**
     * 初始化参数
     */
    private void initParam() {
        body = null;
        url = null;
        requestMethodEnum = null;
        formMap.clear();
        fileMap.clear();
        bodyMap.clear();
        paramMap.clear();
        headerMap.clear();
        headerMap.put(USER_AGENT_KEY, USER_AGENT_VALUE);
    }

    /**
     * 请求方式
     */
    private enum RequestMethodEnum {
        /**
         * GET
         */
        GET,
        /**
         * POST
         */
        POST,
        /**
         * PUT
         */
        PUT,
        /**
         * DELETE
         */
        DELETE
    }

    /**
     * body数据模式
     */
    public enum BodyMode {
        /**
         * json格式
         */
        JSON_MODE,

        /**
         * form格式
         */
        FORM_MODE,

        /**
         * 文件格式
         */
        FILE_MODE
    }


    /**
     * 自定义一个接口回调
     */
    public interface ICallBack {

        /**
         * 请求成功
         *
         * @param call 请求
         * @param data 响应数据
         */
        void onResponse(Call call, String data);

        /**
         * 请求失败
         *
         * @param call     请求
         * @param errorMsg 错误信息
         */
        void onFailure(Call call, String errorMsg);
    }
}
