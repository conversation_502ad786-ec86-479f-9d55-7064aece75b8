package com.gwm.scaffold.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * Scaffold Core 模块自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.enabled", havingValue = "true", matchIfMissing = true)
public class ScaffoldCoreAutoConfiguration {

    public ScaffoldCoreAutoConfiguration() {
        log.debug("GWM Scaffold Core 模块已启用");
    }
}
