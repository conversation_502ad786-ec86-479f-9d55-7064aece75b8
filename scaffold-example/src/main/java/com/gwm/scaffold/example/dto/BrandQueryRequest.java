package com.gwm.scaffold.example.dto;

import java.time.LocalDateTime;
import com.gwm.scaffold.web.cmd.AbstractPageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "查询请求")
public class BrandQueryRequest extends AbstractPageRequest {

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * name
     */
    @Schema(description = "name")
    private String name;

    /**
     * englishName
     */
    @Schema(description = "englishName")
    private String englishName;

    /**
     * country
     */
    @Schema(description = "country")
    private String country;

    /**
     * foundedYear
     */
    @Schema(description = "foundedYear")
    private Integer foundedYear;

    /**
     * website
     */
    @Schema(description = "website")
    private String website;

    /**
     * logoUrl
     */
    @Schema(description = "logoUrl")
    private String logoUrl;

    /**
     * description
     */
    @Schema(description = "description")
    private String description;

    /**
     * status
     */
    @Schema(description = "status")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    private Integer sortWeight;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

    /**
     * deletedTime开始时间
     */
    @Schema(description = "deletedTime开始时间")
    private LocalDateTime deletedTimeStart;

    /**
     * deletedTime结束时间
     */
    @Schema(description = "deletedTime结束时间")
    private LocalDateTime deletedTimeEnd;

}
