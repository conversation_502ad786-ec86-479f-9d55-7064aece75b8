package com.gwm.scaffold.example.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gwm.scaffold.example.entity.Model;
import com.gwm.scaffold.example.dto.ModelCreateRequest;
import com.gwm.scaffold.example.dto.ModelUpdateRequest;
import com.gwm.scaffold.example.dto.ModelQueryRequest;
import com.gwm.scaffold.example.vo.ModelVO;

import java.util.List;

/**
 * 服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface ModelService extends IService<Model> {

    /**
     * 分页查询列表
     *
     * @param request 查询条件
     * @return 分页数据
     */
    IPage<ModelVO> getModelPage(ModelQueryRequest request);

    /**
     * 根据ID获取详情
     *
     * @param id ID
     * @return 详情
     */
    ModelVO getModelById(Long id);

    /**
     * 创建
     *
     * @param request 创建请求
     * @return 详情
     */
    ModelVO createModel(ModelCreateRequest request);

    /**
     * 更新
     *
     * @param request 更新请求
     * @return 详情
     */
    ModelVO updateModel(ModelUpdateRequest request);

    /**
     * 删除
     *
     * @param id ID
     * @return 是否成功
     */
    boolean deleteModel(Long id);

    /**
     * 批量删除
     *
     * @param ids ID列表
     * @return 删除数量
     */
    int batchDeleteModels(List<Long> ids);


}
