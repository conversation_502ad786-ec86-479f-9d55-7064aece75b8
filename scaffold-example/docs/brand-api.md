# 品牌管理API文档

## 概述

品牌管理模块提供了完整的RESTful API接口，支持品牌信息的增删改查、状态管理、统计查询等功能。

## 接口列表

### 1. 分页查询品牌列表

**接口地址：** `GET /api/brands`

**接口描述：** 支持多条件查询的品牌分页列表

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| current | Long | 否 | 当前页码，默认1 | 1 |
| size | Long | 否 | 每页数量，默认10 | 10 |
| name | String | 否 | 品牌名称（模糊查询） | 奔驰 |
| englishName | String | 否 | 英文名称（模糊查询） | Mercedes |
| country | String | 否 | 所属国家 | 德国 |
| status | String | 否 | 状态（ACTIVE/INACTIVE） | ACTIVE |
| foundedYear | Integer | 否 | 成立年份 | 1926 |

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "奔驰",
        "englishName": "Mercedes-Benz",
        "country": "德国",
        "foundedYear": 1926,
        "website": "https://www.mercedes-benz.com",
        "logoUrl": "https://example.com/logo/benz.png",
        "description": "豪华汽车品牌",
        "status": "ACTIVE",
        "statusName": "启用",
        "sortWeight": 100,
        "createTime": "2024-01-01T00:00:00",
        "createUserName": "admin"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 2. 获取品牌详情

**接口地址：** `GET /api/brands/{id}`

**接口描述：** 根据ID获取品牌详细信息

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 品牌ID |

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "奔驰",
    "englishName": "Mercedes-Benz",
    "country": "德国",
    "foundedYear": 1926,
    "website": "https://www.mercedes-benz.com",
    "logoUrl": "https://example.com/logo/benz.png",
    "description": "豪华汽车品牌",
    "status": "ACTIVE",
    "statusName": "启用",
    "sortWeight": 100,
    "createTime": "2024-01-01T00:00:00",
    "createUserName": "admin",
    "updateTime": "2024-01-15T10:30:00",
    "updateUserName": "admin"
  }
}
```

### 3. 创建品牌

**接口地址：** `POST /api/brands`

**接口描述：** 创建新的品牌

**请求体：**
```json
{
  "name": "奔驰",
  "englishName": "Mercedes-Benz",
  "country": "德国",
  "foundedYear": 1926,
  "website": "https://www.mercedes-benz.com",
  "logoUrl": "https://example.com/logo/benz.png",
  "description": "豪华汽车品牌",
  "status": "ACTIVE",
  "sortWeight": 100
}
```

**字段说明：**
| 字段名 | 类型 | 必填 | 校验规则 | 说明 |
|--------|------|------|----------|------|
| name | String | 是 | 2-50字符，中英文数字符号 | 品牌名称 |
| englishName | String | 否 | 2-100字符，英文数字符号 | 英文名称 |
| country | String | 是 | 最大50字符 | 所属国家 |
| foundedYear | Integer | 否 | 1800-当前年份 | 成立年份 |
| website | String | 否 | URL格式，最大255字符 | 官网地址 |
| logoUrl | String | 否 | 最大500字符 | LOGO图片URL |
| description | String | 否 | 最大1000字符 | 品牌简介 |
| status | String | 否 | ACTIVE或INACTIVE | 状态 |
| sortWeight | Integer | 否 | 0-9999 | 排序权重 |

### 4. 更新品牌

**接口地址：** `PUT /api/brands/{id}`

**接口描述：** 更新品牌信息

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 品牌ID |

**请求体：** 同创建接口，但所有字段都是可选的（只更新传入的字段）

### 5. 删除品牌

**接口地址：** `DELETE /api/brands/{id}`

**接口描述：** 删除指定品牌（软删除）

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 品牌ID |

### 6. 批量删除品牌

**接口地址：** `DELETE /api/brands/batch`

**接口描述：** 批量删除品牌

**请求体：**
```json
[1, 2, 3, 4, 5]
```

### 7. 获取启用品牌列表

**接口地址：** `GET /api/brands/active`

**接口描述：** 获取所有启用状态的品牌列表

### 8. 更新品牌状态

**接口地址：** `PUT /api/brands/{id}/status`

**接口描述：** 更新品牌的启用/禁用状态

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 品牌ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | String | 是 | 新状态（ACTIVE/INACTIVE） |

### 9. 按国别统计品牌

**接口地址：** `GET /api/brands/statistics/country`

**接口描述：** 统计各国别的品牌数量

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 业务异常说明

| 异常信息 | 说明 | 解决方案 |
|----------|------|----------|
| 品牌名称已存在 | 品牌名称重复 | 使用不同的品牌名称 |
| 品牌英文名称已存在 | 英文名称重复 | 使用不同的英文名称 |
| 品牌不存在 | 指定ID的品牌不存在 | 检查品牌ID是否正确 |
| 成立年份不能晚于当前年份 | 年份设置错误 | 设置合理的成立年份 |
| 无效的品牌状态 | 状态值不正确 | 使用ACTIVE或INACTIVE |

## 权限说明

| 接口 | 所需权限 |
|------|----------|
| 查询接口 | brand:list, brand:read |
| 创建接口 | brand:create |
| 更新接口 | brand:update |
| 删除接口 | brand:delete |

## 使用示例

### 创建品牌示例

```bash
curl -X POST http://localhost:8080/api/brands \
  -H "Content-Type: application/json" \
  -d '{
    "name": "特斯拉",
    "englishName": "Tesla",
    "country": "美国",
    "foundedYear": 2003,
    "website": "https://www.tesla.com",
    "description": "电动汽车制造商",
    "status": "ACTIVE"
  }'
```

### 查询品牌示例

```bash
curl -X GET "http://localhost:8080/api/brands?name=特斯拉&status=ACTIVE&current=1&size=10"
```
