package com.gwm.scaffold.auth.config;

import com.gwm.scaffold.auth.aspect.AuthAspect;
import com.gwm.scaffold.auth.interceptor.LoginInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * 认证模块自动配置
 * 
 * 导入认证模块的各个组件
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.auth.enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(AuthProperties.class)
@Import({
    AuthAspect.class,
    LoginInterceptor.class,
    AuthWebConfig.class
})
public class AuthAutoConfiguration {

    public AuthAutoConfiguration() {
        log.info("认证模块自动配置已启用");
    }
}
