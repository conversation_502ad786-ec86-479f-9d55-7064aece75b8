CREATE TABLE t_brand (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '品牌ID',
    name VARCHAR(50) NOT NULL COMMENT '品牌名称',
    english_name VARCHAR(100) COMMENT '英文名称',
    country VARCHAR(50) NOT NULL COMMENT '所属国家',
    founded_year SMALLINT COMMENT '成立年份',
    website VARCHAR(255) COMMENT '官网地址',
    logo_url VARCHAR(500) COMMENT 'LOGO图片URL',
    description TEXT COMMENT '品牌简介',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    sort_weight INT DEFAULT 100 COMMENT '排序权重',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    deleted_time DATETIME COMMENT '删除时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) NOT NULL COMMENT '创建人编码',
    create_user_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) COMMENT '更新人编码',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',

    UNIQUE KEY uk_brand_name (name, deleted) COMMENT '品牌名称唯一索引',
    UNIQUE KEY uk_brand_english_name (english_name, deleted) COMMENT '英文名称唯一索引',
    KEY idx_brand_country (country) COMMENT '国家索引',
    KEY idx_brand_status (status) COMMENT '状态索引',
    KEY idx_brand_create_time (create_time) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='品牌表';


CREATE TABLE t_series (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '车系ID',
    name VARCHAR(50) NOT NULL COMMENT '车系名称',
    english_name VARCHAR(100) COMMENT '英文名称',
    brand_id BIGINT NOT NULL COMMENT '所属品牌ID',
    type ENUM('SEDAN', 'SUV', 'MPV', 'COUPE', 'PICKUP', 'HATCHBACK') NOT NULL COMMENT '车系类型',
    level ENUM('A', 'B', 'C', 'D') NOT NULL COMMENT '级别',
    launch_time DATE NOT NULL COMMENT '上市时间',
    discontinue_time DATE COMMENT '停产时间',
    min_price DECIMAL(8,2) COMMENT '最低指导价(万元)',
    max_price DECIMAL(8,2) COMMENT '最高指导价(万元)',
    image_url VARCHAR(500) COMMENT '车系图片URL',
    description TEXT COMMENT '车系简介',
    status ENUM('ON_SALE', 'DISCONTINUED', 'COMING_SOON') DEFAULT 'COMING_SOON' COMMENT '状态',
    sort_weight INT DEFAULT 100 COMMENT '排序权重',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    deleted_time DATETIME COMMENT '删除时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) NOT NULL COMMENT '创建人编码',
    create_user_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) COMMENT '更新人编码',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',

    UNIQUE KEY uk_series_brand_name (brand_id, name, deleted) COMMENT '同品牌下车系名称唯一',
    UNIQUE KEY uk_series_english_name (english_name, deleted) COMMENT '英文名称唯一索引',
    KEY idx_series_brand_id (brand_id) COMMENT '品牌ID索引',
    KEY idx_series_type (type) COMMENT '车系类型索引',
    KEY idx_series_level (level) COMMENT '级别索引',
    KEY idx_series_status (status) COMMENT '状态索引',
    KEY idx_series_launch_time (launch_time) COMMENT '上市时间索引',
    KEY idx_series_create_time (create_time) COMMENT '创建时间索引',

    CONSTRAINT fk_series_brand FOREIGN KEY (brand_id) REFERENCES t_brand(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车系表';


CREATE TABLE t_model (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '车型ID',
    name VARCHAR(100) NOT NULL COMMENT '车型名称',
    series_id BIGINT NOT NULL COMMENT '所属车系ID',
    year_model SMALLINT NOT NULL COMMENT '年款',
    displacement DECIMAL(3,1) COMMENT '排量(L)',
    max_power INT COMMENT '最大功率(kW)',
    max_torque INT COMMENT '最大扭矩(N·m)',
    transmission VARCHAR(20) NOT NULL COMMENT '变速箱类型',
    gear_count TINYINT COMMENT '档位数',
    drive_type ENUM('FWD', 'RWD', 'AWD') NOT NULL COMMENT '驱动方式',
    fuel_type ENUM('GASOLINE', 'DIESEL', 'HYBRID', 'ELECTRIC', 'PLUG_IN_HYBRID') NOT NULL COMMENT '燃料类型',
    body_structure ENUM('SEDAN', 'HATCHBACK', 'SUV', 'MPV', 'COUPE', 'PICKUP') NOT NULL COMMENT '车身结构',
    seat_count TINYINT NOT NULL COMMENT '座位数',
    length INT COMMENT '车长(mm)',
    width INT COMMENT '车宽(mm)',
    height INT COMMENT '车高(mm)',
    wheelbase INT COMMENT '轴距(mm)',
    curb_weight INT COMMENT '整备质量(kg)',
    tank_capacity INT COMMENT '油箱容积(L)',
    fuel_consumption DECIMAL(3,1) COMMENT '综合油耗(L/100km)',
    guide_price DECIMAL(8,2) NOT NULL COMMENT '指导价(万元)',
    launch_time DATE NOT NULL COMMENT '上市时间',
    discontinue_time DATE COMMENT '停售时间',
    image_urls JSON COMMENT '车型图片URL数组',
    config_details JSON COMMENT '配置详情JSON',
    status ENUM('ON_SALE', 'DISCONTINUED', 'COMING_SOON') DEFAULT 'COMING_SOON' COMMENT '状态',
    sort_weight INT DEFAULT 100 COMMENT '排序权重',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    deleted_time DATETIME COMMENT '删除时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_code VARCHAR(50) NOT NULL COMMENT '创建人编码',
    create_user_name VARCHAR(50) NOT NULL COMMENT '创建人姓名',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_code VARCHAR(50) COMMENT '更新人编码',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',

    UNIQUE KEY uk_model_series_name (series_id, name, deleted) COMMENT '同车系下车型名称唯一',
    KEY idx_model_series_id (series_id) COMMENT '车系ID索引',
    KEY idx_model_year (year_model) COMMENT '年款索引',
    KEY idx_model_price (guide_price) COMMENT '价格索引',
    KEY idx_model_fuel_type (fuel_type) COMMENT '燃料类型索引',
    KEY idx_model_drive_type (drive_type) COMMENT '驱动方式索引',
    KEY idx_model_status (status) COMMENT '状态索引',
    KEY idx_model_launch_time (launch_time) COMMENT '上市时间索引',
    KEY idx_model_create_time (create_time) COMMENT '创建时间索引',

    CONSTRAINT fk_model_series FOREIGN KEY (series_id) REFERENCES t_series(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车型表';