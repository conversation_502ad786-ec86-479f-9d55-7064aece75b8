package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("已接入平台编辑菜单参数对象")
public class EditAdminResourceForm implements Serializable {

    @JsonIgnore
    private String account;

    /**
     * 平台码
     */
    @ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 菜单ID
     */
    @ApiModelProperty("菜单ID")
    private String id;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    private String name;

    /**
     * 菜单编号
     */
    @ApiModelProperty("菜单编号")
    private String code;

    /**
     * 资源类型（0菜单，1按钮，2数据，3目录）
     */
    @ApiModelProperty("资源类型（0菜单，1按钮，2数据，3目录）")
    private String type;

    /**
     * 父ID
     */
    @ApiModelProperty("父ID")
    @JsonProperty(value = "parent_id")
    private String parentId;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String remark;

    /**
     * 顺序
     */
    @ApiModelProperty("顺序")
    @JsonProperty(value = "sort_no")
    private Integer sortNo;

    /**
     * 请求地址
     */
    @ApiModelProperty("请求地址")
    private String path;

    /**
     * 打开方式（0页签，1新窗口，默认0）
     */
    @ApiModelProperty("打开方式（0页签，1新窗口，默认0）")
    @JsonProperty(value = "open_method")
    private Integer openMethod;

    /**
     * 图标
     */
    @ApiModelProperty("图标")
    private String ico;

    /**
     * 菜单状态（0隐藏 ，1显示，默认1）
     */
    @ApiModelProperty("菜单状态（0隐藏 ，1显示，默认1）")
    private Integer enable;

    /**
     * 类别（0-菜单管理，1-数据管理）
     */
    @ApiModelProperty("类别（0-菜单管理，1-数据管理）")
    @JsonProperty(value = "category")
    private Integer category;

    /**
     * 是否外链（0否，1是，默认0）
     */
    @ApiModelProperty("是否外链（0否，1是，默认0）")
    @JsonProperty(value = "link_flag")
    private Integer linkFlag;
}