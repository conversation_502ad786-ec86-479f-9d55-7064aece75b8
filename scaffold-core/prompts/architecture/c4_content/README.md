# C4 上下文图自动化生成指南

本文档旨在帮助团队成员理解并使用这套基于 AI 的 C4 上下文图自动化生成工具。

## 1. 它是做什么的？

本工具的核心目标是：**根据一份产品需求文档 (PRD)，全自动地生成一幅标准、规范的 C4 系统上下文图**。

它能带来以下核心价值：
- **效率提升**: 将数小时的手动绘图工作缩短为几分钟的自动化流程。
- **保持一致**: 确保所有架构图都遵循统一的 C4 规范、版式和风格。
- **降低门槛**: 即使不熟悉 PlantUML 的成员也能快速产出高质量的架构图。

## 2. 如何使用？

整个过程非常简单，仅需两步：

### **步骤一：准备你的需求文档**

你只需要一份描述了产品或系统的需求文档。它可以是 `.md`, `.txt` 或其他格式的文件。

为了获得最好的分析效果，我们建议你的文档中清晰地描述了以下几点：
- 系统的**核心功能**和要解决的**主要问题**。
- 有哪些**类型的用户**会使用这个系统。
- 系统是否需要和**其他外部系统**（如：第三方支付、内部数据库、AI引擎等）进行交互。

### **步骤二：启动自动化生成**

在与 AI 助手对话时，引用 `c4_master_playbook.md` 并附上你的需求文档路径。AI 助手会接管后续所有工作。

**示例指令**:
```
@architecture/prompts/c4_content/c4_master_playbook.md
@path/to/your/prd.md
```

或者用自然语言：
```
请根据这份PRD文档，执行C4上下文图的生成任务。
@architecture/prompts/c4_content/c4_master_playbook.md
@path/to/your/prd.md
```

### **步骤三：获取产物**

执行完毕后，AI 助手会向你报告任务完成，并提供：
1.  **图表源码文件 (`.puml`)**: 位于 `architecture/c4/` 目录下。这是最重要的产物，包含了图表的完整代码。
2.  **中间分析文件 (`.md`)**: AI对你文档的结构化分析结果，用于追溯和调试。
3.  **源码预览**: AI 会将 `.puml` 文件的全部内容直接显示在聊天窗口中，方便你快速复制。

### **步骤四：查看与分享图表**

拿到 `.puml` 源码后，你需要通过 PlantUML 官方在线服务器来查看和下载图片。

1.  **复制源码**: 从聊天窗口或者打开 `.puml` 文件，复制全部内容。
2.  **访问官网**: 在浏览器中打开 PlantUML 在线编辑器：  
    [http://www.plantuml.com/plantuml](http://www.plantuml.com/plantuml)
3.  **粘贴与生成**: 将源码粘贴到网站的文本框中，网站会自动实时生成图表。
4.  **下载图片**: 在生成的图表下方，你可以找到下载链接，将图表保存为 PNG 或 SVG 格式。

## 3. 工作原理简介

本工具遵循一个严格的、两阶段的"责任链"模式：

1.  **分析阶段 (Analyze)**: 首先，"需求分析师"角色的 AI 会读取你的 PRD，提取出关键的架构元素（系统、用户、外部依赖、交互关系），并生成一份结构化的中间报告。
2.  **生成阶段 (Generate)**: 接着，"图表生成器"角色的 AI 会接手这份清晰的中间报告，并严格按照 `c4_generator_prompt.md` 中定义的规范，将其转换为高质量的 PlantUML 代码。

这种分离确保了内容分析的准确性和图表生成的规范性。

## 4. 常见问题与最佳实践

- **我可以自定义图表样式吗？**
  不可以。本工具的核心原则是**标准化**。所有的样式（颜色、布局、字体）都由 `c4_generator_prompt.md` 严格限定，个人不允许在生成过程中添加自定义样式，以确保所有图表的高度一致。如需调整标准，应由架构师统一修改 `prompt` 文件。

- **生成的效果不理想怎么办？**
  尝试优化你的 PRD 文档。描述越清晰、越明确，AI 的分析结果就越准确。例如，明确写出"管理员用户可以..."、"系统需要调用XX天气API"等。

---
如有任何问题，请联系架构团队。 