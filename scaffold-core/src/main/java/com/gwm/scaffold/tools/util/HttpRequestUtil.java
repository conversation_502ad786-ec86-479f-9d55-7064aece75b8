package com.gwm.scaffold.tools.util;

import cn.hutool.json.JSONObject;
import com.gwm.scaffold.core.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 *
 * @ClassName：HttpRequestUtil
 * @Description： Http请求
 * <AUTHOR>
 */
@Component
public class HttpRequestUtil {
    private String defaultContentEncoding;

    public HttpRequestUtil() {
        this.defaultContentEncoding = Charset.defaultCharset().name();
    }

    /**
     * 默认的响应字符集
     */
    public String getDefaultContentEncoding() {
        return this.defaultContentEncoding;
    }

    /**
     * 设置默认的响应字符集
     */
    public void setDefaultContentEncoding(String defaultContentEncoding) {
        this.defaultContentEncoding = defaultContentEncoding;
    }

    /**
     * POST
     * @param json
     * @param url
     * @return
     * @throws Exception
     */
    public static String post(JSONObject json, String url) throws Exception {
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(url);
        CloseableHttpResponse  response = null;
        InputStream in = null;
        BufferedReader br = null;
        String result;
        try {
            StringEntity s = new StringEntity(json.toString(), UTF_8.toString());
            s.setContentEncoding(UTF_8.toString());
            /*发送json数据需要设置contentType*/
            s.setContentType(APPLICATION_JSON_VALUE);
            post.setEntity(s);
            post.setHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE);
            response = httpclient.execute(post);
            in = response.getEntity().getContent();
            br = new BufferedReader(new InputStreamReader(in, UTF_8.toString()));
            StringBuilder strber= new StringBuilder();
            String line;
            while((line = br.readLine())!=null){
                strber.append(line+'\n');
            }
            result = strber.toString();
            if(response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
                if(StringUtils.isBlank(result)) {
                    result = "服务器异常";
                }
                throw new ServiceException(result);
            }
            // System.out.println("返回数据="+result);
        } catch (Exception e) {
            //System.err.println("调用接口出错：：：：：：：：：：：："+e.getMessage());
            throw new ServiceException(e.getMessage());
        } finally {
            if(null != br) {
                br.close();
            }
            if(null != br) {
                in.close();
            }
            if(null != response) {
                response.close();
            }
            if(null != httpclient) {
                httpclient.close();
            }
        }
        return result;
    }

    /**
     * POST
     * @param json
     * @param url
     * @param headerMap
     * @return
     * @throws Exception
     */
    public static String post(JSONObject json, String url, Map<String, String> headerMap) throws Exception{
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(url);
        CloseableHttpResponse  response = null;
        InputStream in = null;
        BufferedReader br = null;
        String result;
        try {
            StringEntity s = new StringEntity(json.toString(),UTF_8.toString());
            s.setContentEncoding(UTF_8.toString());
            /*发送json数据需要设置contentType*/
            s.setContentType(APPLICATION_JSON_VALUE);
            post.setEntity(s);
            post.setHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE);
            Set<Entry<String, String>> headerEntries = headerMap.entrySet();
            for (Entry<String, String> headerEntry:headerEntries){
                post.setHeader(headerEntry.getKey(), headerEntry.getValue());
            }
            response = httpclient.execute(post);
            in = response.getEntity().getContent();
            br = new BufferedReader(new InputStreamReader(in, UTF_8));
            StringBuilder strber= new StringBuilder();
            String line;
            while((line = br.readLine())!=null){
                strber.append(line+'\n');
            }
            result = strber.toString();
            if(response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
                if(StringUtils.isBlank(result)) {
                    result = "服务器异常";
                }
                throw new ServiceException(result);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            br.close();
            in.close();
            response.close();
            httpclient.close();
        }
        return result;
    }

    /**
     * ContentType.URLENCODED.getHeader()
     * @param map
     * @param url
     * @param headerMap
     * @param contentType
     * @return
     * @throws Exception
     */
    public static String post(Map<String, String> map, String url, Map<String, String> headerMap, String contentType) throws Exception{
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(url);
        CloseableHttpResponse  response = null;
        InputStream in = null;
        BufferedReader br = null;
        String result;
        try {
            List<NameValuePair> nameValuePairs = getNameValuePairList(map);
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(nameValuePairs, UTF_8.toString());
            /*发送json数据需要设置contentType*/
            urlEncodedFormEntity.setContentType(contentType);
            post.setEntity(urlEncodedFormEntity);
            post.setHeader(CONTENT_TYPE, contentType);
            Set<Entry<String, String>> headerEntries = headerMap.entrySet();
            for (Entry<String, String> headerEntry:headerEntries){
                post.setHeader(headerEntry.getKey(), headerEntry.getValue());
            }
            response = httpclient.execute(post);
            in = response.getEntity().getContent();
            br = new BufferedReader(new InputStreamReader(in, UTF_8));
            StringBuilder strber= new StringBuilder();
            String line;
            while((line = br.readLine())!=null){
                strber.append(line+'\n');
            }
            result = strber.toString();
            if(response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
                if(StringUtils.isBlank(result)) {
                    result = "服务器异常";
                }
                throw new ServiceException(result);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            br.close();
            in.close();
            response.close();
            httpclient.close();
        }
        return result;
    }

    /**
     * 转成FORM
     * @param map
     * @return
     */
    private static List<NameValuePair> getNameValuePairList(Map<String, String> map) {
        List<NameValuePair> list = new ArrayList<>(map.size());
        for(String key : map.keySet()) {
            list.add(new BasicNameValuePair(key,map.get(key)));
        }

        return list;
    }

    /**
     *
     * @param params
     * @param url
     * @param headerMap
     * @return
     * @throws Exception
     */
    public static String post(String params, String url, Map<String, String> headerMap) throws Exception {
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(url);
        CloseableHttpResponse  response = null;
        InputStream in = null;
        BufferedReader br = null;
        String result;
        try {
            StringEntity s = new StringEntity(params.toString(), UTF_8.toString());
            s.setContentEncoding(UTF_8.toString());
            /*发送json数据需要设置contentType*/
            s.setContentType(APPLICATION_JSON_VALUE);
            post.setEntity(s);
            post.setHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE);
            Set<Entry<String, String>> headerEntries = headerMap.entrySet();
            for (Entry<String, String> headerEntry:headerEntries){
                post.setHeader(headerEntry.getKey(), headerEntry.getValue());
            }
            response = httpclient.execute(post);
            in = response.getEntity().getContent();
            br = new BufferedReader(new InputStreamReader(in, UTF_8));
            StringBuilder strber= new StringBuilder();
            String line;
            while((line = br.readLine())!=null){
                strber.append(line+'\n');
            }
            result = strber.toString();
            if(response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
                if(StringUtils.isBlank(result)) {
                    result = "服务器异常";
                }
                throw new ServiceException(result);
            }
            //System.out.println("返回数据="+result);
        } catch (Exception e) {
            //System.err.println("调用接口出错：：：：：：：：：：：："+e.getMessage());
            throw new ServiceException(e.getMessage());
        } finally {
            br.close();
            in.close();
            response.close();
            httpclient.close();
        }
        return result;
    }

    /**
     * PUT
     * @param json
     * @param url
     * @param headerMap
     * @return
     * @throws Exception
     */
    public static String put(JSONObject json, String url, Map<String, String> headerMap) throws Exception {
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpPut post = new HttpPut(url);
        CloseableHttpResponse  response = null;
        InputStream in = null;
        BufferedReader br = null;
        String result;
        try {
            StringEntity s = new StringEntity(json.toString(),UTF_8.toString());
            s.setContentEncoding(UTF_8.toString());
            /*发送json数据需要设置contentType*/
            s.setContentType(APPLICATION_JSON_VALUE);
            post.setEntity(s);
            post.setHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE);
            Set<Entry<String, String>> headerEntries = headerMap.entrySet();
            for (Entry<String, String> headerEntry:headerEntries){
                post.setHeader(headerEntry.getKey(), headerEntry.getValue());
            }
            response = httpclient.execute(post);
            in = response.getEntity().getContent();
            br = new BufferedReader(new InputStreamReader(in, UTF_8));
            StringBuilder strber= new StringBuilder();
            String line;
            while((line = br.readLine())!=null){
                strber.append(line+'\n');
            }
            result = strber.toString();
            if(response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
                if(StringUtils.isBlank(result)) {
                    result = "服务器异常";
                }
                throw new ServiceException(result);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            br.close();
            in.close();
            response.close();
            httpclient.close();
        }
        return result;
    }

    /**
     * DELETE
     * @param url
     * @param headerMap
     * @return
     * @throws Exception
     */
    public static String delete(String url, Map<String, String> headerMap) throws Exception {
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpDelete post = new HttpDelete(url);
        CloseableHttpResponse  response = null;
        InputStream in = null;
        BufferedReader br = null;
        String result;
        try {
            post.setHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE);
            Set<Entry<String, String>> headerEntries = headerMap.entrySet();
            for (Entry<String, String> headerEntry:headerEntries){
                post.setHeader(headerEntry.getKey(), headerEntry.getValue());
            }
            response = httpclient.execute(post);
            in = response.getEntity().getContent();
            br = new BufferedReader(new InputStreamReader(in, UTF_8));
            StringBuilder strber= new StringBuilder();
            String line;
            while((line = br.readLine())!=null){
                strber.append(line+'\n');
            }
            result = strber.toString();
            if(response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
                if(StringUtils.isBlank(result)) {
                    result = "服务器异常";
                }
                throw new ServiceException(result);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            br.close();
            in.close();
            response.close();
            httpclient.close();
        }
        return result;
    }

    /**
     * GET
     * @param paramsObj
     * @param url
     * @param headerMap
     * @return
     * @throws Exception
     */
    public static String get(JSONObject paramsObj, String url, Map<String, String> headerMap) throws Exception {
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        CloseableHttpResponse  response = null;
        InputStream in = null;
        BufferedReader br = null;
        String result;
        try {
            StringBuffer param = new StringBuffer();
            int i = 0;
            Set<Entry<String, Object>> entries = paramsObj.entrySet();
            for (Entry<String, Object> entry:entries){
                if (i == 0) {
                    param.append("?");
                }
                else {
                    param.append("&");
                }
//
                //去除[]""符号
                if (entry.getValue().toString().startsWith("[") && entry.getValue().toString().endsWith("]")){
                    entry.setValue(entry.getValue().toString().substring(2, entry.getValue().toString().length() - 2));
                }
//                String  encode = URLEncoder.encode(entry.getValue().toString(), "UTF-8");
                System.out.println(entry.getValue().toString());
                param.append(entry.getKey()).append("=").append(entry.getValue());
                i++;
            }

            url += param;
            HttpGet post = new HttpGet(url);
//            post.setHeader("Content-Type","application/json;charset=utf-8");
            if (headerMap != null){
                Set<Entry<String, String>> headerEntries = headerMap.entrySet();
                for (Entry<String, String> headerEntry:headerEntries){
                    post.setHeader(headerEntry.getKey(), headerEntry.getValue());
                }

            }

            response = httpclient.execute(post);
            in = response.getEntity().getContent();
            br = new BufferedReader(new InputStreamReader(in, UTF_8));
            StringBuilder strber= new StringBuilder();
            String line;
            while((line = br.readLine())!=null){
                strber.append(line+'\n');
            }
            result = strber.toString();
            if(response.getStatusLine().getStatusCode()!=HttpStatus.SC_OK){
                if(StringUtils.isBlank(result)) {
                    result = "服务器异常";
                }
                throw new ServiceException(result);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            br.close();
            in.close();
            response.close();
            httpclient.close();
        }
        return result;
    }
}
