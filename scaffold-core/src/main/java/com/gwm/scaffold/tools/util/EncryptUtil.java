package com.gwm.scaffold.tools.util;

import com.gwm.framework.encryption.dto.FileEncryptDTO;
import com.gwm.framework.encryption.util.FileEncryptUtil;
import com.gwm.scaffold.tools.config.FileEncryptConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Map;

import static org.springframework.http.HttpMethod.POST;

/**
 * 加解密工具类
 * <AUTHOR>
 */
@Slf4j
public class EncryptUtil {

    public static final int RESPONSE_CODE_SERVER_SUCCESS = 200;

    private final static String HOST = "https://gwapi.gwm.cn/";
    private final static String FILE_ENCRYPT = "/sec/cdg/file_encrypt";
    private final static String FILE_DECRYPT = "/sec/cdg/file_decrypt";
    private final static String PACKAGE_ENCRYPT = "/sec/cdg/package_encrypt";
    private final static String PACKAGE_DECRYPT = "/sec/cdg/package_decrypt";

    /**
     * 文件加密
     * @param file 待加密文件
     * @return 加密后文件内容
     */
    public static String fileEncrypt(File file){
        try {
            FileEncryptConfig config = FileEncryptConfig.getConfig();
            String url = config.getHost() + config.getEnv() + FILE_ENCRYPT;
            Map<String, String> headerMap = HmacSignUtil.createSignHeader(config.getAppKey(), config.getAppSecret(), url, POST.name());
            HttpResponseObj result = uploadFile(url, file, headerMap);
            return result.toString();
        }  catch (Exception e){
            log.error("",e);
        }
        return null;
    }

    public static byte[] fileEncryptByte(File file){
        try {
            FileEncryptConfig config = FileEncryptConfig.getConfig();
            String url = config.getHost() + config.getEnv() + FILE_ENCRYPT;
            Map<String, String> headerMap = HmacSignUtil.createSignHeader(config.getAppKey(), config.getAppSecret(), url, POST.name());
            HttpResponseObj result = uploadFile(url, file, headerMap);
            return result.toByte();
        }  catch (Exception e){
            log.error("",e);
        }
        return null;
    }

    /**
     * 文件解密
     * @param file 待解密文件
     * @param env test:测试环境，rest:正式环境
     * @param appKey 调用方应用appKey
     * @param appSecret 调用方应用appSecret
     * @return 加密后文件内容
     */
    public static String fileDecrypt(File file, String env, String appKey, String appSecret){
        try {
            FileEncryptConfig config = FileEncryptConfig.getConfig();
            String url = config.getHost() + config.getEnv() + FILE_DECRYPT;
            Map<String, String> headerMap = HmacSignUtil.createSignHeader(appKey, appSecret, url, POST.name());
            HttpResponseObj result = uploadFile(url, file, headerMap);
            return result.toString();
        }  catch (Exception e){
            log.error("",e);
        }
        return null;
    }

    public static byte[] fileDecryptByte(File file, String env, String appKey, String appSecret){
        try {
            String url = HOST + env + FILE_DECRYPT;
            Map<String, String> headerMap = HmacSignUtil.createSignHeader(appKey, appSecret, url, POST.name());
            HttpResponseObj result = uploadFile(url, file, headerMap);
            return result.toByte();
        }  catch (Exception e){
            log.error("",e);
        }
        return null;
    }

    public static byte[] fileDecryptByte(File file){
        try {
            FileEncryptConfig config = FileEncryptConfig.getConfig();
            String url = config.getHost() + config.getEnv() + FILE_DECRYPT;
            Map<String, String> headerMap = HmacSignUtil.createSignHeader(config.getAppKey(), config.getAppSecret(), url, POST.name());
            HttpResponseObj result = uploadFile(url, file, headerMap);
            return result.toByte();
        }  catch (Exception e){
            log.error("",e);
        }
        return null;
    }

    /**
     * 文件解密
     * @param file 待加密压缩包
     * @return 加密后文件内容
     */
    public static String packageEncrypt(File file){
        try {
            FileEncryptConfig config = FileEncryptConfig.getConfig();
            String url = config.getHost() + config.getEnv() + PACKAGE_ENCRYPT;
            Map<String, String> headerMap = HmacSignUtil.createSignHeader(config.getAppKey(), config.getAppSecret(), url, POST.name());
            HttpResponseObj result = uploadFile(url, file, headerMap);
            return result.toString();
        }  catch (Exception e){
            log.error("",e);
        }
        return null;
    }

    /**
     * 文件解密
     * @param file 待解密压缩包
     * @return 加密后文件内容
     */
    public static String packageDecrypt(File file){
        try {
            FileEncryptConfig config = FileEncryptConfig.getConfig();
            String url = config.getHost() + config.getEnv() + PACKAGE_DECRYPT;
            Map<String, String> headerMap = HmacSignUtil.createSignHeader(config.getAppKey(), config.getAppSecret(), url, POST.name());
            HttpResponseObj result = uploadFile(url, file, headerMap);
            return result.toString();
        }  catch (Exception e){
            log.error("",e);
        }
        return null;
    }

    private static HttpResponseObj uploadFile(String url, File file, Map<String, String> headerMap) throws IOException {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        CloseableHttpResponse httpResponse = null;
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(200000).setSocketTimeout(200000000).build();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestConfig);
        //httpPost.setProtocolVersion(HttpVersion.HTTP_1_0);
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
//        multipartEntityBuilder.addBinaryBody("file",file);
        FileBody fileBody = new FileBody(file);
        multipartEntityBuilder.addPart("file", fileBody);
        multipartEntityBuilder.addTextBody("file_size", String.valueOf(fileBody.getContentLength()));
//        multipartEntityBuilder.setContentType(ContentType.APPLICATION_OCTET_STREAM);
        multipartEntityBuilder.setCharset(Charset.forName("UTF-8"));
        HttpEntity httpEntity = multipartEntityBuilder.build();
        httpPost.setEntity(httpEntity);
        if (headerMap != null) {
            for (Map.Entry<String, String> stringStringEntry : headerMap.entrySet()) {
                httpPost.addHeader(stringStringEntry.getKey(), stringStringEntry.getValue());
            }
        }

        httpResponse = httpClient.execute(httpPost);
        HttpEntity responseEntity = httpResponse.getEntity();

        HttpResponseObj httpResponseObj = new HttpResponseObj();
        httpResponseObj.setHttpClient(httpClient);
        httpResponseObj.setHttpResponse(httpResponse);
        httpResponseObj.setResponseEntity(responseEntity);
        return httpResponseObj;
    }

    public static InputStream decryptFromOpen(File file, String appKey, String appSecret) throws IOException {
//        File file = new File("");
        FileEncryptConfig config = FileEncryptConfig.getConfig();
        FileEncryptDTO encryptDTO = new FileEncryptDTO();
        encryptDTO.setAppKey(appKey);
        encryptDTO.setAppSecret(appSecret);
        //注意不同环境地址不同
        encryptDTO.setFileEncryptUrl(config.getHost() + config.getEnv() + FILE_DECRYPT);
        encryptDTO.setFile(file);
        return FileEncryptUtil.encryptFromOpen(encryptDTO);
    }

    @Data
    public static class HttpResponseObj{
        private HttpEntity responseEntity;
        private CloseableHttpClient httpClient;
        private CloseableHttpResponse httpResponse;

        private void close() throws IOException {
            httpClient.close();
            if(httpResponse!=null){
                httpResponse.close();
            }
        }

        public byte[] toByte(){
            if(httpResponse == null) {
                return null;
            }
            int statusCode= httpResponse.getStatusLine().getStatusCode();
            if(statusCode !=RESPONSE_CODE_SERVER_SUCCESS){
                return null;
            }
            byte[] bytes = null;
            try {
                bytes = EntityUtils.toByteArray(responseEntity);
                close();
            } catch (IOException e) {
                log.error("",e);
            }
            return bytes;
        }

        @Override
        public String toString(){
            if(httpResponse == null) {
                return null;
            }
            int statusCode= httpResponse.getStatusLine().getStatusCode();
            if(statusCode != RESPONSE_CODE_SERVER_SUCCESS){
                return null;
            }
            String content = null;
            try {
                content = EntityUtils.toString(responseEntity);
                close();
            } catch (IOException e) {
                log.error("",e);
            }
            return content;
        }
    }

}

