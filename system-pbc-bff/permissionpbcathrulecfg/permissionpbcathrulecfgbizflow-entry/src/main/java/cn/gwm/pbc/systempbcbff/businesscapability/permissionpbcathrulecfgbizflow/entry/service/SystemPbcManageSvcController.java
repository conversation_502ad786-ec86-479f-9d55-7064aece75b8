package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcManageSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcManageSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.CustomUserResetPasswordDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.GroupUserTreeDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.RequestUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.SearchUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.TestDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.UserBindPlatformDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.UserDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.UserSaveDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.UserUnbindPlatformDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.lang.Override;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 权限PBC-管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.13
 */
@RestController("PermissionPBCAthRuleCfgBizFlowSystemPbcManageSvcCON")
public class SystemPbcManageSvcController implements SystemPbcManageSvcI {

    @Autowired
    private SystemPbcManageSvc systemPbcManageSvc;

    @Override
    public Response<TestDTO> test(@RequestBody(required = false) TestDTO request) {
    	systemPbcManageSvc.test(request);
		return null;
	}

    @Override
    public Response<Map<String, Object>> queryUser(@RequestBody(required = false) RequestUserListDTO request) {
    	Map mapReturn = systemPbcManageSvc.queryUser(request);
		return Response.buildSuccess(mapReturn);
	}

    @Override
    public Response<Void> bindPlatform(@RequestBody(required = true) UserBindPlatformDTO request) {
    	systemPbcManageSvc.bindPlatform(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<Void> unbindPlatform(@RequestBody(required = true) UserUnbindPlatformDTO request) {
    	systemPbcManageSvc.unbindPlatform(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<Void> save(@RequestBody(required = true) UserSaveDTO request) {
    	systemPbcManageSvc.save(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<Map<String, Object>> upload(@RequestParam(name = "file") MultipartFile request) {
    	Map mapReturn = systemPbcManageSvc.upload(request);
		return Response.buildSuccess(mapReturn);
	}

    @Override
    public Response<Map<String, Object>> detail(@RequestBody(required = true) UserDetailDTO request) {
    	Map<String, Object> detail = systemPbcManageSvc.detail(request);
//		return detail;
		return Response.buildSuccess(detail);
	}

    @Override
    public Response<Map<String, Object>> groupUserTree(@RequestBody(required = true) GroupUserTreeDTO request) {
    	Map mapReturn = systemPbcManageSvc.groupUserTree(request);
		return Response.buildSuccess(mapReturn);
	}

    @Override
    public Response<Map<String, Object>> searchUserList(@RequestBody(required = true) SearchUserListDTO request) {
    	Map mapReturn = systemPbcManageSvc.searchUserList(request);
		return Response.buildSuccess(mapReturn);
	}

    @Override
    public Response<Void> customUserPasswordReset(@RequestBody(required = true) CustomUserResetPasswordDTO request) {
    	systemPbcManageSvc.customUserPasswordReset(request);
		return Response.buildSuccess();
	}
}
