package com.gwm.scaffold.example.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 品牌状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Getter
@AllArgsConstructor
public enum BrandStatus {
    
    /**
     * 启用状态
     */
    ACTIVE("ACTIVE", "启用"),
    
    /**
     * 禁用状态
     */
    INACTIVE("INACTIVE", "禁用");
    
    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 状态代码
     * @return 品牌状态枚举
     */
    public static BrandStatus fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (BrandStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 检查代码是否有效
     * 
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }
}
