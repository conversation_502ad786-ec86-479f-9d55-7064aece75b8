package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRoleResourceSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource.SearchAdminResourceOfRoleForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteRoleResourceService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.SearchAdminResourceOfRoleFormClientDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
/**
 * <AUTHOR>
 */
@Component("systemPbcRoleResourceSvcImpl")
public class SystemPbcRoleResourceSvcImpl implements SystemPbcRoleResourceSvc {

    @Resource
    private RemoteRoleResourceService roleResourceService;

    @Value("${gwm.sso.platform-code}")
    private String platformCode;

    @Override
    public Map<String, Object> searchResourceOfRoleInfo(SearchAdminResourceOfRoleFormClientDTO request) {

        SearchAdminResourceOfRoleForm searchAdminResourceOfRoleForm = new SearchAdminResourceOfRoleForm();

        BeanUtil.copyProperties(request, searchAdminResourceOfRoleForm);

        searchAdminResourceOfRoleForm.setPlatformCode(platformCode);
        CommonRlt<JSONObject> jsonObjectCommonRlt = roleResourceService.searchResourceOfRoleInfo(searchAdminResourceOfRoleForm);
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }
}
