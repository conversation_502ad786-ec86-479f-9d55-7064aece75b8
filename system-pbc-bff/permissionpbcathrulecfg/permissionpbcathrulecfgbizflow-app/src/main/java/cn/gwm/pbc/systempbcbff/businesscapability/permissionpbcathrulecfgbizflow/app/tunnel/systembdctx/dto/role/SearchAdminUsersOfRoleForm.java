package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("查询平台接入中角色下用户参数对象")
public class SearchAdminUsersOfRoleForm implements Serializable {

	@ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 角色ID
     */
    @ApiModelProperty("角色ID")
    private String id;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    @JsonProperty(value = "user_code")
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    @JsonProperty(value = "user_name")
    private String userName;

    /**
     * 查询参数（别名/账号）
     */
    @ApiModelProperty("查询参数（别名/账号）")
    @JsonProperty(value = "query_param")
    private String queryParam;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer page = 1;

    /**
     * 条数
     */
    @ApiModelProperty("条数")
    private Integer size = 10;
}