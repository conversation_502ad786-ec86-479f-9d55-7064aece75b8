# Scaffold Core Module

GWM脚手架核心基础模块，提供最基础的通用组件和工具类。

## 📦 模块内容

### 🏗️ 核心组件

#### 统一返回结果
- **Result<T>**: 统一API响应格式
- 支持成功/失败状态
- 包含状态码、消息、数据、时间戳

#### 基础实体类
- **BaseEntity**: 基础实体接口，用于标识实体类型
- **BaseSuperEntity**: 通用字段基础实体，包含创建时间、创建人、更新时间、更新人等字段

#### 异常体系
- **ServiceException**: 业务逻辑异常
- **WarnException**: 警告级别异常
- **StreamException**: 流处理异常

### 🛠️ 工具类库

#### StringUtil - 字符串处理工具
- `convert(String str)`: 智能类型转换
- `getUniName(String name)`: 生成唯一文件名
- `formatCompanyNames(String companyCode, int level)`: 格式化部门层级名称
- `isBoolean/isInteger/isDouble/isArray/isObject`: 类型判断

#### UUIDUtil - 唯一标识生成工具
- `getUUID()`: 生成UUID（去除横线）
- `getStandardUUID()`: 生成标准UUID（包含横线）
- `getUniqueNo()`: 生成时间戳+随机数格式的唯一编号
- `getUniqueNoWithPrefix(String prefix)`: 生成带前缀的唯一编号
- `getRandomString(int length)`: 生成指定长度随机字符串

#### DateUtil - 日期处理工具
- `calculateUsageDays(LocalDate startDate, LocalDate endDate)`: 计算两日期间天数
- `localDateToDate(LocalDate localDate)`: LocalDate转Date
- `dateToLocalDate(Date date)`: Date转LocalDate
- `isInRange(LocalDate date, LocalDate startDate, LocalDate endDate)`: 判断日期是否在范围内

#### ObjectUtil - 对象处理工具
- `getTargetDate(Integer amount)`: 获取相对当前日期的目标时间
- `isEmpty/isNotEmpty(Object obj)`: 对象空值判断
- `getFirstElement/getLastElement(List<T> list)`: 获取集合首尾元素
- `objectToMap(Object obj)`: 对象转Map
- `safeToString(Object obj)`: 安全的字符串转换

## 🚀 使用方式

### Maven依赖
```xml
<dependency>
    <groupId>com.gwm.scaffold</groupId>
    <artifactId>scaffold-core</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 使用示例

#### 统一返回结果
```java
// 成功返回
return Result.success(data);
return Result.success("操作成功", data);

// 失败返回
return Result.error("操作失败");
return Result.error(500, "系统异常");
```

#### 基础实体类
```java
@Data
@TableName("sys_user")
public class User extends BaseSuperEntity implements BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String username;
    // 其他字段...
}
```

#### 异常处理
```java
// 业务异常
throw new ServiceException("用户不存在");
throw new ServiceException("参数错误", 400);

// 警告异常
throw new WarnException("数据可能存在风险");

// 流异常
throw new StreamException("文件上传失败");
```

#### 工具类使用
```java
// 字符串处理
String uniqueFileName = StringUtil.getUniName("document.pdf");
Object convertedValue = StringUtil.convert("123"); // 自动转换为Integer

// UUID生成
String uuid = UUIDUtil.getUUID();
String orderNo = UUIDUtil.getUniqueNoWithPrefix("ORDER_");

// 日期处理
int days = DateUtil.calculateUsageDays(startDate, endDate);
Date targetDate = ObjectUtil.getTargetDate(7); // 7天后

// 对象处理
boolean isEmpty = ObjectUtil.isEmpty(obj);
Map<String, String> map = ObjectUtil.objectToMap(user);
```

## 📋 设计原则

1. **最小依赖**: 只依赖必要的基础库
2. **向后兼容**: 保持API稳定性
3. **高内聚**: 功能相关的组件放在一起
4. **低耦合**: 减少模块间的依赖关系
5. **易扩展**: 支持功能的平滑扩展

## 🔗 相关模块

- **scaffold-auth**: 认证权限模块
- **scaffold-web**: Web增强模块
- **scaffold-data**: 数据访问增强模块
- **scaffold-tools**: 工具类库模块
- **scaffold-starter**: 自动配置启动器
