<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${packageName}.mapper.${mapperName}">

    <!-- ${tableInfo.tableComment!entityName}结果映射 -->
    <resultMap id="${entityName}ResultMap" type="${packageName}.entity.${entityName}">
<#list tableInfo.columns as column>
        <#if column.primaryKey>
        <id column="${column.columnName}" property="${column.javaField}"/>
        <#else>
        <result column="${column.columnName}" property="${column.javaField}"/>
        </#if>
</#list>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
<#assign columnList = []>
<#list tableInfo.columns as column>
    <#assign columnList = columnList + [column.columnName]>
</#list>
        ${columnList?join(", ")}
    </sql>

    <!-- 分页查询${tableInfo.tableComment!entityName}列表 -->
    <select id="select${entityName}Page" resultMap="${entityName}ResultMap">
        SELECT
        <include refid="BaseColumns"/>
        FROM ${tableInfo.tableName}
        <where>
<#if tableInfo.hasLogicDelete>
            deleted = 0
</#if>
<#list tableInfo.columns as column>
    <#if column.queryField && !column.createTime && !column.updateTime && !column.createUser && !column.updateUser && !column.logicDelete && !column.version>
        <#if column.queryType == "LIKE">
            <if test="request.${column.javaField} != null and request.${column.javaField} != ''">
                AND ${column.columnName} LIKE CONCAT('%', ${r'#{request.'}${column.javaField}}, '%')
            </if>
        <#elseif column.queryType == "EQ">
            <if test="request.${column.javaField} != null">
                AND ${column.columnName} = ${r'#{request.'}${column.javaField}}
            </if>
        <#elseif column.queryType == "BETWEEN">
            <if test="request.${column.javaField}Start != null">
                AND ${column.columnName} >= ${r'#{request.'}${column.javaField}Start}
            </if>
            <if test="request.${column.javaField}End != null">
                AND ${column.columnName} &lt;= ${r'#{request.'}${column.javaField}End}
            </if>
        </#if>
    </#if>
</#list>
        </where>
        ORDER BY create_time DESC
    </select>



</mapper>
