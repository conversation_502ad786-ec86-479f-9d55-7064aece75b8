package com.gwm.scaffold.tools.generator.domain;

import lombok.Data;

/**
 * 字段信息
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Data
public class ColumnInfo {

    /**
     * 字段名
     */
    private String columnName;

    /**
     * 字段注释
     */
    private String columnComment;

    /**
     * 数据库字段类型
     */
    private String columnType;

    /**
     * Java字段类型
     */
    private String javaType;

    /**
     * Java字段名
     */
    private String javaField;

    /**
     * 是否主键
     */
    private boolean primaryKey;

    /**
     * 是否自增
     */
    private boolean autoIncrement;

    /**
     * 是否可为空
     */
    private boolean nullable;

    /**
     * 字段长度
     */
    private Integer columnSize;

    /**
     * 小数位数
     */
    private Integer decimalDigits;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 是否逻辑删除字段
     */
    private boolean logicDelete;

    /**
     * 是否版本字段
     */
    private boolean version;

    /**
     * 是否创建时间字段
     */
    private boolean createTime;

    /**
     * 是否更新时间字段
     */
    private boolean updateTime;

    /**
     * 是否创建人字段
     */
    private boolean createUser;

    /**
     * 是否更新人字段
     */
    private boolean updateUser;

    /**
     * 是否需要在查询条件中
     */
    private boolean queryField;

    /**
     * 是否需要在列表中显示
     */
    private boolean listField;

    /**
     * 是否需要在表单中
     */
    private boolean formField;

    /**
     * 表单类型
     */
    private String formType;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 字典类型
     */
    private String dictType;
}
