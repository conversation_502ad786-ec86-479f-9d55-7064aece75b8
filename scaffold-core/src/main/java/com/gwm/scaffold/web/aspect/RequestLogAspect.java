package com.gwm.scaffold.web.aspect;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 请求响应日志切面
 * 
 * 记录Controller方法的请求参数和响应结果
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Aspect
@Component
@Order(200)
@ConditionalOnProperty(name = "scaffold.web.request-log.enabled", havingValue = "true", matchIfMissing = false)
public class RequestLogAspect {

    /**
     * 环绕通知，记录请求和响应
     */
    @Around("execution(* *..*Controller.*(..))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }
        
        HttpServletRequest request = attributes.getRequest();
        String method = request.getMethod();
        String url = request.getRequestURL().toString();
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        String clientIp = getClientIp(request);
        
        // 获取方法信息
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        // 记录请求日志
        logRequest(method, url, uri, queryString, clientIp, className, methodName, args);
        
        Object result = null;
        Exception exception = null;
        
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            // 记录响应日志
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            logResponse(className, methodName, result, exception, duration);
        }
    }

    /**
     * 记录请求日志
     */
    private void logRequest(String method, String url, String uri, String queryString, 
                           String clientIp, String className, String methodName, Object[] args) {
        try {
            StringBuilder logBuilder = new StringBuilder();
            logBuilder.append("\n========== Request Start ==========");
            logBuilder.append("\nHTTP Method: ").append(method);
            logBuilder.append("\nRequest URL: ").append(url);
            logBuilder.append("\nRequest URI: ").append(uri);
            if (queryString != null) {
                logBuilder.append("\nQuery String: ").append(queryString);
            }
            logBuilder.append("\nClient IP: ").append(clientIp);
            logBuilder.append("\nController: ").append(className);
            logBuilder.append("\nMethod: ").append(methodName);
            
            if (args != null && args.length > 0) {
                logBuilder.append("\nRequest Args: ");
                for (int i = 0; i < args.length; i++) {
                    if (i > 0) {
                        logBuilder.append(", ");
                    }
                    logBuilder.append(formatArgument(args[i]));
                }
            }
            logBuilder.append("\n===================================");
            
            log.info(logBuilder.toString());
        } catch (Exception e) {
            log.warn("记录请求日志失败", e);
        }
    }

    /**
     * 记录响应日志
     */
    private void logResponse(String className, String methodName, Object result, 
                           Exception exception, long duration) {
        try {
            StringBuilder logBuilder = new StringBuilder();
            logBuilder.append("\n========== Response End ===========");
            logBuilder.append("\nController: ").append(className);
            logBuilder.append("\nMethod: ").append(methodName);
            logBuilder.append("\nDuration: ").append(duration).append("ms");
            
            if (exception != null) {
                logBuilder.append("\nException: ").append(exception.getClass().getSimpleName());
                logBuilder.append("\nError Message: ").append(exception.getMessage());
            } else if (result != null) {
                logBuilder.append("\nResponse: ").append(formatResult(result));
            }
            logBuilder.append("\n===================================");
            
            if (exception != null) {
                log.error(logBuilder.toString());
            } else {
                log.info(logBuilder.toString());
            }
        } catch (Exception e) {
            log.warn("记录响应日志失败", e);
        }
    }

    /**
     * 格式化参数
     */
    private String formatArgument(Object arg) {
        if (arg == null) {
            return "null";
        }
        
        // 避免记录敏感信息
        String className = arg.getClass().getSimpleName();
        if (className.contains("Request") || className.contains("Response")) {
            return className;
        }
        
        try {
            String json = JSON.toJSONString(arg);
            // 限制日志长度
            if (json.length() > 1000) {
                return json.substring(0, 1000) + "...";
            }
            return json;
        } catch (Exception e) {
            return arg.toString();
        }
    }

    /**
     * 格式化返回结果
     */
    private String formatResult(Object result) {
        if (result == null) {
            return "null";
        }
        
        try {
            String json = JSON.toJSONString(result);
            // 限制日志长度
            if (json.length() > 2000) {
                return json.substring(0, 2000) + "...";
            }
            return json;
        } catch (Exception e) {
            return result.toString();
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
}
