package com.gwm.scaffold.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Scaffold Data 模块自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.modules.data", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(name = "com.gwm.scaffold.data.config.MybatisPlusConfig")
@Import({
    com.gwm.scaffold.data.config.MybatisPlusConfig.class
})
public class ScaffoldDataAutoConfiguration {

    public ScaffoldDataAutoConfiguration() {
        log.debug("GWM Scaffold Data 模块已启用");
    }
}
