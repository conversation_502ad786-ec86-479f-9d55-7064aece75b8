package com.alibaba.bizworks.demo.swagger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * swagger 示例类
 *
 * <AUTHOR> 管理员
 * @date 2023.12.05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("请求对象类注解")
public class SwaggerDemoDTO {

    /**
     * 请求对象类属性描述注解
     */
    @ApiModelProperty("请求对象类属性描述注解")
    private String code;

    /**
     * 请求对象类属性描述注解
     */
    @ApiModelProperty("请求对象类属性描述注解")
    private Long id;

    /**
     * 请求对象类属性描述注解
     */
    @ApiModelProperty("请求对象类属性描述注解")
    private String name;

}