package com.gwm.scaffold.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Scaffold 统一配置属性
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@ConfigurationProperties(prefix = "scaffold")
public class ScaffoldProperties {

    /**
     * 是否启用Scaffold
     */
    private boolean enabled = true;

    /**
     * 应用信息
     */
    private Application application = new Application();

    /**
     * 功能模块开关
     */
    private Modules modules = new Modules();

    /**
     * 应用信息配置
     */
    @Data
    public static class Application {
        /**
         * 应用名称
         */
        private String name = "GWM Scaffold Application";

        /**
         * 应用版本
         */
        private String version = "1.0.0";

        /**
         * 应用描述
         */
        private String description = "基于GWM Scaffold构建的应用";

        /**
         * 开发团队
         */
        private String team = "GWM开发团队";

        /**
         * 联系邮箱
         */
        private String email = "";

        /**
         * 应用主页
         */
        private String homepage = "";
    }

    /**
     * 功能模块开关配置
     */
    @Data
    public static class Modules {
        /**
         * 是否启用认证模块
         */
        private boolean auth = true;

        /**
         * 是否启用Web增强模块
         */
        private boolean web = true;

        /**
         * 是否启用数据访问模块
         */
        private boolean data = true;

        /**
         * 是否启用工具模块
         */
        private boolean tools = true;

        /**
         * 是否启用监控模块
         */
        private boolean monitor = true;

        /**
         * 是否启用文档模块
         */
        private boolean docs = true;
    }
}
