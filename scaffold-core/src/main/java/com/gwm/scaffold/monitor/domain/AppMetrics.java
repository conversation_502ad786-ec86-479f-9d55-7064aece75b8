package com.gwm.scaffold.monitor.domain;

import lombok.Builder;
import lombok.Data;

/**
 * 应用基础指标
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@Builder
public class AppMetrics {

    /**
     * 应用运行时间（毫秒）
     */
    private long uptime;

    /**
     * 已使用内存（字节）
     */
    private long usedMemory;

    /**
     * 最大内存（字节）
     */
    private long maxMemory;

    /**
     * 内存使用率（百分比）
     */
    private double memoryUsagePercent;

    /**
     * CPU使用率（百分比）
     */
    private double cpuUsagePercent;

    /**
     * 线程数
     */
    private int threadCount;

    /**
     * 可用处理器数
     */
    private int availableProcessors;

    /**
     * 格式化运行时间
     */
    public String getFormattedUptime() {
        long seconds = uptime / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (days > 0) {
            return String.format("%d天%d小时%d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 格式化内存使用
     */
    public String getFormattedMemoryUsage() {
        return formatBytes(usedMemory) + " / " + formatBytes(maxMemory);
    }

    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }
}
