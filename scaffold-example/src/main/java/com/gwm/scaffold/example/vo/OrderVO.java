package com.gwm.scaffold.example.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单视图对象VO
 * 
 * 用于返回订单信息给前端
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@ApiModel(description = "订单信息")
public class OrderVO {

    /**
     * 订单ID
     */
    @ApiModelProperty(value = "订单ID", example = "1")
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", example = "ORD202501150001")
    private String orderNo;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID", example = "1")
    private Long customerId;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名", example = "张三")
    private String customerName;

    /**
     * 客户电话
     */
    @ApiModelProperty(value = "客户电话", example = "13800138001")
    private String customerPhone;

    /**
     * 客户邮箱
     */
    @ApiModelProperty(value = "客户邮箱", example = "<EMAIL>")
    private String customerEmail;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", example = "iPhone 15 Pro Max 256GB")
    private String productName;

    /**
     * 商品SKU
     */
    @ApiModelProperty(value = "商品SKU", example = "IPHONE15PM256GB-BLACK")
    private String productSku;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量", example = "1")
    private Integer quantity;

    /**
     * 商品单价
     */
    @ApiModelProperty(value = "商品单价", example = "9999.00")
    private BigDecimal unitPrice;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额", example = "9999.00")
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    @ApiModelProperty(value = "优惠金额", example = "500.00")
    private BigDecimal discountAmount;

    /**
     * 实际支付金额
     */
    @ApiModelProperty(value = "实际支付金额", example = "9499.00")
    private BigDecimal actualAmount;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态", example = "1")
    private Integer orderStatus;

    /**
     * 订单状态描述
     */
    @ApiModelProperty(value = "订单状态描述", example = "待支付")
    private String orderStatusDesc;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", example = "1")
    private Integer paymentMethod;

    /**
     * 支付方式描述
     */
    @ApiModelProperty(value = "支付方式描述", example = "支付宝")
    private String paymentMethodDesc;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间", example = "2025-01-10 10:30:00")
    private LocalDateTime paymentTime;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址", example = "北京市朝阳区建国门外大街1号")
    private String shippingAddress;

    /**
     * 收货电话
     */
    @ApiModelProperty(value = "收货电话", example = "13800138001")
    private String shippingPhone;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名", example = "张三")
    private String shippingName;

    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间", example = "2025-01-11 09:00:00")
    private LocalDateTime shippingTime;

    /**
     * 送达时间
     */
    @ApiModelProperty(value = "送达时间", example = "2025-01-12 14:30:00")
    private LocalDateTime deliveryTime;

    /**
     * 订单备注
     */
    @ApiModelProperty(value = "订单备注", example = "请小心轻放")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2025-01-10 09:00:00")
    private LocalDateTime createTime;

    /**
     * 创建人工号
     */
    @ApiModelProperty(value = "创建人工号", example = "admin")
    private String createUserCode;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名", example = "管理员")
    private String createUserName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2025-01-10 10:00:00")
    private LocalDateTime updateTime;

    /**
     * 更新人工号
     */
    @ApiModelProperty(value = "更新人工号", example = "admin")
    private String updateUserCode;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名", example = "管理员")
    private String updateUserName;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", example = "0")
    private Integer version;
}
