package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.group.SearchGroupClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.SearchAdminResourceOfRoleFormClientDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * 权限PBC-组织服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcGroupSvcI-1702533187760")
@RequestMapping(path = "/PermissionPBCAthGroupCfgBizFlow")
public interface SystemPbcGroupSvcI {

    /**
     * 权限中心-通过账号获取树形结构 v2-/manage/admin/group/tree
     *
     * @return Response 权限中心-通过账号获取树形结构
     */
    @PostMapping(path = "/SystemPbcGroupSvc/groupTree/v1")
    Map<String, Object> groupTree();

    /**
     * 权限中心-获取完整树形结构（管理组织） v2-/manage/admin/group/tree/all
     *
     * @return Response 权限中心-获取完整树形结构（管理组织）
     */
    @PostMapping(path = "/SystemPbcGroupSvc/groupTreeAll/v1")
    Map<String, Object> groupTreeAll();


    /**
     * 权限中心-获取完整树形结构（HR组织） v2-/manage/admin/group/tree/hr/all
     *
     * @return Response 权限中心-获取完整树形结构（HR组织）
     */
    @PostMapping(path = "/SystemPbcGroupSvc/hrGroupTreeAll/v1")
    Map<String, Object> hrGroupTreeAll();

    /**
     * 权限中心-查询组织管理员 v2-/manage/admin/group/info
     * @param request
     * @return Map<String, Object> 权限中心-查询组织管理员
     */
    @PostMapping(path = "/SystemPbcGroupSvc/groupInfo/v1")
    Map<String, Object> groupInfo(@Parameter(name = "request", required = true) SearchGroupClientDTO request);

}
