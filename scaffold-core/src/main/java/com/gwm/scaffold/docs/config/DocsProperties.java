package com.gwm.scaffold.docs.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 文档配置属性
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@ConfigurationProperties(prefix = "scaffold.docs")
public class DocsProperties {

    /**
     * 是否启用文档
     */
    private boolean enabled = true;

    /**
     * Swagger配置
     */
    private Swagger swagger = new Swagger();

    /**
     * 文档主题配置
     */
    private Theme theme = new Theme();

    /**
     * 安全配置
     */
    private Security security = new Security();

    /**
     * Swagger配置
     */
    @Data
    public static class Swagger {
        /**
         * 是否启用Swagger
         */
        private boolean enabled = true;

        /**
         * API文档标题
         */
        private String title = "API文档";

        /**
         * API文档描述
         */
        private String description = "基于GWM Scaffold构建的API文档";

        /**
         * API版本
         */
        private String version = "1.0.0";

        /**
         * 服务条款URL
         */
        private String termsOfServiceUrl = "";

        /**
         * 联系人信息
         */
        private Contact contact = new Contact();

        /**
         * 许可证信息
         */
        private License license = new License();

        /**
         * 扫描的包路径
         */
        private List<String> basePackages = new ArrayList<>();

        /**
         * 排除的路径
         */
        private List<String> excludePaths = new ArrayList<>();

        @Data
        public static class Contact {
            /**
             * 联系人姓名
             */
            private String name = "开发团队";

            /**
             * 联系人邮箱
             */
            private String email = "";

            /**
             * 联系人网址
             */
            private String url = "";
        }

        @Data
        public static class License {
            /**
             * 许可证名称
             */
            private String name = "Apache 2.0";

            /**
             * 许可证URL
             */
            private String url = "http://www.apache.org/licenses/LICENSE-2.0";
        }
    }

    /**
     * 文档主题配置
     */
    @Data
    public static class Theme {
        /**
         * 使用的UI主题
         */
        private String ui = "bootstrap"; // bootstrap, layui, default

        /**
         * 是否启用搜索
         */
        private boolean enableSearch = true;

        /**
         * 是否启用过滤
         */
        private boolean enableFilter = true;

        /**
         * 是否启用缓存
         */
        private boolean enableCache = true;

        /**
         * 是否显示请求头
         */
        private boolean showRequestHeaders = true;

        /**
         * 是否显示响应头
         */
        private boolean showResponseHeaders = true;

        /**
         * 默认展开层级
         */
        private int defaultExpandLevel = 2;
    }

    /**
     * 安全配置
     */
    @Data
    public static class Security {
        /**
         * 是否启用认证
         */
        private boolean enabled = false;

        /**
         * 认证类型
         */
        private String type = "basic"; // basic, apikey, oauth2

        /**
         * API Key配置
         */
        private ApiKey apiKey = new ApiKey();

        @Data
        public static class ApiKey {
            /**
             * API Key名称
             */
            private String name = "Authorization";

            /**
             * API Key位置
             */
            private String keyLocation = "header"; // header, query

            /**
             * 传递方式
             */
            private String passAs = "header";
        }
    }
}
