package com.gwm.scaffold.monitor.config;

import com.gwm.scaffold.monitor.aspect.PerformanceMonitorAspect;
import com.gwm.scaffold.monitor.controller.MonitorController;
import com.gwm.scaffold.monitor.service.MetricsCollector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import javax.sql.DataSource;

/**
 * 监控模块自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.monitor.enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(MonitorProperties.class)
@Import({
    MonitorController.class,
    PerformanceMonitorAspect.class
})
public class MonitorAutoConfiguration {

    /**
     * 指标收集器
     */
    @Bean
    @ConditionalOnClass(DataSource.class)
    public MetricsCollector metricsCollector(DataSource dataSource) {
        log.info("初始化监控指标收集器");
        return new MetricsCollector(dataSource);
    }

    /**
     * 无数据源的指标收集器
     */
    @Bean
    @ConditionalOnClass(name = "javax.sql.DataSource")
    public MetricsCollector metricsCollectorWithoutDataSource() {
        log.info("初始化监控指标收集器（无数据源）");
        return new MetricsCollector(null);
    }
}
