# 单元测试书写规范

本文档定义了系统中单元测试的书写规范和最佳实践，旨在确保测试代码的一致性、可维护性和有效性。

## 1. 技术栈

### 1.1 核心测试框架
- **JUnit 5**: 主要测试框架
- **Mockito**: Mock对象框架  
- **AssertJ**: 流式断言库
- **Spring Test**: Spring集成测试支持

### 1.2 依赖配置
```xml
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.assertj</groupId>
    <artifactId>assertj-core</artifactId>
    <scope>test</scope>
</dependency>
```

## 2. 测试类结构

### 2.1 基本结构
```java
@ExtendWith(MockitoExtension.class)
@DisplayName("业务功能描述")
class BusinessServiceTest {
    
    @Mock
    private DependencyRepository repository;
    
    @InjectMocks
    private BusinessServiceImpl businessService;
    
    private TestDataModel testData;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }
    
    @Nested
    @DisplayName("功能模块1测试")
    class Feature1Test {
        // 相关测试方法
    }
}
```

### 2.2 注解使用规范
- **@ExtendWith(MockitoExtension.class)**: 必须用于启用Mockito支持
- **@DisplayName**: 必须用中文描述测试目的，格式为"业务功能描述"
- **@Mock**: 用于创建依赖对象的Mock
- **@InjectMocks**: 用于注入被测试对象
- **@Nested**: 用于组织相关测试方法
- **@BeforeEach**: 用于初始化测试数据

## 3. 命名规范

### 3.1 测试类命名
- 格式: `{被测试类名}Test`
- 示例: `IntelligentConfigServiceTest`

### 3.2 测试方法命名
- 格式: `{方法名}_{场景}_{期望结果}`
- 示例: 
  - `createConfig_Success()`
  - `updateConfig_NotFound_ThrowsException()`
  - `getConfigById_InvalidId_ReturnsNull()`

### 3.3 嵌套测试类命名
- 格式: `{功能模块}Test`
- 示例: 
  - `CreateConfigTest`
  - `QueryConfigTest`
  - `UpdateConfigTest`

## 4. 测试数据准备

### 4.1 使用Builder模式
```java
@BeforeEach
void setUp() {
    testData = TestDataModel.builder()
            .id(1L)
            .name("测试数据")
            .status("有效")
            .createTime(LocalDateTime.now())
            .build();
}
```

### 4.2 测试数据原则
- 使用有意义的测试数据，避免随机值
- 测试数据应该与业务场景相符
- 复用测试数据，避免重复创建
- 使用常量定义重要的测试值

## 5. Mock使用规范

### 5.1 Mock行为定义
```java
// 成功场景
when(repository.selectById(1L)).thenReturn(testData);

// 异常场景  
when(repository.insert(any())).thenThrow(new RuntimeException("数据库异常"));

// 无返回值方法
doNothing().when(repository).deleteById(1L);
doThrow(new RuntimeException()).when(repository).deleteById(999L);
```

### 5.2 参数匹配器
- 精确匹配: 使用具体值
- 类型匹配: 使用`any(Class.class)`
- 条件匹配: 使用`argThat()`
- 空值匹配: 使用`isNull()`

### 5.3 验证Mock调用
```java
// 验证方法被调用
verify(repository).selectById(1L);

// 验证方法从未被调用
verify(repository, never()).deleteById(any());

// 验证调用次数
verify(repository, times(2)).selectList(any());

// 验证调用参数
ArgumentCaptor<Entity> captor = ArgumentCaptor.forClass(Entity.class);
verify(repository).insert(captor.capture());
Entity savedEntity = captor.getValue();
assertThat(savedEntity.getName()).isEqualTo("期望值");
```

## 6. 断言规范

### 6.1 使用AssertJ流式断言
```java
// 对象断言
assertThat(result).isNotNull();
assertThat(result.getName()).isEqualTo("期望值");

// 集合断言
assertThat(resultList).hasSize(2);
assertThat(resultList).extracting(Entity::getName)
                     .containsExactly("值1", "值2");

// 异常断言
assertThatThrownBy(() -> service.method())
    .isInstanceOf(BusinessException.class)
    .hasMessageContaining("错误信息");
```

### 6.2 断言原则
- 优先使用AssertJ，提供更好的可读性
- 一个测试方法专注验证一个场景
- 断言信息要明确，便于定位问题
- 避免断言过多细节，关注核心业务逻辑

## 7. 异常测试

### 7.1 异常测试模式
```java
@Test
@DisplayName("无效参数时抛出参数异常")
void method_InvalidParam_ThrowsException() {
    // Given
    InvalidParam param = new InvalidParam();
    
    // When & Then
    assertThatThrownBy(() -> service.method(param))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining("参数无效");
}
```

### 7.2 异常测试原则
- 明确异常类型和异常信息
- 验证异常抛出时的系统状态
- 确保异常场景下不会产生副作用

## 8. Web层测试规范

### 8.1 MockMvc配置
```java
@BeforeEach
void setUp() {
    mockMvc = MockMvcBuilders.standaloneSetup(controller)
            .setControllerAdvice(new GlobalExceptionHandler())
            .defaultRequest(get("/").characterEncoding("UTF-8"))
            .build();
            
    objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
}
```

### 8.2 HTTP测试模式
```java
@Test
@DisplayName("创建资源成功")
void createResource_Success() throws Exception {
    // Given
    when(service.create(any())).thenReturn(mockResult);
    
    // When & Then
    mockMvc.perform(post("/api/v1/resource")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(createDTO)))
            .andDo(print())
            .andExpect(status().isCreated())
            .andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.id").value(1L));
}
```

## 9. 测试组织

### 9.1 使用@Nested分组
```java
@Nested
@DisplayName("创建功能测试")
class CreateTest {
    // 创建相关的所有测试方法
}

@Nested  
@DisplayName("查询功能测试")
class QueryTest {
    // 查询相关的所有测试方法
}
```

### 9.2 分组原则
- 按功能模块分组
- 每个分组包含该功能的所有场景
- 分组内测试方法按重要性排序
- 正常场景在前，异常场景在后

## 10. 测试覆盖率要求

### 10.1 覆盖率目标
- **Service层**: 行覆盖率 ≥ 90%，分支覆盖率 ≥ 85%
- **Controller层**: 行覆盖率 ≥ 85%，分支覆盖率 ≥ 80%
- **Repository层**: 重要查询方法需要测试
- **Util类**: 行覆盖率 ≥ 95%

### 10.2 必测场景
- 正常业务流程
- 边界值处理
- 异常情况处理
- 权限控制逻辑
- 数据验证逻辑

## 11. 性能测试

### 11.1 性能基准测试
```java
@Test
@DisplayName("批量处理性能测试")
void batchProcess_Performance() {
    // Given
    List<Data> largeDataSet = generateLargeDataSet(1000);
    
    // When
    long startTime = System.currentTimeMillis();
    service.batchProcess(largeDataSet);
    long endTime = System.currentTimeMillis();
    
    // Then
    long executionTime = endTime - startTime;
    assertThat(executionTime).isLessThan(5000); // 5秒内完成
}
```

## 12. 最佳实践

### 12.1 测试独立性
- 每个测试方法必须独立运行
- 不依赖其他测试的执行结果
- 使用@BeforeEach和@AfterEach管理测试状态

### 12.2 测试可读性
- 使用Given-When-Then结构
- 添加详细的@DisplayName描述
- 测试方法内逻辑简单明了
- 适当添加注释说明复杂逻辑

### 12.3 测试维护性
- 测试数据使用Builder模式创建
- 提取公共的测试工具方法
- 避免测试代码重复
- 及时更新过时的测试

### 12.4 测试执行效率
- 优先使用单元测试而非集成测试
- 合理使用Mock减少外部依赖
- 避免不必要的数据库操作
- 使用内存数据库进行数据层测试

## 13. 代码示例模板

### 13.1 Service层测试模板
```java
@ExtendWith(MockitoExtension.class)
@DisplayName("业务服务测试")
class BusinessServiceTest {
    
    @Mock
    private BusinessRepository repository;
    
    @InjectMocks
    private BusinessServiceImpl businessService;
    
    private BusinessEntity testEntity;
    private BusinessCreateDTO createDTO;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }
    
    @Nested
    @DisplayName("创建业务对象测试")
    class CreateBusinessTest {
        
        @Test
        @DisplayName("创建成功")
        void create_Success() {
            // Given-When-Then 模式
        }
        
        @Test
        @DisplayName("参数无效时抛出异常")
        void create_InvalidParam_ThrowsException() {
            // 异常测试
        }
    }
}
```

### 13.2 Controller层测试模板
```java
@ExtendWith(MockitoExtension.class)
@DisplayName("业务控制器测试")
class BusinessControllerTest {
    
    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    
    @Mock
    private BusinessService businessService;
    
    @InjectMocks
    private BusinessController businessController;
    
    @BeforeEach
    void setUp() {
        // MockMvc配置
    }
    
    @Test
    @DisplayName("HTTP接口测试")
    void httpEndpoint_Success() throws Exception {
        // HTTP测试逻辑
    }
}
```

## 14. 常见问题和解决方案

### 14.1 Mock对象行为定义
**问题**: Mock对象返回null或默认值
**解决**: 明确定义Mock行为，使用when().thenReturn()

### 14.2 测试数据管理
**问题**: 测试数据混乱，难以维护
**解决**: 使用Builder模式，提取测试数据工厂类

### 14.3 异常测试编写
**问题**: 异常测试不够全面
**解决**: 使用assertThatThrownBy验证异常类型和消息

### 14.4 测试执行缓慢
**问题**: 测试运行时间过长
**解决**: 使用Mock替代真实依赖，避免数据库操作

通过遵循本规范，可以编写出高质量、易维护的单元测试代码，为系统的稳定性和可靠性提供保障。 