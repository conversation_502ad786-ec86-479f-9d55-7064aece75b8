# 03 命名规范

## 通用命名原则

### 基本要求
- 名称应当准确、清晰地描述其用途或含义
- 避免使用缩写，除非是广泛接受的缩写(如HTTP、URL等)
- 避免使用含糊不清的名称(如data、temp、obj等)
- 名称长度应适中，既不过短也不过长
- 名称应尽量使用英文，避免拼音和中英混合

### 一致性
- 同一概念在整个代码库中应使用相同的命名
- 对立的概念应使用对立的命名(如add/remove, create/destroy等)
- 相似的概念应使用相似的命名
- 命名风格应与团队和行业标准保持一致

## 包名命名规范

### 命名规则
- 全部小写字母
- 使用点号(.)分隔
- 避免使用下划线(_)或连字符(-)
- 避免单个单词的包名
- 包名应具有描述性，表达包的用途

### 示例
```
// 正确的包名
com.gwm.globalvehicle.masterdata.brand
com.gwm.globalvehicle.common.exception
com.gwm.globalvehicle.util.date

// 错误的包名
com.gwm.GlobalVehicle.MasterData  // 包含大写字母
com.gwm.global_vehicle.master_data  // 包含下划线
com.gwm.gv.md  // 过度缩写，不清晰
```

## 类名命名规范

### 命名规则
- 使用帕斯卡命名法(PascalCase)，即所有单词首字母大写
- 名词或名词短语
- 类名应表达"是什么"，而不是"做什么"
- 接口名可以使用形容词或名词
- 避免在类名中包含类型信息(如BrandTable, BrandList等)

### 命名约定
- 实体类：直接使用领域名词（如`Brand`, `Manufacturer`）
- 控制器类：领域名词 + `Controller`（如`BrandController`）
- 服务接口：领域名词 + `Service`（如`BrandService`）
- 服务实现：领域名词 + `ServiceImpl`（如`BrandServiceImpl`）
- 数据访问：领域名词 + `Repository`或`Mapper`（如`BrandRepository`）
- 工具类：领域名词或动作 + `Utils`（如`DateUtils`）
- 异常类：问题领域 + `Exception`（如`BrandNotFoundException`）
- 枚举类：领域 + 类别 + `Enum`（如`BrandTypeEnum`）
- 常量类：领域 + `Constants`（如`BrandConstants`）

### 示例
```java
// 正确的类命名
public class BrandController {}
public interface BrandService {}
public class BrandServiceImpl implements BrandService {}
public class DateUtils {}

// 错误的类命名
public class brandController {}  // 首字母小写
public class Brand_Service {}  // 使用下划线
public class ProcessBrand {}  // 动词开头，不清晰表达"是什么"
public class ControllerOfBrand {}  // 过于冗长
```

## 方法名命名规范

### 命名规则
- 使用驼峰命名法(camelCase)，即首单词小写，其后单词首字母大写
- 动词或动词短语开头
- 方法名应清晰表达"做什么"
- 方法名应简洁明了，避免过长

### 常用动词约定
- **获取/查询**：get, find, query, search, retrieve
- **创建/添加**：create, add, insert, save
- **更新/修改**：update, modify, edit, change
- **删除/移除**：delete, remove, clear
- **转换**：convert, transform, parse
- **验证**：validate, check, verify
- **计算**：calculate, compute, count

### 特殊方法命名
- **布尔判断方法**：is, has, can, should等开头
- **初始化方法**：init, initialize
- **转换方法**：to[Type]
- **回调方法**：on[Event]

### 示例
```java
// 正确的方法命名
public Brand getBrandById(Long id) {}
public List<Brand> findBrandsByCountry(String country) {}
public void updateBrandStatus(Long id, Integer status) {}
public boolean isValidBrandCode(String brandCode) {}

// 错误的方法命名
public Brand Brand(Long id) {}  // 没有动词，不清晰表达操作
public List<Brand> brands(String country) {}  // 同上
public void BrandStatusUpdate(Long id, Integer status) {}  // 首字母大写，顺序不合理
public boolean validBrandCode(String brandCode) {}  // 缺少is/has等前缀
```

## 变量名命名规范

### 命名规则
- 使用驼峰命名法(camelCase)
- 名词或名词短语
- 变量名应表达"是什么"
- 避免单字母变量名，除非是临时循环变量(i, j等)或常见数学变量(x, y等)

### 特殊变量命名
- **集合类变量**：复数形式或添加List, Set, Map等后缀
- **布尔类型变量**：is, has, can, should等前缀
- **私有类成员变量**：可选添加下划线(_)前缀

### 示例
```java
// 正确的变量命名
private Long brandId;
private String brandName;
private List<Brand> brands;
private Map<String, Brand> brandMap;
private boolean isActive;
private boolean hasChildren;

// 错误的变量命名
private Long BrandId;  // 首字母大写
private String brand_name;  // 使用下划线
private List<Brand> getBrands;  // 使用动词
private boolean active;  // 布尔值缺少is/has等前缀
```

## 常量命名规范

### 命名规则
- 全部大写字母
- 单词间用下划线(_)分隔
- 名称应表明常量的用途
- 避免使用数字或字母作为常量名的前缀

### 示例
```java
// 正确的常量命名
public static final int MAX_RETRY_COUNT = 3;
public static final String DEFAULT_BRAND_CODE = "UNKNOWN";
public static final long CACHE_TIMEOUT_SECONDS = 3600;

// 错误的常量命名
public static final int maxRetryCount = 3;  // 应使用全大写
public static final String Default_Brand_Code = "UNKNOWN";  // 混合了多种命名风格
public static final long CACHETIMEOUTSECONDS = 3600;  // 缺少下划线分隔，难以阅读
```

## 特殊场景命名

### 数据库相关命名
- **表名**：全小写，下划线分隔，使用前缀区分模块（如cmp_brand）
- **字段名**：全小写，下划线分隔（如brand_name）
- **主键**：id或表名_id
- **外键**：关联表名_id（如brand_id）

### 前后端交互命名
- **API路径**：全小写，连字符分隔（如/api/v1/brands, /api/v1/brands/{id}）
- **请求参数**：驼峰命名法（如brandName, pageSize）
- **响应字段**：驼峰命名法（与Java对象属性保持一致）

### 测试类命名
- 测试类名：被测类名 + `Test`（如`BrandServiceTest`）
- 测试方法名：`test` + 被测方法名 + `场景描述`（如`testCreateBrandSuccess`） 