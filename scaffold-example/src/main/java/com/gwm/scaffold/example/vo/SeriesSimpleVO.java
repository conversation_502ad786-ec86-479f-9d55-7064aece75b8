package com.gwm.scaffold.example.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 车系简单信息VO（用于品牌详情中的关联车系列表）
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "车系简单信息")
public class SeriesSimpleVO {

    /**
     * 车系ID
     */
    @Schema(description = "车系ID", example = "1")
    private Long id;

    /**
     * 车系名称
     */
    @Schema(description = "车系名称", example = "C级")
    private String name;

    /**
     * 车系类型
     */
    @Schema(description = "车系类型", example = "SEDAN")
    private String type;

    /**
     * 车系级别
     */
    @Schema(description = "车系级别", example = "B")
    private String level;

    /**
     * 车系状态
     */
    @Schema(description = "车系状态", example = "ON_SALE")
    private String status;

}
