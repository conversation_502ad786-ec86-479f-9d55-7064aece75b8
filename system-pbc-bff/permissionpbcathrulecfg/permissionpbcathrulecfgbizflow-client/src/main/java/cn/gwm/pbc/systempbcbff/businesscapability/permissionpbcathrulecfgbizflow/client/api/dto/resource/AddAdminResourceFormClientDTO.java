package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "AddAdminResourceFormClientDTO", desc = "null")
@Data
@ApiModel("已接入平台创建菜单参数对象")
public class AddAdminResourceFormClientDTO implements Serializable {

    /**
     * account
     */
    @Field(name = "account", desc = "")
    @JsonIgnore
    private String account;

    /**
     * 平台码
     */
    @Field(name = "platformCode", desc = "")
    @ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 菜单名称
     */
    @Field(name = "name", desc = "")
    @ApiModelProperty("菜单名称")
    private String name;

    /**
     * 菜单编号
     */
    @Field(name = "code", desc = "")
    @ApiModelProperty("菜单编号")
    private String code;

    /**
     * 资源类型（0菜单，1按钮，2数据，3目录)
     */
    @Field(name = "type", desc = "")
    @ApiModelProperty("资源类型（0菜单，1按钮，2数据，3目录)")
    private String type;

    /**
     * 父ID
     */
    @Field(name = "parentId", desc = "")
    @ApiModelProperty("父ID")
    @JsonProperty(value = "parent_id")
    private String parentId;

    /**
     * 描述
     */
    @Field(name = "remark", desc = "")
    @ApiModelProperty("描述")
    private String remark;

    /**
     * 顺序
     */
    @Field(name = "sortNo", desc = "")
    @ApiModelProperty("顺序")
    @JsonProperty(value = "sort_no")
    private Integer sortNo;

    /**
     * 请求地址
     */
    @Field(name = "path", desc = "")
    @ApiModelProperty("请求地址")
    private String path;

    /**
     * 打开方式（0页签，1新窗口，默认0）
     */
    @Field(name = "openMethod", desc = "")
    @ApiModelProperty("打开方式（0页签，1新窗口，默认0）")
    @JsonProperty(value = "open_method")
    private Integer openMethod;

    /**
     * 图标
     */
    @Field(name = "ico", desc = "")
    @ApiModelProperty("图标")
    private String ico;

    /**
     * 菜单状态（0隐藏 ，1显示，默认1）
     */
    @Field(name = "enable", desc = "")
    @ApiModelProperty("菜单状态（0隐藏 ，1显示，默认1）")
    private Integer enable;

    /**
     * 类别（0-菜单管理，1-数据管理）
     */
    @Field(name = "category", desc = "")
    @ApiModelProperty("类别（0-菜单管理，1-数据管理）")
    @JsonProperty(value = "category")
    private Integer category;

    /**
     * 是否外链（0否，1是，默认0）
     */
    @Field(name = "linkFlag", desc = "")
    @ApiModelProperty("是否外链（0否，1是，默认0）")
    @JsonProperty(value = "link_flag")
    private Integer linkFlag;
}