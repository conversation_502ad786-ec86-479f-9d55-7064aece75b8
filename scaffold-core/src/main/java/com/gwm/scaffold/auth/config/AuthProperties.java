package com.gwm.scaffold.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 认证配置属性
 * 
 * <AUTHOR>
 * @date 2025/1/16
 */
@Data
@ConfigurationProperties(prefix = "scaffold.auth")
public class AuthProperties {

    /**
     * 是否启用认证
     */
    private boolean enabled = true;

    /**
     * 白名单路径
     */
    private List<String> whiteList = new ArrayList<>();
}
