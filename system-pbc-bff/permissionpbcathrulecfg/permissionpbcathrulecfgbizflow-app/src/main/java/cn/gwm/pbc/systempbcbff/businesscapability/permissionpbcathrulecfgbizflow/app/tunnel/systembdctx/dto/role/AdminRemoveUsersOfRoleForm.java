package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("解除绑定用户参数对象")
public class AdminRemoveUsersOfRoleForm implements Serializable {

    @JsonIgnore
    private String account;

    /**
     * 平台码
     */
    @ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 角色ID
     */
    @ApiModelProperty("角色ID")
    private String id;

    /**
     * 组织集合
     */
    @ApiModelProperty("组织集合")
    @JsonProperty(value = "group_ids")
    private List<Integer> groupIds;

    /**
     * 用户集合
     */
    @ApiModelProperty("用户集合")
    @JsonProperty(value = "user_ids")
    private String[] userIds;

}