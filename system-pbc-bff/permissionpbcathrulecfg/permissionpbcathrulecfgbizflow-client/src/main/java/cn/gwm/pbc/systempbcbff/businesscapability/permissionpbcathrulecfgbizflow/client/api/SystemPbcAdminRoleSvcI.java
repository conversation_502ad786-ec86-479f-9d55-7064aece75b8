package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.AdminRemoveUsersOfRoleFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleBindGroupFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.SearchAdminUsersOfRoleFormClientDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * 权限PBC-admin角色服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcAdminRoleSvcI-1702533187760")
@RequestMapping(path = "/PermissionPBCAthAdminRoleCfgBizFlow")
public interface SystemPbcAdminRoleSvcI {

    /**
     * 权限中心-角色绑定组织 v2-/manage/admin/role/bind/group
     *
     * @param request request
     * @return Response 权限中心-角色绑定组织
     */
    @PostMapping(path = "/SystemPbcAdminRoleSvc/roleBindGroup/v1")
    Map<String, Object> roleBindGroup(@RequestBody(required = true) RoleBindGroupFormClientDTO request);

    /**
     * 权限中心-查询角色用户和组织列表 v2-/manage/admin/role/userAndGroup/list
     *
     * @param request request
     * @return Response 权限中心-查询角色用户和组织列表
     */
    @PostMapping(path = "/SystemPbcAdminRoleSvc/searchUserAndGroupOfRole/v1")
    Map<String, Object> searchUserAndGroupOfRole(@RequestBody(required = true) SearchAdminUsersOfRoleFormClientDTO request);

    /**
     * 权限中心-解除绑定用户 v2-/manage/admin/role/user/unbind
     *
     * @param request request
     * @return Response 权限中心-解除绑定用户
     */
    @PostMapping(path = "/SystemPbcAdminRoleSvc/removeUsersOfRole/v1")
    Map<String, Object> removeUsersOfRole(@RequestBody(required = true) AdminRemoveUsersOfRoleFormClientDTO request);

}
