package com.gwm.scaffold.tools.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/11
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RoleVO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 扩展字段
     */
    private String ext1;

    /**
     * 扩展字段
     */
    private String ext2;

    /**
     * 扩展字段
     */
    private String ext3;

    /**
     * 	角色来源，0用户直接关联、1:部门关联
     */
    @JSONField(name = "role_origin")
    private Integer roleOrigin;

    /**
     * 平台id
     */
    @JSONField(name = "platform_id")
    private Long platformId;

    /**
     * 平台编码
     */
    @JSONField(name = "platform_code")
    private String platformCode;
}
