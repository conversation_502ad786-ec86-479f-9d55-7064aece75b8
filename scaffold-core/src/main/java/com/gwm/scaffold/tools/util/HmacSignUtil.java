package com.gwm.scaffold.tools.util;

import com.gwm.scaffold.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

import static java.util.Locale.US;
import static java.util.TimeZone.getTimeZone;
import static org.springframework.http.HttpHeaders.ACCEPT;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * 签名生成工具类
 *
 * <AUTHOR>
 * @date 2025/2/25
 **/
@Slf4j
public class HmacSignUtil {
    public static final String EMPTY_STR = "";
    public static final int ZERO = 0;
    private final static String HEADER_APP_KEY = "X-HMAC-ACCESS-KEY";
    private final static String HEADER_SIGN = "X-HMAC-SIGNATURE";
    private final static String HEADER_DATE = "Date";
    private final static String HEADER_ALGORITHM = "X-HMAC-ALGORITHM";
    private final static String HEADER_ALGORITHM_VAL = "hmac-sha256";
    private final static String HMAC_ALGORITHM = "HmacSHA256";
    private final static String SIGNED_HEADERS = "X-HMAC-SIGNED-HEADERS";

    public static final String HTTP_URL = "http://";
    public static final String HTTPS_URL = "https://";
    public static final String SLASH = "/";

    /**
     * 获取签名所需的header各项
     *
     * @param appKey
     * @param appSecret
     * @param uri
     * @param method
     * @return 签名相关的map，访问时直接拼到header里
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static Map<String, String> createSignHeader(String appKey, String appSecret, String uri, String method) {
        return createSignHeader(appKey, appSecret, uri, method, null);
    }

    /**
     * 获取签名所需的header各项
     *
     * @param appKey
     * @param appSecret
     * @param uri
     * @param method
     * @param header
     * @return 签名相关的map，访问时直接拼到header里
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static Map<String, String> createSignHeader(String appKey, String appSecret, String uri, String method, Map<String, String> header) {
        if (!uri.startsWith(HTTP_URL) && !uri.startsWith(HTTPS_URL)) {
            if (!uri.startsWith(SLASH)) {
                uri = "/" + uri;
            }
            uri = "http://127.0.0.1" + uri;
        }
        URL url;
        Mac hasher;
        try {
            url = new URL(uri);
            hasher = Mac.getInstance(HMAC_ALGORITHM);
            hasher.init(new SecretKeySpec(appSecret.getBytes(), HMAC_ALGORITHM));
        } catch (MalformedURLException e) {
            log.error("生成签名异常，url:{},异常！", uri, e);
            throw new ServiceException("生成签名异常，url异常！");
        } catch (NoSuchAlgorithmException e) {
            log.error("生成签名异常，Message Authentication Code异常，没有对应的算法：{}！", HMAC_ALGORITHM, e);
            throw new ServiceException("生成签名异常，Message Authentication Code异常，没有对应的算法！");
        } catch (InvalidKeyException e) {
            log.error("生成签名异常，非法的appKey：{}，appSecret：{}！", appKey, appSecret, e);
            throw new ServiceException("生成签名异常，非法的appKey,appSecret！");
        }
        String dateStr = getDateStr();
        String signString = splicing(
                method.toUpperCase(),
                url.getPath(),
                getSortQuerysString(url.getQuery()),
                appKey,
                dateStr,
                getSortHeaderString(header)
        );
        Map<String, String> headerMap = new HashMap<>(4, 1);
        headerMap.put(HEADER_APP_KEY, appKey);
        headerMap.put(HEADER_DATE, dateStr);
        headerMap.put(HEADER_SIGN, DatatypeConverter.printBase64Binary(hasher.doFinal(signString.getBytes())));
        headerMap.put(HEADER_ALGORITHM, HEADER_ALGORITHM_VAL);
        if (!CollectionUtils.isEmpty(header)) {
            headerMap.put(SIGNED_HEADERS, getSignedHeader(header));
            for (String key : header.keySet()) {
                headerMap.put(key, header.get(key) == null ? EMPTY_STR : header.get(key));
            }
        }
        return headerMap;
    }

    private static String getSignedHeader(Map<String, String> header) {
        StringBuilder content = new StringBuilder();
        int i = ZERO;
        for (String key : header.keySet()) {
            if (i != ZERO) {
                content.append(';');
            }
            content.append(key);
            i++;
        }
        return content.toString();
    }

    /**
     * 将url中的queryString参数排序后重新拼接
     *
     * @param queryStr
     * @return 已排序和重新拼装的queryString
     */
    private static String getSortQuerysString(String queryStr) {
        if (!StringUtils.hasText(queryStr)) {
            return EMPTY_STR;
        }
        Map<String, String> querys = new HashMap<>(16);
        String[] arr = queryStr.split("&");
        for (String item : arr) {
            if (!StringUtils.hasText(item)) {
                continue;
            }
            String[] kvs = item.split("=");
            if (kvs.length > 1) {
                querys.put(kvs[0], kvs[1]);
            } else {
                querys.put(kvs[0], EMPTY_STR);
            }
        }
        StringBuilder content = new StringBuilder();
        List<String> keys = new ArrayList<>(querys.keySet());
        Collections.sort(keys);
        for (int i = ZERO, len = keys.size(); i < len; ++i) {
            String key = keys.get(i);
            String value = String.valueOf(querys.get(key));
            content.append(i == ZERO ? EMPTY_STR : "&").append(key).append("=");
            if (StringUtils.hasText(value)) {
                content.append(value);
            }
        }
        return content.toString();
    }

    /**
     * 获取签名时间标签
     *
     * @return 签名时间标签字符串
     */
    public static String getDateStr() {
//        if(true) return "Fri,12 Mar 2021 01:42:18 GMT";
        SimpleDateFormat sdf = new SimpleDateFormat("EEE,dd MMM yyyy HH:mm:ss 'GMT'", US);
        // 设置时区为GMT
        sdf.setTimeZone(getTimeZone("GMT"));
        return sdf.format(Calendar.getInstance().getTime());
    }

    /**
     * 拼接字符串
     *
     * @param method        方法
     * @param uri           uri
     * @param queryString   请求uri中的参数串
     * @param accessKey     appKey
     * @param date          GMT时间
     * @param headersString 需要签名的请求头拼接
     * @return 待签名字符串
     */
    private static String splicing(String method, String uri, String queryString, String accessKey, String date, String headersString) {
        StringBuilder sb = new StringBuilder(method.toUpperCase());
        sb.append("\n").append(uri).append("\n");
        // 如果请求数据为url
        if (queryString == null) {
            queryString = EMPTY_STR;
        }
        sb.append(queryString)
                .append("\n")
                .append(accessKey)
                .append("\n")
                .append(date)
                .append("\n");
        if (StringUtils.hasText(headersString)) {
            sb.append(headersString).append("\n");
        }
        // 拼接字符串
        return sb.toString();
    }

    private static String getSortHeaderString(Map<String, String> header) {
        if (CollectionUtils.isEmpty(header)) {
            return EMPTY_STR;
        }
        List<String> keys = new ArrayList<>(header.keySet());
        keys.sort(String::compareTo);
        StringBuilder content = new StringBuilder();
        int i = ZERO;
        for (String key : keys) {
            if (i != ZERO) {
                content.append('\n');
            }
            content.append(key);
            content.append(":");
            content.append(header.get(key) == null ? EMPTY_STR : header.get(key));
            i++;
        }
        return content.toString();
    }

    /**
     * 封装加密请求头参数
     *
     * @param appKey    appKey
     * @param appSecret appSecret
     * @param url       url
     * @return org.springframework.http.HttpEntity<java.lang.String>
     */
    public static HttpHeaders getHttpHeaders(String appKey, String appSecret, String url, String method) {
        Map<String, String> headerMap = Collections.emptyMap();
        try {
            headerMap = createSignHeader(appKey, appSecret, url, method);
        } catch (Exception e) {
            log.error(EMPTY_STR, e);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
        headers.add(ACCEPT, APPLICATION_JSON_VALUE);
        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            headers.put(entry.getKey(), Collections.singletonList(entry.getValue()));
        }
        return headers;
    }
}