package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "MenuEditDTO", desc = "")
@Data
public class MenuEditDTO implements Serializable {

    /**
     * 菜单编码
     */
    @Field(name = "菜单编码", desc = "菜单编码")
    private String code;
    /**
     * 扩展字段1
     */
    @Field(name = "扩展字段1", desc = "扩展字段1")
    private String ext1;
    /**
     * 扩展字段2
     */
    @Field(name = "扩展字段2", desc = "扩展字段2")
    private String ext2;
    /**
     * 扩展字段3
     */
    @Field(name = "扩展字段3", desc = "扩展字段3")
    private String ext3;
    /**
     * 菜单ID
     */
    @Field(name = "菜单ID", desc = "菜单ID")
    private String id;
    /**
     * 菜单名称
     */
    @Field(name = "菜单名称", desc = "菜单名称")
    private String name;
    /**
     * 父节点ID，根节点传0
     */
    @Field(name = "父节点ID，根节点传0", desc = "父节点ID，根节点传0")
    @JsonProperty("parent_id")
    private String parentId;
    /**
     * 描述
     */
    @Field(name = "描述", desc = "描述")
    private String remark;
    /**
     * 顺序
     */
    @Field(name = "顺序", desc = "顺序")
    @JsonProperty("sort_no")
    private Integer sortNo;
    /**
     * 资源类型（0为菜单，1为按钮）
     */
    @Field(name = "资源类型（0为菜单，1为按钮）", desc = "资源类型（0为菜单，1为按钮）")
    private String type;
    /**
     * 菜单地址
     */
    @Field(name = "菜单地址", desc = "菜单地址")
    private String url;
    /**
     * 图标
     */
    @Field(name = "图标", desc = "图标")
    private String ico;
    /**
     * 请求路径
     */
    @Field(name = "请求路径", desc = "请求路径")
    private String path;
    /**
     * 打开方式，0页签，1新窗口
     */
    @Field(name = "打开方式，0页签，1新窗口", desc = "打开方式，0页签，1新窗口")
    @JsonProperty("open_method")
    private Integer openMethod;
    /**
     * 菜单状态，0隐藏1显示
     */
    @Field(name = "菜单状态，0隐藏1显示", desc = "菜单状态，0隐藏1显示")
    private Integer enable;
    /**
     * 是否外链，0否，1是
     */
    @Field(name = "是否外链，0否，1是", desc = "是否外链，0否，1是")
    @JsonProperty("link_flag")
    private Integer linkFlag;

}
