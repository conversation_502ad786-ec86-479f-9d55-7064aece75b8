<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GWM Scaffold 文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .toolbar {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            text-decoration: none;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid #667eea;
            color: #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .content {
            padding: 40px;
            max-width: none;
        }
        
        .loading {
            text-align: center;
            padding: 60px;
            color: #7f8c8d;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Markdown样式 */
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .markdown-content h1 {
            font-size: 2.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .markdown-content h2 {
            font-size: 2rem;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 8px;
        }
        
        .markdown-content h3 {
            font-size: 1.5rem;
        }
        
        .markdown-content p {
            margin-bottom: 15px;
            line-height: 1.8;
        }
        
        .markdown-content ul,
        .markdown-content ol {
            margin-bottom: 15px;
            padding-left: 30px;
        }
        
        .markdown-content li {
            margin-bottom: 5px;
        }
        
        .markdown-content code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #e83e8c;
        }
        
        .markdown-content pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 20px 0;
            position: relative;
        }
        
        .markdown-content pre code {
            background: none;
            color: inherit;
            padding: 0;
            font-size: 0.9rem;
        }
        
        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .markdown-content th,
        .markdown-content td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .markdown-content th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .markdown-content tr:hover {
            background: #f8f9fa;
        }
        
        .markdown-content blockquote {
            border-left: 4px solid #667eea;
            padding: 15px 20px;
            margin: 20px 0;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .markdown-content a {
            color: #667eea;
            text-decoration: none;
        }
        
        .markdown-content a:hover {
            text-decoration: underline;
        }
        
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }
        
        .toc h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin-bottom: 8px;
        }
        
        .toc a {
            color: #667eea;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
            display: block;
        }
        
        .toc a:hover {
            background: rgba(102, 126, 234, 0.1);
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            
            .content {
                padding: 20px;
            }
            
            .toolbar {
                flex-direction: column;
                gap: 10px;
            }
            
            .markdown-content h1 {
                font-size: 2rem;
            }
            
            .markdown-content h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GWM Scaffold</h1>
            <p>企业级Spring Boot脚手架使用指南</p>
        </div>
        
        <div class="toolbar">
            <div>
                <button class="btn btn-outline" onclick="toggleToc()">📋 目录</button>
                <button class="btn btn-outline" onclick="printDoc()">🖨️ 打印</button>
            </div>
            <div>
                <a href="/api/scaffold/docs" class="btn">📚 交互式文档</a>
                <a href="/" class="btn">🏠 返回首页</a>
            </div>
        </div>
        
        <div class="content">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在加载文档...</p>
            </div>
            
            <div id="toc" class="toc" style="display: none;">
                <h3>📋 文档目录</h3>
                <ul id="toc-list"></ul>
            </div>
            
            <div id="markdown-content" class="markdown-content" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入marked.js用于Markdown解析 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        // 页面加载完成后获取Markdown内容
        document.addEventListener('DOMContentLoaded', function() {
            loadMarkdownContent();
        });

        // 加载Markdown内容
        function loadMarkdownContent() {
            fetch('/api/scaffold/markdown')
                .then(response => response.text())
                .then(markdown => {
                    // 解析Markdown为HTML
                    const html = marked.parse(markdown);
                    
                    // 显示内容
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('markdown-content').innerHTML = html;
                    document.getElementById('markdown-content').style.display = 'block';
                    
                    // 生成目录
                    generateToc();
                    
                    // 添加代码复制功能
                    addCodeCopyButtons();
                    
                    // 平滑滚动
                    addSmoothScroll();
                })
                .catch(error => {
                    console.error('加载文档失败:', error);
                    document.getElementById('loading').innerHTML = 
                        '<p style="color: #e74c3c;">❌ 文档加载失败，请刷新页面重试</p>';
                });
        }

        // 生成目录
        function generateToc() {
            const headers = document.querySelectorAll('#markdown-content h1, #markdown-content h2, #markdown-content h3');
            const tocList = document.getElementById('toc-list');
            
            headers.forEach((header, index) => {
                // 为标题添加ID
                const id = 'heading-' + index;
                header.id = id;
                
                // 创建目录项
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = '#' + id;
                a.textContent = header.textContent;
                a.style.paddingLeft = (header.tagName === 'H2' ? '20px' : header.tagName === 'H3' ? '40px' : '0');
                
                li.appendChild(a);
                tocList.appendChild(li);
            });
        }

        // 添加代码复制功能
        function addCodeCopyButtons() {
            const codeBlocks = document.querySelectorAll('pre');
            
            codeBlocks.forEach(block => {
                const button = document.createElement('button');
                button.textContent = '📋 复制';
                button.className = 'btn btn-outline';
                button.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    font-size: 0.8rem;
                    padding: 5px 10px;
                `;
                
                block.style.position = 'relative';
                block.appendChild(button);
                
                button.addEventListener('click', async () => {
                    const code = block.querySelector('code');
                    const text = code ? code.textContent : block.textContent;
                    
                    try {
                        await navigator.clipboard.writeText(text);
                        button.textContent = '✅ 已复制';
                        setTimeout(() => {
                            button.textContent = '📋 复制';
                        }, 2000);
                    } catch (err) {
                        console.error('复制失败:', err);
                        button.textContent = '❌ 复制失败';
                        setTimeout(() => {
                            button.textContent = '📋 复制';
                        }, 2000);
                    }
                });
            });
        }

        // 添加平滑滚动
        function addSmoothScroll() {
            const links = document.querySelectorAll('a[href^="#"]');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // 切换目录显示
        function toggleToc() {
            const toc = document.getElementById('toc');
            toc.style.display = toc.style.display === 'none' ? 'block' : 'none';
        }

        // 打印文档
        function printDoc() {
            window.print();
        }
    </script>
</body>
</html>
