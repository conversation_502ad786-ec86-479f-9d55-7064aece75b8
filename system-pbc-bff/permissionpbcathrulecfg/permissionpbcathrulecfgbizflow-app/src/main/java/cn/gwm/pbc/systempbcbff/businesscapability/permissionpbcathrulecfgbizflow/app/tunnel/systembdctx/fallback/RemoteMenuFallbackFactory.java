package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteMenuService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.menu.*;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 登录降级处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteMenuFallbackFactory implements FallbackFactory<RemoteMenuService> {

    @Override
    public RemoteMenuService create(Throwable throwable) {
        return new RemoteMenuService() {

            @Override
            public CommonRlt<JSONObject> menuList(RemoteMenuListDTO menuListDTO) {
                log.error("调用查询菜单列表异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> menuEdit(RemoteMenuEditDTO menuEditDTO) {
                log.error("调用编辑菜单异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONArray> menuTree() {
                log.error("调用平台下菜单树异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> menuDetail(RemoteMenuDetailDTO menuDetailDTO) {
                log.error("调用查询菜单详情异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> menuDelete(RemoteMenuDeleteDTO menuDeleteDTO) {
                log.error("调用删除菜单异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> menuCreate(RemoteMenuCreateDTO menuCreateDTO) {
                log.error("调用创建菜单异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> menuUserTree(RemoteMenuUserTreeDTO menuUserTreeDTO) {
                log.error("调用用户菜单权限树异常：" + throwable.getMessage());
                return null;
            }
        };

    }
}
