package com.gwm.scaffold.example.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 更新请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "更新请求")
public class ModelUpdateRequest {

    /**
     * id
     */
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "id不能为空")
    @Min(value = 1, message = "id必须大于0")
    private Long id;

    /**
     * name
     */
    @Schema(description = "name")
    @Size(max = 100, message = "name长度不能超过100")
    private String name;

    /**
     * seriesId
     */
    @Schema(description = "seriesId")
    @Min(value = 1, message = "seriesId必须大于0")
    private Long seriesId;

    /**
     * yearModel
     */
    @Schema(description = "yearModel")
    @Min(value = 1, message = "yearModel必须大于0")
    private Integer yearModel;

    /**
     * displacement
     */
    @Schema(description = "displacement")
    private BigDecimal displacement;

    /**
     * maxPower
     */
    @Schema(description = "maxPower")
    @Min(value = 1, message = "maxPower必须大于0")
    private Integer maxPower;

    /**
     * maxTorque
     */
    @Schema(description = "maxTorque")
    @Min(value = 1, message = "maxTorque必须大于0")
    private Integer maxTorque;

    /**
     * transmission
     */
    @Schema(description = "transmission")
    @Size(max = 20, message = "transmission长度不能超过20")
    private String transmission;

    /**
     * gearCount
     */
    @Schema(description = "gearCount")
    @Min(value = 1, message = "gearCount必须大于0")
    private Integer gearCount;

    /**
     * driveType
     */
    @Schema(description = "driveType")
    @Size(max = 10, message = "driveType长度不能超过10")
    private String driveType;

    /**
     * fuelType
     */
    @Schema(description = "fuelType")
    @Size(max = 20, message = "fuelType长度不能超过20")
    private String fuelType;

    /**
     * bodyStructure
     */
    @Schema(description = "bodyStructure")
    @Size(max = 20, message = "bodyStructure长度不能超过20")
    private String bodyStructure;

    /**
     * seatCount
     */
    @Schema(description = "seatCount")
    @Min(value = 1, message = "seatCount必须大于0")
    private Integer seatCount;

    /**
     * length
     */
    @Schema(description = "length")
    @Min(value = 1, message = "length必须大于0")
    private Integer length;

    /**
     * width
     */
    @Schema(description = "width")
    @Min(value = 1, message = "width必须大于0")
    private Integer width;

    /**
     * height
     */
    @Schema(description = "height")
    @Min(value = 1, message = "height必须大于0")
    private Integer height;

    /**
     * wheelbase
     */
    @Schema(description = "wheelbase")
    @Min(value = 1, message = "wheelbase必须大于0")
    private Integer wheelbase;

    /**
     * curbWeight
     */
    @Schema(description = "curbWeight")
    @Min(value = 1, message = "curbWeight必须大于0")
    private Integer curbWeight;

    /**
     * tankCapacity
     */
    @Schema(description = "tankCapacity")
    @Min(value = 1, message = "tankCapacity必须大于0")
    private Integer tankCapacity;

    /**
     * fuelConsumption
     */
    @Schema(description = "fuelConsumption")
    private BigDecimal fuelConsumption;

    /**
     * guidePrice
     */
    @Schema(description = "guidePrice")
    private BigDecimal guidePrice;

    /**
     * launchTime
     */
    @Schema(description = "launchTime")
    private Date launchTime;

    /**
     * discontinueTime
     */
    @Schema(description = "discontinueTime")
    private Date discontinueTime;

    /**
     * imageUrls
     */
    @Schema(description = "imageUrls")
    @Size(max = 2147483647, message = "imageUrls长度不能超过2147483647")
    private String imageUrls;

    /**
     * configDetails
     */
    @Schema(description = "configDetails")
    @Size(max = 2147483647, message = "configDetails长度不能超过2147483647")
    private String configDetails;

    /**
     * status
     */
    @Schema(description = "status")
    @Size(max = 20, message = "status长度不能超过20")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    @Min(value = 1, message = "sortWeight必须大于0")
    private Integer sortWeight;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

}
