package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "gwm.open")
@Data
public class OpenPlatformProperties {

    private String url;

    private String env;

    private String appKey;

    private String appSecret;

}
