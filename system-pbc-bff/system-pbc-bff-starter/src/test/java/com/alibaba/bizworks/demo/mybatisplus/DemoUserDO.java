package com.alibaba.bizworks.demo.mybatisplus;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * mybatis plus实体示例类
 *
 * <AUTHOR> 管理员
 * @date 2023.12.05
 */
@Data
@TableName(value = "user")
public class DemoUserDO {
    @TableId(type = IdType.AUTO) // 主键不采用雪花算法，用数据库的自增长
    private Long id;

    @TableField(value = "name")
    private String name;

    private Integer age;

    private String phone;
}