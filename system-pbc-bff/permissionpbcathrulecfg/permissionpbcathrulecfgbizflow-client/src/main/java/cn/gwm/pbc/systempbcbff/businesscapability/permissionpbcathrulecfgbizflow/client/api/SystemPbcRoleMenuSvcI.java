package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.menu.RoleMenuBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.menu.RoleMenuTreeDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 权限PBC-角色菜单管理管理服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.15
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcRoleMenuSvcI-1702610562214")
@RequestMapping(path = "/PermissionPBCAthRuleCfgBizFlow")
public interface SystemPbcRoleMenuSvcI {

    /**
     * 权限中心-角色下绑定菜单 system/role/menu/bind
     *
     * @param request request
     * @return Response
     */
    @PostMapping(path = "/SystemPbcRoleMenuSvc/bind/v1")
    Response<Void> bind(@RequestBody(required = true) RoleMenuBindDTO request);

    /**
     * 权限中心-查询角色绑定菜单id列表 system/role/menu/checkIds
     *
     * @param request request
     * @return Response 权限中心-查询角色绑定菜单id列表
     */
    @PostMapping(path = "/SystemPbcRoleMenuSvc/checkIds/v1")
    Response<List<String>> checkIds(@RequestBody(required = true) RoleMenuTreeDTO request);
}
