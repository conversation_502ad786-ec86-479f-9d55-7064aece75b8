package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcSsoCallbackSvc;

import java.lang.Override;
import java.util.Map;
import javax.annotation.Resource;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.LoginRltVoClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.LoginUserInfoClientDTO;
import com.alibaba.bizworks.core.runtime.context.UserContext;
import com.gwm.pbc.auth.context.AuthenticateService;
import org.springframework.boot.json.JacksonJsonParser;
import org.springframework.boot.json.JsonParser;
import org.springframework.stereotype.Component;
/**
 * <AUTHOR>
 */
@Component("systemPbcSsoCallbackSvc")
public class SystemPbcSsoCallbackSvcImpl implements SystemPbcSsoCallbackSvc {

    @Resource
    private AuthenticateService authenticateService;

    private final JsonParser jsonParser = new JacksonJsonParser();

    @Override
    public LoginRltVoClientDTO ssoCallback() {
//        Map<String, String> stringStringMap = authenticateService.checkToken();
        String userExt = UserContext.getUserExt();
        Map<String, Object> userExtMap = jsonParser.parseMap(userExt);
        LoginRltVoClientDTO result = convertLoginVO(userExtMap);
        return result;
	}

    private LoginRltVoClientDTO convertLoginVO(Map<String, Object> userExtMap) {
        LoginRltVoClientDTO rltVO = new LoginRltVoClientDTO();
        rltVO.setAccessToken(String.valueOf(userExtMap.get("token")));
        LoginUserInfoClientDTO userInfo = new LoginUserInfoClientDTO();

        userInfo.setIsFormal((Integer) userExtMap.get("is_formal"));
        userInfo.setSex((Integer) userExtMap.get("sex"));
        userInfo.setUserId( Long.parseLong((String) userExtMap.get("user_id")));
        userInfo.setUserCode((String) userExtMap.get("user_code"));
        userInfo.setUserName((String) userExtMap.get("user_name"));
        userInfo.setGroupId((String) userExtMap.get("group_id"));
        userInfo.setGroupName((String) userExtMap.get("group_name"));
        userInfo.setDutyName((String) userExtMap.get("duty_name"));
        userInfo.setPositionName((String) userExtMap.get("position_name"));
        rltVO.setUserInfo(userInfo);
        rltVO.setExpireTime((Long) userExtMap.get("expire_time"));
        return rltVO;
	}
}
