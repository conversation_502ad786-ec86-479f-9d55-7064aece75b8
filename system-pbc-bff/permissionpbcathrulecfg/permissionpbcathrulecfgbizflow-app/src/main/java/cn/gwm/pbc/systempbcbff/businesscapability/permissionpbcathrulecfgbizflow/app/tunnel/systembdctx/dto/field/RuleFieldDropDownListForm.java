package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("规则字段下拉列表请求参数对象")
public class RuleFieldDropDownListForm implements Serializable {

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer page;

    /**
     * 每页显示几条
     */
    @ApiModelProperty(value = "每页显示几条")
    private Integer size;

    /**
     * 字段编码
     */
    @ApiModelProperty("字段编码")
    @JsonProperty(value = "field_code")
    private String fieldCode;

    /**
     * 组织ID
     */
    @ApiModelProperty("组织ID")
    @JsonProperty(value = "group_id")
    private Integer groupId;

    /**
     * 岗位ID集合
     */
    @ApiModelProperty("岗位ID集合")
    @JsonProperty(value = "position_ids")
    private List<Long> positionIds;
}