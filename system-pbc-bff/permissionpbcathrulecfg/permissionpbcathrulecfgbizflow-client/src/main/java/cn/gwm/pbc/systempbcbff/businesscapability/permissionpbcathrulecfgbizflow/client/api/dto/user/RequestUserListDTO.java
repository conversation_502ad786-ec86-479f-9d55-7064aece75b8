package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import com.alibaba.bizworks.core.specification.Field;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
/**
 * <AUTHOR>
 */
@StructureObject(name = "RequestUserListDTO", desc = "")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RequestUserListDTO {

    /**
     * 页码
     */
    @Field(desc = "页码", name = "页码")
    private Integer page;
    
    /**
     * 条数（默认返回10条）
     */
    @Field(desc = "条数", name = "条数")
    private Integer size;

    /**
     * 是否启用 0否 1是
     */
    @Field(name = "是否启用0否1是", desc = "是否启用0否1是")
    private Integer enable;

    /**
     * 别名
     */
    @Field(name = "别名", desc = "别名")
    private String alias;

    /**
     * 用户账号
     */
    @Field(name = "用户账号", desc = "用户账号")
    @Alias("user_code")
    private String code;

    /**
     * 用户类型（1 正式工 2自定义）
     */
    @Field(name = "用户类型（1正式工2自定义）", desc = "用户类型（1正式工2自定义）")
    @Alias("user_type")
    private String type;

}
