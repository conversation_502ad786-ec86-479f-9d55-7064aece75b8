package ${packageName}.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import ${packageName}.entity.${entityName};
import ${packageName}.dto.${createRequestName};
import ${packageName}.dto.${updateRequestName};
import ${packageName}.dto.${queryRequestName};
import ${packageName}.vo.${voName};

import java.util.List;

/**
 * ${tableInfo.tableComment!entityName}服务接口
 * 
 * <AUTHOR>
 * @date ${date}
 */
public interface ${serviceName} extends IService<${entityName}> {

    /**
     * 分页查询${tableInfo.tableComment!entityName}列表
     *
     * @param request 查询条件
     * @return ${tableInfo.tableComment!entityName}分页数据
     */
    IPage<${voName}> get${entityName}Page(${queryRequestName} request);

    /**
     * 根据ID获取${tableInfo.tableComment!entityName}详情
     *
     * @param id ${tableInfo.tableComment!entityName}ID
     * @return ${tableInfo.tableComment!entityName}详情
     */
    ${voName} get${entityName}ById(${tableInfo.primaryKey.javaType} id);

    /**
     * 创建${tableInfo.tableComment!entityName}
     *
     * @param request 创建请求
     * @return ${tableInfo.tableComment!entityName}详情
     */
    ${voName} create${entityName}(${createRequestName} request);

    /**
     * 更新${tableInfo.tableComment!entityName}
     *
     * @param request 更新请求
     * @return ${tableInfo.tableComment!entityName}详情
     */
    ${voName} update${entityName}(${updateRequestName} request);

    /**
     * 删除${tableInfo.tableComment!entityName}
     *
     * @param id ${tableInfo.tableComment!entityName}ID
     * @return 是否成功
     */
    boolean delete${entityName}(${tableInfo.primaryKey.javaType} id);

    /**
     * 批量删除${tableInfo.tableComment!entityName}
     *
     * @param ids ${tableInfo.tableComment!entityName}ID列表
     * @return 删除数量
     */
    int batchDelete${entityName}s(List<${tableInfo.primaryKey.javaType}> ids);


}
