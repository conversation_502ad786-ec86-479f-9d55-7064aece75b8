package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
/**
 * <AUTHOR>
 */
@Data
@ApiModel("创建平台用户关系")
public class PlatUserForm implements Serializable {

    @JsonIgnore
    private String platformCode;

    @JsonIgnore
    private String account;

    /**
     * 组织集合
     */
    @ApiModelProperty("组织集合")
    @JsonProperty(value = "group_ids")
    @Size(max = 50, message = DepartmentCode.DE_0003)
    private List<Integer> groupIds;

    /**
     * 用户ID集合
     */
    @ApiModelProperty("用户ID集合")
    @JsonProperty(value = "user_ids")
    private List<Long> userIds;

    /**
     * 用户账号集合
     */
    @JsonIgnore
    @ApiModelProperty("用户账号集合")
    @JsonProperty(value = "user_codes")
    private List<String> userCodes;
}
