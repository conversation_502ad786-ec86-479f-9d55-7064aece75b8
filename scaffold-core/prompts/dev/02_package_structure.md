# 02 包结构管理要求

## 基础包结构定义

### 顶层包结构
```
com.gwm.globalvehicle
├── masterdata/       # 主数据管理模块
├── common/           # 公共组件
├── config/           # 全局配置
└── util/             # 工具类
```

### 模块内部包结构
以主数据管理模块为例：
```
com.gwm.globalvehicle.masterdata
├── brand/            # 品牌管理
├── manufacturer/     # 生产厂商管理
├── vehicle/          # 车型管理
├── vehiclegrade/     # 车款管理
└── common/           # 主数据模块公共组件
```

### 业务模块内部结构
以生产厂商管理模块为例：
```
com.gwm.globalvehicle.masterdata
├── controller/         # 控制层
│   └── ManufacturerController.java
│   └── BrandController.java
├── service/           # 业务逻辑层
│   ├── ManufacturerService.java
│   ├── BrandService.java
│   └── impl/
│       └── ManufacturerServiceImpl.java
│       └── BrandServiceImpl.java
├── repository/        # 数据访问层
│   └── ManufacturerMapper.java
│   └── BrandMapper.java
├── domain/           # 领域模型
│   └── Manufacturer.java
│   └── Brand.java
├── dto/              # 数据传输对象
│   ├── ManufacturerCreateDTO.java
│   ├── ManufacturerUpdateDTO.java
│   └── ManufacturerQueryDTO.java
├── vo/               # 视图层对象
│   ├── ManufacturerVO.java
│   └── ManufacturerDetailVO.java
├── enums/            # 枚举定义
│   ├── ManufacturerTypeEnum.java
│   └── OperationStatusEnum.java
└── constants/        # 常量定义
    └── Constants.java
```

## 分层架构设计原则

### 表示层 (Controller)
- 负责处理HTTP请求和响应
- 参数校验和请求转换
- 不包含业务逻辑
- 调用服务层处理业务
- 异常转换为HTTP响应

### 业务逻辑层 (Service)
- 包含核心业务逻辑
- 事务管理和控制
- 调用数据访问层获取数据
- 数据转换和业务规则校验
- 不直接处理HTTP相关内容

### 数据访问层 (Repository/Mapper)
- 负责数据库交互
- SQL语句定义和执行
- 实体类映射和转换
- 不包含业务逻辑
- 提供基础的CRUD操作

### 领域模型 (Domain)
- 定义业务实体和关系
- 映射数据库表结构
- 包含字段校验注解
- 实体间关联定义
- 不包含业务逻辑方法

### 数据传输对象 (DTO/VO)
- DTO用于服务间数据传输
- VO用于视图层数据展示
- 包含数据校验注解
- 不直接映射数据库表
- 可根据视图需求灵活定义

## 包职责边界定义

### 跨层调用原则
- 严格遵循自上而下的调用方向
- Controller只能调用Service
- Service只能调用Repository
- 禁止跨层调用（如Controller直接调用Repository）
- 禁止循环依赖

### 包间引用规则
- 同层包之间可以相互引用
- 上层包可以引用下层包
- 下层包不能引用上层包
- 公共组件可以被所有层引用
- 避免包间过度耦合

### 公共组件管理
- 全局公共组件放在common包中
- 模块内公共组件放在模块的common包中
- 公共组件应具有高内聚性
- 避免公共组件间相互依赖
- 公共组件不应包含业务逻辑

## 多模块协作

### 模块间通信方式
- 优先使用接口定义交互契约
- 避免直接依赖其他模块的实现类
- 考虑使用事件驱动通信
- 大型系统考虑微服务架构
- 模块间通信应有明确的错误处理机制

### 依赖管理
- 清晰定义模块间依赖关系
- 避免循环依赖
- 控制依赖传递
- 使用依赖倒置原则解耦
- 定期检查并优化依赖结构

## 包命名规范

### 基本原则
- 包名全部小写
- 使用点号分隔
- 避免使用下划线或连字符
- 包名应简洁明了
- 包名应反映其内容

### 命名建议
- 功能模块包使用名词
- 工具类包使用util后缀
- 配置类包使用config后缀
- 避免使用缩写（除非广为接受）
- 避免过长的包名 