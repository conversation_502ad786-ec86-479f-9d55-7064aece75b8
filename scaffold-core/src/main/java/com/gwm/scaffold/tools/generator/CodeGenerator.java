package com.gwm.scaffold.tools.generator;

import com.gwm.scaffold.tools.generator.config.GeneratorConfig;
import com.gwm.scaffold.tools.generator.domain.TableInfo;
import com.gwm.scaffold.tools.generator.util.DatabaseMetaUtil;
import com.gwm.scaffold.tools.generator.util.TemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.sql.DataSource;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 代码生成器
 * 
 * 根据数据库表结构生成符合脚手架规范的代码
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
public class CodeGenerator {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private TemplateUtil templateUtil;

    @Autowired
    private DatabaseMetaUtil databaseMetaUtil;

    /**
     * 生成代码
     *
     * @param config 生成配置
     * @return 生成结果
     */
    public Map<String, Object> generateCode(GeneratorConfig config) {
        log.info("开始生成代码，表名：{}", config.getTableName());
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 获取表信息
            TableInfo tableInfo = databaseMetaUtil.getTableInfo(config.getTableName());
            if (tableInfo == null) {
                result.put("success", false);
                result.put("message", "表不存在：" + config.getTableName());
                return result;
            }
            
            // 2. 构建模板数据
            Map<String, Object> templateData = buildTemplateData(tableInfo, config);
            
            // 3. 生成各层代码
            generateEntity(templateData, config);
            generateMapper(templateData, config);
            generateMapperXml(templateData, config);
            generateService(templateData, config);
            generateServiceImpl(templateData, config);
            generateController(templateData, config);
            generateDto(templateData, config);
            generateVo(templateData, config);
            
            result.put("success", true);
            result.put("message", "代码生成成功");
            result.put("tableInfo", tableInfo);
            
            log.info("代码生成完成，表名：{}", config.getTableName());
            
        } catch (Exception e) {
            log.error("代码生成失败", e);
            result.put("success", false);
            result.put("message", "代码生成失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 构建模板数据
     */
    private Map<String, Object> buildTemplateData(TableInfo tableInfo, GeneratorConfig config) {
        Map<String, Object> data = new HashMap<>();
        
        // 基础信息
        data.put("tableInfo", tableInfo);
        data.put("config", config);
        data.put("packageName", config.getPackageName());
        data.put("moduleName", config.getModuleName());
        data.put("author", config.getAuthor());
        data.put("date", java.time.LocalDate.now().toString());
        
        // 类名信息
        data.put("entityName", tableInfo.getEntityName());
        data.put("entityNameLower", tableInfo.getEntityNameLower());
        data.put("serviceName", tableInfo.getEntityName() + "Service");
        data.put("serviceImplName", tableInfo.getEntityName() + "ServiceImpl");
        data.put("mapperName", tableInfo.getEntityName() + "Mapper");
        data.put("controllerName", tableInfo.getEntityName() + "Controller");
        data.put("createRequestName", tableInfo.getEntityName() + "CreateRequest");
        data.put("updateRequestName", tableInfo.getEntityName() + "UpdateRequest");
        data.put("queryRequestName", tableInfo.getEntityName() + "QueryRequest");
        data.put("voName", tableInfo.getEntityName() + "VO");
        
        return data;
    }

    /**
     * 生成实体类
     */
    private void generateEntity(Map<String, Object> templateData, GeneratorConfig config) throws Exception {
        String content = templateUtil.processTemplate("entity.ftl", templateData);
        String fileName = templateData.get("entityName") + ".java";
        String filePath = config.getOutputPath() + "/entity/" + fileName;
        writeFile(filePath, content);
        log.info("生成实体类：{}", filePath);
    }

    /**
     * 生成Mapper接口
     */
    private void generateMapper(Map<String, Object> templateData, GeneratorConfig config) throws Exception {
        String content = templateUtil.processTemplate("mapper.ftl", templateData);
        String fileName = templateData.get("mapperName") + ".java";
        String filePath = config.getOutputPath() + "/mapper/" + fileName;
        writeFile(filePath, content);
        log.info("生成Mapper接口：{}", filePath);
    }

    /**
     * 生成Mapper XML
     */
    private void generateMapperXml(Map<String, Object> templateData, GeneratorConfig config) throws Exception {
        String content = templateUtil.processTemplate("mapper-xml.ftl", templateData);
        String fileName = templateData.get("mapperName") + ".xml";
        // 将XML文件生成到scaffold-example模块的resources/mapper目录下
        String filePath = "scaffold-example/src/main/resources/mapper/" + fileName;
        writeFile(filePath, content);
        log.info("生成Mapper XML：{}", filePath);
    }

    /**
     * 生成Service接口
     */
    private void generateService(Map<String, Object> templateData, GeneratorConfig config) throws Exception {
        String content = templateUtil.processTemplate("service.ftl", templateData);
        String fileName = templateData.get("serviceName") + ".java";
        String filePath = config.getOutputPath() + "/service/" + fileName;
        writeFile(filePath, content);
        log.info("生成Service接口：{}", filePath);
    }

    /**
     * 生成Service实现类
     */
    private void generateServiceImpl(Map<String, Object> templateData, GeneratorConfig config) throws Exception {
        String content = templateUtil.processTemplate("service-impl.ftl", templateData);
        String fileName = templateData.get("serviceImplName") + ".java";
        String filePath = config.getOutputPath() + "/service/impl/" + fileName;
        writeFile(filePath, content);
        log.info("生成Service实现类：{}", filePath);
    }

    /**
     * 生成Controller
     */
    private void generateController(Map<String, Object> templateData, GeneratorConfig config) throws Exception {
        String content = templateUtil.processTemplate("controller.ftl", templateData);
        String fileName = templateData.get("controllerName") + ".java";
        String filePath = config.getOutputPath() + "/controller/" + fileName;
        writeFile(filePath, content);
        log.info("生成Controller：{}", filePath);
    }

    /**
     * 生成DTO
     */
    private void generateDto(Map<String, Object> templateData, GeneratorConfig config) throws Exception {
        // 生成创建请求DTO
        String createContent = templateUtil.processTemplate("create-request-dto.ftl", templateData);
        String createFileName = templateData.get("createRequestName") + ".java";
        String createFilePath = config.getOutputPath() + "/dto/" + createFileName;
        writeFile(createFilePath, createContent);
        log.info("生成创建请求DTO：{}", createFilePath);

        // 生成更新请求DTO
        String updateContent = templateUtil.processTemplate("update-request-dto.ftl", templateData);
        String updateFileName = templateData.get("updateRequestName") + ".java";
        String updateFilePath = config.getOutputPath() + "/dto/" + updateFileName;
        writeFile(updateFilePath, updateContent);
        log.info("生成更新请求DTO：{}", updateFilePath);

        // 生成查询请求DTO
        String queryContent = templateUtil.processTemplate("query-request-dto.ftl", templateData);
        String queryFileName = templateData.get("queryRequestName") + ".java";
        String queryFilePath = config.getOutputPath() + "/dto/" + queryFileName;
        writeFile(queryFilePath, queryContent);
        log.info("生成查询请求DTO：{}", queryFilePath);
    }

    /**
     * 生成VO
     */
    private void generateVo(Map<String, Object> templateData, GeneratorConfig config) throws Exception {
        String content = templateUtil.processTemplate("vo.ftl", templateData);
        String fileName = templateData.get("voName") + ".java";
        String filePath = config.getOutputPath() + "/vo/" + fileName;
        writeFile(filePath, content);
        log.info("生成VO：{}", filePath);
    }

    /**
     * 写入文件
     */
    private void writeFile(String filePath, String content) throws Exception {
        // 确保路径是基于当前工作目录的
        String basePath = System.getProperty("user.dir");

        // 构建完整的文件路径
        String fullPath;
        if (filePath.startsWith("/") || filePath.contains(":")) {
            // 绝对路径，直接使用
            fullPath = filePath;
        } else {
            // 相对路径，基于当前工作目录
            fullPath = basePath + File.separator + filePath;
        }

        File file = new File(fullPath);
        File parentDir = file.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();
        }

        try (java.io.FileWriter writer = new java.io.FileWriter(file)) {
            writer.write(content);
        }

        log.info("文件已生成：{}", file.getAbsolutePath());
    }
}
