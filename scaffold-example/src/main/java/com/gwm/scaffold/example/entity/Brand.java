package com.gwm.scaffold.example.entity;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.gwm.scaffold.core.entity.BaseSuperEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 品牌实体类
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("T_BRAND")
@Schema(description = "品牌信息")
public class Brand extends BaseSuperEntity {

    /**
     * 品牌ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "品牌ID", hidden = true, requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    /**
     * 品牌英文名称
     */
    @Schema(description = "品牌英文名称")
    private String englishName;

    /**
     * 所属国家
     */
    @Schema(description = "所属国家", requiredMode = Schema.RequiredMode.REQUIRED)
    private String country;

    /**
     * 成立年份
     */
    @Schema(description = "成立年份")
    private Integer foundedYear;

    /**
     * 官网地址
     */
    @Schema(description = "官网地址")
    private String website;

    /**
     * LOGO图片URL
     */
    @Schema(description = "LOGO图片URL")
    private String logoUrl;

    /**
     * 品牌简介
     */
    @Schema(description = "品牌简介")
    private String description;

    /**
     * 状态（ACTIVE-启用，INACTIVE-禁用）
     */
    @Schema(description = "状态（ACTIVE-启用，INACTIVE-禁用）")
    private String status;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重")
    private Integer sortWeight;

    /**
     * 是否删除（0-否，1-是）
     */
    @TableLogic
    @Schema(description = "是否删除（0-否，1-是）")
    private Integer deleted;

    /**
     * 删除时间
     */
    @Schema(description = "删除时间")
    private LocalDateTime deletedTime;

}
