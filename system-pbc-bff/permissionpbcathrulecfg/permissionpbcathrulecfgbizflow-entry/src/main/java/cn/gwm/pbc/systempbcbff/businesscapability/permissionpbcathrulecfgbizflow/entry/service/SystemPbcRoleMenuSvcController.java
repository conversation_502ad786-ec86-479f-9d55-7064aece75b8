package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRoleMenuSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcRoleMenuSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.menu.RoleMenuBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.menu.RoleMenuTreeDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.lang.Override;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限PBC-角色菜单管理管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.15
 */
@RestController("PermissionPBCAthRuleCfgBizFlowSystemPbcRoleMenuSvcCON")
public class SystemPbcRoleMenuSvcController implements SystemPbcRoleMenuSvcI {

    @Autowired
    private SystemPbcRoleMenuSvc systemPbcRoleMenuSvc;

    @Override
    public Response<Void> bind(@RequestBody(required = true) RoleMenuBindDTO request) {
    	systemPbcRoleMenuSvc.bind(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<List<String>> checkIds(@RequestBody(required = true) RoleMenuTreeDTO request) {
    	List<String> listReturn = systemPbcRoleMenuSvc.checkIds(request);
		return Response.buildSuccess(listReturn);
	}
}
