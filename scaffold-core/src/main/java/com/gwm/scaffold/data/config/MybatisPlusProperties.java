package com.gwm.scaffold.data.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * MyBatis Plus 配置属性
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@ConfigurationProperties(prefix = "scaffold.data.mybatis-plus")
public class MybatisPlusProperties {

    /**
     * 是否启用防全表更新删除插件
     */
    private boolean blockAttackEnabled = true;

    /**
     * 是否启用乐观锁插件
     */
    private boolean optimisticLockerEnabled = true;

    /**
     * 分页配置
     */
    private PaginationProperties pagination = new PaginationProperties();

    /**
     * 分页配置属性
     */
    @Data
    public static class PaginationProperties {
        /**
         * 是否启用分页插件
         */
        private boolean enabled = true;

        /**
         * 数据库类型
         */
        private String dbType = "mysql";

        /**
         * 设置请求的页面大于最大页后操作，true回到首页，false继续请求
         */
        private boolean overflow = true;

        /**
         * 设置最大单页限制数量，默认500，-1不受限
         */
        private Long maxLimit = 500L;
    }
}
