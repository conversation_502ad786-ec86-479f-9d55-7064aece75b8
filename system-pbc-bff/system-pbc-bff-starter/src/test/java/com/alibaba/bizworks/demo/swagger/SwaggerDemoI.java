package com.alibaba.bizworks.demo.swagger;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * swagger 示例类
 *
 * <AUTHOR> 管理员
 * @date 2023.12.05
 */
@RequestMapping(path = "/swagger-demo")
@Api(tags = "swagger注解类")
public interface SwaggerDemoI {

    /**
     * 方法总体描述
     */
    @PostMapping(path = "/swaggerdemo/test/v1")
    @ApiOperation(value = "方法总体描述", notes = "方法详细描述")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "swaggerDemoDTO", value = "参数描述", dataType = "SwaggerDemoDTO",
            dataTypeClass = SwaggerDemoDTO.class, required = true, paramType = "body"),
    })
    @ApiResponses({
        @ApiResponse(code = 10000, message = "返回错误码描述"),
    })
    Boolean test(@RequestBody SwaggerDemoDTO swaggerDemoDTO);
}