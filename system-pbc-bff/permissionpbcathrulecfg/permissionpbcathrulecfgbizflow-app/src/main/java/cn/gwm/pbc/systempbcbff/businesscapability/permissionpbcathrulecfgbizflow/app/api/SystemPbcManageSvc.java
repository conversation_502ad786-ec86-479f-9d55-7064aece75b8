package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.*;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;

import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

/**
 * 权限PBC-管理服务
 *
 * <AUTHOR> 管理员
 * @date 2023.12.13
 */
@BusinessCapabilityService(name = "权限PBC-管理服务")
public interface SystemPbcManageSvc {

    /**
     * test
     * @param request
     * @return TestDTO
     */
    @Method(name = "test")
    @ReturnValue
    TestDTO test(@Parameter(name = "请求参数", required = false) TestDTO request);

    /**
     * 权限中心-用户列表system/user/list
     * Request URL: http://assemble-preview-cnp-bdtest.gwmit.cn/SYSTEM_PATH/system/user/list?page=1&size=10
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue
    @Method(name = "权限中心-用户列表", desc = "权限中心-用户列表，system/user/list")
    Map<String, Object> queryUser(@Parameter(name = "请求参数", required = false) RequestUserListDTO request);
    /**
     * 权限中心-用户列表绑定接口结果
     * @param request
     * @return
     */
    @ReturnValue(desc = "权限中心-用户列表绑定接口结果")
    @Method(desc = "权限中心-用户列表绑定", name = "权限中心-用户列表绑定 system/user/bind/platform")
    void bindPlatform(@Parameter(name = "request", required = true) UserBindPlatformDTO request);
    /**
     * 权限中心-用户列表解绑平台
     * @param request
     * @return
     */
    @ReturnValue(desc = "权限中心-用户列表解绑平台")
    @Method(desc = "权限中心-用户列表解绑平台", name = "权限中心-用户列表解绑平台 system/user/unbind/platform")
    void unbindPlatform(@Parameter(name = "request", required = true) UserUnbindPlatformDTO request);
    /**
     * 权限中心-用户列表创建用户
     * @param request
     * @return
     */
    @ReturnValue(desc = "权限中心-用户列表创建用户")
    @Method(desc = "权限中心-用户列表创建用户", name = "权限中心-用户列表创建用户 system/user/save")
    void save(@Parameter(name = "request", required = true) UserSaveDTO request);
    /**
     * 权限中心-上传头像
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-上传头像")
    @Method(desc = "权限中心-上传头像", name = "权限中心-上传头像 system/user/avatar/upload")
    Map<String, Object> upload(@Parameter(name = "request", required = true) MultipartFile request);
    /**
     * 权限中心-获取用户详情
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-获取用户详情")
    @Method(desc = "权限中心-获取用户详情", name = "权限中心-获取用户详情 system/user/detail")
    Map<String, Object> detail(@Parameter(name = "request", required = true) UserDetailDTO request);
    /**
     * 权限中心-用户列表创建用户
     * @param request
     * @return
     */
    @ReturnValue(desc = "权限中心-获取组织用户树")
    @Method(desc = "权限中心-获取用户详情", name = "权限中心-获取用户详情 system/user/groupUserTree")
    Map<String, Object> groupUserTree(@Parameter(name = "request", required = true) GroupUserTreeDTO request);
    /**
     * 权限中心-搜索用户列表
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-搜索用户列表")
    @Method(desc = "权限中心-搜索用户列表", name = "权限中心-搜索用户列表 system/user/searchUserList")
    Map<String, Object> searchUserList(@Parameter(name = "request", required = true) SearchUserListDTO request);
    /**
     * 权限中心-重置自定义用户密码
     * @param request
     * @return
     */
    @ReturnValue(desc = "权限中心-重置自定义用户密码")
    @Method(desc = "权限中心-重置自定义用户密码", name = "权限中心-重置自定义用户密码 system/user/custom/password/reset")
    void customUserPasswordReset(@Parameter(name = "request", required = true) CustomUserResetPasswordDTO request);
}
