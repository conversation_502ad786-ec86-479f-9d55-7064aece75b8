package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcResourceSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRoleResourceSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcResourceSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcRoleResourceSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 权限PBC-角色资源管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@RestController("PermissionPBCAthRoleResourceCfgBizFlowSystemPbcRuleSvcCON")
public class SystemPbcRoleResourceSvcController implements SystemPbcRoleResourceSvcI {

    @Autowired
    private SystemPbcRoleResourceSvc systemPbcRoleResourceSvc;

    /**
     * 权限中心-查询角色菜单树（返回所有树和选择节点） v2-/manage/admin/role/resource/Info
     *
     * @param request request
     * @return Response 权限中心-查询角色菜单树（返回所有树和选择节点）
     */
    @Override
    public Map<String, Object> searchResourceOfRoleInfo(SearchAdminResourceOfRoleFormClientDTO request) {
        return systemPbcRoleResourceSvc.searchResourceOfRoleInfo(request);
    }
}
