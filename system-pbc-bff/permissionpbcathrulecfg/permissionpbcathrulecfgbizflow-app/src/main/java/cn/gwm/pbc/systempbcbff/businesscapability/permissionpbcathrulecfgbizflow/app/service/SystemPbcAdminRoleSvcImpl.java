package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcAdminRoleSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.AdminRemoveUsersOfRoleForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.RoleBindGroupForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.SearchAdminUsersOfRoleForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteAdminRoleService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.AdminRemoveUsersOfRoleFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleBindGroupFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.SearchAdminUsersOfRoleFormClientDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
/**
 * <AUTHOR>
 */
@Component("systemPbcAdminRoleSvcImpl")
public class SystemPbcAdminRoleSvcImpl implements SystemPbcAdminRoleSvc {

    @Value("${gwm.sso.platform-code}")
    private String platformCode;

    @Autowired
    private RemoteAdminRoleService adminRoleService;

    @Override
    public Map<String, Object> roleBindGroup(RoleBindGroupFormClientDTO request) {
        RoleBindGroupForm remote = new RoleBindGroupForm();
        BeanUtil.copyProperties(request, remote);
        remote.setPlatformCode(platformCode);
        CommonRlt<JSONObject> jsonObjectCommonRlt = adminRoleService.roleBindGroup(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> searchUserAndGroupOfRole(SearchAdminUsersOfRoleFormClientDTO request) {
        SearchAdminUsersOfRoleForm remote = new SearchAdminUsersOfRoleForm();
        BeanUtil.copyProperties(request, remote);
        remote.setPlatformCode(platformCode);
        CommonRlt<JSONObject> jsonObjectCommonRlt = adminRoleService.searchUserAndGroupOfRole(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> removeUsersOfRole(AdminRemoveUsersOfRoleFormClientDTO request) {
        AdminRemoveUsersOfRoleForm remote = new AdminRemoveUsersOfRoleForm();
        BeanUtil.copyProperties(request, remote);
        remote.setPlatformCode(platformCode);
        CommonRlt<JSONObject> jsonObjectCommonRlt = adminRoleService.removeUsersOfRole(remote);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }
}
