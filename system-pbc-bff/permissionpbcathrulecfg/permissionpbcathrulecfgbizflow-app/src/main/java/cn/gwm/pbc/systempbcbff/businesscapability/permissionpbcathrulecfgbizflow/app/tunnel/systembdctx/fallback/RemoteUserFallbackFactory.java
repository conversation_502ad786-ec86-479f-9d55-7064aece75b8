package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.PlatformUserFeign;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 登录降级处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteUserFallbackFactory implements FallbackFactory<PlatformUserFeign> {

    @Override
    public PlatformUserFeign create(Throwable throwable) {
        return new PlatformUserFeign() {

            @Override
            public CommonRlt<JSONObject> groupUserTree(RemoteGroupUserTreeDTO groupUserTreeDTO) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> userPlatformList(RemoteUserPlatformListDTO userPlatformListDTO) {
                log.error("调用获取用户关联的应用列表异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> userAliasEdit(RemoteUserAliasEditDTO userAliasEditDTO) {
                log.error("调用修改用户别名异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> uploadAvatar(MultipartFile file) {
                return null;
            }

            @Override
            public CommonRlt<JSONArray> groupUserTreeInit() {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> searchUserList(RemoteSearchUserListDTO searchUserListDTO) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> platformUserUnbind(RemotePlatformUserUnbindDTO platformUserUnbindDTO) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> userPasswordEdit(RemoteUserPasswordEditDTO userPasswordEditDTO) {
                log.error("调用修改自定义用户密码异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> platformUserList(RemotePlatformUserListDTO platformUserListDTO) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> resetCustomUserPassword(RemoteCustomUserResetPasswordDTO customUserResetPasswordDTO) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> platformUserBind(RemotePlatformUserBindDTO platformUserBindDTO) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> list(SearchUserListForm searchUserListForm) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> userCreate(RemoteUserCreateDTO userCreateDTO) {
                log.error("调用创建用户异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> userBindRole(RemoteUserBindRoleDTO userBindRoleDTO) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> userModify(RemoteUserEditDTO userEditDTO) {
                return null;
            }

            @Override
            public CommonRlt<JSONObject> userList(RemoteUserListDTO userListDTO) {
                log.error("调用查询用户列表异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> userDetail(RemoteUserDetailDTO userDetailDTO) {
                log.error("调用查询用户详情异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONArray> userRoleList(RemoteUserRoleListDTO userRoleListDTO) {
                log.error("调用查询用户的角色列表异常：" + throwable.getMessage());
                return null;
            }
        };

    }
}
