package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field;

import com.alibaba.bizworks.core.specification.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleFieldListFormClientDTO", desc = "null")
@Data
@ApiModel("规则字段列表请求参数对象")
public class RuleFieldListFormClientDTO implements Serializable {

    /**
     * 当前页
     */
    @Field(name = "page", desc = "")
    @ApiModelProperty(value = "当前页")
    private Integer page;

    /**
     * 每页显示几条
     */
    @Field(name = "size", desc = "")
    @ApiModelProperty(value = "每页显示几条")
    private Integer size;

    /**
     * 类型（1人事字段，2自定义字段）
     */
    @Field(name = "类型（1人事字段，2自定义字段）", desc = "类型（1人事字段，2自定义字段）")
    @ApiModelProperty("类型（1人事字段，2自定义字段）")
    private Integer type;
}