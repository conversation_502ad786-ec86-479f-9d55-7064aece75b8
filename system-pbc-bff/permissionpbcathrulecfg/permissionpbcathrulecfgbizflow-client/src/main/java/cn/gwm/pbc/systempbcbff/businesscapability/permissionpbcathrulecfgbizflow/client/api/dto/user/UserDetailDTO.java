package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "UserDetailDTO", desc = "")
@Data
public class UserDetailDTO implements Serializable {

    /**
     * code
     */
    @Field(name = "code", desc = "")
    @Alias("user_code")
    private String code;

}
