package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRoleSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.role.RoleInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcRoleSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleDeleteDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleSaveDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.lang.Override;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限PBC-角色管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.15
 */
@RestController("PermissionPBCAthRuleCfgBizFlowSystemPbcRoleSvcCON")
public class SystemPbcRoleSvcController implements SystemPbcRoleSvcI {

    @Autowired
    private SystemPbcRoleSvc systemPbcRoleSvc;

    @Override
    public Response<Void> save(@RequestBody(required = true) RoleSaveDTO request) {
    	systemPbcRoleSvc.save(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<Void> delete(@RequestBody(required = true) RoleDeleteDTO request) {
    	systemPbcRoleSvc.delete(request);
		return Response.buildSuccess();
	}

    @Override
    public Response<RoleInfoItemVO> detail(@RequestBody(required = true) RoleDetailDTO request) {
    	RoleInfoItemVO roleInfoItemVOReturn = systemPbcRoleSvc.detail(request);
		return Response.buildSuccess(roleInfoItemVOReturn);
	}

    @Override
    public Response<Map<String, Object>> list(@RequestBody(required = true) RoleListDTO request) {
    	Map mapReturn = systemPbcRoleSvc.list(request);
		return Response.buildSuccess(mapReturn);
	}
}
