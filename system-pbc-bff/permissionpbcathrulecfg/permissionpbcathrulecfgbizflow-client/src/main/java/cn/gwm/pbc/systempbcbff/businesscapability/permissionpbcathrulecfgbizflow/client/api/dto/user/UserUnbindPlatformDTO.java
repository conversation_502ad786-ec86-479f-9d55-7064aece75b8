package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "UserUnbindPlatformDTO", desc = "")
@Data
public class UserUnbindPlatformDTO implements Serializable {

    /**
     * userIds
     */
    @Field(name = "userIds", desc = "")
    @Alias("user_ids")
    private String[] userIds;

}
