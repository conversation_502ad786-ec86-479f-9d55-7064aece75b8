package com.gwm.scaffold.tools.vo;

import com.gwm.scaffold.auth.domain.LoginUserInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 用户登返回对象信息
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class LoginRltVO implements Serializable {
    /**
     * 用户token
     */
    private String accessToken;

    /**
     * 失效时间
     */
    private Long expireTime;

    /**
     * 用户信息
     */
    private LoginUserInfo userInfo;

}