package com.gwm.scaffold.example.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 产品信息表更新请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "产品信息表更新请求")
public class ProductUpdateRequest {

    /**
     * 产品ID
     */
    @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "产品ID不能为空")
    @Min(value = 1, message = "产品ID必须大于0")
    private Long id;

    /**
     * 产品编码
     */
    @Schema(description = "产品编码")
    @Size(max = 50, message = "产品编码长度不能超过50")
    private String productCode;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    @Size(max = 200, message = "产品名称长度不能超过200")
    private String productName;

    /**
     * 产品英文名称
     */
    @Schema(description = "产品英文名称")
    @Size(max = 200, message = "产品英文名称长度不能超过200")
    private String productNameEn;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    @Min(value = 1, message = "分类ID必须大于0")
    private Long categoryId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    @Size(max = 100, message = "分类名称长度不能超过100")
    private String categoryName;

    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID")
    @Min(value = 1, message = "品牌ID必须大于0")
    private Long brandId;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    @Size(max = 100, message = "品牌名称长度不能超过100")
    private String brandName;

    /**
     * 产品类型 1:实物商品 2:虚拟商品 3:服务商品
     */
    @Schema(description = "产品类型 1:实物商品 2:虚拟商品 3:服务商品")
    @Min(value = 1, message = "产品类型 1:实物商品 2:虚拟商品 3:服务商品必须大于0")
    private Integer productType;

    /**
     * 计量单位
     */
    @Schema(description = "计量单位")
    @Size(max = 20, message = "计量单位长度不能超过20")
    private String unit;

    /**
     * 重量(kg)
     */
    @Schema(description = "重量(kg)")
    private BigDecimal weight;

    /**
     * 体积(立方米)
     */
    @Schema(description = "体积(立方米)")
    private BigDecimal volume;

    /**
     * 长度(cm)
     */
    @Schema(description = "长度(cm)")
    private BigDecimal length;

    /**
     * 宽度(cm)
     */
    @Schema(description = "宽度(cm)")
    private BigDecimal width;

    /**
     * 高度(cm)
     */
    @Schema(description = "高度(cm)")
    private BigDecimal height;

    /**
     * 成本价
     */
    @Schema(description = "成本价")
    private BigDecimal costPrice;

    /**
     * 销售价
     */
    @Schema(description = "销售价")
    private BigDecimal salePrice;

    /**
     * 市场价
     */
    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    /**
     * 最小库存
     */
    @Schema(description = "最小库存")
    @Min(value = 1, message = "最小库存必须大于0")
    private Integer minStock;

    /**
     * 最大库存
     */
    @Schema(description = "最大库存")
    @Min(value = 1, message = "最大库存必须大于0")
    private Integer maxStock;

    /**
     * 当前库存
     */
    @Schema(description = "当前库存")
    @Min(value = 1, message = "当前库存必须大于0")
    private Integer currentStock;

    /**
     * 销售数量
     */
    @Schema(description = "销售数量")
    @Min(value = 1, message = "销售数量必须大于0")
    private Integer salesCount;

    /**
     * 浏览次数
     */
    @Schema(description = "浏览次数")
    @Min(value = 1, message = "浏览次数必须大于0")
    private Integer viewCount;

    /**
     * 状态 1:上架 2:下架 3:停产
     */
    @Schema(description = "状态 1:上架 2:下架 3:停产")
    private Integer status;

    /**
     * 是否热销 0:否 1:是
     */
    @Schema(description = "是否热销 0:否 1:是")
    @Min(value = 1, message = "是否热销 0:否 1:是必须大于0")
    private Integer isHot;

    /**
     * 是否新品 0:否 1:是
     */
    @Schema(description = "是否新品 0:否 1:是")
    @Min(value = 1, message = "是否新品 0:否 1:是必须大于0")
    private Integer isNew;

    /**
     * 是否推荐 0:否 1:是
     */
    @Schema(description = "是否推荐 0:否 1:是")
    @Min(value = 1, message = "是否推荐 0:否 1:是必须大于0")
    private Integer isRecommend;

    /**
     * 排序号
     */
    @Schema(description = "排序号")
    @Min(value = 1, message = "排序号必须大于0")
    private Integer sortOrder;

    /**
     * 主图片URL
     */
    @Schema(description = "主图片URL")
    @Size(max = 500, message = "主图片URL长度不能超过500")
    private String mainImage;

    /**
     * 图片列表JSON
     */
    @Schema(description = "图片列表JSON")
    @Size(max = 1000000000, message = "图片列表JSON长度不能超过1000000000")
    private String imageList;

    /**
     * 视频URL
     */
    @Schema(description = "视频URL")
    @Size(max = 500, message = "视频URL长度不能超过500")
    private String videoUrl;

    /**
     * 产品描述
     */
    @Schema(description = "产品描述")
    @Size(max = 1000000000, message = "产品描述长度不能超过1000000000")
    private String description;

    /**
     * 规格参数JSON
     */
    @Schema(description = "规格参数JSON")
    @Size(max = 1000000000, message = "规格参数JSON长度不能超过1000000000")
    private String specification;

    /**
     * 产品特色
     */
    @Schema(description = "产品特色")
    @Size(max = 1000000000, message = "产品特色长度不能超过1000000000")
    private String features;

    /**
     * 售后服务信息
     */
    @Schema(description = "售后服务信息")
    @Size(max = 1000000000, message = "售后服务信息长度不能超过1000000000")
    private String serviceInfo;

    /**
     * 关键词
     */
    @Schema(description = "关键词")
    @Size(max = 500, message = "关键词长度不能超过500")
    private String keywords;

    /**
     * SEO标题
     */
    @Schema(description = "SEO标题")
    @Size(max = 200, message = "SEO标题长度不能超过200")
    private String metaTitle;

    /**
     * SEO描述
     */
    @Schema(description = "SEO描述")
    @Size(max = 500, message = "SEO描述长度不能超过500")
    private String metaDescription;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @Min(value = 1, message = "供应商ID必须大于0")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    @Size(max = 100, message = "供应商名称长度不能超过100")
    private String supplierName;

    /**
     * 保修期(月)
     */
    @Schema(description = "保修期(月)")
    @Min(value = 1, message = "保修期(月)必须大于0")
    private Integer warrantyPeriod;

    /**
     * 保质期(天)
     */
    @Schema(description = "保质期(天)")
    @Min(value = 1, message = "保质期(天)必须大于0")
    private Integer shelfLife;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    private Date productionDate;

    /**
     * 过期日期
     */
    @Schema(description = "过期日期")
    private Date expiryDate;

    /**
     * 条形码
     */
    @Schema(description = "条形码")
    @Size(max = 50, message = "条形码长度不能超过50")
    private String barcode;

    /**
     * 二维码
     */
    @Schema(description = "二维码")
    @Size(max = 500, message = "二维码长度不能超过500")
    private String qrCode;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500")
    private String remark;

    /**
     * 版本号
     */
    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    @Min(value = 0, message = "版本号不能为负数")
    private Integer version;

}
