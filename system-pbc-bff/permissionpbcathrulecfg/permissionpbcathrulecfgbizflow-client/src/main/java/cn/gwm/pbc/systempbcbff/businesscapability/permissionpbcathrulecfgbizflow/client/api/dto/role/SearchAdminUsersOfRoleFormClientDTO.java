package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "SearchAdminUsersOfRoleFormClientDTO", desc = "null")
@Data
@ApiModel("查询平台接入中角色下用户参数对象")
public class SearchAdminUsersOfRoleFormClientDTO implements Serializable {

	@Field(name = "platformCode", desc = "")
    @ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 角色ID
     */
    @Field(name = "id", desc = "")
    @ApiModelProperty("角色ID")
    private String id;

    /**
     * 工号
     */
    @Field(name = "userCode", desc = "")
    @ApiModelProperty("工号")
    @JsonProperty(value = "user_code")
    private String userCode;

    /**
     * 姓名
     */
    @Field(name = "userName", desc = "")
    @ApiModelProperty("姓名")
    @JsonProperty(value = "user_name")
    private String userName;

    /**
     * 查询参数（别名/账号）
     */
    @Field(name = "queryParam", desc = "")
    @ApiModelProperty("查询参数（别名/账号）")
    @JsonProperty(value = "query_param")
    private String queryParam;

    /**
     * 页码
     */
    @Field(name = "page", desc = "")
    @ApiModelProperty("页码")
    private Integer page ;

    /**
     * 条数
     */
    @Field(name = "size", desc = "")
    @ApiModelProperty("条数")
    private Integer size = 10;
}