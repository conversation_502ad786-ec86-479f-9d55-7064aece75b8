package com.gwm.scaffold.tools.util;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
/**
 * <AUTHOR> 王明莹
 * @Description 时间工具类
 * @Date
 **/
public class DateUtil {

    /**
     * 计算两个日期之间的天数（包含起止当天）
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 使用天数（包含起止当天）
     */
    public static int calculateUsageDays(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("日期不能为null");
        }
        if (startDate.isAfter(endDate)) {
            return 0; // 或抛出异常 IllegalArgumentException("开始日期不能晚于结束日期")
        }
        // 计算间隔天数（含头尾）
        return (int) (ChronoUnit.DAYS.between(startDate, endDate) + 1);
    }

    /**
     * LocalDate 转Date
     * @param localDate
     * @return
     */
    public static Date LocalDateToDate(LocalDate localDate){
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());

        // 步骤 2：转换为 Instant，再生成 Date
        Date date = Date.from(zonedDateTime.toInstant());
        return date;
    }

    /**
     * Date 转LocalDate
     * @param date
     * @return
     */
    public static LocalDate LocalDateToDate(Date date){
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate;
    }
}
