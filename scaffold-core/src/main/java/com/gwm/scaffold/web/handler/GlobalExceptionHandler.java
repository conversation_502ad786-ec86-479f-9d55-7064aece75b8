package com.gwm.scaffold.web.handler;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONPathException;
import com.gwm.scaffold.core.domain.Result;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.core.exception.StreamException;
import com.gwm.scaffold.core.exception.WarnException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.util.StringUtils;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.security.InvalidParameterException;
import java.sql.SQLException;
import java.util.Set;

import static org.springframework.http.HttpStatus.*;
import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * 全局异常处理器
 * 
 * 统一处理系统中的各种异常，返回友好的错误信息给前端
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理警告异常
     *
     * @param e 警告异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(WarnException.class)
    @ResponseStatus(FAILED_DEPENDENCY)
    public Result<?> handleWarnException(WarnException e) {
        log.warn("警告异常：", e);
        Integer code = e.getCode() != null ? e.getCode() : 424;
        String message = StringUtils.hasText(e.getMessage()) ? e.getMessage() : "警告异常";
        return Result.error(code, message);
    }

    /**
     * 处理流异常
     *
     * @param e 流异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(StreamException.class)
    public ResponseEntity<Result<Void>> handleStreamException(StreamException e) {
        log.error("流异常：", e);
        Integer code = e.getCode() != null ? e.getCode() : 415;
        String message = StringUtils.hasText(e.getMessage()) ? e.getMessage() : "流处理异常";
        return ResponseEntity.status(OK)
                .contentType(APPLICATION_JSON)
                .body(Result.error(code, message));
    }

    /**
     * 处理参数校验异常
     *
     * @param e 参数校验异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(InvalidParameterException.class)
    public Result<?> handleInvalidParameterException(InvalidParameterException e) {
        log.error("参数校验异常：", e);
        String message = StringUtils.hasText(e.getMessage()) ? e.getMessage() : "参数校验失败";
        return Result.error(400, message);
    }

    /**
     * 处理方法参数校验异常
     *
     * @param e 方法参数校验异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error("方法参数校验异常：", e);
        StringBuilder strBuilder = new StringBuilder("参数校验失败：");
        for (ObjectError objectError : e.getBindingResult().getAllErrors()) {
            strBuilder.append(objectError.getDefaultMessage()).append("；");
        }
        return Result.error(400, strBuilder.toString());
    }

    /**
     * 处理约束校验异常
     *
     * @param e 约束校验异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(ConstraintViolationException.class)
    public Result<?> handleConstraintViolationException(ConstraintViolationException e) {
        log.error("约束校验异常：", e);
        StringBuilder strBuilder = new StringBuilder("参数校验失败：");
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            strBuilder.append(violation.getMessage()).append("；");
        }
        return Result.error(400, strBuilder.toString());
    }

    /**
     * 处理数据完整性违反异常
     *
     * @param e 数据完整性违反异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(DataIntegrityViolationException.class)
    public Result<?> handleDataIntegrityViolationException(DataIntegrityViolationException e) {
        log.error("数据完整性违反异常：", e);
        String message = "数据库约束违反";
        if (e.getCause() != null && StringUtils.hasText(e.getCause().getMessage())) {
            message += "：" + e.getCause().getMessage();
        }
        return Result.error(500, message);
    }

    /**
     * 处理SQL语法错误异常
     *
     * @param e SQL语法错误异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(BadSqlGrammarException.class)
    public Result<?> handleBadSqlGrammarException(BadSqlGrammarException e) {
        log.error("SQL语法错误异常：", e);
        return Result.error(500, "数据库查询异常");
    }

    /**
     * 处理SQL异常
     *
     * @param e SQL异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(SQLException.class)
    public Result<?> handleSQLException(SQLException e) {
        log.error("SQL异常：", e);
        return Result.error(500, "数据库操作异常");
    }

    /**
     * 处理JSON解析异常
     *
     * @param e JSON解析异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler({JSONException.class, JSONPathException.class})
    public Result<?> handleJSONException(Exception e) {
        log.error("JSON解析异常：", e);
        return Result.error(500, "JSON数据解析异常");
    }

    /**
     * 处理服务异常
     *
     * @param e 服务异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(ServiceException.class)
    public Result<?> handleServiceException(ServiceException e) {
        log.error("服务异常：", e);
        String message = StringUtils.hasText(e.getMessage()) ? e.getMessage() : "服务异常";
        Integer code = e.getCode() != null ? e.getCode() : 500;
        return Result.error(code, message);
    }

    /**
     * 处理运行时异常
     *
     * @param e 运行时异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(RuntimeException.class)
    public Result<?> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常：", e);
        return Result.error(500, "系统运行异常，请联系管理员");
    }

    /**
     * 处理所有未捕获的异常
     *
     * @param e 未知异常
     * @return 错误响应
     */
    @ResponseBody
    @ExceptionHandler(Exception.class)
    @ResponseStatus(INTERNAL_SERVER_ERROR)
    public Result<?> handleAllException(Exception e) {
        log.error("未知异常：", e);
        return Result.error(500, "系统异常，请联系管理员");
    }
}
