package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.menu.MenuInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuDeleteDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuSaveDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;
import com.gwm.framework.core.exception.BusinessException;

import java.util.List;
import java.util.Map;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-应用菜单管理服务")
public interface SystemPbcMenuSvc {
    /**
     * 权限中心-创建/编辑菜单
     * @param request
     * @throws BusinessException
     */
    @ReturnValue(desc = "权限中心-创建/编辑菜单")
    @Method(desc = "权限中心-创建/编辑菜单", name = "权限中心-创建/编辑菜单 system/menu/save")
    void save(@Parameter(name = "request", required = true) MenuSaveDTO request) throws BusinessException;
    /**
     * 权限中心-删除菜单
     * @param request
     */
    @ReturnValue(desc = "权限中心-删除菜单")
    @Method(desc = "权限中心-删除菜单", name = "权限中心-删除菜单 system/menu/delete")
    void delete(@Parameter(name = "request", required = true) MenuDeleteDTO request);
    /**
     * 权限中心-获取菜单详情
     * @param request
     * @return   MenuInfoItemVO
     */
    @ReturnValue(desc = "权限中心-获取菜单详情")
    @Method(desc = "权限中心-获取菜单详情", name = "权限中心-获取菜单详情 system/menu/detail")
    MenuInfoItemVO detail(@Parameter(name = "request", required = true) MenuDetailDTO request);
    /**
     * 权限中心-获取当前登录用户菜单树
     * @return   Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-获取当前登录用户菜单树")
    @Method(desc = "权限中心-获取当前登录用户菜单树", name = "权限中心-获取当前登录用户菜单树 system/menu/tree/user")
    Map<String, Object> treeUser();
    /**
     * 权限中心-查询平台所有菜单树
     * @return   List<MenuInfoItemVO>
     */
    @ReturnValue(desc = "权限中心-查询平台所有菜单树")
    @Method(desc = "权限中心-查询平台所有菜单树", name = "权限中心-查询平台所有菜单树 system/menu/tree/all")
    List<MenuInfoItemVO> treeAll();
}
