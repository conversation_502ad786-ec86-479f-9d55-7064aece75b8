package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.resource;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("查询已接入平台角色菜单参数对象")
public class SearchAdminResourceOfRoleForm implements Serializable {

	@ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 角色ID
     */
    @ApiModelProperty("角色ID")
    private String id;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    @JsonProperty(value = "resource_name")
    private String resourceName;

    /**
     * 菜单编号
     */
    @ApiModelProperty("菜单编号")
    @JsonProperty(value = "resource_code")
    private String resourceCode;

    /**
     * 类别（0-菜单管理，1-数据管理）
     */
    @ApiModelProperty("类别（0-菜单管理，1-数据管理）")
    @JsonProperty(value = "category")
    private Integer category;

}