<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwm.scaffold.example.mapper.SeriesMapper">

    <!-- 结果映射 -->
    <resultMap id="SeriesResultMap" type="com.gwm.scaffold.example.entity.Series">
        <id column="ID" property="id"/>
        <result column="NAME" property="name"/>
        <result column="ENGLISH_NAME" property="englishName"/>
        <result column="BRAND_ID" property="brandId"/>
        <result column="TYPE" property="type"/>
        <result column="LEVEL" property="level"/>
        <result column="LAUNCH_TIME" property="launchTime"/>
        <result column="DISCONTINUE_TIME" property="discontinueTime"/>
        <result column="MIN_PRICE" property="minPrice"/>
        <result column="MAX_PRICE" property="maxPrice"/>
        <result column="IMAGE_URL" property="imageUrl"/>
        <result column="DESCRIPTION" property="description"/>
        <result column="STATUS" property="status"/>
        <result column="SORT_WEIGHT" property="sortWeight"/>
        <result column="DELETED" property="deleted"/>
        <result column="DELETED_TIME" property="deletedTime"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_USER_CODE" property="createUserCode"/>
        <result column="CREATE_USER_NAME" property="createUserName"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="UPDATE_USER_CODE" property="updateUserCode"/>
        <result column="UPDATE_USER_NAME" property="updateUserName"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        ID, NAME, ENGLISH_NAME, BRAND_ID, TYPE, LEVEL, LAUNCH_TIME, DISCONTINUE_TIME, MIN_PRICE, MAX_PRICE, IMAGE_URL, DESCRIPTION, STATUS, SORT_WEIGHT, DELETED, DELETED_TIME, CREATE_TIME, CREATE_USER_CODE, CREATE_USER_NAME, UPDATE_TIME, UPDATE_USER_CODE, UPDATE_USER_NAME
    </sql>

    <!-- 分页查询列表 -->
    <select id="selectSeriesPage" resultMap="SeriesResultMap">
        SELECT
        <include refid="BaseColumns"/>
        FROM T_SERIES
        <where>
            deleted = 0
            <if test="request.id != null">
                AND ID = #{request.id}
            </if>
            <if test="request.name != null and request.name != ''">
                AND NAME LIKE CONCAT('%', #{request.name}, '%')
            </if>
            <if test="request.englishName != null and request.englishName != ''">
                AND ENGLISH_NAME LIKE CONCAT('%', #{request.englishName}, '%')
            </if>
            <if test="request.brandId != null">
                AND BRAND_ID = #{request.brandId}
            </if>
            <if test="request.type != null and request.type != ''">
                AND TYPE LIKE CONCAT('%', #{request.type}, '%')
            </if>
            <if test="request.level != null and request.level != ''">
                AND LEVEL LIKE CONCAT('%', #{request.level}, '%')
            </if>
            <if test="request.launchTimeStart != null">
                AND LAUNCH_TIME >= #{request.launchTimeStart}
            </if>
            <if test="request.launchTimeEnd != null">
                AND LAUNCH_TIME &lt;= #{request.launchTimeEnd}
            </if>
            <if test="request.discontinueTimeStart != null">
                AND DISCONTINUE_TIME >= #{request.discontinueTimeStart}
            </if>
            <if test="request.discontinueTimeEnd != null">
                AND DISCONTINUE_TIME &lt;= #{request.discontinueTimeEnd}
            </if>
            <if test="request.minPrice != null">
                AND MIN_PRICE = #{request.minPrice}
            </if>
            <if test="request.maxPrice != null">
                AND MAX_PRICE = #{request.maxPrice}
            </if>
            <if test="request.imageUrl != null and request.imageUrl != ''">
                AND IMAGE_URL LIKE CONCAT('%', #{request.imageUrl}, '%')
            </if>
            <if test="request.description != null and request.description != ''">
                AND DESCRIPTION LIKE CONCAT('%', #{request.description}, '%')
            </if>
            <if test="request.status != null and request.status != ''">
                AND STATUS LIKE CONCAT('%', #{request.status}, '%')
            </if>
            <if test="request.sortWeight != null">
                AND SORT_WEIGHT = #{request.sortWeight}
            </if>
            <if test="request.deletedTimeStart != null">
                AND DELETED_TIME >= #{request.deletedTimeStart}
            </if>
            <if test="request.deletedTimeEnd != null">
                AND DELETED_TIME &lt;= #{request.deletedTimeEnd}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>



</mapper>
