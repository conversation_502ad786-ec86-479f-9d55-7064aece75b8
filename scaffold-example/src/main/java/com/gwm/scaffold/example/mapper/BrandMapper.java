package com.gwm.scaffold.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwm.scaffold.example.entity.Brand;
import com.gwm.scaffold.example.dto.BrandQueryRequest;
import com.gwm.scaffold.example.vo.BrandVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 品牌Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface BrandMapper extends BaseMapper<Brand> {

    /**
     * 分页查询品牌列表
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 分页数据
     */
    IPage<Brand> selectBrandPage(Page<Brand> page, @Param("request") BrandQueryRequest request);

    /**
     * 根据品牌名称模糊查询
     *
     * @param brandName 品牌名称关键词
     * @return 品牌列表
     */
    List<Brand> selectByBrandNameLike(@Param("brandName") String brandName);

    /**
     * 根据国家查询品牌
     *
     * @param country 国家名称
     * @return 品牌列表
     */
    List<Brand> selectByCountry(@Param("country") String country);

    /**
     * 统计各国家的品牌数量
     *
     * @return 统计结果
     */
    List<BrandVO> countByCountry();

    /**
     * 检查品牌名称是否存在（排除指定ID）
     *
     * @param name 品牌名称
     * @param excludeId 排除的品牌ID
     * @return 数量
     */
    int checkBrandNameExists(@Param("name") String name, @Param("excludeId") Long excludeId);

}
