package com.gwm.scaffold.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.monitor.annotation.Monitor;
import com.gwm.scaffold.example.entity.Product;
import com.gwm.scaffold.example.mapper.ProductMapper;
import com.gwm.scaffold.example.service.ProductService;
import com.gwm.scaffold.example.dto.ProductCreateRequest;
import com.gwm.scaffold.example.dto.ProductUpdateRequest;
import com.gwm.scaffold.example.dto.ProductQueryRequest;
import com.gwm.scaffold.example.vo.ProductVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 产品信息表服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Override
    @Monitor(value = "产品信息表分页查询", threshold = 1000)
    public IPage<ProductVO> getProductPage(ProductQueryRequest request) {
        Page<Product> page = new Page<>(request.getCurrent(), request.getSize());
        IPage<Product> entityPage = baseMapper.selectProductPage(page, request);

        // 转换为VO
        Page<ProductVO> voPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<ProductVO> voList = entityPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    @Monitor(value = "获取产品信息表详情")
    public ProductVO getProductById(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("产品信息表ID不能为空");
        }

        Product product = getById(id);
        if (product == null) {
            throw new ServiceException("产品信息表不存在");
        }

        return convertToVO(product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "创建产品信息表")
    public ProductVO createProduct(ProductCreateRequest request) {
        if (request == null) {
            throw new ServiceException("创建请求不能为空");
        }

        // 创建产品信息表
        Product product = new Product();
        BeanUtils.copyProperties(request, product);

        boolean success = save(product);
        if (!success) {
            throw new ServiceException("创建产品信息表失败");
        }

        log.info("创建产品信息表成功，ID：{}", product.getId());
        return getProductById(product.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新产品信息表")
    public ProductVO updateProduct(ProductUpdateRequest request) {
        if (request == null) {
            throw new ServiceException("更新请求不能为空");
        }
        if (request.getId() == null || request.getId() <= 0) {
            throw new ServiceException("产品信息表ID不能为空");
        }

        // 检查产品信息表是否存在
        Product existingProduct = getById(request.getId());
        if (existingProduct == null) {
            throw new ServiceException("产品信息表不存在");
        }

        // 更新产品信息表
        BeanUtils.copyProperties(request, existingProduct);

        boolean success = updateById(existingProduct);
        if (!success) {
            throw new ServiceException("更新产品信息表失败");
        }

        log.info("更新产品信息表成功，ID：{}", request.getId());
        return getProductById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "删除产品信息表")
    public boolean deleteProduct(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("产品信息表ID不能为空");
        }

        Product product = getById(id);
        if (product == null) {
            throw new ServiceException("产品信息表不存在");
        }

        boolean success = removeById(id);
        if (success) {
            log.info("删除产品信息表成功，ID：{}", id);
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "批量删除产品信息表")
    public int batchDeleteProducts(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("产品信息表ID列表不能为空");
        }

        boolean success = removeByIds(ids);
        int count = success ? ids.size() : 0;

        log.info("批量删除产品信息表完成，删除数量：{}", count);
        return count;
    }

    /**
     * 将实体转换为VO
     */
    private ProductVO convertToVO(Product entity) {
        if (entity == null) {
            return null;
        }

        ProductVO vo = new ProductVO();
        BeanUtils.copyProperties(entity, vo);

        // 可以在这里添加额外的转换逻辑，例如状态名称等
        if (entity.getStatus() != null) {
            switch (entity.getStatus()) {
                case 1:
                    vo.setStatusName("正常");
                    break;
                case 2:
                    vo.setStatusName("禁用");
                    break;
                default:
                    vo.setStatusName("未知");
            }
        }

        return vo;
    }
}
