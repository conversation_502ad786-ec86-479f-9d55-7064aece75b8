package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user;



import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MultiLangData {

    /**
     * 中文
     */
    private static Map<String, String> CN = new HashMap<>();
    /**
     * 英文
     */
    private static Map<String, String> EN = new HashMap<>();
    /**
     * 泰文
     */
    private static Map<String, String> TH = new HashMap<>();
    /**
     * 俄文
     */
    private static Map<String, String> RU = new HashMap<>();

    public static String getContent(String lang, String code) {
        String rlt = getMap(lang).get(code);
        return rlt == null ? "" : rlt;
    }

    public static void put(String key, String cn, String en, String th, String ru) {
        CN.put(key, cn);
        EN.put(key, en);
        TH.put(key, th);
        RU.put(key, ru);
    }

    public static boolean checkKeyExist(String key) {
        return CN.containsKey(key);
    }

    private static Map<String, String> getMap(String lang) {
    	lang = "zh";
        if (UniversalConstant.CN_STR.equalsIgnoreCase(lang)) {
            return CN;
        } else if (UniversalConstant.EN_STR.equalsIgnoreCase(lang)) {
            return EN;
        } else if (UniversalConstant.TH_STR.equalsIgnoreCase(lang)) {
            return TH;
        } else if (UniversalConstant.RU_STR.equalsIgnoreCase(lang)) {
            return RU;
        } else {
            return CN;
        }
    }

}
