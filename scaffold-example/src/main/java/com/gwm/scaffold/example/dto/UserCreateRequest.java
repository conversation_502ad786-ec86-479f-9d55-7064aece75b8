package com.gwm.scaffold.example.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 用户创建请求DTO
 * 
 * 展示如何使用参数校验注解和API文档注解
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@ApiModel(description = "用户创建请求")
public class UserCreateRequest {

    @ApiModelProperty(value = "用户名", required = true, example = "zhangsan")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    @ApiModelProperty(value = "密码", required = true, example = "123456")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20之间")
    private String password;

    @ApiModelProperty(value = "真实姓名", required = true, example = "张三")
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 50, message = "真实姓名长度不能超过50")
    private String realName;

    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100")
    private String email;

    @ApiModelProperty(value = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    @ApiModelProperty(value = "性别", example = "1", notes = "1:男 2:女")
    @Min(value = 1, message = "性别值不正确")
    @Max(value = 2, message = "性别值不正确")
    private Integer gender;

    @ApiModelProperty(value = "年龄", example = "25")
    @Min(value = 1, message = "年龄必须大于0")
    @Max(value = 150, message = "年龄不能超过150")
    private Integer age;

    @ApiModelProperty(value = "部门ID", example = "1")
    @NotNull(message = "部门ID不能为空")
    @Min(value = 1, message = "部门ID必须大于0")
    private Long departmentId;

    @ApiModelProperty(value = "部门名称", example = "技术部")
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 100, message = "部门名称长度不能超过100")
    private String departmentName;

    @ApiModelProperty(value = "职位", example = "Java开发工程师")
    @Size(max = 100, message = "职位长度不能超过100")
    private String position;

    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    @Size(max = 500, message = "头像URL长度不能超过500")
    private String avatar;

    @ApiModelProperty(value = "备注", example = "这是一个测试用户")
    @Size(max = 500, message = "备注长度不能超过500")
    private String remark;
}
