package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.StructureObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TestDTO
 *
 * <AUTHOR> 管理员
 * @date 2023.12.13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@StructureObject(name = "TestDTO")
public class TestDTO {

    /**
     * id
     */
    @Field(name = "id")
    private String id;
}
