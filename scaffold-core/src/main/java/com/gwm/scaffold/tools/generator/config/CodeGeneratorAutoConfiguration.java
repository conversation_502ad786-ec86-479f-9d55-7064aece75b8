package com.gwm.scaffold.tools.generator.config;

import com.gwm.scaffold.tools.generator.CodeGenerator;
import com.gwm.scaffold.tools.generator.util.DatabaseMetaUtil;
import com.gwm.scaffold.tools.generator.util.TemplateUtil;
import com.gwm.scaffold.tools.generator.web.CodeGeneratorController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * 代码生成器自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
@Configuration
@ConditionalOnClass({DataSource.class})
@ConditionalOnProperty(name = "scaffold.generator.enabled", havingValue = "true", matchIfMissing = true)
public class CodeGeneratorAutoConfiguration {

    public CodeGeneratorAutoConfiguration() {
        log.debug("GWM Scaffold 代码生成器已启用");
    }

    @Bean
    @ConditionalOnMissingBean
    public TemplateUtil templateUtil() {
        return new TemplateUtil();
    }

    @Bean
    @ConditionalOnMissingBean
    public DatabaseMetaUtil databaseMetaUtil() {
        return new DatabaseMetaUtil();
    }

    @Bean
    @ConditionalOnMissingBean
    public CodeGenerator codeGenerator() {
        return new CodeGenerator();
    }

    /**
     * Web控制器配置
     */
    @Configuration
    @ConditionalOnWebApplication
    @ConditionalOnProperty(name = "scaffold.generator.web.enabled", havingValue = "true", matchIfMissing = true)
    static class CodeGeneratorWebConfiguration {

        @Bean
        @ConditionalOnMissingBean
        public CodeGeneratorController codeGeneratorController() {
            log.debug("GWM Scaffold 代码生成器Web界面已启用");
            return new CodeGeneratorController();
        }
    }
}
