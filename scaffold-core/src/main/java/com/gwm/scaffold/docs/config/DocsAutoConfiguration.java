package com.gwm.scaffold.docs.config;

import com.gwm.scaffold.docs.controller.DocsController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * 文档模块自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.docs.enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(DocsProperties.class)
@Import({
    OpenApiConfig.class,
    DocsController.class
})
public class DocsAutoConfiguration {

    public DocsAutoConfiguration() {
        log.info("GWM Scaffold 文档模块已启用");
        log.info("OpenAPI UI 访问地址: /swagger-ui.html");
        log.info("OpenAPI JSON 访问地址: /v3/api-docs");
        log.info("文档首页访问地址: /scaffold/docs");
    }
}
