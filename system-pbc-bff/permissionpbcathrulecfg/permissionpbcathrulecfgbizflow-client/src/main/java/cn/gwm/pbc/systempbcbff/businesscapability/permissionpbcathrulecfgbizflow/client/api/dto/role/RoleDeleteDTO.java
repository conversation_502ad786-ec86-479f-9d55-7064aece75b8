package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role;

import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleDeleteDTO", desc = "")
@Data
public class RoleDeleteDTO implements Serializable {

    /**
     * ids
     */
    @Field(name = "ids", desc = "")
    private String[] ids;

}
