# 品牌管理API Postman测试指南

## 概述

本目录包含品牌管理模块的完整Postman测试集合，提供了全面的API测试用例，包括正常流程测试、异常场景测试、数据校验测试等。

## 文件说明

### 1. 测试集合文件
- **Brand-Management-API.postman_collection.json**: 主要的测试集合文件
- **Brand-Management-Environment.postman_environment.json**: 环境配置文件

### 2. 测试覆盖范围

#### 品牌查询接口测试
- ✅ 分页查询品牌列表（支持多条件筛选）
- ✅ 根据ID查询品牌详情
- ✅ 获取启用状态品牌列表
- ✅ 按国别统计品牌数量

#### 品牌创建接口测试
- ✅ 创建品牌-完整信息（包含所有字段）
- ✅ 创建品牌-最小信息（仅必填字段）
- ✅ 创建品牌-重复名称校验（应失败）

#### 品牌更新接口测试
- ✅ 更新品牌信息（部分字段更新）
- ✅ 更新品牌状态（启用/禁用切换）

#### 品牌删除接口测试
- ✅ 删除单个品牌（软删除）
- ✅ 批量删除品牌

#### 数据校验测试
- ✅ 缺少必填字段校验
- ✅ 无效年份校验
- ✅ URL格式校验
- ✅ 状态值校验

## 导入和使用步骤

### 步骤1：导入测试集合

1. 打开Postman应用
2. 点击左上角的"Import"按钮
3. 选择"File"选项卡
4. 上传`Brand-Management-API.postman_collection.json`文件
5. 点击"Import"完成导入

### 步骤2：导入环境配置

1. 在Postman中点击右上角的齿轮图标（Manage Environments）
2. 点击"Import"按钮
3. 上传`Brand-Management-Environment.postman_environment.json`文件
4. 选择导入的环境"品牌管理API环境"

### 步骤3：配置环境变量

在环境配置中设置以下变量：

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| baseUrl | API服务器地址 | http://localhost:8080 |
| token | 认证令牌（如需要） | Bearer xxx |
| userId | 用户ID | admin |
| userName | 用户名称 | 管理员 |

### 步骤4：运行测试

#### 单个测试运行
1. 选择要运行的测试用例
2. 点击"Send"按钮
3. 查看响应结果和测试断言

#### 批量测试运行
1. 右键点击测试集合或文件夹
2. 选择"Run collection"或"Run folder"
3. 在Collection Runner中配置运行参数
4. 点击"Run"开始批量测试

## 测试用例详解

### 1. 基础CRUD测试流程

```
创建品牌 → 查询品牌 → 更新品牌 → 删除品牌
```

**推荐执行顺序：**
1. 先运行"品牌创建接口"中的"创建品牌-完整信息"
2. 使用返回的brandId运行查询和更新测试
3. 最后运行删除测试

### 2. 数据校验测试

这些测试用例专门验证API的数据校验功能：

- **缺少必填字段**: 验证name和country字段的必填校验
- **无效年份**: 验证foundedYear字段的范围校验
- **URL格式**: 验证website字段的URL格式校验
- **重复名称**: 验证品牌名称的唯一性约束

### 3. 状态管理测试

测试品牌状态的管理功能：

- **状态更新**: 测试ACTIVE和INACTIVE状态切换
- **启用品牌查询**: 验证只返回启用状态的品牌

## 自动化测试脚本

每个测试用例都包含了自动化测试脚本，主要功能：

### Pre-request Scripts（前置脚本）
- 设置默认环境变量
- 生成测试数据
- 设置认证信息

### Test Scripts（测试脚本）
- 验证HTTP状态码
- 验证响应数据结构
- 验证业务逻辑正确性
- 提取和保存关键数据到环境变量

### 示例测试脚本

```javascript
// 验证响应状态码
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

// 验证响应结构
pm.test("Response has correct structure", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('code');
    pm.expect(jsonData).to.have.property('data');
    pm.expect(jsonData.data).to.have.property('id');
});

// 保存品牌ID到环境变量
if (pm.response.code === 200) {
    var jsonData = pm.response.json();
    if (jsonData.data && jsonData.data.id) {
        pm.environment.set("brandId", jsonData.data.id);
    }
}
```

## 常见问题解决

### 1. 连接失败
- 检查baseUrl是否正确
- 确认服务器是否启动
- 检查网络连接

### 2. 认证失败
- 检查token是否有效
- 确认用户权限是否足够
- 检查认证头设置

### 3. 测试失败
- 查看Console输出的错误信息
- 检查测试数据是否符合要求
- 确认API接口是否正常工作

### 4. 环境变量问题
- 确认环境已正确选择
- 检查变量名是否正确
- 验证变量值是否有效

## 扩展测试

### 添加新的测试用例

1. 右键点击相应的文件夹
2. 选择"Add Request"
3. 配置请求参数
4. 添加测试脚本
5. 保存并运行测试

### 性能测试

可以使用Postman的Collection Runner进行简单的性能测试：

1. 设置较大的迭代次数
2. 配置延迟时间
3. 监控响应时间
4. 分析测试报告

## 测试报告

运行完成后，Postman会生成详细的测试报告，包括：

- 测试通过率
- 响应时间统计
- 失败用例详情
- 环境变量使用情况

建议定期运行完整的测试套件，确保API功能的稳定性和正确性。
