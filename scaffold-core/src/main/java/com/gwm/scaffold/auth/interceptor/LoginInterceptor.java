package com.gwm.scaffold.auth.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gwm.scaffold.auth.domain.LoginResult;
import com.gwm.scaffold.auth.service.AuthenticateService;
import com.gwm.scaffold.auth.util.LoginUserUtil;
import com.gwm.scaffold.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.springframework.http.MediaType.*;

/**
 * 登录拦截器
 * 
 * 拦截请求进行用户身份认证和权限校验
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Component
public class LoginInterceptor implements HandlerInterceptor {

    private static final String ACCESS_TOKEN = "accessToken";
    private static final String PBC_TOKEN = "pbc_token";
    private static final String X_REAL_IP = "X-Real-IP";
    
    private static final String JSON_ARRAY_START = "[";
    private static final String JSON_ARRAY_END = "]";
    private static final String JSON_OBJECT_START = "{";
    private static final String JSON_OBJECT_END = "}";
    
    private static final int RESPONSE_CODE_UNAUTHORIZED = 401;

    @Autowired
    private AuthenticateService authenticateService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        String token = getTokenFromRequest(request);

        // 检查token是否为空
        if (!StringUtils.hasText(token) || "null".equals(token)) {
            handleUnauthorizedException(request.getRequestURL().toString(), response,
                    new ServiceException("accessToken不能为空，请检查登录状态是否过期！"));
            log.warn("访问被拒绝，缺少accessToken，请求地址：{}", request.getRequestURL());
            return false;
        }

        try {
            LoginResult loginResult = authenticateService.checkToken(token);
            if (loginResult != null && loginResult.isSuccess() && loginResult.getUserInfo() != null) {
                // 设置用户信息到ThreadLocal
                loginResult.getUserInfo()
                        .setToken(token)
                        .setIp(getRealIp(request))
                        .setUrl(request.getRequestURL().toString());
                LoginUserUtil.setUser(loginResult.getUserInfo());

                // 记录访问日志
                logAccessInfo(request);
                return true;
            } else {
                String errorMsg = loginResult != null ? loginResult.getErrorMessage() : "Token验证失败";
                handleUnauthorizedException(request.getRequestURL().toString(), response,
                        new ServiceException(errorMsg));
                log.warn("Token验证失败，请求地址：{}，token:{}，错误信息：{}",
                        request.getRequestURL(), token, errorMsg);
                return false;
            }
        } catch (Exception e) {
            handleUnauthorizedException(request.getRequestURL().toString(), response, e);
            log.error("登录异常，请求地址：{}，token:{}", request.getRequestURL(), token, e);
            return false;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理ThreadLocal，防止内存泄漏
        LoginUserUtil.clear();
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String token = request.getHeader(PBC_TOKEN);
        if (!StringUtils.hasText(token)) {
            token = request.getHeader(ACCESS_TOKEN);
        }
        if (!StringUtils.hasText(token)) {
            token = request.getParameter(ACCESS_TOKEN);
        }
        return token;
    }

    /**
     * 记录访问信息
     */
    private void logAccessInfo(HttpServletRequest request) throws Exception {
        // 解析URL参数
        String parameter = null;
        if (!CollectionUtils.isEmpty(request.getParameterMap())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.putAll(request.getParameterMap());
            parameter = jsonObject.toJSONString();
        }

        // 检查是否为JSON请求
        if (!isJsonRequest(request)) {
            log.info("token：{}，用户：{}，用户IP：{}，服务器IP：{}，请求uri：{} {}，url参数：{}", 
                    LoginUserUtil.getToken(), LoginUserUtil.getUserNameCode(), LoginUserUtil.getIp(), 
                    request.getLocalAddr(), request.getMethod(), request.getRequestURI(), parameter);
            return;
        }

        // 解析请求体参数
        String body = getRequestBody(request);
        String jsonPayload = parseJsonPayload(body);
        
        log.info("token：{}，用户：{}，用户IP：{}，服务器IP：{}，请求uri：{} {}，url参数：{}，body参数：{}", 
                LoginUserUtil.getToken(), LoginUserUtil.getUserNameCode(), LoginUserUtil.getIp(), 
                request.getLocalAddr(), request.getMethod(), request.getRequestURI(), parameter, jsonPayload);
    }

    /**
     * 判断是否为JSON请求
     */
    private boolean isJsonRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        if (!StringUtils.hasText(contentType)) {
            return false;
        }
        MediaType mediaType = MediaType.parseMediaType(contentType);
        return APPLICATION_JSON.isCompatibleWith(mediaType);
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        // 这里需要使用RequestWrapper来获取请求体
        // 由于原始代码中使用了RequestWrapper，这里简化处理
        try {
            return request.getReader().lines().reduce("", (accumulator, actual) -> accumulator + actual);
        } catch (Exception e) {
            log.warn("获取请求体失败", e);
            return "";
        }
    }

    /**
     * 解析JSON载荷
     */
    private String parseJsonPayload(String body) {
        if (!StringUtils.hasText(body)) {
            return null;
        }

        try {
            if (body.startsWith(JSON_OBJECT_START) && body.endsWith(JSON_OBJECT_END)) {
                return objectMapper.readValue(body, JSONObject.class).toJSONString();
            } else if (body.startsWith(JSON_ARRAY_START) && body.endsWith(JSON_ARRAY_END)) {
                return objectMapper.readValue(body, JSONArray.class).toJSONString();
            } else {
                return body;
            }
        } catch (Exception e) {
            log.warn("解析JSON载荷失败", e);
            return body;
        }
    }

    /**
     * 获取真实IP地址
     */
    private String getRealIp(HttpServletRequest request) {
        String ip = request.getHeader(X_REAL_IP);
        return ip != null ? ip : request.getRemoteAddr();
    }

    /**
     * 处理未授权异常
     */
    private void handleUnauthorizedException(String requestUrl, HttpServletResponse response, Exception e) throws IOException {
        response.setStatus(RESPONSE_CODE_UNAUTHORIZED);
        response.setContentType(APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(UTF_8.toString());

        Map<String, Object> errorResponse = new ConcurrentHashMap<>(8);
        errorResponse.put("timestamp", new Date());
        errorResponse.put("status", RESPONSE_CODE_UNAUTHORIZED);
        errorResponse.put("error", "Unauthorized");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("path", requestUrl);

        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
    }
}
