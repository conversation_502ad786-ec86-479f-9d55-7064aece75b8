package com.gwm.scaffold.example.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwm.scaffold.auth.annotation.RequirePermission;
import com.gwm.scaffold.core.domain.Result;
import com.gwm.scaffold.example.dto.OrderCreateRequest;
import com.gwm.scaffold.example.dto.OrderQueryRequest;
import com.gwm.scaffold.example.dto.OrderUpdateRequest;
import com.gwm.scaffold.example.service.OrderService;
import com.gwm.scaffold.example.vo.OrderVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.gwm.scaffold.web.util.ValidationUtil;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 订单管理控制器
 * 
 * 展示如何使用Scaffold的各种功能：
 * 1. 统一返回结果 Result
 * 2. 全局异常处理
 * 3. 权限控制注解
 * 4. API文档注解
 * 5. 参数校验
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Tag(name = "订单管理", description = "订单相关接口")
@RestController
@RequestMapping("/api/orders")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 分页查询订单列表
     */
    @Operation(summary = "分页查询订单列表", description = "支持按订单号、客户信息、商品名称等条件查询")
    @GetMapping
    @RequirePermission("order:list")
    public Result<IPage<OrderVO>> list(OrderQueryRequest request) {
        log.info("分页查询订单列表，参数：{}", request);
        
        // 参数校验
//        ValidationUtil.validatePageParams(request.getCurrent(), request.getSize());
        
        IPage<OrderVO> result = orderService.getOrderPage(request);
        return Result.success(result);
    }

    /**
     * 根据ID获取订单详情
     */
    @Operation(summary = "获取订单详情", description = "根据订单ID获取详细信息")
    @GetMapping("/{id}")
    @RequirePermission("order:read")
    public Result<OrderVO> getById(@Parameter(description = "订单ID") @PathVariable Long id) {
        log.info("获取订单详情，ID：{}", id);
        
//        ValidationUtil.validateNotNull(id, "订单ID");
        
        OrderVO result = orderService.getOrderById(id);
        return Result.success(result);
    }

    /**
     * 创建订单
     */
    @Operation(summary = "创建订单", description = "创建新的订单")
    @PostMapping
    @RequirePermission("order:create")
    public Result<OrderVO> create(@Valid @RequestBody OrderCreateRequest request) {
        log.info("创建订单，参数：{}", request);
        
        OrderVO result = orderService.createOrder(request);
        return Result.success(result);
    }

    /**
     * 更新订单
     */
    @Operation(summary = "更新订单", description = "更新订单信息")
    @PutMapping("/{id}")
    @RequirePermission("order:update")
    public Result<OrderVO> update(
            @Parameter(description = "订单ID") @PathVariable Long id,
            @Valid @RequestBody OrderUpdateRequest request) {
        log.info("更新订单，ID：{}，参数：{}", id, request);
        
//        ValidationUtil.validateNotNull(id, "订单ID");
        request.setId(id);
        
        OrderVO result = orderService.updateOrder(request);
        return Result.success(result);
    }

    /**
     * 删除订单
     */
    @Operation(summary = "删除订单", description = "根据ID删除订单")
    @DeleteMapping("/{id}")
    @RequirePermission("order:delete")
    public Result<Void> delete(@Parameter(description = "订单ID") @PathVariable Long id) {
        log.info("删除订单，ID：{}", id);
        
//        ValidationUtil.validateNotNull(id, "订单ID");
        
        boolean success = orderService.deleteOrder(id);
        return success ? Result.success() : Result.error("删除失败");
    }

    /**
     * 批量删除订单
     */
    @Operation(summary = "批量删除订单", description = "根据ID列表批量删除订单")
    @DeleteMapping("/batch")
    @RequirePermission("order:delete")
    public Result<String> batchDelete(@RequestBody List<Long> ids) {
        log.info("批量删除订单，IDs：{}", ids);
        
//        ValidationUtil.validateNotEmpty(ids, "订单ID列表");
        
        int count = orderService.batchDeleteOrders(ids);
        return Result.success("成功删除 " + count + " 个订单");
    }

    /**
     * 更新订单状态
     */
    @Operation(summary = "更新订单状态", description = "更新订单的状态")
    @PutMapping("/{id}/status")
    @RequirePermission("order:update")
    public Result<Void> updateStatus(
            @Parameter(description = "订单ID") @PathVariable Long id,
            @Parameter(description = "订单状态") @RequestParam Integer status) {
        log.info("更新订单状态，ID：{}，状态：{}", id, status);
        
//        ValidationUtil.validateNotNull(id, "订单ID");
//        ValidationUtil.validateNotNull(status, "订单状态");
        
        boolean success = orderService.updateOrderStatus(id, status);
        return success ? Result.success() : Result.error("更新失败");
    }

    /**
     * 批量更新订单状态
     */
    @Operation(summary = "批量更新订单状态", description = "批量更新订单状态")
    @PutMapping("/batch/status")
    @RequirePermission("order:update")
    public Result<String> batchUpdateStatus(
            @RequestBody List<Long> ids,
            @Parameter(description = "订单状态") @RequestParam Integer status) {
        log.info("批量更新订单状态，IDs：{}，状态：{}", ids, status);
        
//        ValidationUtil.validateNotEmpty(ids, "订单ID列表");
//        ValidationUtil.validateNotNull(status, "订单状态");
        
        int count = orderService.batchUpdateOrderStatus(ids, status);
        return Result.success("成功更新 " + count + " 个订单状态");
    }

    /**
     * 根据订单号查询订单
     */
    @Operation(summary = "根据订单号查询", description = "根据订单号查询订单信息")
    @GetMapping("/orderNo/{orderNo}")
    @RequirePermission("order:read")
    public Result<OrderVO> getByOrderNo(@Parameter(description = "订单号") @PathVariable String orderNo) {
        log.info("根据订单号查询订单，订单号：{}", orderNo);
        
//        ValidationUtil.validateNotBlank(orderNo, "订单号");
        
        OrderVO result = orderService.getOrderByOrderNo(orderNo);
        return Result.success(result);
    }

    /**
     * 根据客户ID查询订单列表
     */
    @Operation(summary = "根据客户ID查询订单", description = "根据客户ID查询该客户的所有订单")
    @GetMapping("/customer/{customerId}")
    @RequirePermission("order:read")
    public Result<List<OrderVO>> getByCustomerId(@Parameter(description = "客户ID") @PathVariable Long customerId) {
        log.info("根据客户ID查询订单列表，客户ID：{}", customerId);
        
//        ValidationUtil.validateNotNull(customerId, "客户ID");
        
        List<OrderVO> result = orderService.getOrdersByCustomerId(customerId);
        return Result.success(result);
    }

    /**
     * 获取订单状态统计
     */
    @Operation(summary = "订单状态统计", description = "获取各状态订单的数量统计")
    @GetMapping("/statistics/status")
    @RequirePermission("order:read")
    public Result<Map<String, Long>> getStatusStatistics() {
        log.info("获取订单状态统计");
        
        Map<String, Long> result = orderService.getOrderStatusStatistics();
        return Result.success(result);
    }

    /**
     * 生成订单号
     */
    @Operation(summary = "生成订单号", description = "生成新的订单号")
    @GetMapping("/generate/orderNo")
    @RequirePermission("order:create")
    public Result<String> generateOrderNo() {
        log.info("生成订单号");
        
        String orderNo = orderService.generateOrderNo();
        return Result.success(orderNo);
    }
}
