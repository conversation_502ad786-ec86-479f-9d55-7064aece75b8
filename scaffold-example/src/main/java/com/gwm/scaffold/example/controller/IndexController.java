package com.gwm.scaffold.example.controller;

import com.gwm.scaffold.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 首页控制器
 * 
 * 提供应用首页和基本信息
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Api(tags = "首页", description = "应用首页相关接口")
@RestController
public class IndexController {

    @ApiOperation(value = "应用首页", notes = "显示应用首页")
    @GetMapping(value = "/", produces = MediaType.TEXT_HTML_VALUE)
    public void index(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");
        
        try {
            ClassPathResource resource = new ClassPathResource("static/index.html");
            String html = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            response.getWriter().write(html);
        } catch (Exception e) {
            response.getWriter().write(getDefaultIndexPage());
        }
    }

    @ApiOperation(value = "应用信息", notes = "获取应用基本信息")
    @GetMapping("/api/info")
    public Result<Map<String, Object>> getAppInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", "GWM Scaffold 企业级脚手架");
        info.put("version", "1.1.0");
        info.put("description", "企业级Spring Boot项目模板，提供完整的基础设施和最佳实践");
        info.put("author", "GWM开发团队");
        info.put("timestamp", System.currentTimeMillis());

        Map<String, String> links = new HashMap<>();
        links.put("脚手架文档", "/api/scaffold/docs");
        links.put("脚手架介绍", "/api/scaffold/intro");
        links.put("代码生成器", "/generator.html");
        links.put("代码生成器介绍", "/api/scaffold/code-generator-intro");
        links.put("认证鉴权模块", "/api/scaffold/auth-module");
        links.put("认证鉴权介绍", "/api/scaffold/auth-module-intro");
        links.put("工具类库", "/api/scaffold/tools");
        links.put("快速开始", "/api/scaffold/quickstart");
        links.put("模块架构", "/api/scaffold/architecture");
        links.put("最佳实践", "/api/scaffold/best-practices");
        links.put("Markdown文档", "/api/scaffold/markdown-viewer");
        links.put("API文档", "/swagger-ui.html");
        links.put("Bootstrap UI", "/doc.html");
        links.put("监控面板", "/actuator");
        links.put("数据库控制台", "/h2-console");
        links.put("Druid监控", "/druid");
        info.put("links", links);

        return Result.success(info);
    }

    @ApiOperation(value = "健康检查", notes = "检查应用是否正常运行")
    @GetMapping("/api/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("uptime", System.currentTimeMillis() - getStartTime());
        
        return Result.success(health);
    }

    /**
     * 获取应用启动时间（简化实现）
     */
    private long getStartTime() {
        // 这里简化处理，实际可以通过ApplicationContext获取
        return System.currentTimeMillis() - 60000; // 假设启动了1分钟
    }

    /**
     * 获取默认首页HTML
     */
    private String getDefaultIndexPage() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>GWM Scaffold 示例应用</title>\n" +
                "    <style>\n" +
                "        * {\n" +
                "            margin: 0;\n" +
                "            padding: 0;\n" +
                "            box-sizing: border-box;\n" +
                "        }\n" +
                "        \n" +
                "        body {\n" +
                "            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n" +
                "            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                "            min-height: 100vh;\n" +
                "            display: flex;\n" +
                "            align-items: center;\n" +
                "            justify-content: center;\n" +
                "            padding: 20px;\n" +
                "        }\n" +
                "        \n" +
                "        .container {\n" +
                "            background: rgba(255,255,255,0.95);\n" +
                "            padding: 50px;\n" +
                "            border-radius: 20px;\n" +
                "            box-shadow: 0 20px 60px rgba(0,0,0,0.1);\n" +
                "            backdrop-filter: blur(20px);\n" +
                "            text-align: center;\n" +
                "            max-width: 900px;\n" +
                "            width: 100%;\n" +
                "        }\n" +
                "        \n" +
                "        .title {\n" +
                "            color: #2c3e50;\n" +
                "            font-size: 3rem;\n" +
                "            font-weight: 700;\n" +
                "            margin-bottom: 15px;\n" +
                "            background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "            -webkit-background-clip: text;\n" +
                "            -webkit-text-fill-color: transparent;\n" +
                "            background-clip: text;\n" +
                "        }\n" +
                "        \n" +
                "        .subtitle {\n" +
                "            color: #7f8c8d;\n" +
                "            font-size: 1.2rem;\n" +
                "            margin-bottom: 30px;\n" +
                "        }\n" +
                "        \n" +
                "        .description {\n" +
                "            color: #95a5a6;\n" +
                "            font-size: 1rem;\n" +
                "            line-height: 1.6;\n" +
                "            margin-bottom: 40px;\n" +
                "        }\n" +
                "        \n" +
                "        .links-grid {\n" +
                "            display: grid;\n" +
                "            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n" +
                "            gap: 20px;\n" +
                "            margin-bottom: 30px;\n" +
                "        }\n" +
                "        \n" +
                "        .link-card {\n" +
                "            background: rgba(255,255,255,0.8);\n" +
                "            border: 2px solid transparent;\n" +
                "            border-radius: 15px;\n" +
                "            padding: 20px;\n" +
                "            text-decoration: none;\n" +
                "            transition: all 0.3s ease;\n" +
                "            backdrop-filter: blur(10px);\n" +
                "        }\n" +
                "        \n" +
                "        .link-card:hover {\n" +
                "            transform: translateY(-5px);\n" +
                "            border-color: rgba(102, 126, 234, 0.3);\n" +
                "            box-shadow: 0 10px 30px rgba(0,0,0,0.1);\n" +
                "            text-decoration: none;\n" +
                "        }\n" +
                "        \n" +
                "        .link-icon {\n" +
                "            font-size: 2rem;\n" +
                "            margin-bottom: 10px;\n" +
                "            display: block;\n" +
                "        }\n" +
                "        \n" +
                "        .link-title {\n" +
                "            color: #2c3e50;\n" +
                "            font-size: 1.1rem;\n" +
                "            font-weight: 600;\n" +
                "            margin-bottom: 5px;\n" +
                "        }\n" +
                "        \n" +
                "        .link-desc {\n" +
                "            color: #7f8c8d;\n" +
                "            font-size: 0.9rem;\n" +
                "        }\n" +
                "        \n" +
                "        .features {\n" +
                "            margin-top: 40px;\n" +
                "            padding-top: 30px;\n" +
                "            border-top: 1px solid rgba(0,0,0,0.1);\n" +
                "        }\n" +
                "        \n" +
                "        .features-title {\n" +
                "            color: #2c3e50;\n" +
                "            font-size: 1.5rem;\n" +
                "            font-weight: 600;\n" +
                "            margin-bottom: 20px;\n" +
                "        }\n" +
                "        \n" +
                "        .features-grid {\n" +
                "            display: grid;\n" +
                "            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n" +
                "            gap: 15px;\n" +
                "        }\n" +
                "        \n" +
                "        .feature {\n" +
                "            text-align: center;\n" +
                "        }\n" +
                "        \n" +
                "        .feature-icon {\n" +
                "            font-size: 1.5rem;\n" +
                "            margin-bottom: 8px;\n" +
                "            display: block;\n" +
                "        }\n" +
                "        \n" +
                "        .feature-title {\n" +
                "            color: #2c3e50;\n" +
                "            font-size: 0.9rem;\n" +
                "            font-weight: 600;\n" +
                "            margin-bottom: 3px;\n" +
                "        }\n" +
                "        \n" +
                "        .feature-desc {\n" +
                "            color: #95a5a6;\n" +
                "            font-size: 0.8rem;\n" +
                "        }\n" +
                "        \n" +
                "        @media (max-width: 768px) {\n" +
                "            .container {\n" +
                "                padding: 30px 20px;\n" +
                "            }\n" +
                "            \n" +
                "            .title {\n" +
                "                font-size: 2.5rem;\n" +
                "            }\n" +
                "            \n" +
                "            .links-grid {\n" +
                "                grid-template-columns: 1fr;\n" +
                "            }\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <h1 class=\"title\">🚀 GWM Scaffold</h1>\n" +
                "        <div class=\"subtitle\">企业级Spring Boot脚手架</div>\n" +
                "        <div class=\"description\">\n" +
                "            这是一个企业级的Spring Boot项目模板，提供完整的基础设施和最佳实践。<br>\n" +
                "            包含17个企业级工具类、安全加密、网络请求、数据处理、监控面板等完整功能。\n" +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"links-grid\">\n" +
                "            <a href=\"/api/scaffold/docs\" class=\"link-card\">\n" +
                "                <span class=\"link-icon\">📚</span>\n" +
                "                <div class=\"link-title\">脚手架文档</div>\n" +
                "                <div class=\"link-desc\">完整的脚手架使用文档</div>\n" +
                "            </a>\n" +
                "            \n" +
                "            <a href=\"/api/scaffold/intro\" class=\"link-card\">\n" +
                "                <span class=\"link-icon\">📋</span>\n" +
                "                <div class=\"link-title\">脚手架介绍</div>\n" +
                "                <div class=\"link-desc\">脚手架概览和核心特性</div>\n" +
                "            </a>\n" +
                "            \n" +
                "            <a href=\"/generator.html\" class=\"link-card\">\n" +
                "                <span class=\"link-icon\">⚡</span>\n" +
                "                <div class=\"link-title\">代码生成器</div>\n" +
                "                <div class=\"link-desc\">一键生成完整的CRUD代码</div>\n" +
                "            </a>\n" +
                "            \n" +
                "            <a href=\"/api/scaffold/auth-module\" class=\"link-card\">\n" +
                "                <span class=\"link-icon\">🔐</span>\n" +
                "                <div class=\"link-title\">认证鉴权</div>\n" +
                "                <div class=\"link-desc\">完整的认证权限控制体系</div>\n" +
                "            </a>\n" +
                "            \n" +
                "            <a href=\"/api/scaffold/tools\" class=\"link-card\">\n" +
                "                <span class=\"link-icon\">🛠️</span>\n" +
                "                <div class=\"link-title\">工具类库</div>\n" +
                "                <div class=\"link-desc\">17个企业级工具类详细介绍</div>\n" +
                "            </a>\n" +
                "            \n" +
                "            <a href=\"/api/scaffold/quickstart\" class=\"link-card\">\n" +
                "                <span class=\"link-icon\">🚀</span>\n" +
                "                <div class=\"link-title\">快速开始</div>\n" +
                "                <div class=\"link-desc\">快速开始使用指南</div>\n" +
                "            </a>\n" +
                "            \n" +
                "            <a href=\"/api/scaffold/markdown-viewer\" class=\"link-card\">\n" +
                "                <span class=\"link-icon\">📝</span>\n" +
                "                <div class=\"link-title\">Markdown文档</div>\n" +
                "                <div class=\"link-desc\">Markdown格式的完整文档</div>\n" +
                "            </a>\n" +
                "            \n" +
                "            <a href=\"/swagger-ui.html\" class=\"link-card\">\n" +
                "                <span class=\"link-icon\">📖</span>\n" +
                "                <div class=\"link-title\">API文档</div>\n" +
                "                <div class=\"link-desc\">Swagger API文档界面</div>\n" +
                "            </a>\n" +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"features\">\n" +
                "            <div class=\"features-title\">✨ 核心特性</div>\n" +
                "            <div class=\"features-grid\">\n" +
                "                <div class=\"feature\">\n" +
                "                    <span class=\"feature-icon\">🔧</span>\n" +
                "                    <div class=\"feature-title\">开箱即用</div>\n" +
                "                    <div class=\"feature-desc\">引入依赖即可使用</div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <div class=\"feature\">\n" +
                "                    <span class=\"feature-icon\">🛠️</span>\n" +
                "                    <div class=\"feature-title\">工具丰富</div>\n" +
                "                    <div class=\"feature-desc\">17个企业级工具类</div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <div class=\"feature\">\n" +
                "                    <span class=\"feature-icon\">🔐</span>\n" +
                "                    <div class=\"feature-title\">认证鉴权</div>\n" +
                "                    <div class=\"feature-desc\">完整的权限控制体系</div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <div class=\"feature\">\n" +
                "                    <span class=\"feature-icon\">🛡️</span>\n" +
                "                    <div class=\"feature-title\">安全加密</div>\n" +
                "                    <div class=\"feature-desc\">文件加密、HMAC签名</div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <div class=\"feature\">\n" +
                "                    <span class=\"feature-icon\">🌐</span>\n" +
                "                    <div class=\"feature-title\">网络请求</div>\n" +
                "                    <div class=\"feature-desc\">HTTP工具、OkHttp封装</div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <div class=\"feature\">\n" +
                "                    <span class=\"feature-icon\">📊</span>\n" +
                "                    <div class=\"feature-title\">数据处理</div>\n" +
                "                    <div class=\"feature-desc\">字符串、集合、分页工具</div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <div class=\"feature\">\n" +
                "                    <span class=\"feature-icon\">⚡</span>\n" +
                "                    <div class=\"feature-title\">代码生成</div>\n" +
                "                    <div class=\"feature-desc\">一键生成CRUD代码</div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <div class=\"feature\">\n" +
                "                    <span class=\"feature-icon\">🎯</span>\n" +
                "                    <div class=\"feature-title\">模块化</div>\n" +
                "                    <div class=\"feature-desc\">按需引入功能模块</div>\n" +
                "                </div>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }
}
