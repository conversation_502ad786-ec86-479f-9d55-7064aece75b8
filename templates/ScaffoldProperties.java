package com.gwm.scaffold.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Scaffold 统一配置属性
 * 
 * 整合所有模块的配置属性，提供统一的配置入口
 *
 * <AUTHOR>
 * @date 2025/1/16
 */
@Data
@ConfigurationProperties(prefix = "scaffold")
public class ScaffoldProperties {

    /**
     * 是否启用Scaffold
     */
    private boolean enabled = true;

    /**
     * 应用信息配置
     */
    private ApplicationConfig application = new ApplicationConfig();

    /**
     * 认证模块配置
     */
    private AuthConfig auth = new AuthConfig();

    /**
     * Web模块配置
     */
    private WebConfig web = new WebConfig();

    /**
     * 数据访问模块配置
     */
    private DataConfig data = new DataConfig();

    /**
     * 工具模块配置
     */
    private ToolsConfig tools = new ToolsConfig();

    /**
     * 监控模块配置
     */
    private MonitorConfig monitor = new MonitorConfig();

    /**
     * 文档模块配置
     */
    private DocsConfig docs = new DocsConfig();

    /**
     * 应用信息配置
     */
    @Data
    public static class ApplicationConfig {
        /**
         * 应用名称
         */
        private String name = "GWM Scaffold Application";

        /**
         * 应用版本
         */
        private String version = "1.0.0";

        /**
         * 应用描述
         */
        private String description = "基于GWM Scaffold构建的应用";

        /**
         * 开发团队
         */
        private String team = "GWM开发团队";

        /**
         * 联系邮箱
         */
        private String email = "";

        /**
         * 应用主页
         */
        private String homepage = "";
    }

    /**
     * 认证模块配置
     */
    @Data
    public static class AuthConfig {
        /**
         * 是否启用认证模块
         */
        private boolean enabled = true;

        /**
         * Token请求头名称
         */
        private String tokenHeader = "Authorization";

        /**
         * Token前缀
         */
        private String tokenPrefix = "Bearer ";

        /**
         * Token参数名称
         */
        private String tokenParameter = "token";

        /**
         * 白名单路径
         */
        private String[] whiteList = {"/public/**", "/health/**"};

        /**
         * 认证鉴权分离配置
         */
        private SeparationConfig separation = new SeparationConfig();

        @Data
        public static class SeparationConfig {
            /**
             * 是否启用认证鉴权分离
             */
            private boolean enabled = false;

            /**
             * 默认策略
             */
            private String defaultStrategy = "REQUIRED";

            /**
             * 路径规则配置
             */
            private PathRulesConfig pathRules = new PathRulesConfig();

            @Data
            public static class PathRulesConfig {
                /**
                 * 跳过认证的路径模式
                 */
                private String[] skipPatterns = {"/public/**"};

                /**
                 * 可选认证的路径模式
                 */
                private String[] optionalPatterns = {"/content/**"};

                /**
                 * 仅验证Token的路径模式
                 */
                private String[] tokenOnlyPatterns = {"/api/*/data/**"};
            }
        }
    }

    /**
     * Web模块配置
     */
    @Data
    public static class WebConfig {
        /**
         * 是否启用Web模块
         */
        private boolean enabled = true;

        /**
         * 是否启用全局异常处理
         */
        private boolean globalExceptionHandler = true;

        /**
         * 跨域配置
         */
        private CorsConfig cors = new CorsConfig();

        /**
         * 请求日志配置
         */
        private RequestLogConfig requestLog = new RequestLogConfig();

        @Data
        public static class CorsConfig {
            /**
             * 是否启用跨域
             */
            private boolean enabled = true;

            /**
             * 路径模式
             */
            private String pathPattern = "/**";

            /**
             * 允许的源
             */
            private String[] allowedOriginPatterns = {"*"};

            /**
             * 允许的方法
             */
            private String[] allowedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};

            /**
             * 允许的头部
             */
            private String[] allowedHeaders = {"*"};

            /**
             * 是否允许凭证
             */
            private boolean allowCredentials = true;

            /**
             * 预检请求缓存时间
             */
            private long maxAge = 3600;
        }

        @Data
        public static class RequestLogConfig {
            /**
             * 是否启用请求日志
             */
            private boolean enabled = true;

            /**
             * 是否记录请求体
             */
            private boolean logRequestBody = true;

            /**
             * 是否记录响应体
             */
            private boolean logResponseBody = false;

            /**
             * 排除的路径
             */
            private String[] excludePaths = {"/health/**", "/actuator/**"};
        }
    }

    /**
     * 数据访问模块配置
     */
    @Data
    public static class DataConfig {
        /**
         * 是否启用数据访问模块
         */
        private boolean enabled = true;

        /**
         * MyBatis Plus配置
         */
        private MybatisPlusConfig mybatisPlus = new MybatisPlusConfig();

        /**
         * 数据权限配置
         */
        private DataPermissionConfig dataPermission = new DataPermissionConfig();

        @Data
        public static class MybatisPlusConfig {
            /**
             * 是否启用防全表更新删除
             */
            private boolean blockAttackEnabled = true;

            /**
             * 是否启用乐观锁
             */
            private boolean optimisticLockerEnabled = true;

            /**
             * 分页配置
             */
            private PaginationConfig pagination = new PaginationConfig();

            @Data
            public static class PaginationConfig {
                /**
                 * 是否启用分页
                 */
                private boolean enabled = true;

                /**
                 * 数据库类型
                 */
                private String dbType = "mysql";

                /**
                 * 是否溢出总页数后设置第一页
                 */
                private boolean overflow = true;

                /**
                 * 单页最大限制数量
                 */
                private long maxLimit = 500;
            }
        }

        @Data
        public static class DataPermissionConfig {
            /**
             * 是否启用数据权限
             */
            private boolean enabled = false;

            /**
             * 默认数据权限处理器
             */
            private String defaultHandler = "defaultDataPermissionHandler";
        }
    }

    /**
     * 工具模块配置
     */
    @Data
    public static class ToolsConfig {
        /**
         * 是否启用工具模块
         */
        private boolean enabled = true;

        /**
         * HTTP工具配置
         */
        private HttpConfig http = new HttpConfig();

        /**
         * Excel工具配置
         */
        private ExcelConfig excel = new ExcelConfig();

        @Data
        public static class HttpConfig {
            /**
             * 连接超时时间（毫秒）
             */
            private int connectTimeout = 5000;

            /**
             * 读取超时时间（毫秒）
             */
            private int readTimeout = 10000;

            /**
             * 写入超时时间（毫秒）
             */
            private int writeTimeout = 10000;
        }

        @Data
        public static class ExcelConfig {
            /**
             * 默认工作表名称
             */
            private String defaultSheetName = "Sheet1";

            /**
             * 最大行数
             */
            private int maxRows = 100000;
        }
    }

    /**
     * 监控模块配置
     */
    @Data
    public static class MonitorConfig {
        /**
         * 是否启用监控模块
         */
        private boolean enabled = false;

        /**
         * 监控路径
         */
        private String path = "/scaffold/monitor";

        /**
         * 是否需要认证
         */
        private boolean requireAuth = true;

        /**
         * 性能监控配置
         */
        private PerformanceConfig performance = new PerformanceConfig();

        @Data
        public static class PerformanceConfig {
            /**
             * 是否启用性能监控
             */
            private boolean enabled = true;

            /**
             * 慢查询阈值（毫秒）
             */
            private long slowQueryThreshold = 1000;

            /**
             * 是否记录SQL
             */
            private boolean logSql = false;
        }
    }

    /**
     * 文档模块配置
     */
    @Data
    public static class DocsConfig {
        /**
         * 是否启用文档模块
         */
        private boolean enabled = false;

        /**
         * 文档路径
         */
        private String path = "/scaffold/docs";

        /**
         * Swagger配置
         */
        private SwaggerConfig swagger = new SwaggerConfig();

        @Data
        public static class SwaggerConfig {
            /**
             * 文档标题
             */
            private String title = "API文档";

            /**
             * 文档描述
             */
            private String description = "基于GWM Scaffold构建的API文档";

            /**
             * 文档版本
             */
            private String version = "1.0.0";

            /**
             * 联系人信息
             */
            private String contactName = "开发团队";

            /**
             * 联系邮箱
             */
            private String contactEmail = "";

            /**
             * 扫描的包路径
             */
            private String basePackage = "com.gwm";
        }
    }
}
