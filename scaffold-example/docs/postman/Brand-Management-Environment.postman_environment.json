{"id": "brand-management-env", "name": "品牌管理API环境", "values": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "default", "enabled": true}, {"key": "brandId", "value": "", "type": "default", "enabled": true}, {"key": "token", "value": "", "type": "secret", "enabled": true}, {"key": "userId", "value": "admin", "type": "default", "enabled": true}, {"key": "userName", "value": "管理员", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-07-18T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}