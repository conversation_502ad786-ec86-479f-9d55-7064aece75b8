package com.gwm.scaffold.auth.annotation;

import java.lang.annotation.*;

/**
 * 角色校验注解
 * 
 * 用于方法级别的角色控制
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireRole {

    /**
     * 角色标识
     */
    String[] value() default {};

    /**
     * 角色标识（别名）
     */
    String[] roles() default {};

    /**
     * 逻辑关系：AND-所有角色都必须满足，OR-满足任一角色即可
     */
    Logical logical() default Logical.AND;

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        /**
         * 且
         */
        AND,
        /**
         * 或
         */
        OR
    }
}
