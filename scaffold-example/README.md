# Scaffold Example Project

GWM Scaffold 示例项目，展示如何使用GWM Scaffold快速构建企业级Spring Boot应用。

## 📦 项目介绍

这是一个完整的示例项目，展示了GWM Scaffold的所有核心功能：

- ✅ **用户管理模块**: 完整的CRUD操作示例
- ✅ **认证权限**: 基于注解的权限控制
- ✅ **API文档**: 自动生成的Swagger文档
- ✅ **监控面板**: 实时应用状态监控
- ✅ **数据访问**: MyBatis Plus增强功能
- ✅ **全局异常处理**: 统一的错误处理
- ✅ **参数校验**: 完整的参数验证示例

## 🚀 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+

### 启动应用

```bash
# 克隆项目
git clone <repository-url>

# 进入项目目录
cd scaffold-parent/scaffold-example

# 编译并启动
mvn spring-boot:run
```

### 访问应用

启动成功后，访问以下地址：

- **应用首页**: http://localhost:8080
- **用户管理API**: http://localhost:8080/api/users
- **API文档首页**: http://localhost:8080/scaffold/docs
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **Bootstrap UI**: http://localhost:8080/doc.html
- **监控面板**: http://localhost:8080/scaffold/monitor
- **数据库控制台**: http://localhost:8080/h2-console
- **Druid监控**: http://localhost:8080/druid

## 📊 功能演示

### 1. 用户管理API

#### 获取用户列表
```bash
curl -X GET "http://localhost:8080/api/users?current=1&size=10" \
  -H "Authorization: admin_token"
```

#### 创建用户
```bash
curl -X POST "http://localhost:8080/api/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: admin_token" \
  -d '{
    "username": "newuser",
    "password": "123456",
    "realName": "新用户",
    "email": "<EMAIL>",
    "mobile": "13800000000",
    "gender": 1,
    "age": 25,
    "departmentId": 1,
    "departmentName": "技术部",
    "position": "开发工程师"
  }'
```

#### 获取用户详情
```bash
curl -X GET "http://localhost:8080/api/users/1" \
  -H "Authorization: admin_token"
```

#### 更新用户
```bash
curl -X PUT "http://localhost:8080/api/users/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: admin_token" \
  -d '{
    "id": 1,
    "realName": "更新后的姓名",
    "email": "<EMAIL>",
    "version": 0
  }'
```

#### 删除用户
```bash
curl -X DELETE "http://localhost:8080/api/users/1" \
  -H "Authorization: admin_token"
```

### 2. 权限控制演示

不同的Token对应不同的权限：

- **admin_token**: 管理员，拥有所有权限
- **zhangsan_token**: 张三，只有用户读取权限
- **lisi_token**: 李四，有用户读取和创建权限
- **wangwu_token**: 王五，有用户的所有权限

```bash
# 使用张三的Token（只有读取权限）
curl -X GET "http://localhost:8080/api/users" \
  -H "Authorization: zhangsan_token"

# 尝试创建用户（会被拒绝）
curl -X POST "http://localhost:8080/api/users" \
  -H "Authorization: zhangsan_token" \
  -d '{"username":"test"}'
```

### 3. 参数校验演示

```bash
# 发送无效参数（会返回校验错误）
curl -X POST "http://localhost:8080/api/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: admin_token" \
  -d '{
    "username": "a",
    "password": "123",
    "email": "invalid-email"
  }'
```

## 🔧 配置说明

### 数据库配置

项目使用H2内存数据库，无需额外配置：

```yaml
spring:
  datasource:
    url: jdbc:h2:mem:scaffold_example
    username: sa
    password: 
    driver-class-name: org.h2.Driver
```

### 认证配置

```yaml
scaffold:
  auth:
    enabled: true
    white-list:
      - /
      - /h2-console/**
      - /druid/**
      - /scaffold/**
```

### 监控配置

```yaml
scaffold:
  monitor:
    enabled: true
    dashboard:
      enabled: true
      path: "/scaffold/monitor"
```

### 文档配置

```yaml
scaffold:
  docs:
    enabled: true
    swagger:
      title: "GWM Scaffold 示例应用 API"
      description: "展示如何使用GWM Scaffold构建企业级应用的API文档"
```

## 📋 项目结构

```
scaffold-example/
├── src/main/java/com/gwm/scaffold/example/
│   ├── ScaffoldExampleApplication.java     # 启动类
│   ├── controller/                         # 控制器层
│   │   ├── IndexController.java           # 首页控制器
│   │   └── UserController.java            # 用户控制器
│   ├── service/                           # 服务层
│   │   ├── UserService.java               # 用户服务接口
│   │   └── impl/
│   │       ├── UserServiceImpl.java       # 用户服务实现
│   │       └── MockAuthenticateServiceImpl.java # 模拟认证服务
│   ├── mapper/                            # 数据访问层
│   │   └── UserMapper.java               # 用户Mapper
│   ├── entity/                            # 实体类
│   │   └── User.java                     # 用户实体
│   ├── dto/                               # 数据传输对象
│   │   ├── UserCreateRequest.java        # 用户创建请求
│   │   ├── UserUpdateRequest.java        # 用户更新请求
│   │   └── UserQueryRequest.java         # 用户查询请求
│   └── vo/                                # 视图对象
│       └── UserVO.java                   # 用户视图对象
├── src/main/resources/
│   ├── application.yml                    # 应用配置
│   ├── data.sql                          # 初始化数据
│   └── mapper/
│       └── UserMapper.xml               # MyBatis映射文件
└── src/test/java/                        # 测试代码
    └── com/gwm/scaffold/example/
        ├── ScaffoldExampleApplicationTests.java
        └── controller/
            └── UserControllerTest.java
```

## 🎯 核心特性展示

### 1. 统一返回结果
所有API都使用统一的`Result<T>`格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": { ... },
  "timestamp": 1641801600000
}
```

### 2. 全局异常处理
自动捕获和处理各种异常：

```java
@RequirePermission("user:create")
@PostMapping
public Result<UserVO> createUser(@RequestBody @Valid UserCreateRequest request) {
    // 参数校验失败会自动被GlobalExceptionHandler处理
    // 业务异常也会被统一处理
    return Result.success(userService.createUser(request));
}
```

### 3. 权限控制
基于注解的权限控制：

```java
@RequirePermission("user:read")   // 需要用户读取权限
@RequireRole("admin")             // 需要管理员角色
@GetMapping
public Result<IPage<UserVO>> getUsers() {
    // 方法实现
}
```

### 4. 性能监控
自动监控方法执行性能：

```java
@Monitor(value = "用户分页查询", threshold = 1000)
public IPage<UserVO> getUserPage(int current, int size, UserQueryRequest request) {
    // 执行时间超过1000ms会记录警告日志
}
```

### 5. 数据权限
支持数据级权限控制：

```java
@DataPermission(value = DataPermission.Type.USER)
public List<User> getUserList() {
    // 只能查看自己创建的用户
}
```

## 🧪 测试

### 运行测试
```bash
mvn test
```

### 测试覆盖
- 单元测试：Controller层测试
- 集成测试：应用启动测试
- API测试：使用curl或Postman测试

## 📈 监控和运维

### 应用监控
访问 http://localhost:8080/scaffold/monitor 查看：
- 应用运行状态
- 内存和CPU使用情况
- 数据库连接状态
- 请求统计和性能指标

### 数据库监控
访问 http://localhost:8080/druid 查看：
- 连接池状态
- SQL执行统计
- 慢查询分析

### 健康检查
访问 http://localhost:8080/actuator/health 获取应用健康状态

## 🔗 相关链接

- [GWM Scaffold 文档](../README.md)
- [API文档](http://localhost:8080/scaffold/docs)
- [监控面板](http://localhost:8080/scaffold/monitor)
- [GitHub仓库](https://github.com/gwm/scaffold)

## 📝 许可证

Apache License 2.0
