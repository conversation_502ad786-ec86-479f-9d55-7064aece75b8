package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.*;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;

import java.util.Map;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-角色资源服务")
public interface SystemPbcRoleResourceSvc {
    /**
     * 权限中心-查询角色菜单树（返回所有树和选择节点）
     * @param request
     * @return  Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-查询角色菜单树（返回所有树和选择节点）")
    @Method(desc = "权限中心-查询角色菜单树（返回所有树和选择节点）", name = "权限中心-查询角色菜单树（返回所有树和选择节点） v2-/manage/admin/role/resource/Info")
    Map<String, Object> searchResourceOfRoleInfo(@Parameter(name = "request", required = true) SearchAdminResourceOfRoleFormClientDTO request);

}
