package com.gwm.scaffold.core.exception;

import lombok.*;

/**
 * 流异常
 * 
 * 用于处理文件流、网络流等IO操作相关的异常
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public final class StreamException extends RuntimeException {

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 错误详情
     */
    private String detailMessage;

    /**
     * 根据异常构造流异常
     *
     * @param e 原始异常
     */
    public StreamException(Exception e) {
        code = 415;
        if (null != e) {
            message = e.getMessage();
        }
    }

    /**
     * 根据错误信息构造流异常
     *
     * @param message 错误信息
     */
    public StreamException(String message) {
        code = 415;
        this.message = message;
    }

    /**
     * 根据错误信息和错误码构造流异常
     *
     * @param message 错误信息
     * @param code    错误码
     */
    public StreamException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }
}
