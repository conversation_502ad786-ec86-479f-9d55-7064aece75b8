package com.gwm.scaffold.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import static com.baomidou.mybatisplus.annotation.FieldFill.INSERT;
import static com.baomidou.mybatisplus.annotation.FieldFill.INSERT_UPDATE;
import static com.baomidou.mybatisplus.annotation.FieldStrategy.NEVER;

/**
 * 通用字段基础实体
 * 
 * 包含创建时间、创建人、更新时间、更新人等通用字段
 * 所有业务实体都应该继承此类
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
public abstract class BaseSuperEntity implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 创建时间
     */
    @TableField(fill = INSERT, updateStrategy = NEVER)
    private Date createTime;

    /**
     * 创建人工号
     */
    @TableField(fill = INSERT)
    private String createUserCode;

    /**
     * 创建人姓名
     */
    @TableField(fill = INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(fill = INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人工号
     */
    @TableField(fill = INSERT_UPDATE, insertStrategy = NEVER)
    private String updateUserCode;

    /**
     * 更新人姓名
     */
    @TableField(fill = INSERT_UPDATE, insertStrategy = NEVER)
    private String updateUserName;
}
