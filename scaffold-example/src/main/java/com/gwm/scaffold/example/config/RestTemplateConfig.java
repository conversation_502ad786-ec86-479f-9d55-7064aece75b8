package com.gwm.scaffold.example.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate 配置类
 * 
 * 配置HTTP客户端用于调用外部服务
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Configuration
public class RestTemplateConfig {

    /**
     * 配置RestTemplate
     */
    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory());
        log.info("RestTemplate 配置完成");
        return restTemplate;
    }

    /**
     * 配置HTTP请求工厂
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 连接超时时间（毫秒）
        factory.setConnectTimeout(10000);
        // 读取超时时间（毫秒）
        factory.setReadTimeout(30000);
        return factory;
    }
}
