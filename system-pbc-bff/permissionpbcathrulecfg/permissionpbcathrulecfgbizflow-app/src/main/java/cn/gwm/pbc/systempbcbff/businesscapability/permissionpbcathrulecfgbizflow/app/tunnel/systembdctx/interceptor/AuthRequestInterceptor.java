package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.properties.OpenPlatformProperties;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.properties.PermissionProperties;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.util.HmacSignUtil;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.util.PermissionSignUtil;
import com.alibaba.bizworks.core.runtime.context.UserContext;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.json.JacksonJsonParser;
import org.springframework.boot.json.JsonParser;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Collections;
import java.util.Map;

/**
 * FeignClient认证拦截器
 * <AUTHOR> 100013880
 */
@Slf4j
public class AuthRequestInterceptor implements RequestInterceptor {

    @Value("${gwm.permission.platform-code}")
    private String platformCode;

    @Resource
    private OpenPlatformProperties openPlatformProperties;

    @Resource
    private PermissionProperties permissionProperties;

    private final JsonParser jsonParser = new JacksonJsonParser();

    @Override
    public void apply(RequestTemplate template) {

        String userExt = UserContext.getUserExt();
        if (userExt != null && !userExt.isEmpty()) {
            Map<String, Object> userExtMap = jsonParser.parseMap(userExt);
            String token = (String) userExtMap.get("token");
            template.header("pbc_token", token);
            template.header("Authorization", token);
        }

        String account = UserContext.getUserCode();
        String target = openPlatformProperties.getUrl() + "/" + openPlatformProperties.getEnv();
        template.target(target);
        String url = template.url();
        String method = template.method();
        String path = template.path();
        log.info("本次请求path = {}", path);
        log.info("本次请求account = {}", account);

        String platformCode = permissionProperties.getPlatformCode();
        //权限管理平台密钥access_secret
        String permissionAppSecret = permissionProperties.getPlatformSecret();
        //开放平台密钥
        //开放平台app_key
        String openAppKey = openPlatformProperties.getAppKey();
        //开放平台secret
        String openAppSecret = openPlatformProperties.getAppSecret();

        Map<String, String> openHeaderMap = null, permissionHeaderMap = null;
        try {
            openHeaderMap = HmacSignUtil.createSignHeader(openAppKey, openAppSecret, url, method);
            permissionHeaderMap = PermissionSignUtil.createSignHeader(platformCode, permissionAppSecret, url, method, null, account);
        } catch (Exception e) {
            log.error("调用加密异常。");
            e.printStackTrace();
        }
        if (openHeaderMap != null) {
            openHeaderMap.forEach((key, value) -> template.header(key, Collections.singletonList(value)));
        }
        template.header("account", Collections.singletonList(account));
        if (permissionHeaderMap != null) {
            template.header("platform_code", Collections.singletonList(platformCode));
            template.header("offset", Collections.singletonList(permissionHeaderMap.get("OFFSET")));
            template.header("signature", Collections.singletonList(permissionHeaderMap.get("SIGNATURE")));
        }
        log.info("请求地址：" + url);
    }

}