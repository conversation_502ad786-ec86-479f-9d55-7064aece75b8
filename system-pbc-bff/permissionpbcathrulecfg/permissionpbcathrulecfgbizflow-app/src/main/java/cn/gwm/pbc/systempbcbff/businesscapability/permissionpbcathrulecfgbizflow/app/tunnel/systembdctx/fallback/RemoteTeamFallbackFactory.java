package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteTeamService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.team.RemoteTeamListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.team.RemoteTeamTreeDTO;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 登录降级处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteTeamFallbackFactory implements FallbackFactory<RemoteTeamService> {

    @Override
    public RemoteTeamService create(Throwable throwable) {
        return new RemoteTeamService() {

            @Override
            public CommonRlt<JSONObject> teamTree(RemoteTeamTreeDTO teamTreeDTO) {
                log.error("调用查询应用下用户组树形列表异常：" + throwable.getMessage());
                return null;
            }

            @Override
            public CommonRlt<JSONObject> teamList(RemoteTeamListDTO teamListDTO) {
                log.error("调用根据工号查询应用下用户组列表异常：" + throwable.getMessage());
                return null;
            }
        };

    }
}
