package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcAdminTeamSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field.RuleFieldListForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteAdminTeamService;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component("systemPbcAdminTeamSvcImpl")
public class SystemPbcAdminTeamSvcImpl implements SystemPbcAdminTeamSvc {

    @Autowired
    private RemoteAdminTeamService remoteAdminTeamService;

    @Override
    public Map<String, Object> getTeamTree() {
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteAdminTeamService.getTeamTree();
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }
}
