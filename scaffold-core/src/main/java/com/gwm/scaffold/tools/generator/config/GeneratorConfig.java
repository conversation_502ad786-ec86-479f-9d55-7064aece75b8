package com.gwm.scaffold.tools.generator.config;

import lombok.Data;

/**
 * 代码生成器配置
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Data
public class GeneratorConfig {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 模块名
     */
    private String moduleName;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 作者
     */
    private String author;

    /**
     * 输出路径
     */
    private String outputPath;

    /**
     * 是否覆盖已存在的文件
     */
    private boolean overwrite;

    /**
     * 表前缀（生成类名时会去掉）
     */
    private String tablePrefix;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 功能描述
     */
    private String functionName;

    /**
     * API路径前缀
     */
    private String apiPrefix;

    /**
     * 是否生成Swagger注解
     */
    private boolean generateSwagger;

    /**
     * 是否生成校验注解
     */
    private boolean generateValidation;

    /**
     * 是否生成权限注解
     */
    private boolean generatePermission;

    /**
     * 默认构造函数
     */
    public GeneratorConfig() {
        this.author = "scaffold";
        this.packageName = "com.gwm.scaffold.example";
        this.outputPath = "scaffold-example/src/main/java/com/gwm/scaffold/example";
        this.overwrite = false;
        this.generateSwagger = true;
        this.generateValidation = true;
        this.generatePermission = true;
        this.apiPrefix = "/api";
    }

    /**
     * 构造函数
     */
    public GeneratorConfig(String tableName, String moduleName) {
        this();
        this.tableName = tableName;
        this.moduleName = moduleName;
        this.businessName = moduleName;
        this.functionName = moduleName + "管理";
    }
}
