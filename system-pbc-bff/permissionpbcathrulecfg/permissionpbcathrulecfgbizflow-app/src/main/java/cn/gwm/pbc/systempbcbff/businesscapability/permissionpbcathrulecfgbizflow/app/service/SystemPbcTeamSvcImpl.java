package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcTeamSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteTeamService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.team.RemoteTeamTreeDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.team.TeamInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.department.TeamTreeDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import java.lang.Override;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
/**
 * <AUTHOR>
 */
@Component("SystemPbcTeamSvcImpl")
public class SystemPbcTeamSvcImpl implements SystemPbcTeamSvc {

    @Resource
    private RemoteTeamService remoteTeamService;

    @Override
    public Map<String, Object> tree(TeamTreeDTO request) {
    	RemoteTeamTreeDTO remoteDTO = new RemoteTeamTreeDTO();
        BeanUtil.copyProperties(request, remoteDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteTeamService.teamTree(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject result = jsonObjectCommonRlt.getData();
        JSONArray rosterTeam = result.getJSONArray("roster_team");
        JSONArray supplierTeam = result.getJSONArray("supplier_team");
        JSONArray customTeam = result.getJSONArray("custom_team");


        List<TeamInfoItemVO> rosterTeamList = rosterTeam.toJavaList(TeamInfoItemVO.class);
        List<TeamInfoItemVO> supplierTeamList = supplierTeam.toJavaList(TeamInfoItemVO.class);
        List<TeamInfoItemVO> customTeamList = customTeam.toJavaList(TeamInfoItemVO.class);

        JSONObject rlt = new JSONObject();
        rlt.put("rosterTeam", rosterTeamList);
        rlt.put("supplierTeam", supplierTeamList);
        rlt.put("customTeam", customTeamList);

        return rlt;
	}
}
