package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.field;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleFieldSettingsFormClientDTO", desc = "null")
@Data
@ApiModel("规则字段设置请求参数对象")
public class RuleFieldSettingsFormClientDTO implements Serializable {

    /**
     * 操作人账号
     */
    @Field(name = "account", desc = "")
    @JsonIgnore
    @ApiModelProperty("操作人账号")
    private String account;

    /**
     * 角色ID（虚拟角色）
     */
    @Field(name = "角色ID（虚拟角色）", desc = "角色ID（虚拟角色）")
    @ApiModelProperty("角色ID")
    @JsonProperty(value = "role_set_id")
    private Long roleSetId;

    /**
     * 规则字段数据
     */
    @Field(name = "规则字段数据", desc = "规则字段数据")
    @ApiModelProperty("规则字段数据")
    @JsonProperty(value = "rule_field_data")
    List<RuleFieldDataFormClientDTO> ruleFieldDataList;
}