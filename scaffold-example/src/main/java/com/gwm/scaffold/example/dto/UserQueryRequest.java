package com.gwm.scaffold.example.dto;

import com.gwm.scaffold.web.cmd.AbstractPageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户查询请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "用户查询请求")
public class UserQueryRequest extends AbstractPageRequest {

    @ApiModelProperty(value = "用户名", example = "zhangsan")
    private String username;

    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String realName;

    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String mobile;

    @ApiModelProperty(value = "性别", example = "1", notes = "1:男 2:女")
    private Integer gender;

    @ApiModelProperty(value = "部门ID", example = "1")
    private Long departmentId;

    @ApiModelProperty(value = "部门名称", example = "技术部")
    private String departmentName;

    @ApiModelProperty(value = "职位", example = "Java开发工程师")
    private String position;

    @ApiModelProperty(value = "状态", example = "1", notes = "1:启用 0:禁用")
    private Integer status;

    @ApiModelProperty(value = "搜索关键词", example = "张三")
    private String keyword;

    @ApiModelProperty(value = "年龄范围-最小值", example = "18")
    private Integer minAge;

    @ApiModelProperty(value = "年龄范围-最大值", example = "65")
    private Integer maxAge;

    @ApiModelProperty(value = "创建时间-开始", example = "2025-01-01")
    private String createTimeStart;

    @ApiModelProperty(value = "创建时间-结束", example = "2025-12-31")
    private String createTimeEnd;
}
