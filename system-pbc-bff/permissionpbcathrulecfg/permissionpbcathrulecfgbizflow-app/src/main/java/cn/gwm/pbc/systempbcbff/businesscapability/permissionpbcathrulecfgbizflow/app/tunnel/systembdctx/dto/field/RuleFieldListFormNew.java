package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RuleFieldListFormNew implements Serializable {
    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer page = 1;

    /**
     * 每页显示几条
     */
    @ApiModelProperty(value = "每页显示几条")
    private Integer size = 10;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long orgId;

    /**
     * 组织id
     */
    @ApiModelProperty("员工账号")
    private String personnelNo;
}
