package com.gwm.scaffold.example.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gwm.scaffold.example.dto.UserCreateRequest;
import com.gwm.scaffold.example.dto.UserQueryRequest;
import com.gwm.scaffold.example.dto.UserUpdateRequest;
import com.gwm.scaffold.example.entity.User;
import com.gwm.scaffold.example.vo.UserVO;

import java.util.List;

/**
 * 用户服务接口
 * 
 * 展示如何定义业务服务接口
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
public interface UserService extends IService<User> {

    /**
     * 分页查询用户列表
     *
     * @param request 查询条件（包含分页参数）
     * @return 用户分页数据
     */
    IPage<UserVO> getUserPage(UserQueryRequest request);

    /**
     * 根据ID获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    UserVO getUserById(Long id);

    /**
     * 创建用户
     *
     * @param request 创建请求
     * @return 用户详情
     */
    UserVO createUser(UserCreateRequest request);

    /**
     * 更新用户
     *
     * @param request 更新请求
     * @return 用户详情
     */
    UserVO updateUser(UserUpdateRequest request);

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 是否成功
     */
    boolean deleteUser(Long id);

    /**
     * 批量删除用户
     *
     * @param ids 用户ID列表
     * @return 删除数量
     */
    int batchDeleteUsers(List<Long> ids);

    /**
     * 启用/禁用用户
     *
     * @param id     用户ID
     * @param status 状态 (1:启用 0:禁用)
     * @return 是否成功
     */
    boolean updateUserStatus(Long id, Integer status);

    /**
     * 批量更新用户状态
     *
     * @param ids    用户ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdateUserStatus(List<Long> ids, Integer status);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeId);

    /**
     * 检查手机号是否存在
     *
     * @param mobile 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isMobileExists(String mobile, Long excludeId);

    /**
     * 根据部门ID获取用户列表
     *
     * @param departmentId 部门ID
     * @return 用户列表
     */
    List<UserVO> getUsersByDepartmentId(Long departmentId);

    /**
     * 重置用户密码
     *
     * @param id 用户ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean resetPassword(Long id, String newPassword);
}
