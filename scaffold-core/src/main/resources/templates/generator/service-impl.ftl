package ${packageName}.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.monitor.annotation.Monitor;
import ${packageName}.entity.${entityName};
import ${packageName}.mapper.${mapperName};
import ${packageName}.service.${serviceName};
import ${packageName}.dto.${createRequestName};
import ${packageName}.dto.${updateRequestName};
import ${packageName}.dto.${queryRequestName};
import ${packageName}.vo.${voName};
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * ${tableInfo.tableComment!entityName}服务实现类
 * 
 * <AUTHOR>
 * @date ${date}
 */
@Slf4j
@Service
public class ${serviceImplName} extends ServiceImpl<${mapperName}, ${entityName}> implements ${serviceName} {

    @Override
    @Monitor(value = "${tableInfo.tableComment!entityName}分页查询", threshold = 1000)
    public IPage<${voName}> get${entityName}Page(${queryRequestName} request) {
        Page<${entityName}> page = new Page<>(request.getCurrent(), request.getSize());
        IPage<${entityName}> entityPage = baseMapper.select${entityName}Page(page, request);

        // 转换为VO
        Page<${voName}> voPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<${voName}> voList = entityPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    @Monitor(value = "获取${tableInfo.tableComment!entityName}详情")
    public ${voName} get${entityName}ById(${tableInfo.primaryKey.javaType} id) {
        if (id == null || id <= 0) {
            throw new ServiceException("${tableInfo.tableComment!entityName}ID不能为空");
        }

        ${entityName} ${entityNameLower} = getById(id);
        if (${entityNameLower} == null) {
            throw new ServiceException("${tableInfo.tableComment!entityName}不存在");
        }

        return convertToVO(${entityNameLower});
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "创建${tableInfo.tableComment!entityName}")
    public ${voName} create${entityName}(${createRequestName} request) {
        if (request == null) {
            throw new ServiceException("创建请求不能为空");
        }

        // 创建${tableInfo.tableComment!entityName}
        ${entityName} ${entityNameLower} = new ${entityName}();
        BeanUtils.copyProperties(request, ${entityNameLower});

        boolean success = save(${entityNameLower});
        if (!success) {
            throw new ServiceException("创建${tableInfo.tableComment!entityName}失败");
        }

        log.info("创建${tableInfo.tableComment!entityName}成功，ID：{}", ${entityNameLower}.getId());
        return get${entityName}ById(${entityNameLower}.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新${tableInfo.tableComment!entityName}")
    public ${voName} update${entityName}(${updateRequestName} request) {
        if (request == null) {
            throw new ServiceException("更新请求不能为空");
        }
        if (request.getId() == null || request.getId() <= 0) {
            throw new ServiceException("${tableInfo.tableComment!entityName}ID不能为空");
        }

        // 检查${tableInfo.tableComment!entityName}是否存在
        ${entityName} existing${entityName} = getById(request.getId());
        if (existing${entityName} == null) {
            throw new ServiceException("${tableInfo.tableComment!entityName}不存在");
        }

        // 更新${tableInfo.tableComment!entityName}
        BeanUtils.copyProperties(request, existing${entityName});

        boolean success = updateById(existing${entityName});
        if (!success) {
            throw new ServiceException("更新${tableInfo.tableComment!entityName}失败");
        }

        log.info("更新${tableInfo.tableComment!entityName}成功，ID：{}", request.getId());
        return get${entityName}ById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "删除${tableInfo.tableComment!entityName}")
    public boolean delete${entityName}(${tableInfo.primaryKey.javaType} id) {
        if (id == null || id <= 0) {
            throw new ServiceException("${tableInfo.tableComment!entityName}ID不能为空");
        }

        ${entityName} ${entityNameLower} = getById(id);
        if (${entityNameLower} == null) {
            throw new ServiceException("${tableInfo.tableComment!entityName}不存在");
        }

        boolean success = removeById(id);
        if (success) {
            log.info("删除${tableInfo.tableComment!entityName}成功，ID：{}", id);
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "批量删除${tableInfo.tableComment!entityName}")
    public int batchDelete${entityName}s(List<${tableInfo.primaryKey.javaType}> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("${tableInfo.tableComment!entityName}ID列表不能为空");
        }

        boolean success = removeByIds(ids);
        int count = success ? ids.size() : 0;

        log.info("批量删除${tableInfo.tableComment!entityName}完成，删除数量：{}", count);
        return count;
    }

    /**
     * 将实体转换为VO
     */
    private ${voName} convertToVO(${entityName} entity) {
        if (entity == null) {
            return null;
        }

        ${voName} vo = new ${voName}();
        BeanUtils.copyProperties(entity, vo);

        // 可以在这里添加额外的转换逻辑，例如状态名称等

        return vo;
    }
}
