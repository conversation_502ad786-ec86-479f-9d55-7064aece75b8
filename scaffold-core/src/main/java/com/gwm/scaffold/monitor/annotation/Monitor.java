package com.gwm.scaffold.monitor.annotation;

import java.lang.annotation.*;

/**
 * 性能监控注解
 * 
 * 用于标识需要进行性能监控的方法
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Monitor {

    /**
     * 监控名称
     */
    String value() default "";

    /**
     * 性能阈值（毫秒），超过此值会记录警告日志
     */
    long threshold() default 1000;

    /**
     * 是否启用日志记录
     */
    boolean logEnabled() default true;

    /**
     * 是否记录参数
     */
    boolean logArgs() default false;

    /**
     * 是否记录返回值
     */
    boolean logResult() default false;
}
