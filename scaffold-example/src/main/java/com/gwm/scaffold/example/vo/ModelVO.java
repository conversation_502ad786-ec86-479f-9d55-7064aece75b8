package com.gwm.scaffold.example.vo;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 视图对象VO
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "信息")
public class ModelVO {

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * name
     */
    @Schema(description = "name")
    private String name;

    /**
     * seriesId
     */
    @Schema(description = "seriesId")
    private Long seriesId;

    /**
     * yearModel
     */
    @Schema(description = "yearModel")
    private Integer yearModel;

    /**
     * displacement
     */
    @Schema(description = "displacement")
    private BigDecimal displacement;

    /**
     * maxPower
     */
    @Schema(description = "maxPower")
    private Integer maxPower;

    /**
     * maxTorque
     */
    @Schema(description = "maxTorque")
    private Integer maxTorque;

    /**
     * transmission
     */
    @Schema(description = "transmission")
    private String transmission;

    /**
     * gearCount
     */
    @Schema(description = "gearCount")
    private Integer gearCount;

    /**
     * driveType
     */
    @Schema(description = "driveType")
    private String driveType;

    /**
     * fuelType
     */
    @Schema(description = "fuelType")
    private String fuelType;

    /**
     * bodyStructure
     */
    @Schema(description = "bodyStructure")
    private String bodyStructure;

    /**
     * seatCount
     */
    @Schema(description = "seatCount")
    private Integer seatCount;

    /**
     * length
     */
    @Schema(description = "length")
    private Integer length;

    /**
     * width
     */
    @Schema(description = "width")
    private Integer width;

    /**
     * height
     */
    @Schema(description = "height")
    private Integer height;

    /**
     * wheelbase
     */
    @Schema(description = "wheelbase")
    private Integer wheelbase;

    /**
     * curbWeight
     */
    @Schema(description = "curbWeight")
    private Integer curbWeight;

    /**
     * tankCapacity
     */
    @Schema(description = "tankCapacity")
    private Integer tankCapacity;

    /**
     * fuelConsumption
     */
    @Schema(description = "fuelConsumption")
    private BigDecimal fuelConsumption;

    /**
     * guidePrice
     */
    @Schema(description = "guidePrice")
    private BigDecimal guidePrice;

    /**
     * launchTime
     */
    @Schema(description = "launchTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date launchTime;

    /**
     * discontinueTime
     */
    @Schema(description = "discontinueTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date discontinueTime;

    /**
     * imageUrls
     */
    @Schema(description = "imageUrls")
    private String imageUrls;

    /**
     * configDetails
     */
    @Schema(description = "configDetails")
    private String configDetails;

    /**
     * status
     */
    @Schema(description = "status")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    private Integer sortWeight;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

    /**
     * createTime
     */
    @Schema(description = "createTime")
    private LocalDateTime createTime;

    /**
     * createUserCode
     */
    @Schema(description = "createUserCode")
    private String createUserCode;

    /**
     * createUserName
     */
    @Schema(description = "createUserName")
    private String createUserName;

    /**
     * updateTime
     */
    @Schema(description = "updateTime")
    private LocalDateTime updateTime;

    /**
     * updateUserCode
     */
    @Schema(description = "updateUserCode")
    private String updateUserCode;

    /**
     * updateUserName
     */
    @Schema(description = "updateUserName")
    private String updateUserName;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

}
