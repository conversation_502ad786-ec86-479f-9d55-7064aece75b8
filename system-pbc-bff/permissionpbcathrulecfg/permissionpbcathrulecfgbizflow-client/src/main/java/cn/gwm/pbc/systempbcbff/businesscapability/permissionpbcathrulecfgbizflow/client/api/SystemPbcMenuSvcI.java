package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.menu.MenuInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuDeleteDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuDetailDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu.MenuSaveDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.util.List;
import java.util.Map;

import com.gwm.framework.core.exception.BusinessException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 权限PBC-应用菜单管理服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.15
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcMenuSvcI-1702610562185")
@RequestMapping(path = "/PermissionPBCAthRuleCfgBizFlow")
public interface SystemPbcMenuSvcI {

    /**
     * 权限中心-创建/编辑菜单 system/menu/save
     *
     * @param request request
     * @return Response
     * @throws BusinessException
     */
    @PostMapping(path = "/SystemPbcMenuSvc/save/v1")
    Response<Void> save(@RequestBody(required = true) MenuSaveDTO request) throws BusinessException;

    /**
     * 权限中心-删除菜单 system/menu/delete
     *
     * @param request request
     * @return Responsea
     */
    @PostMapping(path = "/SystemPbcMenuSvc/delete/v1")
    Response<Void> delete(@RequestBody(required = true) MenuDeleteDTO request);

    /**
     * 权限中心-获取菜单详情 system/menu/detail
     *
     * @param request request
     * @return Response 权限中心-获取菜单详情
     */
    @PostMapping(path = "/SystemPbcMenuSvc/detail/v1")
    Response<MenuInfoItemVO> detail(@RequestBody(required = true) MenuDetailDTO request);

    /**
     * 权限中心-获取当前登录用户菜单树 system/menu/tree/user
     *
     * @return Response 权限中心-获取当前登录用户菜单树
     */
    @PostMapping(path = "/SystemPbcMenuSvc/treeUser/v1")
    Response<Map<String, Object>> treeUser();

    /**
     * 权限中心-查询平台所有菜单树 system/menu/tree/all
     *
     * @return Response 权限中心-查询平台所有菜单树
     */
    @PostMapping(path = "/SystemPbcMenuSvc/treeAll/v1")
    Response<List<MenuInfoItemVO>> treeAll();
}
