package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("规则编辑请求参数对象")
public class RuleEditForm implements Serializable {

    /**
     * 操作人账号
     */
    @JsonIgnore
    @ApiModelProperty("操作人账号")
    private String account;

    /**
     * 规则ID
     */
    @ApiModelProperty("规则ID")
    private Long id;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    private String name;

    /**
     * 规则内容
     */
    @ApiModelProperty("规则内容")
    private String content;

    /**
     * 是否启用（0禁用，1启用，默认1）
     */
    @ApiModelProperty("是否启用（0禁用，1启用，默认1）")
    private Integer enable;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}