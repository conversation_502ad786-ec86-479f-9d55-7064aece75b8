package com.gwm.scaffold.tools.vo;

import com.alibaba.cola.dto.DTO;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 枚举变量
 *
 * <AUTHOR>
 * @date 2025/4/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class EnumDTO<T> extends DTO {

    private String name;

    private T value;

    private String type;
}
