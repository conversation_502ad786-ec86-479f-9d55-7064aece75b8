package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "RuleEditFormClientDTO", desc = "null")
@Data
@ApiModel("规则编辑请求参数对象")
public class RuleEditFormClientDTO implements Serializable {

    /**
     * 操作人账号
     */
    @Field(name = "account", desc = "")
    @JsonIgnore
    @ApiModelProperty("操作人账号")
    private String account;

    /**
     * 规则ID
     */
    @Field(name = "规则ID", desc = "规则ID")
    @ApiModelProperty("规则ID")
    private Long id;

    /**
     * 规则名称
     */
    @Field(name = "规则名称", desc = "规则名称")
    @ApiModelProperty("规则名称")
    private String name;

    /**
     * 规则内容
     */
    @Field(name = "规则内容", desc = "规则内容")
    @ApiModelProperty("规则内容")
    private String content;

    /**
     * 是否启用（0禁用，1启用，默认1）
     */
    @Field(name = "是否启用（0禁用，1启用，默认1）", desc = "是否启用（0禁用，1启用，默认1）")
    @ApiModelProperty("是否启用（0禁用，1启用，默认1）")
    private Integer enable;

    /**
     * 备注
     */
    @Field(name = "备注", desc = "备注")
    @ApiModelProperty("备注")
    private String remark;
}