package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "UserBindPlatformDTO", desc = "")
@Data
public class UserBindPlatformDTO implements Serializable {

    /**
     * userIds
     */
    @Field(name = "userIds", desc = "")
    @Alias("user_ids")
    private String[] userIds;

    /**
     * groupIds
     */
    @Field(name = "groupIds", desc = "")
    @Alias("group_ids")
    private String[] groupIds;

}
