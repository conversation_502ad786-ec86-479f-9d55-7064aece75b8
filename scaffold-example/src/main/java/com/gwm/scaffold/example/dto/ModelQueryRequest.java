package com.gwm.scaffold.example.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import com.gwm.scaffold.web.cmd.AbstractPageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "查询请求")
public class ModelQueryRequest extends AbstractPageRequest {

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * name
     */
    @Schema(description = "name")
    private String name;

    /**
     * seriesId
     */
    @Schema(description = "seriesId")
    private Long seriesId;

    /**
     * yearModel
     */
    @Schema(description = "yearModel")
    private Integer yearModel;

    /**
     * displacement
     */
    @Schema(description = "displacement")
    private BigDecimal displacement;

    /**
     * maxPower
     */
    @Schema(description = "maxPower")
    private Integer maxPower;

    /**
     * maxTorque
     */
    @Schema(description = "maxTorque")
    private Integer maxTorque;

    /**
     * transmission
     */
    @Schema(description = "transmission")
    private String transmission;

    /**
     * gearCount
     */
    @Schema(description = "gearCount")
    private Integer gearCount;

    /**
     * driveType
     */
    @Schema(description = "driveType")
    private String driveType;

    /**
     * fuelType
     */
    @Schema(description = "fuelType")
    private String fuelType;

    /**
     * bodyStructure
     */
    @Schema(description = "bodyStructure")
    private String bodyStructure;

    /**
     * seatCount
     */
    @Schema(description = "seatCount")
    private Integer seatCount;

    /**
     * length
     */
    @Schema(description = "length")
    private Integer length;

    /**
     * width
     */
    @Schema(description = "width")
    private Integer width;

    /**
     * height
     */
    @Schema(description = "height")
    private Integer height;

    /**
     * wheelbase
     */
    @Schema(description = "wheelbase")
    private Integer wheelbase;

    /**
     * curbWeight
     */
    @Schema(description = "curbWeight")
    private Integer curbWeight;

    /**
     * tankCapacity
     */
    @Schema(description = "tankCapacity")
    private Integer tankCapacity;

    /**
     * fuelConsumption
     */
    @Schema(description = "fuelConsumption")
    private BigDecimal fuelConsumption;

    /**
     * guidePrice
     */
    @Schema(description = "guidePrice")
    private BigDecimal guidePrice;

    /**
     * launchTime
     */
    @Schema(description = "launchTime")
    private Date launchTime;

    /**
     * launchTime开始时间
     */
    @Schema(description = "launchTime开始时间")
    private Date launchTimeStart;

    /**
     * launchTime结束时间
     */
    @Schema(description = "launchTime结束时间")
    private Date launchTimeEnd;

    /**
     * discontinueTime
     */
    @Schema(description = "discontinueTime")
    private Date discontinueTime;

    /**
     * discontinueTime开始时间
     */
    @Schema(description = "discontinueTime开始时间")
    private Date discontinueTimeStart;

    /**
     * discontinueTime结束时间
     */
    @Schema(description = "discontinueTime结束时间")
    private Date discontinueTimeEnd;

    /**
     * imageUrls
     */
    @Schema(description = "imageUrls")
    private String imageUrls;

    /**
     * configDetails
     */
    @Schema(description = "configDetails")
    private String configDetails;

    /**
     * status
     */
    @Schema(description = "status")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    private Integer sortWeight;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

    /**
     * deletedTime开始时间
     */
    @Schema(description = "deletedTime开始时间")
    private LocalDateTime deletedTimeStart;

    /**
     * deletedTime结束时间
     */
    @Schema(description = "deletedTime结束时间")
    private LocalDateTime deletedTimeEnd;

}
