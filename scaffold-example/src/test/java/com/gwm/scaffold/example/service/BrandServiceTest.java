package com.gwm.scaffold.example.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.example.vo.BrandVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 品牌服务测试类
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@SpringBootTest
@Transactional
public class BrandServiceTest {

    @Autowired
    private BrandService brandService;

    /**
     * 测试创建品牌_成功场景
     */
    @Test
    public void testCreateBrand_Success() {
        // 准备测试数据
        BrandCreateRequest request = new BrandCreateRequest();
        request.setName("测试品牌");
        request.setEnglishName("Test Brand");
        request.setCountry("中国");
        request.setFoundedYear(2020);
        request.setWebsite("https://www.testbrand.com");
        request.setDescription("这是一个测试品牌");
        request.setStatus("ACTIVE");
        request.setSortWeight(100);

        // 执行测试
        BrandVO result = brandService.createBrand(request);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals("测试品牌", result.getName());
        assertEquals("Test Brand", result.getEnglishName());
        assertEquals("中国", result.getCountry());
        assertEquals(2020, result.getFoundedYear());
        assertEquals("ACTIVE", result.getStatus());
        assertEquals("启用", result.getStatusName());
    }

    /**
     * 测试创建品牌_品牌名称重复
     */
    @Test
    public void testCreateBrand_DuplicateName() {
        // 先创建一个品牌
        BrandCreateRequest request1 = new BrandCreateRequest();
        request1.setName("重复品牌");
        request1.setCountry("中国");
        brandService.createBrand(request1);

        // 尝试创建同名品牌
        BrandCreateRequest request2 = new BrandCreateRequest();
        request2.setName("重复品牌");
        request2.setCountry("美国");

        // 验证抛出异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            brandService.createBrand(request2);
        });
        assertTrue(exception.getMessage().contains("品牌名称已存在"));
    }

    /**
     * 测试更新品牌_成功场景
     */
    @Test
    public void testUpdateBrand_Success() {
        // 先创建一个品牌
        BrandCreateRequest createRequest = new BrandCreateRequest();
        createRequest.setName("原始品牌");
        createRequest.setCountry("中国");
        BrandVO created = brandService.createBrand(createRequest);

        // 更新品牌
        BrandUpdateRequest updateRequest = new BrandUpdateRequest();
        updateRequest.setId(created.getId());
        updateRequest.setName("更新后品牌");
        updateRequest.setCountry("美国");
        updateRequest.setFoundedYear(2021);

        // 执行更新
        BrandVO result = brandService.updateBrand(updateRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(created.getId(), result.getId());
        assertEquals("更新后品牌", result.getName());
        assertEquals("美国", result.getCountry());
        assertEquals(2021, result.getFoundedYear());
    }

    /**
     * 测试分页查询品牌
     */
    @Test
    public void testGetBrandPage_Success() {
        // 创建测试数据
        for (int i = 1; i <= 5; i++) {
            BrandCreateRequest request = new BrandCreateRequest();
            request.setName("品牌" + i);
            request.setCountry("中国");
            request.setStatus("ACTIVE");
            brandService.createBrand(request);
        }

        // 执行查询
        BrandQueryRequest queryRequest = new BrandQueryRequest();
        queryRequest.setCurrent(1L);
        queryRequest.setSize(3L);
        queryRequest.setStatus("ACTIVE");

        IPage<BrandVO> result = brandService.getBrandPage(queryRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getTotal() >= 5);
        assertEquals(3, result.getSize());
        assertFalse(result.getRecords().isEmpty());
    }

    /**
     * 测试删除品牌_成功场景
     */
    @Test
    public void testDeleteBrand_Success() {
        // 先创建一个品牌
        BrandCreateRequest createRequest = new BrandCreateRequest();
        createRequest.setName("待删除品牌");
        createRequest.setCountry("中国");
        BrandVO created = brandService.createBrand(createRequest);

        // 执行删除
        boolean result = brandService.deleteBrand(created.getId());

        // 验证结果
        assertTrue(result);

        // 验证品牌已被删除
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            brandService.getBrandById(created.getId());
        });
        assertTrue(exception.getMessage().contains("不存在"));
    }

    /**
     * 测试批量删除品牌
     */
    @Test
    public void testBatchDeleteBrands_Success() {
        // 创建测试数据
        BrandCreateRequest request1 = new BrandCreateRequest();
        request1.setName("批量删除1");
        request1.setCountry("中国");
        BrandVO brand1 = brandService.createBrand(request1);

        BrandCreateRequest request2 = new BrandCreateRequest();
        request2.setName("批量删除2");
        request2.setCountry("中国");
        BrandVO brand2 = brandService.createBrand(request2);

        // 执行批量删除
        List<Long> ids = Arrays.asList(brand1.getId(), brand2.getId());
        int result = brandService.batchDeleteBrands(ids);

        // 验证结果
        assertEquals(2, result);
    }

    /**
     * 测试检查品牌名称是否存在
     */
    @Test
    public void testCheckBrandNameExists() {
        // 创建一个品牌
        BrandCreateRequest request = new BrandCreateRequest();
        request.setName("存在的品牌");
        request.setCountry("中国");
        BrandVO created = brandService.createBrand(request);

        // 测试存在的名称
        Boolean exists = brandService.checkBrandNameExists("存在的品牌", null);
        assertTrue(exists);

        // 测试不存在的名称
        Boolean notExists = brandService.checkBrandNameExists("不存在的品牌", null);
        assertFalse(notExists);

        // 测试排除自身的情况
        Boolean excludeSelf = brandService.checkBrandNameExists("存在的品牌", created.getId());
        assertFalse(excludeSelf);
    }

    /**
     * 测试更新品牌状态
     */
    @Test
    public void testUpdateBrandStatus_Success() {
        // 创建一个品牌
        BrandCreateRequest request = new BrandCreateRequest();
        request.setName("状态测试品牌");
        request.setCountry("中国");
        request.setStatus("ACTIVE");
        BrandVO created = brandService.createBrand(request);

        // 更新状态为禁用
        Boolean result = brandService.updateBrandStatus(created.getId(), "INACTIVE");
        assertTrue(result);

        // 验证状态已更新
        BrandVO updated = brandService.getBrandById(created.getId());
        assertEquals("INACTIVE", updated.getStatus());
        assertEquals("禁用", updated.getStatusName());
    }

    /**
     * 测试获取启用状态的品牌列表
     */
    @Test
    public void testGetActiveBrandList() {
        // 创建测试数据
        BrandCreateRequest activeRequest = new BrandCreateRequest();
        activeRequest.setName("启用品牌");
        activeRequest.setCountry("中国");
        activeRequest.setStatus("ACTIVE");
        brandService.createBrand(activeRequest);

        BrandCreateRequest inactiveRequest = new BrandCreateRequest();
        inactiveRequest.setName("禁用品牌");
        inactiveRequest.setCountry("中国");
        inactiveRequest.setStatus("INACTIVE");
        brandService.createBrand(inactiveRequest);

        // 执行查询
        List<BrandVO> result = brandService.getActiveBrandList();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 验证所有返回的品牌都是启用状态
        result.forEach(brand -> assertEquals("ACTIVE", brand.getStatus()));
    }

    /**
     * 测试根据国家查询品牌列表
     */
    @Test
    public void testGetBrandListByCountry() {
        // 创建测试数据
        BrandCreateRequest chinaRequest = new BrandCreateRequest();
        chinaRequest.setName("中国品牌");
        chinaRequest.setCountry("中国");
        chinaRequest.setStatus("ACTIVE");
        brandService.createBrand(chinaRequest);

        BrandCreateRequest usaRequest = new BrandCreateRequest();
        usaRequest.setName("美国品牌");
        usaRequest.setCountry("美国");
        usaRequest.setStatus("ACTIVE");
        brandService.createBrand(usaRequest);

        // 执行查询
        List<BrandVO> result = brandService.getBrandListByCountry("中国");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 验证所有返回的品牌都是中国的
        result.forEach(brand -> assertEquals("中国", brand.getCountry()));
    }

}
