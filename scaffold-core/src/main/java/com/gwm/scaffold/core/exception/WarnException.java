package com.gwm.scaffold.core.exception;

import lombok.*;

/**
 * 警告异常
 * 
 * 用于表示警告级别的异常，通常不会中断业务流程，但需要提醒用户注意
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class WarnException extends RuntimeException {
    
    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 错误详情
     */
    private String detailMessage;

    /**
     * 根据异常构造警告异常
     *
     * @param e 原始异常
     */
    public WarnException(Exception e) {
        if (null != e) {
            message = e.getMessage();
        }
    }

    /**
     * 根据错误信息构造警告异常
     *
     * @param message 错误信息
     */
    public WarnException(String message) {
        this.message = message;
    }

    /**
     * 根据错误信息和错误码构造警告异常
     *
     * @param message 错误信息
     * @param code    错误码
     */
    public WarnException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }
}
