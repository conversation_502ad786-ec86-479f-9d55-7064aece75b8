package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "GroupUserTreeDTO", desc = "")
@Data
public class GroupUserTreeDTO implements Serializable {

    /**
     * 组织ID
     */
    @Field(name = "组织ID", desc = "组织ID")
    @Alias("group_id")
    private Long groupId;

}
