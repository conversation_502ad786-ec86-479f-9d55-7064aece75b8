package com.gwm.scaffold.auth.config;

import com.gwm.scaffold.auth.interceptor.LoginInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

import java.util.List;

/**
 * 认证模块Web配置
 * 
 * 配置登录拦截器和白名单
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.auth.enabled", havingValue = "true", matchIfMissing = true)
public class AuthWebConfig implements WebMvcConfigurer {

    @Autowired
    private LoginInterceptor loginInterceptor;

    @Autowired
    private AuthProperties authProperties;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 默认白名单路径
        String[] defaultWhiteList = {
            "/error",
            "/favicon.ico",
            "/actuator/**",
            "/h2-console/**",
            "/druid/**",
            "/swagger-ui.html",
            "/doc.html",
            "/webjars/**",
            "/swagger-resources/**",
            "/v2/api-docs",
            "/v3/api-docs/**",
            "/swagger-ui/**"
        };

        // 合并默认白名单和配置的白名单
        java.util.Set<String> allWhiteList = new java.util.HashSet<>(java.util.Arrays.asList(defaultWhiteList));
        List<String> configWhiteList = authProperties.getWhiteList();
        if (configWhiteList != null && !configWhiteList.isEmpty()) {
            allWhiteList.addAll(configWhiteList);
            log.info("从配置文件读取到白名单：{}", configWhiteList);
        } else {
            log.warn("配置文件中没有读取到白名单配置");
        }

        // 注册登录拦截器
        registry.addInterceptor(loginInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(allWhiteList.toArray(new String[0]));

        log.info("登录拦截器已注册，白名单路径：{}", allWhiteList);
    }
}
