# 05 数据传输对象规范

## DTO设计原则

### 核心原则
- DTO应专注于数据传输，不包含业务逻辑
- 每种DTO应有明确的使用场景和目的
- 字段命名应与前端展示和使用方式保持一致
- 包含必要的校验注解确保数据有效性
- 提供完整的文档注解便于API使用

### DTO分类
- **创建请求DTO (CreateDTO)**: 用于创建资源的请求数据
- **更新请求DTO (UpdateDTO)**: 用于更新资源的请求数据
- **查询请求DTO (QueryDTO)**: 用于查询资源的请求参数
- **响应DTO (ResponseDTO/VO)**: 用于向客户端返回数据
- **详情DTO (DetailDTO/DetailVO)**: 用于返回资源详细信息
- **列表项DTO (ListItemDTO/ListItemVO)**: 用于列表展示的简化信息

## 请求DTO设计规范

### 通用要求
- 使用合适的注解进行参数校验
- 提供清晰的API文档注解
- 包含必要的序列化/反序列化配置
- 根据业务需求设置字段默认值
- 考虑前端使用便利性

### 创建请求DTO

```java
/**
 * 品牌创建请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "品牌创建请求")
public class BrandCreateDTO {
    
    @ApiModelProperty(value = "品牌代码", required = true, example = "BMW")
    @NotBlank(message = "品牌代码不能为空")
    @Size(max = 50, message = "品牌代码长度不能超过50个字符")
    @Pattern(regexp = "^[A-Z0-9_]+$", message = "品牌代码只能包含大写字母、数字和下划线")
    private String brandCode;
    
    @ApiModelProperty(value = "品牌中文名称", required = true, example = "宝马")
    @NotBlank(message = "品牌中文名称不能为空")
    @Size(max = 100, message = "品牌中文名称长度不能超过100个字符")
    private String brandNameCn;
    
    @ApiModelProperty(value = "品牌英文名称", example = "BMW")
    @Size(max = 100, message = "品牌英文名称长度不能超过100个字符")
    private String brandNameEn;
    
    @ApiModelProperty(value = "品牌Logo URL", example = "https://example.com/logo/bmw.png")
    @Size(max = 500, message = "品牌Logo URL长度不能超过500个字符")
    private String brandLogoUrl;
    
    @ApiModelProperty(value = "品牌所属国家/地区", example = "德国")
    @Size(max = 50, message = "品牌所属国家/地区长度不能超过50个字符")
    private String brandCountry;
    
    @ApiModelProperty(value = "品牌定位", example = "LUXURY", 
                     allowableValues = "LUXURY,PREMIUM,MAINSTREAM,ECONOMY")
    @Size(max = 20, message = "品牌定位长度不能超过20个字符")
    private String brandPositioning;
}
```

### 更新请求DTO

```java
/**
 * 品牌更新请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "品牌更新请求")
public class BrandUpdateDTO {
    
    @ApiModelProperty(value = "品牌中文名称", example = "宝马")
    @Size(max = 100, message = "品牌中文名称长度不能超过100个字符")
    private String brandNameCn;
    
    @ApiModelProperty(value = "品牌英文名称", example = "BMW")
    @Size(max = 100, message = "品牌英文名称长度不能超过100个字符")
    private String brandNameEn;
    
    @ApiModelProperty(value = "品牌Logo URL", example = "https://example.com/logo/bmw.png")
    @Size(max = 500, message = "品牌Logo URL长度不能超过500个字符")
    private String brandLogoUrl;
    
    @ApiModelProperty(value = "品牌所属国家/地区", example = "德国")
    @Size(max = 50, message = "品牌所属国家/地区长度不能超过50个字符")
    private String brandCountry;
    
    @ApiModelProperty(value = "品牌定位", example = "LUXURY", 
                     allowableValues = "LUXURY,PREMIUM,MAINSTREAM,ECONOMY")
    @Size(max = 20, message = "品牌定位长度不能超过20个字符")
    private String brandPositioning;
    
    @ApiModelProperty(value = "状态(1:启用,0:停用)", example = "1")
    private Integer status;
}
```

### 查询请求DTO

```java
/**
 * 品牌查询请求DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "品牌查询请求")
public class BrandQueryDTO extends PageRequest {
    
    @ApiModelProperty(value = "品牌代码", example = "BMW")
    private String brandCode;
    
    @ApiModelProperty(value = "品牌名称关键词", example = "宝马")
    private String brandName;
    
    @ApiModelProperty(value = "品牌所属国家/地区", example = "德国")
    private String brandCountry;
    
    @ApiModelProperty(value = "品牌定位", example = "LUXURY")
    private String brandPositioning;
    
    @ApiModelProperty(value = "状态(1:启用,0:停用)", example = "1")
    private Integer status;
}
```

## 响应DTO (VO) 设计规范

### 通用要求
- 根据视图展示需求设计字段
- 包含适当的序列化配置（如日期格式化）
- 提供清晰的API文档注解
- 按需添加衍生字段，简化前端处理
- 避免返回敏感信息

### 基础响应VO

```java
/**
 * 品牌响应VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "品牌响应")
public class BrandVO {
    
    @ApiModelProperty(value = "品牌ID", example = "1")
    private Long id;
    
    @ApiModelProperty(value = "品牌代码", example = "BMW")
    private String brandCode;
    
    @ApiModelProperty(value = "品牌中文名称", example = "宝马")
    private String brandNameCn;
    
    @ApiModelProperty(value = "品牌英文名称", example = "BMW")
    private String brandNameEn;
    
    @ApiModelProperty(value = "品牌Logo URL", example = "https://example.com/logo/bmw.png")
    private String brandLogoUrl;
    
    @ApiModelProperty(value = "品牌所属国家/地区", example = "德国")
    private String brandCountry;
    
    @ApiModelProperty(value = "品牌定位", example = "LUXURY")
    private String brandPositioning;
    
    @ApiModelProperty(value = "品牌定位描述", example = "豪华品牌")
    private String brandPositioningDesc;
    
    @ApiModelProperty(value = "状态", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "状态描述", example = "启用")
    private String statusDesc;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
```

### 详情响应VO

```java
/**
 * 品牌详情响应VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "品牌详情响应")
public class BrandDetailVO extends BrandVO {
    
    @ApiModelProperty(value = "厂商列表")
    private List<ManufacturerVO> manufacturers;
    
    @ApiModelProperty(value = "创建人用户名", example = "张三")
    private String createUserName;
    
    @ApiModelProperty(value = "更新人用户名", example = "李四")
    private String updateUserName;
    
    @ApiModelProperty(value = "厂商数量", example = "5")
    private Integer manufacturerCount;
    
    @ApiModelProperty(value = "车型数量", example = "20")
    private Integer vehicleModelCount;
}
```

### 列表项VO

```java
/**
 * 品牌列表项VO（用于下拉选择等场景）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "品牌列表项")
public class BrandListItemVO {
    
    @ApiModelProperty(value = "品牌ID", example = "1")
    private Long id;
    
    @ApiModelProperty(value = "品牌代码", example = "BMW")
    private String brandCode;
    
    @ApiModelProperty(value = "品牌名称", example = "宝马")
    private String brandName;
    
    @ApiModelProperty(value = "品牌Logo URL", example = "https://example.com/logo/bmw.png")
    private String brandLogoUrl;
}
```

## 分页和批量操作DTO

### 分页请求基类

```java
/**
 * 分页请求基类
 */
@Data
@ApiModel(description = "分页请求基类")
public class PageRequest {
    
    @ApiModelProperty(value = "当前页码", example = "1")
    @Min(value = 1, message = "页码最小为1")
    private Integer current = 1;
    
    @ApiModelProperty(value = "每页条数", example = "10")
    @Min(value = 1, message = "每页条数最小为1")
    @Max(value = 100, message = "每页条数最大为100")
    private Integer size = 10;
    
    @ApiModelProperty(value = "排序字段", example = "createTime")
    private String sortField;
    
    @ApiModelProperty(value = "排序方式", example = "desc", allowableValues = "asc,desc")
    private String sortOrder;
}
```

### 分页响应

```java
/**
 * 分页响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "分页响应")
public class PageResult<T> {
    
    @ApiModelProperty(value = "数据列表")
    private List<T> records;
    
    @ApiModelProperty(value = "总记录数", example = "100")
    private Long total;
    
    @ApiModelProperty(value = "每页条数", example = "10")
    private Long size;
    
    @ApiModelProperty(value = "当前页码", example = "1")
    private Long current;
    
    @ApiModelProperty(value = "总页数", example = "10")
    private Long pages;
    
    @ApiModelProperty(value = "是否有上一页", example = "false")
    public boolean hasPrevious() {
        return current > 1;
    }
    
    @ApiModelProperty(value = "是否有下一页", example = "true")
    public boolean hasNext() {
        return current < pages;
    }
}
```

### 批量操作请求

```java
/**
 * 批量ID请求
 */
@Data
@ApiModel(description = "批量ID请求")
public class BatchIdsRequest {
    
    @ApiModelProperty(value = "ID列表", required = true, example = "[1, 2, 3]")
    @NotEmpty(message = "ID列表不能为空")
    @Size(min = 1, max = 100, message = "ID列表大小必须在1到100之间")
    private List<Long> ids;
}
```

## DTO转换规范

### 实体与DTO转换
- 使用MapStruct进行对象转换
- 显式处理复杂类型转换
- 处理枚举与字符串的转换
- 处理日期时间的格式化
- 处理集合类型的转换

### MapStruct转换器示例

```java
/**
 * 品牌对象转换器
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BrandMapper {
    
    /**
     * 实体转VO
     */
    @Mapping(target = "statusDesc", expression = "java(convertStatusToDesc(brand.getStatus()))")
    @Mapping(target = "brandPositioningDesc", expression = "java(convertPositioningToDesc(brand.getBrandPositioning()))")
    BrandVO toVO(Brand brand);
    
    /**
     * 实体列表转VO列表
     */
    List<BrandVO> toVOList(List<Brand> brands);
    
    /**
     * 创建DTO转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", constant = "1")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    @Mapping(target = "updateUserId", ignore = true)
    Brand fromCreateDTO(BrandCreateDTO createDTO);
    
    /**
     * 更新DTO转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "brandCode", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    void updateFromDTO(BrandUpdateDTO updateDTO, @MappingTarget Brand brand);
    
    /**
     * 状态转换为描述
     */
    default String convertStatusToDesc(Integer status) {
        if (status == null) return null;
        return status == 1 ? "启用" : "停用";
    }
    
    /**
     * 品牌定位转换为描述
     */
    default String convertPositioningToDesc(String positioning) {
        if (positioning == null) return null;
        
        Map<String, String> positioningMap = new HashMap<>();
        positioningMap.put("LUXURY", "豪华品牌");
        positioningMap.put("PREMIUM", "高端品牌");
        positioningMap.put("MAINSTREAM", "主流品牌");
        positioningMap.put("ECONOMY", "经济品牌");
        
        return positioningMap.getOrDefault(positioning, "未知");
    }
}
```

## 校验注解使用规范

### 常用校验注解
- `@NotNull`: 不能为null
- `@NotBlank`: 字符串不能为空白
- `@NotEmpty`: 集合不能为空
- `@Size`: 字符串长度或集合大小范围
- `@Min`/`@Max`: 数值范围
- `@Pattern`: 正则表达式模式
- `@Email`: 电子邮件格式
- `@Past`/`@Future`: 日期验证

### 校验注解最佳实践
- 根据业务需求选择合适的校验注解
- 提供明确的错误消息
- 对于复杂校验逻辑，使用自定义校验器
- 分组校验处理不同场景的校验需求
- 控制器方法参数使用`@Valid`或`@Validated`触发校验

### 自定义校验器示例

```java
/**
 * 品牌代码格式校验注解
 */
@Documented
@Constraint(validatedBy = BrandCodeValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidBrandCode {
    
    String message() default "无效的品牌代码格式";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}

/**
 * 品牌代码校验器
 */
public class BrandCodeValidator implements ConstraintValidator<ValidBrandCode, String> {
    
    private static final String BRAND_CODE_PATTERN = "^[A-Z0-9_]{2,50}$";
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // 空值由@NotNull或@NotBlank处理
        }
        
        return Pattern.matches(BRAND_CODE_PATTERN, value);
    }
}
```

## JSON序列化配置

### 日期时间格式化
- 使用`@JsonFormat`注解格式化日期时间
- 全局配置默认日期时间格式
- 特殊场景使用自定义序列化器

### 枚举序列化
- 枚举转为代码和描述
- 支持前端友好的枚举展示
- 处理枚举的反序列化

### 敏感信息处理
- 使用`@JsonIgnore`忽略敏感字段
- 或使用`@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)`
- 实现自定义序列化器脱敏敏感信息 