package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.group;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "SearchGroupClientDTO", desc = "null")
@Data
@ApiModel("查询组织管理员")
public class SearchGroupClientDTO implements Serializable {

    /**
     * 组织ID
     */
    @Field(name = "组织ID", desc = "组织ID")
    @ApiModelProperty("组织ID")
    @JsonProperty(value = "group_id")
    private Integer groupId;

}