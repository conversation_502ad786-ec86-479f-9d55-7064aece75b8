package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.department.TeamTreeDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 权限PBC-用户组管理服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcTeamSvcI-1702533187760")
@RequestMapping(path = "/PermissionPBCAthRuleCfgBizFlow")
public interface SystemPbcTeamSvcI {

    /**
     * 权限中心-查询应用下用户组树形列表 system/team/tree
     *
     * @param request request
     * @return Response 权限中心-查询应用下用户组树形列表
     */
    @PostMapping(path = "/SystemPbcTeamSvc/tree/v1")
    Response<Map<String, Object>> tree(@RequestBody(required = true) TeamTreeDTO request);
}
