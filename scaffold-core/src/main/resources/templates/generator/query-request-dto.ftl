package ${packageName}.dto;

<#list tableInfo.imports as import>
import ${import};
</#list>
import com.gwm.scaffold.web.cmd.AbstractPageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ${tableInfo.tableComment!entityName}查询请求DTO
 *
 * <AUTHOR>
 * @date ${date}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "${tableInfo.tableComment!entityName}查询请求")
public class ${queryRequestName} extends AbstractPageRequest {

<#list tableInfo.columns as column>
    <#if column.queryField && !column.createTime && !column.updateTime && !column.createUser && !column.updateUser && !column.logicDelete && !column.version>
    /**
     * ${column.columnComment!column.javaField}
     */
    @Schema(description = "${column.columnComment!column.javaField}")
    private ${column.javaType} ${column.javaField};

        <#if column.queryType == "BETWEEN" && (column.javaType == "Date" || column.javaType == "LocalDateTime")>
    /**
     * ${column.columnComment!column.javaField}开始时间
     */
    @Schema(description = "${column.columnComment!column.javaField}开始时间")
    private ${column.javaType} ${column.javaField}Start;

    /**
     * ${column.columnComment!column.javaField}结束时间
     */
    @Schema(description = "${column.columnComment!column.javaField}结束时间")
    private ${column.javaType} ${column.javaField}End;

        </#if>
    </#if>
</#list>
}
