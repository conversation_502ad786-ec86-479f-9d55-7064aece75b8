package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRoleMenuSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteRoleService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.RemoteRoleBindMenuDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.RemoteRoleMenuTreeDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.menu.RoleMenuBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.menu.RoleMenuTreeDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
/**
 * <AUTHOR>
 */
@Component("PermissionPBCAthRuleCfgBizFlowSystemPbcRoleMenuSvcBS")
public class SystemPbcRoleMenuSvcImpl implements SystemPbcRoleMenuSvc {

    @Resource
    private RemoteRoleService remoteRoleService;

    @Override
    public List<String> checkIds(RoleMenuTreeDTO request) {
    	JSONArray tree = this.tree(request);
        List<String> ids = new ArrayList<>();
        ids = this.collect(tree, ids);
        return ids;
	}

    private List<String> collect(JSONArray tree, List<String> ids) {
    	for (int i = 0; i < tree.size(); i++) {
            JSONObject jsonObject = tree.getJSONObject(i);
            ids.add(jsonObject.getString("id"));
            JSONArray children = jsonObject.getJSONArray("children");
            ids = this.collect(children, ids);
        }
        return ids;
	}

    public JSONArray tree(RoleMenuTreeDTO request) {
    	RemoteRoleMenuTreeDTO remoteDTO = new RemoteRoleMenuTreeDTO();
        BeanUtil.copyProperties(request, remoteDTO);
        CommonRlt<JSONArray> jsonObjectCommonRlt = remoteRoleService.roleMenuTree(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return jsonObjectCommonRlt.getData();
	}

    @Override
    public void bind(RoleMenuBindDTO request) {
    	RemoteRoleBindMenuDTO remoteDTO = new RemoteRoleBindMenuDTO();
        BeanUtil.copyProperties(request, remoteDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteRoleService.roleBindMenu(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}
}
