<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GWM Scaffold 脚手架介绍</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255,255,255,0.8);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .card:hover {
            transform: translateY(-5px);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .card-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .card-desc {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .card-features {
            list-style: none;
        }
        
        .card-features li {
            color: #95a5a6;
            font-size: 0.9rem;
            margin-bottom: 5px;
            padding-left: 15px;
            position: relative;
        }
        
        .card-features li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .tech-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .tech-desc {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .steps {
            counter-reset: step-counter;
        }
        
        .step {
            counter-increment: step-counter;
            background: rgba(255,255,255,0.8);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #667eea;
            position: relative;
        }
        
        .step:before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 20px;
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .step-title {
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .step-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            text-decoration: none;
            color: white;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GWM Scaffold</h1>
            <p>企业级Spring Boot脚手架 - 让开发更简单</p>
        </div>
        
        <div class="content">
            <!-- 概览部分 -->
            <div class="section">
                <h2 class="section-title">📋 项目概览</h2>
                <p style="color: #7f8c8d; line-height: 1.8; font-size: 1.1rem; margin-bottom: 30px;">
                    GWM Spring Boot 脚手架是一个企业级的项目模板，旨在帮助开发团队快速构建高质量的Spring Boot应用。
                    它集成了企业开发中常用的技术栈和最佳实践，提供了完整的基础设施和丰富的工具类库。
                </p>
                
                <div class="grid">
                    <div class="card">
                        <span class="card-icon">🔧</span>
                        <div class="card-title">开箱即用</div>
                        <div class="card-desc">引入依赖即可使用，无需复杂配置</div>
                    </div>
                    <div class="card">
                        <span class="card-icon">🛠️</span>
                        <div class="card-title">工具丰富</div>
                        <div class="card-desc">17个企业级工具类，覆盖各种开发场景</div>
                    </div>
                    <div class="card">
                        <span class="card-icon">🔐</span>
                        <div class="card-title">安全加密</div>
                        <div class="card-desc">文件加密、HMAC签名等安全工具</div>
                    </div>
                    <div class="card">
                        <span class="card-icon">🌐</span>
                        <div class="card-title">网络请求</div>
                        <div class="card-desc">HTTP工具、OkHttp封装</div>
                    </div>
                    <div class="card">
                        <span class="card-icon">📊</span>
                        <div class="card-title">数据处理</div>
                        <div class="card-desc">字符串、集合、分页等数据处理工具</div>
                    </div>
                    <div class="card">
                        <span class="card-icon">🎯</span>
                        <div class="card-title">模块化</div>
                        <div class="card-desc">按需引入功能模块，避免冗余</div>
                    </div>
                </div>
            </div>
            
            <!-- 技术栈部分 -->
            <div class="section">
                <h2 class="section-title">🏗️ 技术栈</h2>
                <div class="tech-stack">
                    <div class="tech-item">
                        <div class="tech-name">Spring Boot 2.7.18</div>
                        <div class="tech-desc">企业级应用框架</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-name">MyBatis Plus 3.5.6</div>
                        <div class="tech-desc">ORM框架增强</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-name">MySQL 8.0+</div>
                        <div class="tech-desc">主数据库</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-name">HikariCP + Druid</div>
                        <div class="tech-desc">高性能连接池</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-name">Swagger 2.9.2</div>
                        <div class="tech-desc">API文档生成</div>
                    </div>
                    <div class="tech-item">
                        <div class="tech-name">17个自研工具类</div>
                        <div class="tech-desc">企业级专用工具</div>
                    </div>
                </div>
            </div>
            
            <!-- 快速开始部分 -->
            <div class="section">
                <h2 class="section-title">🚀 快速开始</h2>
                <div class="steps">
                    <div class="step">
                        <div class="step-title">克隆项目</div>
                        <div class="step-desc">获取脚手架代码到本地</div>
                        <div class="code-block">git clone &lt;repository-url&gt; my-project<br>cd my-project</div>
                    </div>
                    <div class="step">
                        <div class="step-title">引入依赖</div>
                        <div class="step-desc">在pom.xml中添加脚手架核心依赖</div>
                        <div class="code-block">&lt;dependency&gt;<br>  &lt;groupId&gt;com.gwm.scaffold&lt;/groupId&gt;<br>  &lt;artifactId&gt;scaffold-core&lt;/artifactId&gt;<br>  &lt;version&gt;1.0.0-SNAPSHOT&lt;/version&gt;<br>&lt;/dependency&gt;</div>
                    </div>
                    <div class="step">
                        <div class="step-title">配置应用</div>
                        <div class="step-desc">在application.yml中配置脚手架功能模块</div>
                        <div class="code-block">scaffold:<br>  enabled: true<br>  modules:<br>    auth: true<br>    web: true<br>    data: true</div>
                    </div>
                    <div class="step">
                        <div class="step-title">启动应用</div>
                        <div class="step-desc">使用Maven启动Spring Boot应用</div>
                        <div class="code-block">mvn spring-boot:run</div>
                    </div>
                </div>
            </div>
            
            <!-- 导航按钮 -->
            <div class="nav-buttons">
                <a href="/api/scaffold/tools" class="btn">
                    🛠️ 查看工具类库
                </a>
                <a href="/api/scaffold/architecture" class="btn">
                    🏗️ 模块架构
                </a>
                <a href="/api/scaffold/best-practices" class="btn">
                    ⭐ 最佳实践
                </a>
                <a href="/swagger-ui.html" class="btn">
                    📚 API文档
                </a>
            </div>
        </div>
    </div>
</body>
</html>
