package com.gwm.scaffold.example.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gwm.scaffold.auth.domain.LoginResult;
import com.gwm.scaffold.auth.domain.LoginUserInfo;
import com.gwm.scaffold.auth.service.AuthenticateService;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.example.service.OpenClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.springframework.http.MediaType.APPLICATION_JSON;

/**
 * 真实认证服务实现
 * 
 * 对接SSO认证系统进行用户认证和权限校验
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "scaffold.auth.sso.enabled", havingValue = "true", matchIfMissing = false)
public class AuthenticateServiceImpl implements AuthenticateService {

    private static final String REMOTE_KEY = "key";
    private static final String REMOTE_SUCCESS = "S_0000";
    private static final String JSON_KEY_CODE = "code";
    private static final String JSON_KEY_MESSAGE = "message";
    private static final String JSON_KEY_RESULT = "result";
    private static final String JSON_KEY_DATA = "data";
    private static final String RESPONSE_SUCCESS = "200";
    private static final String EMPTY_STR = "";
    private static final int ONE = 1;

    @Value("${scaffold.auth.sso.platform-code:1}")
    private String platformCode = EMPTY_STR;

    @Value("${scaffold.auth.sso.check-token-url:}")
    private String ssoUrl = EMPTY_STR;

    @Value("${scaffold.auth.sso.get-origin-url:}")
    private String getOriginUrl = EMPTY_STR;

    @Value("${scaffold.auth.sso.domain:}")
    private String domain = EMPTY_STR;

    @Value("${openPlatform.mdm.getOrigin}")
    private String originUrl;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OpenClientService openClientService;

    /**
     * 验证token
     *
     * @param token 访问令牌
     * @return 登录结果
     */
    @Override
    public LoginResult checkToken(String token) {
        if (!StringUtils.hasText(token)) {
            log.warn("校验的token信息为空");
            return LoginResult.failure("Token不能为空");
        }
        return remoteCheckToken(token);
    }

    /**
     * 远程验证token
     */
    private LoginResult remoteCheckToken(String token) {
        try {
            String url = ssoUrl + "/authenticate/check_token?platform_code=" + platformCode + "&access_token=" + token;
            log.debug("调用SSO验证Token，URL：{}", url);
            
            ResponseEntity<JSONObject> response = restTemplate.getForEntity(url, JSONObject.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("获取用户信息访问：{}，响应失败，错误码：{}", url, response.getStatusCode());
                return LoginResult.failure("获取用户信息响应失败，错误码：" + response.getStatusCode());
            }
            
            JSONObject jsonObject = response.getBody();
            if (null == jsonObject) {
                return LoginResult.failure("获取用户信息响应失败，body为空");
            }
            
            if (!RESPONSE_SUCCESS.equals(String.valueOf(jsonObject.get(JSON_KEY_CODE)))) {
                String errorMsg = (String) jsonObject.get(JSON_KEY_MESSAGE);
                return LoginResult.failure(errorMsg != null ? errorMsg : "Token验证失败");
            }
            
            if (REMOTE_SUCCESS.equals(jsonObject.getString(REMOTE_KEY))) {
                JSONObject userInfoObject = jsonObject.getJSONObject(JSON_KEY_RESULT);
                if (null == userInfoObject) {
                    return LoginResult.failure("用户信息为空");
                }
                
                LoginUserInfo userInfo = buildUserInfo(userInfoObject, token);
                
                // 获取组织架构信息
                enrichUserWithOrganization(userInfo, userInfoObject.getString("group_id"), token);
                
                // 获取用户角色信息
                enrichUserWithRoles(userInfo, token);
                
                return LoginResult.success(token, userInfoObject.getLong("expire_time"), userInfo);
            }
            
            return LoginResult.failure("Token验证失败");
            
        } catch (Exception e) {
            log.error("远程验证Token异常，token：{}", token, e);
            return LoginResult.failure("Token验证异常：" + e.getMessage());
        }
    }

    /**
     * 构建用户基本信息
     */
    private LoginUserInfo buildUserInfo(JSONObject userInfoObject, String token) {
        return LoginUserInfo.builder()
                .userId(userInfoObject.getLong("user_id"))
                .userCode(userInfoObject.getString("user_code") != null ? 
                         userInfoObject.getString("user_code").toUpperCase() : null)
                .userName(userInfoObject.getString("user_name"))
                .isFormal(userInfoObject.getInteger("is_formal"))
                .sex(userInfoObject.getInteger("sex"))
                .dutyName(userInfoObject.getString("duty_name"))
                .token(token)
                .build();
    }

    /**
     * 丰富用户组织架构信息
     */
    private void enrichUserWithOrganization(LoginUserInfo userInfo, String groupId, String token) {
        if (!StringUtils.hasText(groupId) || !StringUtils.hasText(getOriginUrl)) {
            return;
        }
        
        try {
            Map<String, Object> paramMap = new HashMap<>(1, 1);
            paramMap.put("group_id", groupId);
            
            // 这里简化处理，实际项目中需要调用具体的组织架构服务
            JSONObject orgData = getOrganizationData(paramMap);
            if (orgData != null && orgData.getJSONObject(JSON_KEY_RESULT) != null) {
                JSONObject result = orgData.getJSONObject(JSON_KEY_RESULT);
                
                // 设置组织架构信息
                userInfo.setUnitId(result.getLong("unit_id"))
                        .setUnitName(result.getString("unit_name"))
                        .setDepartmentId(result.getLong("department_id"))
                        .setDepartmentName(result.getString("department_name"))
                        .setTeamId(result.getLong("team_id"))
                        .setTeamName(result.getString("team_name"))
                        .setParentGroupId(result.getLong("parent_id"))
                        .setParentGroupName(result.getString("parent_group_name"))
                        .setGroupId(result.getLong("id"))
                        .setGroupName(result.getString("group_name"))
                        .setDirectLeaderCode(result.getString("direct_leader"))
                        .setDirectLeaderName(result.getString("direct_leader_name"))
                        .setGrade(result.getInteger("grade"))
                        .setLevel(result.getInteger("level"));
                
                // 设置组织架构ID和名称列表
                List<Long> organizationIds = Arrays.asList(
                    result.getLong("unit_id"),
                    result.getLong("department_id"),
                    result.getLong("team_id"),
                    result.getLong("parent_id"),
                    result.getLong("id")
                );
                
                List<String> organizationNames = Arrays.asList(
                    result.getString("unit_name"),
                    result.getString("department_name"),
                    result.getString("team_name"),
                    result.getString("parent_group_name"),
                    result.getString("group_name")
                );
                
                userInfo.setOrganizationIds(organizationIds)
                        .setOrganizationNames(organizationNames);
            }
        } catch (Exception e) {
            log.warn("获取组织架构信息失败，groupId：{}", groupId, e);
        }
    }

    /**
     * 丰富用户角色信息
     */
    private void enrichUserWithRoles(LoginUserInfo userInfo, String token) {
        if (!StringUtils.hasText(userInfo.getUserCode()) || !StringUtils.hasText(domain)) {
            return;
        }
        
        try {
            Map<String, Object> roles = getUserRoles(token, userInfo.getUserCode());
            userInfo.setRoles(roles);
        } catch (Exception e) {
            log.warn("获取用户角色信息失败，userCode：{}", userInfo.getUserCode(), e);
        }
    }

    /**
     * 获取组织架构数据（模拟实现）
     */
    private JSONObject getOrganizationData(Map<String, Object> paramMap) {

        log.info("获取组织架构数据：regionUrl：{}, paramMap：{}", originUrl, paramMap);
        // 获取组织架构
        JSONObject result = openClientService.getOpenClientResult(originUrl, paramMap);
        log.info("获取组织架构数据结果：{}", result);
        return result;
    }

    /**
     * 获取用户角色信息
     */
    private Map<String, Object> getUserRoles(String token, String userCode) {
        try {
            final String userInfoUri = "/PermissionPBCAthRuleCfgBizFlow/SystemPbcManageSvc/xx/laited/v1";;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(APPLICATION_JSON);
            headers.set("accessToken", token);
            
            Map<String, Object> body = new HashMap<>(ONE, ONE);
            body.put(JSON_KEY_CODE, userCode);
            
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            ResponseEntity<JSONObject> response = restTemplate.postForEntity(domain + userInfoUri, requestEntity, JSONObject.class);
            
            if (!response.getStatusCode().is2xxSuccessful() || null == response.getBody()) {
                log.error("请求用户角色服务响应异常，参数：{}，响应：{}", JSON.toJSONString(requestEntity), JSON.toJSONString(response));
                return Collections.emptyMap();
            }
            
            JSONObject responseBody = response.getBody();
            if (responseBody.getJSONObject(JSON_KEY_DATA) != null) {
                return responseBody.getJSONObject(JSON_KEY_DATA).getInnerMap();
            }
            
        } catch (Exception e) {
            log.error("获取用户角色信息异常，userCode：{}", userCode, e);
        }
        
        return Collections.emptyMap();
    }

    @Override
    public LoginResult refreshToken(String refreshToken) {
        // 实现刷新Token逻辑
        return null;
    }

    @Override
    public boolean logout(String token) {
        // 实现登出逻辑
        return false;
    }

    @Override
    public boolean hasPermission(String userCode, String permission) {
        // 实现权限检查逻辑
        // 这里可以根据用户角色信息进行权限判断
        return true;
    }

    @Override
    public boolean hasRole(String userCode, String role) {
        // 实现角色检查逻辑
        return false;
    }
}
