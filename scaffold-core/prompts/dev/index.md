# Master Data Documentation Index

This document serves as a table of contents for the master data documentation, providing a brief overview of each document's purpose and content.

## Table of Contents

1. [01_architecture_principles.md](01_architecture_principles.md)
   - Describes the fundamental architectural principles and patterns used in the master data system
   - Covers system design principles, component organization, and integration guidelines

2. [02_package_structure.md](02_package_structure.md)
   - Details the package organization and structure of the codebase
   - Explains the purpose and responsibilities of each package
   - Provides guidelines for package creation and maintenance

3. [03_naming_conventions.md](03_naming_conventions.md)
   - Defines naming conventions for classes, methods, variables, and other code elements
   - Establishes consistent naming patterns across the codebase
   - Includes guidelines for naming database entities and API endpoints

4. [04_domain_model_design.md](04_domain_model_design.md)
   - Outlines the design principles for domain models
   - Describes entity relationships and business rules
   - Provides guidelines for creating maintainable and scalable domain models

5. [05_dto_design.md](05_dto_design.md)
   - Explains the design principles for Data Transfer Objects (DTOs)
   - Details mapping strategies between domain models and DTOs
   - Provides guidelines for DTO validation and transformation

6. [06_controller_design.md](06_controller_design.md)
   - Defines the structure and patterns for REST controllers
   - Explains request/response handling and error management
   - Provides guidelines for controller layer implementation

7. [07_service_design.md](07_service_design.md)
   - Details the service layer architecture and patterns
   - Explains business logic implementation guidelines
   - Provides transaction management and service composition rules

8. [08_repository_design.md](08_repository_design.md)
   - Describes the repository layer implementation
   - Explains data access patterns and query optimization
   - Provides guidelines for database operations and caching

9. [09_exception_handling.md](09_exception_handling.md)
   - Defines the exception handling strategy
   - Explains error classification and propagation rules
   - Provides guidelines for error logging and user feedback

10. [10_api_documentation.md](10_api_documentation.md)
    - Contains comprehensive API documentation
    - Details endpoints, request/response formats, and usage examples
    - Provides versioning and deprecation guidelines

11. [11_testing_guidelines.md](11_testing_guidelines.md)
    - Defines testing strategies and frameworks
    - Explains test organization and maintenance
    - Provides guidelines for unit, integration, and system testing

12. [12_unit_testing_guidelines.md](12_unit_testing_guidelines.md)
    - 定义单元测试书写规范和最佳实践
    - 涵盖测试技术栈、代码结构、命名规范等内容
    - 提供完整的测试代码模板和示例
    - 指导大模型生成高质量的单元测试代码

Each document provides detailed guidance on its specific area and should be consulted when implementing or modifying the corresponding components of the system.
