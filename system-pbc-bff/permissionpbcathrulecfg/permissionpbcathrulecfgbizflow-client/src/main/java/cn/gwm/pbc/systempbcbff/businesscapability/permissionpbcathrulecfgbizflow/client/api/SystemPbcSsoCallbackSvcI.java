package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user.LoginRltVoClientDTO;
import com.alibaba.bizworks.core.runtime.common.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 权限PBC-单点认证服务管理服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.15
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcSsoCallbackSvcI-1702610562242")
@RequestMapping(path = "/PermissionPBCAthRuleCfgBizFlow")
public interface SystemPbcSsoCallbackSvcI {

    /**
     * 权限中心-单点登录转换 system/sso/callback
     *
     * @return Response 权限中心-单点登录转换
     */
    @PostMapping(path = "/SystemPbcSsoCallbackSvc/ssoCallback/v1")
    Response<LoginRltVoClientDTO> ssoCallback();
}
