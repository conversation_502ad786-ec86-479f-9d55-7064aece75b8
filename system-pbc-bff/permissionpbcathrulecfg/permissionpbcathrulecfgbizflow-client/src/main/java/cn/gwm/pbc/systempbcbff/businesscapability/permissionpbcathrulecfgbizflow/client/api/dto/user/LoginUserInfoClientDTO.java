package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
public class LoginUserInfoClientDTO implements Serializable {
                        private Integer isFormal;
                        private Integer sex;
                        private Long userId;
                        private String userCode;
                        private String userName;
                        private String groupId;
                        private String groupName;
                        private String dutyName;
                        private String positionName;
}
