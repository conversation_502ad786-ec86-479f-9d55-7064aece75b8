# 06 控制器层规范

## 控制器设计原则

### 核心原则
- 控制器职责单一，专注于处理HTTP请求和响应
- 不包含业务逻辑，业务逻辑应当委托给服务层
- 不处理异常，输出异常日志后，让异常向上传播到全局异常处理器
- 负责参数校验、格式转换和响应封装
- 提供清晰的API文档和接口规范

### 控制器结构规范
- 必须继承`com.gwm.globalvehicle.common.base.BaseController`基类
- 使用`@RestController`注解标记REST控制器
- 使用`@RequestMapping`定义基础URL路径
- 对每个请求处理方法使用适当的HTTP方法注解
- 方法命名应体现其操作含义（如：getBrand、createBrand、updateBrand、deleteBrand）
- 按照资源和操作组织控制器类和方法

- 不包含业务逻辑，业务逻辑应当委托给服务层
- catch ServiceException不处理异常，输出异常日志后，让异常向上传播到全局异常处理器
- catch BusinessException不处理异常，输出异常日志后，让异常向上传播到全局异常处理器
### 异常处理原则
- **依赖全局异常处理**：让异常自然向上传播到GlobalExceptionHandler
- **参数校验异常**：使用@Valid/@Validated自动触发参数校验异常
- **业务逻辑异常**：在Service层抛出，Controller层打印异常信息后抛出

## RESTful API设计规范

### URL路径设计
- 使用名词复数表示资源集合（如`/api/v1/brands`）
- 使用资源ID标识特定资源（如`/api/v1/brands/{id}`）
- 子资源使用嵌套路径（如`/api/v1/brands/{brandId}/models`）
- 查询参数用于过滤、排序和分页
- 使用连字符（-）连接多个单词（如`/api/v1/vehicle-models`）

### HTTP方法使用
- **GET**: 获取资源，不应有副作用
- **POST**: 创建新资源或执行复杂操作
- **PUT**: 全量更新资源
- **PATCH**: 部分更新资源
- **DELETE**: 删除资源

### 状态码使用
- **200 OK**: 所有请求响应时，HTTP状态码均是200
- 具体的业务成功/失败通过响应体中的code字段表示

## 请求参数处理

### 路径参数
```java
@GetMapping("/{id}")
public AjaxResult<BrandVO> getBrand(@PathVariable Long id) {
    // 路径参数自动转换和校验
    BrandVO brand = brandService.getBrandById(id);
    return AjaxResult.success(brand);
}
```

### 分页请求处理
- 使用统一的`PageRequest`基类处理分页请求
- 包含以下标准分页参数：
  - `current`: 当前页码（默认1）
  - `size`: 每页条数（默认10，最大100）
  - `sortField`: 排序字段
  - `sortOrder`: 排序方式（asc/desc）
  - `params`: 业务参数
- 使用`@Valid`进行参数校验

```java
/**
 * 分页查询品牌列表
 */
@GetMapping
public AjaxResult<PageResult<BrandVO>> getBrands(@Valid PageRequest pageRequest) {
    // 1. 从pageRequest.getParams()获取查询参数
    Map<String, Object> params = (Map<String, Object>) pageRequest.getParams();
    
    // 2. 调用服务层查询
    Page<BrandVO> page = brandService.getBrands(pageRequest, params);
    
    // 3. 转换为统一的分页响应
    return toPageResult(page);
}
```

### 请求体参数
```java
/**
 * 创建品牌
 */
@PostMapping
public AjaxResult<Long> createBrand(@Valid @RequestBody BrandCreateDTO createDTO) {
    // 参数校验失败会自动抛出BusinessException
    Long brandId = brandService.createBrand(createDTO);
    return AjaxResult.success(brandId);
}

/**
 * 更新品牌
 */
@PutMapping("/{id}")
public AjaxResult<Void> updateBrand(@PathVariable Long id, 
                                   @Valid @RequestBody BrandUpdateDTO updateDTO) {
    brandService.updateBrand(id, updateDTO);
    return AjaxResult.success();
}
```

### 表单参数和文件上传
```java
/**
 * 上传品牌Logo
 */
@PostMapping("/{id}/logo")
public AjaxResult<String> uploadBrandLogo(@PathVariable Long id,
                                         @RequestParam("file") MultipartFile file) {
    String logoUrl = brandService.uploadLogo(id, file);
    return AjaxResult.success(logoUrl);
}

/**
 * 批量导入品牌
 */
@PostMapping("/import")
public AjaxResult<Void> importBrands(@RequestParam("file") MultipartFile file) {
    brandService.importBrands(file);
    return AjaxResult.success();
}
```

### 查询参数
```java
/**
 * 根据条件查询品牌
 */
@GetMapping("/search")
public AjaxResult<List<BrandVO>> searchBrands(
        @RequestParam(required = false) String name,
        @RequestParam(required = false) String code,
        @RequestParam(required = false) Integer status) {
    List<BrandVO> brands = brandService.searchBrands(name, code, status);
    return AjaxResult.success(brands);
}
```

## 响应封装规范

### 统一响应格式
- **强制要求**：所有控制器方法必须统一使用`com.gwm.globalvehicle.common.base.AjaxResult`作为返回值
- **禁止行为**：不允许自定义其他响应封装类
- **工具方法**：通过BaseController中提供的工具方法简化响应创建
- **响应结构**：包含code、message、data和timestamp字段

```java
// 成功响应示例
AjaxResult.success();                      // 无数据响应
AjaxResult.success(data);                 // 带数据响应

// 错误响应由全局异常处理器自动生成，Controller层不处理
```

### 分页查询规范
- 分页查询请求必须使用`com.gwm.globalvehicle.common.base.PageRequest`作为基类
- 分页查询结果必须使用`com.gwm.globalvehicle.common.base.PageResult`封装
- 使用BaseController的toPageResult方法将分页结果转换为AjaxResult响应

```java
/**
 * 分页查询示例
 */
@GetMapping
public AjaxResult<PageResult<BrandVO>> getBrands(@Valid PageRequest pageRequest) {
    Page<BrandVO> page = brandService.getBrands(pageRequest);
    return toPageResult(page);
}
```

## Controller层完整示例

### 标准CRUD控制器
```java
/**
 * 品牌管理控制器
 */
@RestController
@RequestMapping("/api/v1/brands")
@Slf4j
public class BrandController extends BaseController {
    
    @Resource
    private BrandService brandService;
    
    /**
     * 分页查询品牌列表
     */
    @GetMapping
    public AjaxResult<PageResult<BrandVO>> getBrands(@Valid PageRequest pageRequest) {
        Page<BrandVO> page = brandService.getBrands(pageRequest);
        return toPageResult(page);
    }
    
    /**
     * 根据ID查询品牌详情
     */
    @GetMapping("/{id}")
    public AjaxResult<BrandVO> getBrand(@PathVariable Long id) {
        BrandVO brand = brandService.getBrandById(id);
        return AjaxResult.success(brand);
    }
    
    /**
     * 创建品牌
     */
    @PostMapping
    public AjaxResult<Long> createBrand(@Valid @RequestBody BrandCreateDTO createDTO) {
        Long brandId = brandService.createBrand(createDTO);
        return AjaxResult.success(brandId);
    }
    
    /**
     * 更新品牌
     */
    @PutMapping("/{id}")
    public AjaxResult<Void> updateBrand(@PathVariable Long id, 
                                       @Valid @RequestBody BrandUpdateDTO updateDTO) {
        brandService.updateBrand(id, updateDTO);
        return AjaxResult.success();
    }
    
    /**
     * 删除品牌
     */
    @DeleteMapping("/{id}")
    public AjaxResult<Void> deleteBrand(@PathVariable Long id) {
        brandService.deleteBrand(id);
        return AjaxResult.success();
    }
    
    /**
     * 批量删除品牌
     */
    @DeleteMapping("/batch")
    public AjaxResult<Void> deleteBrands(@Valid @RequestBody BatchIdsRequest request) {
        brandService.deleteBrands(request.getIds());
        return AjaxResult.success();
    }
    
    /**
     * 获取品牌下拉选项
     */
    @GetMapping("/options")
    public AjaxResult<List<SelectOption>> getBrandOptions() {
        List<SelectOption> options = brandService.getBrandOptions();
        return AjaxResult.success(options);
    }
    
    /**
     * 更新品牌状态
     */
    @PatchMapping("/{id}/status")
    public AjaxResult<Void> updateBrandStatus(@PathVariable Long id,
                                             @RequestParam Integer status) {
        brandService.updateBrandStatus(id, status);
        return AjaxResult.success();
    }
}
```

## 通用请求响应参数

### 基础分页请求
PageRequest包含以下标准参数：
- `current`: 当前页码（默认1）
- `size`: 每页条数（默认10，最大100）
- `sortField`: 排序字段
- `sortOrder`: 排序方式（asc/desc）
- `params`: 业务参数（Map类型，包含具体的查询条件）

### 分页响应结果
PageResult包含以下字段：
- `records`: 数据列表
- `total`: 总记录数
- `size`: 每页条数
- `current`: 当前页码
- `pages`: 总页数
- `hasPrevious()`: 是否有上一页
- `hasNext()`: 是否有下一页

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "宝马",
        "code": "BMW",
        "status": 1
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10,
    "hasPrevious": false,
    "hasNext": true
  },
  "timestamp": 1701334523456
}
```

### 批量操作请求
BatchIdsRequest包含：
- `ids`: ID列表（最大100个）

### 通用下拉选择响应
SelectOption包含：
- `value`: 选项值
- `label`: 选项标签
- `disabled`: 是否禁用
- `groupLabel`: 分组标签

### 树形结构响应
TreeNode包含：
- `id`: 节点ID
- `label`: 节点标签
- `value`: 节点值
- `parentId`: 父节点ID
- `children`: 子节点列表
- `disabled`: 是否禁用

## Controller层开发要点

### 参数校验
- 使用`@Valid`和`@Validated`注解进行自动参数校验
- 校验失败会自动抛出BusinessException，无需手动处理
- 在DTO类中定义校验规则和错误消息

### 异常处理
- **禁止使用try-catch**：Controller层不捕获任何异常
- **自然传播**：让异常向上传播到GlobalExceptionHandler
- **参数校验**：校验失败自动抛出BusinessException
- **业务异常**：在Service层抛出，Controller层不处理

```java
// ✅ 推荐：简洁的Controller方法
@PostMapping
public AjaxResult<Long> createBrand(@Valid @RequestBody BrandCreateDTO createDTO) {
    Long brandId = brandService.createBrand(createDTO);
    return AjaxResult.success(brandId);
}

// ❌ 禁止：在Controller层捕获异常
@PostMapping
public AjaxResult<Long> createBrand(@Valid @RequestBody BrandCreateDTO createDTO) {
    try {
        Long brandId = brandService.createBrand(createDTO);
        return AjaxResult.success(brandId);
    } catch (BusinessException e) {
        return AjaxResult.error(e.getCode(), e.getMessage());
    }
}
```

### 方法命名规范
- **查询单个**：getBrand、getBrandById
- **查询列表**：getBrands、searchBrands
- **创建资源**：createBrand
- **更新资源**：updateBrand
- **删除资源**：deleteBrand、deleteBrands
- **状态更新**：updateBrandStatus
- **获取选项**：getBrandOptions
