package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.menu.RoleMenuBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.menu.RoleMenuTreeDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserUnbindDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;
import com.gwm.framework.core.domain.Result;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-角色菜单管理管理服务")
public interface SystemPbcRoleMenuSvc {
    /**
     * 权限中心-角色下绑定菜单
     * @param request
     */
    @ReturnValue(desc = "权限中心-角色下绑定菜单")
    @Method(desc = "权限中心-角色下绑定菜单", name = "权限中心-角色下绑定菜单 system/role/menu/bind")
    void bind(@Parameter(name = "request", required = true) RoleMenuBindDTO request);
    /**
     * 权限中心-查询角色绑定菜单id列表
     * @param request
     * @return List<String>
     */
    @ReturnValue(desc = "权限中心-查询角色绑定菜单id列表")
    @Method(desc = "权限中心-查询角色绑定菜单id列表", name = "权限中心-查询角色绑定菜单id列表 system/role/menu/checkIds")
    List<String> checkIds(@Parameter(name = "request", required = true) RoleMenuTreeDTO request);
}
