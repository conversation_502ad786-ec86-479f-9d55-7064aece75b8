/**
 * 品牌管理API测试数据准备脚本
 * 
 * 使用方法：
 * 1. 在Postman的Pre-request Script中引入此脚本
 * 2. 或者复制相关函数到具体的测试用例中
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */

// 测试品牌数据模板
const brandTemplates = {
    // 完整品牌信息模板
    complete: {
        name: "测试品牌{{timestamp}}",
        englishName: "Test Brand {{timestamp}}",
        country: "中国",
        foundedYear: 2020,
        website: "https://www.testbrand{{timestamp}}.com",
        logoUrl: "https://example.com/logo/test{{timestamp}}.png",
        description: "这是一个用于测试的品牌，创建时间：{{timestamp}}",
        status: "ACTIVE",
        sortWeight: 100
    },
    
    // 最小品牌信息模板
    minimal: {
        name: "最小品牌{{timestamp}}",
        country: "美国"
    },
    
    // 豪华品牌模板
    luxury: {
        name: "豪华品牌{{timestamp}}",
        englishName: "Luxury Brand {{timestamp}}",
        country: "德国",
        foundedYear: 1950,
        website: "https://www.luxury{{timestamp}}.com",
        description: "德国豪华汽车品牌",
        status: "ACTIVE",
        sortWeight: 50
    },
    
    // 电动车品牌模板
    electric: {
        name: "电动品牌{{timestamp}}",
        englishName: "Electric Brand {{timestamp}}",
        country: "美国",
        foundedYear: 2010,
        website: "https://www.electric{{timestamp}}.com",
        description: "专注于电动汽车的创新品牌",
        status: "ACTIVE",
        sortWeight: 150
    }
};

// 国家列表
const countries = [
    "中国", "美国", "德国", "日本", "韩国", 
    "法国", "意大利", "英国", "瑞典", "捷克"
];

// 状态列表
const statuses = ["ACTIVE", "INACTIVE"];

/**
 * 生成随机时间戳
 */
function generateTimestamp() {
    return new Date().getTime();
}

/**
 * 生成随机整数
 */
function randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 从数组中随机选择一个元素
 */
function randomChoice(array) {
    return array[Math.floor(Math.random() * array.length)];
}

/**
 * 替换模板中的占位符
 */
function replacePlaceholders(template, replacements = {}) {
    let result = JSON.stringify(template);
    
    // 默认替换时间戳
    const timestamp = replacements.timestamp || generateTimestamp();
    result = result.replace(/\{\{timestamp\}\}/g, timestamp);
    
    // 替换其他占位符
    Object.keys(replacements).forEach(key => {
        const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        result = result.replace(regex, replacements[key]);
    });
    
    return JSON.parse(result);
}

/**
 * 生成测试品牌数据
 */
function generateBrandData(type = 'complete', customData = {}) {
    const template = brandTemplates[type] || brandTemplates.complete;
    const timestamp = generateTimestamp();
    
    // 基础替换数据
    const replacements = {
        timestamp: timestamp,
        randomCountry: randomChoice(countries),
        randomStatus: randomChoice(statuses),
        randomYear: randomInt(1900, 2024),
        randomWeight: randomInt(1, 999),
        ...customData
    };
    
    return replacePlaceholders(template, replacements);
}

/**
 * 生成批量测试数据
 */
function generateBatchBrandData(count = 5, type = 'complete') {
    const brands = [];
    for (let i = 0; i < count; i++) {
        const customData = {
            timestamp: generateTimestamp() + i,
            index: i + 1
        };
        brands.push(generateBrandData(type, customData));
    }
    return brands;
}

/**
 * 生成无效数据用于测试校验
 */
function generateInvalidBrandData(invalidType) {
    const baseData = generateBrandData('minimal');
    
    switch (invalidType) {
        case 'missingName':
            delete baseData.name;
            break;
        case 'missingCountry':
            delete baseData.country;
            break;
        case 'invalidYear':
            baseData.foundedYear = 2030; // 未来年份
            break;
        case 'invalidUrl':
            baseData.website = 'invalid-url';
            break;
        case 'invalidStatus':
            baseData.status = 'INVALID_STATUS';
            break;
        case 'longName':
            baseData.name = 'a'.repeat(100); // 超长名称
            break;
        case 'emptyName':
            baseData.name = '';
            break;
        default:
            break;
    }
    
    return baseData;
}

/**
 * 设置环境变量
 */
function setEnvironmentVariables(data) {
    Object.keys(data).forEach(key => {
        pm.environment.set(key, data[key]);
    });
}

/**
 * 获取预定义的测试品牌数据
 */
function getPredefinedBrands() {
    return [
        {
            name: "奔驰",
            englishName: "Mercedes-Benz",
            country: "德国",
            foundedYear: 1926,
            website: "https://www.mercedes-benz.com",
            description: "德国豪华汽车品牌",
            status: "ACTIVE",
            sortWeight: 100
        },
        {
            name: "宝马",
            englishName: "BMW",
            country: "德国",
            foundedYear: 1916,
            website: "https://www.bmw.com",
            description: "德国豪华汽车制造商",
            status: "ACTIVE",
            sortWeight: 90
        },
        {
            name: "特斯拉",
            englishName: "Tesla",
            country: "美国",
            foundedYear: 2003,
            website: "https://www.tesla.com",
            description: "美国电动汽车制造商",
            status: "ACTIVE",
            sortWeight: 200
        },
        {
            name: "丰田",
            englishName: "Toyota",
            country: "日本",
            foundedYear: 1937,
            website: "https://www.toyota.com",
            description: "日本汽车制造商",
            status: "ACTIVE",
            sortWeight: 80
        },
        {
            name: "比亚迪",
            englishName: "BYD",
            country: "中国",
            foundedYear: 1995,
            website: "https://www.byd.com",
            description: "中国新能源汽车制造商",
            status: "ACTIVE",
            sortWeight: 150
        }
    ];
}

// 使用示例（在Postman的Pre-request Script中使用）

// 示例1：生成完整的测试品牌数据
// const brandData = generateBrandData('complete');
// pm.environment.set('testBrandData', JSON.stringify(brandData));

// 示例2：生成最小测试数据
// const minimalData = generateBrandData('minimal');
// pm.environment.set('minimalBrandData', JSON.stringify(minimalData));

// 示例3：生成无效数据用于校验测试
// const invalidData = generateInvalidBrandData('missingName');
// pm.environment.set('invalidBrandData', JSON.stringify(invalidData));

// 示例4：生成批量测试数据
// const batchData = generateBatchBrandData(3, 'luxury');
// pm.environment.set('batchBrandData', JSON.stringify(batchData));

// 示例5：设置预定义品牌数据
// const predefinedBrands = getPredefinedBrands();
// pm.environment.set('predefinedBrands', JSON.stringify(predefinedBrands));

// 导出函数（如果在Node.js环境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateBrandData,
        generateBatchBrandData,
        generateInvalidBrandData,
        getPredefinedBrands,
        setEnvironmentVariables,
        brandTemplates,
        countries,
        statuses
    };
}
