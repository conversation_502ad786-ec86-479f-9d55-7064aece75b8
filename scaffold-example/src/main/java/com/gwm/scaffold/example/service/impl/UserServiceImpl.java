package com.gwm.scaffold.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.example.dto.UserCreateRequest;
import com.gwm.scaffold.example.dto.UserQueryRequest;
import com.gwm.scaffold.example.dto.UserUpdateRequest;
import com.gwm.scaffold.example.entity.User;
import com.gwm.scaffold.example.mapper.UserMapper;
import com.gwm.scaffold.example.service.UserService;
import com.gwm.scaffold.example.vo.UserVO;
import com.gwm.scaffold.monitor.annotation.Monitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 用户服务实现类
 * 
 * 展示如何使用Scaffold的各种功能
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    @Monitor(value = "用户分页查询", threshold = 1000)
    public IPage<UserVO> getUserPage(UserQueryRequest request) {
        Page<UserVO> page = new Page<>(request.getCurrent(), request.getSize());
        return baseMapper.selectUserPage(page, request);
    }

    @Override
    @Monitor(value = "获取用户详情")
    public UserVO getUserById(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("用户ID不能为空");
        }

        User user = getById(id);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);
        return userVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "创建用户")
    public UserVO createUser(UserCreateRequest request) {
        // 参数校验
        validateCreateRequest(request);

        // 检查用户名是否存在
        if (isUsernameExists(request.getUsername(), null)) {
            throw new ServiceException("用户名已存在");
        }

        // 检查邮箱是否存在
        if (StringUtils.hasText(request.getEmail()) && isEmailExists(request.getEmail(), null)) {
            throw new ServiceException("邮箱已存在");
        }

        // 检查手机号是否存在
        if (StringUtils.hasText(request.getMobile()) && isMobileExists(request.getMobile(), null)) {
            throw new ServiceException("手机号已存在");
        }

        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(request, user);
        user.setStatus(1); // 默认启用
        user.setVersion(0); // 初始版本

        // 密码加密（这里简化处理，实际应该使用BCrypt等）
        user.setPassword(encryptPassword(request.getPassword()));

        boolean success = save(user);
        if (!success) {
            throw new ServiceException("创建用户失败");
        }

        log.info("创建用户成功，用户名：{}", request.getUsername());
        return getUserById(user.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新用户")
    public UserVO updateUser(UserUpdateRequest request) {
        // 参数校验
        if (request.getId() == null || request.getId() <= 0) {
            throw new ServiceException("用户ID不能为空");
        }

        // 检查用户是否存在
        User existUser = getById(request.getId());
        if (existUser == null) {
            throw new ServiceException("用户不存在");
        }

        // 检查邮箱是否存在
        if (StringUtils.hasText(request.getEmail()) && isEmailExists(request.getEmail(), request.getId())) {
            throw new ServiceException("邮箱已存在");
        }

        // 检查手机号是否存在
        if (StringUtils.hasText(request.getMobile()) && isMobileExists(request.getMobile(), request.getId())) {
            throw new ServiceException("手机号已存在");
        }

        // 更新用户
        User user = new User();
        BeanUtils.copyProperties(request, user);

        boolean success = updateById(user);
        if (!success) {
            throw new ServiceException("更新用户失败");
        }

        log.info("更新用户成功，用户ID：{}", request.getId());
        return getUserById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "删除用户")
    public boolean deleteUser(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("用户ID不能为空");
        }

        User user = getById(id);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        boolean success = removeById(id);
        if (success) {
            log.info("删除用户成功，用户ID：{}", id);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "批量删除用户")
    public int batchDeleteUsers(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("用户ID列表不能为空");
        }

        boolean success = removeByIds(ids);
        int count = success ? ids.size() : 0;
        
        if (success) {
            log.info("批量删除用户成功，删除数量：{}", count);
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新用户状态")
    public boolean updateUserStatus(Long id, Integer status) {
        if (id == null || id <= 0) {
            throw new ServiceException("用户ID不能为空");
        }

        if (status == null || (status != 0 && status != 1)) {
            throw new ServiceException("状态值不正确");
        }

        User user = new User();
        user.setId(id);
        user.setStatus(status);

        boolean success = updateById(user);
        if (success) {
            log.info("更新用户状态成功，用户ID：{}，状态：{}", id, status);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "批量更新用户状态")
    public int batchUpdateUserStatus(List<Long> ids, Integer status) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("用户ID列表不能为空");
        }

        if (status == null || (status != 0 && status != 1)) {
            throw new ServiceException("状态值不正确");
        }

        int count = baseMapper.batchUpdateStatus(ids, status);
        log.info("批量更新用户状态成功，更新数量：{}", count);
        return count;
    }

    @Override
    public boolean isUsernameExists(String username, Long excludeId) {
        if (!StringUtils.hasText(username)) {
            return false;
        }

        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUsername, username);
        if (excludeId != null) {
            wrapper.ne(User::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    @Override
    public boolean isEmailExists(String email, Long excludeId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }

        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getEmail, email);
        if (excludeId != null) {
            wrapper.ne(User::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    @Override
    public boolean isMobileExists(String mobile, Long excludeId) {
        if (!StringUtils.hasText(mobile)) {
            return false;
        }

        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getMobile, mobile);
        if (excludeId != null) {
            wrapper.ne(User::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    @Override
    public List<UserVO> getUsersByDepartmentId(Long departmentId) {
        if (departmentId == null || departmentId <= 0) {
            throw new ServiceException("部门ID不能为空");
        }

        return baseMapper.selectByDepartmentId(departmentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "重置用户密码")
    public boolean resetPassword(Long id, String newPassword) {
        if (id == null || id <= 0) {
            throw new ServiceException("用户ID不能为空");
        }

        if (!StringUtils.hasText(newPassword)) {
            throw new ServiceException("新密码不能为空");
        }

        User user = new User();
        user.setId(id);
        user.setPassword(encryptPassword(newPassword));

        boolean success = updateById(user);
        if (success) {
            log.info("重置用户密码成功，用户ID：{}", id);
        }
        return success;
    }

    /**
     * 校验创建请求参数
     */
    private void validateCreateRequest(UserCreateRequest request) {
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }

        if (!StringUtils.hasText(request.getUsername())) {
            throw new ServiceException("用户名不能为空");
        }

        if (!StringUtils.hasText(request.getPassword())) {
            throw new ServiceException("密码不能为空");
        }

        if (!StringUtils.hasText(request.getRealName())) {
            throw new ServiceException("真实姓名不能为空");
        }

        if (request.getDepartmentId() == null || request.getDepartmentId() <= 0) {
            throw new ServiceException("部门ID不能为空");
        }
    }

    /**
     * 密码加密（简化实现）
     */
    private String encryptPassword(String password) {
        // 实际项目中应该使用BCrypt等安全的加密方式
        return password + "_encrypted";
    }
}
