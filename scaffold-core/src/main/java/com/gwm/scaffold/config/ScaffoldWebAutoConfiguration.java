package com.gwm.scaffold.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Scaffold Web 模块自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.modules.web", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(name = "com.gwm.scaffold.web.config.WebConfig")
@Import({
    com.gwm.scaffold.web.config.WebConfig.class
})
public class ScaffoldWebAutoConfiguration {

    public ScaffoldWebAutoConfiguration() {
        log.debug("GWM Scaffold Web 模块已启用");
    }
}
