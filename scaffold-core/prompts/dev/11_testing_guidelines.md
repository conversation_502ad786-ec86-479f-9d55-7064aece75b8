# 11 测试规范

## 测试类型

### 单元测试
- 测试独立类和方法
- 模拟外部依赖
- 覆盖核心业务逻辑
- 确保代码正确性

### 集成测试
- 测试组件间交互
- 验证与数据库等外部系统集成
- 测试服务间调用
- 确保系统协同工作

### API测试
- 验证API行为和响应
- 测试各种请求参数
- 验证错误处理
- 确保API符合规范

### 性能测试
- 测试系统负载表现
- 验证响应时间和吞吐量
- 识别性能瓶颈
- 确保系统满足性能要求

## 单元测试规范

### 测试框架
- JUnit 5
- Mockito
- AssertJ
- JaCoCo

### 测试命名
```java
@Test
void should返回结果_when条件() {
    // 测试内容
}
```

### 测试结构
```java
// Given
Type input = prepareTestData();
when(mockDependency.method(any())).thenReturn(expectedValue);

// When
Type result = objectUnderTest.methodUnderTest(input);

// Then
assertThat(result).isEqualTo(expectedOutput);
```

### 覆盖率要求
- 业务逻辑层: 80%
- 数据访问层: 70%
- 控制器层: 60%

## 集成测试规范

### 测试环境
- 测试配置文件
- 内存数据库或测试数据库
- 测试容器模拟外部依赖
- 测试数据隔离

### 示例
```java
@SpringBootTest
@ActiveProfiles("test")
class BrandServiceIntegrationTest {
    
    @Autowired
    private BrandService brandService;
    
    @Test
    void shouldCreateBrand() {
        // Given
        BrandCreateDTO createDTO = prepareBrandDTO();
        
        // When
        Long brandId = brandService.createBrand(createDTO);
        
        // Then
        assertThat(brandId).isNotNull();
    }
}
```

## API测试规范

### 测试框架
- Spring MVC Test
- RestAssured
- WireMock

### 示例
```java
@WebMvcTest(BrandController.class)
class BrandControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private BrandService brandService;
    
    @Test
    void shouldReturnBrand_whenGetById() throws Exception {
        // Given
        when(brandService.getBrandById(1L)).thenReturn(brandVO);
        
        // When & Then
        mockMvc.perform(get("/api/v1/brands/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
```

## 性能测试规范

### 测试工具
- JMeter
- Gatling
- Prometheus + Grafana

### 性能指标
- 响应时间: 90% < 300ms
- 吞吐量: 峰值 > 100 TPS
- 错误率: < 0.1%
- 资源使用率: CPU < 70%, 内存 < 80%

## 测试自动化

### CI/CD集成
- PR时运行单元测试
- 合并前运行集成测试
- 定期执行完整测试
- 生成测试报告

## 测试数据管理

### 测试数据工厂
```java
public class BrandTestFactory {
    
    public static BrandCreateDTO createBrandDTO() {
        return BrandCreateDTO.builder()
                .brandCode("TEST")
                .brandNameCn("测试品牌")
                .build();
    }
    
    public static Brand createBrand(Long id) {
        Brand brand = new Brand();
        brand.setId(id);
        brand.setBrandCode("TEST");
        brand.setBrandNameCn("测试品牌");
        brand.setStatus(1);
        return brand;
    }
}
```

## 测试最佳实践

### 编写可测试代码
- 使用依赖注入
- 避免静态方法和单例
- 分离业务逻辑和外部依赖
- 遵循单一职责原则

### 避免脆弱测试
- 不依赖执行顺序
- 不依赖外部状态
- 不使用随机数据
- 不依赖绝对时间 