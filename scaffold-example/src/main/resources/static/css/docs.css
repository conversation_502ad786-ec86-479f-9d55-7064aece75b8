/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* 文档容器 */
.docs-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar-header {
    padding: 30px 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h2 {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.sidebar-header p {
    opacity: 0.8;
    font-size: 0.9rem;
}

.nav-menu {
    list-style: none;
    padding: 20px 0;
}

.nav-menu li {
    margin-bottom: 5px;
}

.nav-link {
    display: block;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover,
.nav-link.active {
    color: white;
    background-color: rgba(255,255,255,0.1);
    border-left-color: white;
}

.sidebar-footer {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-bottom: 10px;
    width: 100%;
    text-align: center;
}

.btn-outline {
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
}

.btn-outline:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    text-decoration: none;
}

/* 主内容区域 */
.main-content {
    margin-left: 280px;
    padding: 40px;
    flex: 1;
    max-width: calc(100% - 280px);
}

/* 内容部分 */
.content-section {
    display: none;
    animation: fadeIn 0.3s ease-in;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    margin-bottom: 40px;
}

.section-header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
}

.section-desc {
    font-size: 1.2rem;
    color: #7f8c8d;
    line-height: 1.6;
}

/* 特性网格 */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 50px;
}

.feature-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.feature-card h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.feature-card p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* 技术栈 */
.tech-stack {
    margin-bottom: 50px;
}

.tech-stack h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 25px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tech-item {
    background: white;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.tech-item strong {
    display: block;
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.tech-item span {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* 步骤样式 */
.requirements {
    background: white;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 40px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.requirements h2 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.requirements ul {
    list-style: none;
    padding-left: 0;
}

.requirements li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
}

.requirements li:before {
    content: "✓";
    color: #27ae60;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.steps {
    margin-bottom: 50px;
}

.step {
    display: flex;
    margin-bottom: 30px;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    align-items: flex-start;
}

.step-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin-right: 25px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.step-content p {
    color: #7f8c8d;
    margin-bottom: 15px;
}

.code-block {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    overflow-x: auto;
    margin-top: 10px;
}

.code-block code {
    background: none;
    color: inherit;
    padding: 0;
    font-size: inherit;
}

/* 快速链接 */
.quick-links {
    margin-bottom: 50px;
}

.quick-links h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 25px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.link-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.link-card {
    display: flex;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.link-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-decoration: none;
    color: inherit;
}

.link-icon {
    font-size: 2rem;
    margin-right: 15px;
}

.link-card strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 3px;
}

.link-card small {
    color: #7f8c8d;
    font-size: 0.8rem;
}

/* 工具类库样式 */
.tool-categories {
    margin-bottom: 50px;
}

.tool-category {
    margin-bottom: 50px;
}

.tool-category h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 25px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.tool-list {
    display: grid;
    gap: 25px;
}

.tool-item {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.tool-item h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.tool-item p {
    color: #7f8c8d;
    margin-bottom: 15px;
    line-height: 1.6;
}

.tool-features {
    margin-bottom: 15px;
}

.feature-tag {
    display: inline-block;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-right: 8px;
    margin-bottom: 5px;
}

.code-example {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.code-example code {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #2c3e50;
    background: none;
}

/* 代码生成器样式 */
.generator-intro {
    margin-bottom: 50px;
}

.intro-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
}

.intro-card h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 25px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.feature-item {
    text-align: center;
    padding: 20px;
}

.feature-item .feature-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.feature-item h3 {
    color: #2c3e50;
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.feature-item p {
    color: #7f8c8d;
    line-height: 1.6;
}

.file-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.file-type {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.file-type strong {
    color: #2c3e50;
    font-weight: 600;
    margin-right: 8px;
}

.generator-steps {
    display: grid;
    gap: 20px;
}

.generator-steps .step {
    display: flex;
    align-items: flex-start;
    background: rgba(102, 126, 234, 0.05);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.generator-steps .step-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 20px;
    flex-shrink: 0;
}

.generator-steps .step-content h3 {
    color: #2c3e50;
    margin-bottom: 8px;
}

.generator-steps .step-content p {
    color: #7f8c8d;
    margin: 0;
}

.generator-action {
    text-align: center;
    padding: 40px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 15px;
    margin-top: 30px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.btn-large {
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: 600;
}

.action-desc {
    margin-top: 15px;
    color: #7f8c8d;
    font-size: 1.1rem;
}

/* 认证鉴权样式 */
.auth-intro {
    margin-bottom: 50px;
}

.component-list {
    display: grid;
    gap: 15px;
}

.component-item {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.component-item strong {
    color: #2c3e50;
    font-weight: 600;
    margin-right: 8px;
}

.annotation-list {
    display: grid;
    gap: 25px;
}

.annotation-item {
    background: rgba(102, 126, 234, 0.05);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.annotation-item h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.annotation-item p {
    color: #7f8c8d;
    margin-bottom: 15px;
    line-height: 1.6;
}

.config-list {
    display: grid;
    gap: 15px;
}

.config-item {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    display: flex;
    flex-direction: column;
}

.config-item strong {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 5px;
}

.config-item span {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.auth-action {
    text-align: center;
    padding: 40px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 15px;
    margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        max-width: 100%;
        padding: 20px;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin-right: 0;
        margin-bottom: 20px;
    }
}
