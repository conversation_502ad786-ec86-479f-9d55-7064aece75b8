package com.gwm.scaffold.docs.controller;

import com.gwm.scaffold.core.domain.Result;
import com.gwm.scaffold.docs.config.DocsProperties;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 文档控制器
 * 
 * 提供API文档相关的功能
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Api(tags = "文档管理", description = "API文档相关功能")
@RestController
@RequestMapping("/scaffold/docs")
@ConditionalOnProperty(name = "scaffold.docs.enabled", havingValue = "true", matchIfMissing = true)
public class DocsController {

    @Autowired
    private DocsProperties docsProperties;

    /**
     * 获取文档配置信息
     */
    @ApiOperation(value = "获取文档配置", notes = "获取当前API文档的配置信息")
    @GetMapping("/config")
    public Result<Map<String, Object>> getDocsConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("enabled", docsProperties.isEnabled());
            config.put("swagger", docsProperties.getSwagger());
            config.put("theme", docsProperties.getTheme());
            config.put("security", docsProperties.getSecurity());
            
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取文档配置失败", e);
            return Result.error("获取文档配置失败：" + e.getMessage());
        }
    }

    /**
     * 文档首页
     */
    @ApiOperation(value = "文档首页", notes = "显示API文档首页")
    @GetMapping(value = {"", "/", "/index"}, produces = MediaType.TEXT_HTML_VALUE)
    public void index(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");
        
        try {
            ClassPathResource resource = new ClassPathResource("static/docs/index.html");
            String html = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            
            // 替换配置信息
            html = html.replace("{{title}}", docsProperties.getSwagger().getTitle());
            html = html.replace("{{description}}", docsProperties.getSwagger().getDescription());
            html = html.replace("{{version}}", docsProperties.getSwagger().getVersion());
            
            response.getWriter().write(html);
        } catch (Exception e) {
            log.error("加载文档首页失败", e);
            response.getWriter().write(getDefaultIndexPage());
        }
    }

    /**
     * API文档统计信息
     */
    @ApiOperation(value = "文档统计", notes = "获取API文档的统计信息")
    @GetMapping("/stats")
    public Result<Map<String, Object>> getDocsStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 这里可以添加统计逻辑，比如：
            // - API接口数量
            // - 模型数量
            // - 文档访问次数等
            stats.put("apiCount", 0);
            stats.put("modelCount", 0);
            stats.put("lastUpdateTime", System.currentTimeMillis());
            stats.put("version", docsProperties.getSwagger().getVersion());
            
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取文档统计失败", e);
            return Result.error("获取文档统计失败：" + e.getMessage());
        }
    }

    /**
     * 文档健康检查
     */
    @ApiOperation(value = "文档健康检查", notes = "检查API文档服务是否正常")
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("swagger", docsProperties.getSwagger().isEnabled() ? "UP" : "DOWN");
            health.put("timestamp", System.currentTimeMillis());
            
            return Result.success(health);
        } catch (Exception e) {
            log.error("文档健康检查失败", e);
            return Result.error("文档健康检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取默认首页
     */
    private String getDefaultIndexPage() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>API文档 - GWM Scaffold</title>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n" +
                "            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                "            margin: 0;\n" +
                "            padding: 0;\n" +
                "            min-height: 100vh;\n" +
                "            display: flex;\n" +
                "            align-items: center;\n" +
                "            justify-content: center;\n" +
                "        }\n" +
                "        .container {\n" +
                "            background: rgba(255,255,255,0.95);\n" +
                "            padding: 40px;\n" +
                "            border-radius: 15px;\n" +
                "            box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n" +
                "            backdrop-filter: blur(10px);\n" +
                "            text-align: center;\n" +
                "            max-width: 600px;\n" +
                "            margin: 20px;\n" +
                "        }\n" +
                "        h1 {\n" +
                "            color: #2c3e50;\n" +
                "            margin-bottom: 20px;\n" +
                "            font-size: 2.5rem;\n" +
                "        }\n" +
                "        .description {\n" +
                "            color: #7f8c8d;\n" +
                "            margin-bottom: 30px;\n" +
                "            font-size: 1.1rem;\n" +
                "            line-height: 1.6;\n" +
                "        }\n" +
                "        .links {\n" +
                "            display: flex;\n" +
                "            flex-direction: column;\n" +
                "            gap: 15px;\n" +
                "            align-items: center;\n" +
                "        }\n" +
                "        .link {\n" +
                "            display: inline-block;\n" +
                "            padding: 12px 30px;\n" +
                "            background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "            color: white;\n" +
                "            text-decoration: none;\n" +
                "            border-radius: 25px;\n" +
                "            transition: transform 0.3s ease;\n" +
                "            font-weight: 500;\n" +
                "            min-width: 200px;\n" +
                "        }\n" +
                "        .link:hover {\n" +
                "            transform: translateY(-2px);\n" +
                "            text-decoration: none;\n" +
                "            color: white;\n" +
                "        }\n" +
                "        .version {\n" +
                "            margin-top: 30px;\n" +
                "            color: #95a5a6;\n" +
                "            font-size: 0.9rem;\n" +
                "        }\n" +
                "        @media (max-width: 768px) {\n" +
                "            .container {\n" +
                "                padding: 30px 20px;\n" +
                "            }\n" +
                "            h1 {\n" +
                "                font-size: 2rem;\n" +
                "            }\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <h1>📚 API文档</h1>\n" +
                "        <div class=\"description\">\n" +
                "            欢迎使用基于GWM Scaffold构建的API文档系统。<br>\n" +
                "            选择下面的链接访问不同风格的API文档界面。\n" +
                "        </div>\n" +
                "        <div class=\"links\">\n" +
                "            <a href=\"/swagger-ui.html\" class=\"link\">🎨 Swagger UI</a>\n" +
                "            <a href=\"/doc.html\" class=\"link\">🚀 Bootstrap UI</a>\n" +
                "            <a href=\"/scaffold/docs/config\" class=\"link\">⚙️ 文档配置</a>\n" +
                "            <a href=\"/scaffold/docs/stats\" class=\"link\">📊 文档统计</a>\n" +
                "        </div>\n" +
                "        <div class=\"version\">\n" +
                "            版本: " + docsProperties.getSwagger().getVersion() + "\n" +
                "        </div>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }
}
