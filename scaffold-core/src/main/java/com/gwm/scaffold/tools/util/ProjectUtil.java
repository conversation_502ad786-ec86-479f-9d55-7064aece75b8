package com.gwm.scaffold.tools.util;

import com.gwm.scaffold.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

import static com.gwm.scaffold.tools.util.Constants.*;
import static java.nio.charset.StandardCharsets.UTF_8;
import static org.springframework.core.io.support.ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX;
import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;
import static org.springframework.http.HttpMethod.POST;
import static org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE;
import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;
import static org.springframework.util.MimeTypeUtils.APPLICATION_JSON_VALUE;

/**
 * 项目工具类
 *
 * <AUTHOR>
 * @date 2025/4/12
 */
@Slf4j
public final class ProjectUtil {

    /**
     * 获取链接中的参数
     *
     * @param url url
     * @return 参数集合
     */
    public static Map<String, String> getUrlParams(String url) {
        if (!StringUtils.hasText(url)) {
            return Collections.emptyMap();
        }
        try {
            new URL(url);
        } catch (MalformedURLException e) {
            log.error("不规范的url：{}", url);
            return Collections.emptyMap();
        }

        // 分割各个参数
        String[] paramPairs = url.split("\\?");
        if (paramPairs.length <= ONE) {
            return Collections.emptyMap();
        }

        // 分割各个参数
        String[] params = paramPairs[ONE].split("&");
        Map<String, String> map = new HashMap<>(params.length);
        for (String pair : params) {
            // 分割参数名和参数值
            String[] parts = pair.split("=", TWO);
            if (parts.length == TWO) {
                try {
                    map.put(URLDecoder.decode(parts[ZERO], UTF_8.name()), URLDecoder.decode(parts[ONE], UTF_8.name()));
                } catch (UnsupportedEncodingException e) {
                    log.error("url decode 异常", e);
                }
            }
        }
        return map;
    }


    /**
     * 获取链接中的指定参数
     *
     * @param url url
     * @param key 指定参数
     * @return 参数集合
     */
    public static String getUrlParam(String url,String key){
        return getUrlParams(url).get(key);
    }


    public static void main(String[] args) {
        String str = "https://bpmuat.gwm.cn/portal/r/or?oauthName=GreatWall&cmd=CLIENT_BPM_FORM_MAIN_PAGE_OPEN&processInstId=fac50b0d-3bdd-4ffc-907c-19f1aeee6c12&taskInstId=2f955a55-a218-4144-8c59-0b03d3431ff3&openState=1&token=Bpm_TokenValue, target=GW00367104, processInstId=fac50b0d-3bdd-4ffc-907c-19f1aeee6c12, id=2f955a55-a218-4144-8c59-0b03d3431ff3";
        System.out.println(getUrlParam(str,"taskInstId"));
    }

    /**
     * 是json入参
     *
     * @return 是否
     */
    public static boolean isJSONParam() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            return POST.name().equalsIgnoreCase(request.getMethod()) && (APPLICATION_JSON_VALUE.equalsIgnoreCase(request.getContentType()) || APPLICATION_JSON_UTF8_VALUE.equalsIgnoreCase(request.getContentType()));
        }
        return false;
    }

    /**
     * 获取httpRequest
     *
     * @return httpRequest
     */
    public static HttpServletRequest getRequest(){
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null == attributes) {
            return null;
        }
        return attributes.getRequest();
    }

    /**
     * 获取httpRequest
     *
     * @return httpRequest
     */
    public static String getHeader(String key) {
        HttpServletRequest request = getRequest();
        if (null == request || !StringUtils.hasText(key)) {
            return null;
        }
        return request.getHeader(key);
    }

    /**
     * 获取请求头映射
     *
     * @return 请求头映射
     */
    public static Map<String, String> getRequestHeaders() {
        HttpServletRequest request = getRequest();
        if (null == request) {
            return Collections.emptyMap();
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        if (null == headerNames) {
            return Collections.emptyMap();
        }
        Map<String, String> result = new HashMap<>(16, 1);
        String headerName;
        while (headerNames.hasMoreElements()) {
            headerName = headerNames.nextElement();
            result.put(headerName, String.join(", ", Collections.list(request.getHeaders(headerName))));
        }
        return result;
    }

    /**
     * 获取指定包下的所有类对象集合
     *
     * @param basePackage 包名
     * @return 类对象集合
     */
    public static List<Class<?>> listClasses(String basePackage) {
        ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
        List<Class<?>> classes;
        try {
            Resource[] resources = resourcePatternResolver.getResources(CLASSPATH_ALL_URL_PREFIX + ClassUtils.convertClassNameToResourcePath(basePackage) + "/**/*.class");
            classes = new ArrayList<>(resources.length);
            for (Resource resource : resources) {
                if (resource.isReadable()) {
                    classes.add(Class.forName(new CachingMetadataReaderFactory(resourcePatternResolver).getMetadataReader(resource).getClassMetadata().getClassName()));
                }
            }
        } catch (IOException | ClassNotFoundException e) {
            log.error("", e);
            throw new ServiceException("变量包下集合异常" + e.getMessage());
        }
        return classes;
    }

    /**
     * 获取对象指定值的内容
     *
     * @param object    指定对象
     * @param fieldName 指定属性名称
     * @return 属性值
     */
    public static Object getField(Object object, String fieldName) {
        if (null == object || !StringUtils.hasText(fieldName)) {
            return null;
        }
        Field field;
        try {
            field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(object);
        } catch (NoSuchFieldException e) {
            log.error("没有找到：{}.{}的值", object, fieldName, e);
            throw new ServiceException(e.getMessage());
        } catch (IllegalAccessException e) {
            log.error("非法访问：{}.{}的值", object, fieldName, e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 下载
     *
     * @param fileName  文件名
     * @param byteArray 字节数组
     * @param response  响应
     */
    public static void download(String fileName, byte[] byteArray, HttpServletResponse response) {
        if (null == byteArray || ZERO >= byteArray.length) {
            throw new ServiceException("字节流为空");
        }
        download(fileName, new ByteArrayInputStream(byteArray), response);
    }

    /**
     * 下载
     *
     * @param fileName 文件名
     * @param is       输入流
     * @param response 响应
     */
    public static void download(String fileName, InputStream is, HttpServletResponse response) {
        response.reset();
        response.setCharacterEncoding(UTF_8.name());
        response.setContentType(APPLICATION_OCTET_STREAM_VALUE);
        byte[] buffer = new byte[4 * STREAM_BUFFER_SIZE];
        try (OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())) {
            if (null == is || ZERO >= is.available()) {
                throw new ServiceException("字节流为空");
            }
            response.setHeader(CONTENT_DISPOSITION, "attachment;fileName=" + URLEncoder.encode(fileName, UTF_8.name()));
            for (int length; (length = is.read(buffer)) > ZERO; ) {
                outputStream.write(buffer, ZERO, length);
            }
        } catch (IOException e) {
            log.error("文件读写异常，文件名：{}", fileName, e);
        } catch (Exception e) {
            log.error("下载过程发生异常，文件名：{}", fileName, e);
        }
    }

    private ProjectUtil() {
    }
}
