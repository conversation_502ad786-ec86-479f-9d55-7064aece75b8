package com.gwm.scaffold.core.exception;

import lombok.*;

/**
 * 服务异常
 * 
 * 用于业务逻辑层抛出的异常，会被全局异常处理器捕获并返回友好的错误信息
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public final class ServiceException extends RuntimeException {

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 根据异常构造服务异常
     *
     * @param e 原始异常
     */
    public ServiceException(Exception e) {
        code = 500;
        if (null != e) {
            message = e.getMessage();
        }
    }

    /**
     * 根据错误信息构造服务异常
     *
     * @param message 错误信息
     */
    public ServiceException(String message) {
        code = 500;
        this.message = message;
    }

    /**
     * 根据错误信息和错误码构造服务异常
     *
     * @param message 错误信息
     * @param code    错误码
     */
    public ServiceException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }
}
