package com.gwm.scaffold.monitor.domain;

import lombok.Builder;
import lombok.Data;

/**
 * 业务指标
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@Builder
public class BusinessMetrics {

    /**
     * 总请求数
     */
    private long totalRequests;

    /**
     * 总错误数
     */
    private long totalErrors;

    /**
     * 错误率（百分比）
     */
    private double errorRate;

    /**
     * 平均响应时间（毫秒）
     */
    private long avgResponseTime;

    /**
     * 当前活跃用户数
     */
    private int activeUsers;

    /**
     * QPS（每秒查询数）
     */
    private double qps;

    /**
     * 获取成功请求数
     */
    public long getSuccessRequests() {
        return totalRequests - totalErrors;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        return 100.0 - errorRate;
    }

    /**
     * 格式化QPS
     */
    public String getFormattedQps() {
        if (qps < 1) {
            return String.format("%.2f", qps);
        } else if (qps < 1000) {
            return String.format("%.1f", qps);
        } else {
            return String.format("%.0f", qps);
        }
    }

    /**
     * 格式化响应时间
     */
    public String getFormattedResponseTime() {
        if (avgResponseTime < 1000) {
            return avgResponseTime + "ms";
        } else {
            return String.format("%.1fs", avgResponseTime / 1000.0);
        }
    }
}
