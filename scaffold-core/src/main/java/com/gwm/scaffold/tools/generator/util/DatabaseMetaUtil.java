package com.gwm.scaffold.tools.generator.util;

import com.gwm.scaffold.tools.generator.domain.ColumnInfo;
import com.gwm.scaffold.tools.generator.domain.TableInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 数据库元数据工具类
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
public class DatabaseMetaUtil {

    @Autowired
    private DataSource dataSource;

    /**
     * 获取表信息
     */
    public TableInfo getTableInfo(String tableName) {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            TableInfo tableInfo = new TableInfo();
            tableInfo.setTableName(tableName);
            tableInfo.setEntityName(NamingUtil.underlineToCamel(tableName, true));
            tableInfo.setEntityNameLower(NamingUtil.underlineToCamel(tableName, false));
            
            // 获取表注释
            String tableComment = getTableComment(connection, tableName);
            tableInfo.setTableComment(tableComment);
            
            // 获取字段信息
            List<ColumnInfo> columns = getColumnInfos(metaData, tableName);
            tableInfo.setColumns(columns);
            
            // 设置主键
            ColumnInfo primaryKey = columns.stream()
                    .filter(ColumnInfo::isPrimaryKey)
                    .findFirst()
                    .orElse(null);
            tableInfo.setPrimaryKey(primaryKey);
            
            // 检查特殊字段
            checkSpecialFields(tableInfo, columns);
            
            // 设置导入包
            Set<String> imports = getImports(columns);
            tableInfo.setImports(new ArrayList<>(imports));
            
            return tableInfo;
            
        } catch (SQLException e) {
            log.error("获取表信息失败：{}", tableName, e);
            return null;
        }
    }

    /**
     * 获取表注释
     */
    private String getTableComment(Connection connection, String tableName) {
        try {
            // 首先尝试使用DatabaseMetaData获取表注释（推荐方式）
            DatabaseMetaData metaData = connection.getMetaData();

            // H2数据库可能将表名存储为大写，所以尝试多种形式
            String[] tableNameVariants = {tableName, tableName.toUpperCase(), tableName.toLowerCase()};

            for (String nameVariant : tableNameVariants) {
                try (ResultSet rs = metaData.getTables(null, null, nameVariant, new String[]{"TABLE"})) {
                    if (rs.next()) {
                        String remarks = rs.getString("REMARKS");
                        if (remarks != null && !remarks.trim().isEmpty()) {
                            log.debug("通过DatabaseMetaData获取到表注释：{} -> {}", nameVariant, remarks);
                            return remarks.trim();
                        }
                    }
                }
            }

            // 如果DatabaseMetaData方式失败，尝试直接查询INFORMATION_SCHEMA
            // 不同数据库的字段名可能不同：H2使用REMARKS，MySQL使用TABLE_COMMENT
            String[] commentColumns = {"REMARKS", "TABLE_COMMENT"};

            for (String columnName : commentColumns) {
                for (String nameVariant : tableNameVariants) {
                    String sql = "SELECT " + columnName + " FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ?";
                    try (PreparedStatement ps = connection.prepareStatement(sql)) {
                        ps.setString(1, nameVariant);
                        try (ResultSet rs = ps.executeQuery()) {
                            if (rs.next()) {
                                String comment = rs.getString(columnName);
                                if (comment != null && !comment.trim().isEmpty()) {
                                    log.debug("通过INFORMATION_SCHEMA获取到表注释：{}.{} -> {}", nameVariant, columnName, comment);
                                    return comment.trim();
                                }
                            }
                        }
                    } catch (SQLException e) {
                        // 如果当前字段名不存在，尝试下一个
                        log.debug("尝试字段 {}.{} 失败：{}", nameVariant, columnName, e.getMessage());
                    }
                }
            }

        } catch (SQLException e) {
            log.warn("获取表注释失败：{}", tableName, e);
        }
        return "";
    }

    /**
     * 获取字段信息列表
     */
    private List<ColumnInfo> getColumnInfos(DatabaseMetaData metaData, String tableName) throws SQLException {
        List<ColumnInfo> columns = new ArrayList<>();
        
        // 获取主键信息
        Set<String> primaryKeys = getPrimaryKeys(metaData, tableName);
        
        // 获取字段信息
        try (ResultSet rs = metaData.getColumns(null, null, tableName, null)) {
            while (rs.next()) {
                ColumnInfo column = new ColumnInfo();
                
                String columnName = rs.getString("COLUMN_NAME");
                column.setColumnName(columnName);
                column.setJavaField(NamingUtil.underlineToCamel(columnName, false));
                column.setColumnComment(rs.getString("REMARKS"));
                column.setColumnType(rs.getString("TYPE_NAME"));
                column.setColumnSize(rs.getInt("COLUMN_SIZE"));
                column.setDecimalDigits(rs.getInt("DECIMAL_DIGITS"));
                column.setNullable("YES".equals(rs.getString("IS_NULLABLE")));
                column.setDefaultValue(rs.getString("COLUMN_DEF"));
                column.setAutoIncrement("YES".equals(rs.getString("IS_AUTOINCREMENT")));
                column.setPrimaryKey(primaryKeys.contains(columnName));
                
                // 设置Java类型
                column.setJavaType(getJavaType(column.getColumnType()));
                
                // 检查特殊字段
                checkSpecialColumn(column);
                
                columns.add(column);
            }
        }
        
        return columns;
    }

    /**
     * 获取主键列表
     */
    private Set<String> getPrimaryKeys(DatabaseMetaData metaData, String tableName) throws SQLException {
        Set<String> primaryKeys = new HashSet<>();
        try (ResultSet rs = metaData.getPrimaryKeys(null, null, tableName)) {
            while (rs.next()) {
                primaryKeys.add(rs.getString("COLUMN_NAME"));
            }
        }
        return primaryKeys;
    }

    /**
     * 数据库类型转Java类型
     */
    private String getJavaType(String columnType) {
        String type = columnType.toUpperCase();
        
        if (type.contains("CHAR") || type.contains("TEXT")) {
            return "String";
        } else if (type.contains("BIGINT")) {
            return "Long";
        } else if (type.contains("INT")) {
            return "Integer";
        } else if (type.contains("DECIMAL") || type.contains("NUMERIC")) {
            return "BigDecimal";
        } else if (type.contains("DOUBLE")) {
            return "Double";
        } else if (type.contains("FLOAT")) {
            return "Float";
        } else if (type.contains("BOOLEAN") || type.contains("BIT")) {
            return "Boolean";
        } else if (type.contains("DATE")) {
            return "Date";
        } else if (type.contains("TIME")) {
            return "LocalDateTime";
        } else if (type.contains("BLOB")) {
            return "byte[]";
        } else {
            return "String";
        }
    }

    /**
     * 检查特殊字段
     */
    private void checkSpecialColumn(ColumnInfo column) {
        String fieldName = column.getJavaField();
        
        // 逻辑删除字段
        if ("deleted".equals(fieldName) || "delFlag".equals(fieldName)) {
            column.setLogicDelete(true);
        }
        
        // 版本字段
        if ("version".equals(fieldName)) {
            column.setVersion(true);
        }
        
        // 创建时间字段
        if ("createTime".equals(fieldName)) {
            column.setCreateTime(true);
        }
        
        // 更新时间字段
        if ("updateTime".equals(fieldName)) {
            column.setUpdateTime(true);
        }
        
        // 创建人字段
        if ("createUserCode".equals(fieldName) || "createUserName".equals(fieldName)) {
            column.setCreateUser(true);
        }
        
        // 更新人字段
        if ("updateUserCode".equals(fieldName) || "updateUserName".equals(fieldName)) {
            column.setUpdateUser(true);
        }
        
        // 设置是否在查询、列表、表单中显示
        setFieldDisplay(column);
    }

    /**
     * 设置字段显示属性
     */
    private void setFieldDisplay(ColumnInfo column) {
        String fieldName = column.getJavaField();
        
        // 不在表单中显示的字段
        boolean isFormField = !column.isPrimaryKey() 
                && !column.isCreateTime() 
                && !column.isUpdateTime()
                && !column.isCreateUser() 
                && !column.isUpdateUser()
                && !column.isLogicDelete()
                && !column.isVersion();
        
        // 可以作为查询条件的字段
        boolean isQueryField = !column.isCreateTime() 
                && !column.isUpdateTime()
                && !column.isCreateUser() 
                && !column.isUpdateUser()
                && !column.isLogicDelete()
                && !column.isVersion();
        
        // 在列表中显示的字段
        boolean isListField = !column.isLogicDelete();
        
        column.setFormField(isFormField);
        column.setQueryField(isQueryField);
        column.setListField(isListField);
        
        // 设置表单类型
        if (isFormField) {
            setFormType(column);
        }
        
        // 设置查询类型
        if (isQueryField) {
            setQueryType(column);
        }
    }

    /**
     * 设置表单类型
     */
    private void setFormType(ColumnInfo column) {
        String javaType = column.getJavaType();

        if ("String".equals(javaType)) {
            if (column.getColumnSize() != null && column.getColumnSize() > 500) {
                column.setFormType("textarea");
            } else {
                column.setFormType("input");
            }
        } else if ("Integer".equals(javaType) || "Long".equals(javaType)) {
            column.setFormType("number");
        } else if ("Date".equals(javaType) || "LocalDateTime".equals(javaType)) {
            column.setFormType("datetime");
        } else if ("Boolean".equals(javaType)) {
            column.setFormType("radio");
        } else {
            column.setFormType("input");
        }
    }

    /**
     * 设置查询类型
     */
    private void setQueryType(ColumnInfo column) {
        String javaType = column.getJavaType();

        if ("String".equals(javaType)) {
            column.setQueryType("LIKE");
        } else if ("Date".equals(javaType) || "LocalDateTime".equals(javaType)) {
            column.setQueryType("BETWEEN");
        } else {
            column.setQueryType("EQ");
        }
    }

    /**
     * 检查表的特殊字段
     */
    private void checkSpecialFields(TableInfo tableInfo, List<ColumnInfo> columns) {
        for (ColumnInfo column : columns) {
            if (column.isLogicDelete()) {
                tableInfo.setHasLogicDelete(true);
            }
            if (column.isVersion()) {
                tableInfo.setHasVersion(true);
            }
            if (column.isCreateTime()) {
                tableInfo.setHasCreateTime(true);
            }
            if (column.isUpdateTime()) {
                tableInfo.setHasUpdateTime(true);
            }
        }
    }

    /**
     * 获取需要导入的包
     */
    private Set<String> getImports(List<ColumnInfo> columns) {
        Set<String> imports = new HashSet<>();

        for (ColumnInfo column : columns) {
            String javaType = column.getJavaType();

            if ("BigDecimal".equals(javaType)) {
                imports.add("java.math.BigDecimal");
            } else if ("Date".equals(javaType)) {
                imports.add("java.util.Date");
            } else if ("LocalDateTime".equals(javaType)) {
                imports.add("java.time.LocalDateTime");
            }
        }

        return imports;
    }
}
