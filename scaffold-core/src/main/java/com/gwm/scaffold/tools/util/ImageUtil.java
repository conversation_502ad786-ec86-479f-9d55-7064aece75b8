package com.gwm.scaffold.tools.util;

import com.gwm.scaffold.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;

import static com.gwm.scaffold.tools.util.Constants.STREAM_BUFFER_SIZE;
import static com.gwm.scaffold.tools.util.Constants.ZERO;
import static java.util.Base64.getEncoder;

/**
 * 图片工具类
 *
 * <AUTHOR>
 * @date 2025/4/9
 */
@Slf4j
public final class ImageUtil {

    /**
     * 将图片流转化为base64
     *
     * @param filePath 文件路径
     * @return base64
     */
    public static String convertImageToBase64(String filePath) {
        try {
            return convertImageToBase64(new FileInputStream(filePath));
        } catch (FileNotFoundException e) {
            log.error("图片转换base64失败", e);
            throw new ServiceException("图片转换base64失败: " + e.getMessage());
        }
    }

    /**
     * 将路径中的图片转化为base64
     *
     * @param inputStream 输入流
     * @return base64
     */
    public static String convertImageToBase64(InputStream inputStream) {
        byte[] buffer = new byte[STREAM_BUFFER_SIZE];
        int bytesRead;
        try (ByteArrayOutputStream imageOutFile = new ByteArrayOutputStream()) {
            while ((bytesRead = inputStream.read(buffer)) > ZERO) {
                imageOutFile.write(buffer, ZERO, bytesRead);
            }
            return getEncoder().encodeToString(imageOutFile.toByteArray());
        } catch (Exception e) {
            log.error("图片转换base64失败", e);
            throw new ServiceException("图片转换base64失败: " + e.getMessage());
        }
    }

    private ImageUtil() {
    }
}
