package com.gwm.scaffold.data.aspect;

import com.gwm.scaffold.data.annotation.DataPermission;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 数据权限切面
 * 
 * 处理@DataPermission注解的数据权限控制
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Aspect
@Component
@Order(100)
@ConditionalOnProperty(name = "scaffold.data.permission.enabled", havingValue = "true", matchIfMissing = false)
public class DataPermissionAspect {

    /**
     * 数据权限处理
     */
    @Around("@annotation(dataPermission)")
    public Object around(ProceedingJoinPoint joinPoint, DataPermission dataPermission) throws Throwable {
        // 获取当前用户信息
        String currentUser = getCurrentUser();
        
        // 设置数据权限上下文
        DataPermissionContext.setPermission(dataPermission, currentUser);
        
        try {
            log.debug("开始执行数据权限控制，用户：{}，权限类型：{}", currentUser, dataPermission.value());
            return joinPoint.proceed();
        } finally {
            // 清理数据权限上下文
            DataPermissionContext.clear();
            log.debug("数据权限控制执行完成，已清理上下文");
        }
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        try {
            // 尝试从认证模块获取用户信息
            Class<?> loginUserUtilClass = Class.forName("com.gwm.scaffold.auth.util.LoginUserUtil");
            Object userCode = loginUserUtilClass.getMethod("getUserCode").invoke(null);
            return userCode != null ? userCode.toString() : "anonymous";
        } catch (Exception e) {
            log.debug("无法获取当前用户信息，使用匿名用户");
            return "anonymous";
        }
    }

    /**
     * 数据权限上下文
     */
    public static class DataPermissionContext {
        private static final ThreadLocal<PermissionInfo> CONTEXT = new ThreadLocal<>();

        /**
         * 设置权限信息
         */
        public static void setPermission(DataPermission dataPermission, String currentUser) {
            CONTEXT.set(new PermissionInfo(dataPermission, currentUser));
        }

        /**
         * 获取权限信息
         */
        public static PermissionInfo getPermission() {
            return CONTEXT.get();
        }

        /**
         * 清理上下文
         */
        public static void clear() {
            CONTEXT.remove();
        }

        /**
         * 权限信息
         */
        public static class PermissionInfo {
            private final DataPermission dataPermission;
            private final String currentUser;

            public PermissionInfo(DataPermission dataPermission, String currentUser) {
                this.dataPermission = dataPermission;
                this.currentUser = currentUser;
            }

            public DataPermission getDataPermission() {
                return dataPermission;
            }

            public String getCurrentUser() {
                return currentUser;
            }
        }
    }
}
