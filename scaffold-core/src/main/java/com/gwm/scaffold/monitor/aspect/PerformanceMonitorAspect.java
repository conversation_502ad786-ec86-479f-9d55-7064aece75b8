package com.gwm.scaffold.monitor.aspect;

import com.gwm.scaffold.monitor.annotation.Monitor;
import com.gwm.scaffold.monitor.service.MetricsCollector;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 性能监控切面
 * 
 * 监控方法执行性能和Controller请求性能
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Aspect
@Component
@Order(300)
@ConditionalOnProperty(name = "scaffold.monitor.metrics.http", havingValue = "true", matchIfMissing = true)
public class PerformanceMonitorAspect {

    @Autowired
    private MetricsCollector metricsCollector;

    /**
     * 监控Controller方法性能
     */
    @Around("execution(* *..*Controller.*(..))")
    public Object monitorController(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        boolean isError = false;
        String methodName = getMethodName(joinPoint);
        
        try {
            Object result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            isError = true;
            throw e;
        } finally {
            long responseTime = System.currentTimeMillis() - startTime;
            
            // 记录请求指标
            metricsCollector.recordRequest(responseTime, isError);
            
            // 记录详细日志
            logPerformance(methodName, responseTime, isError);
        }
    }

    /**
     * 监控带@Monitor注解的方法
     */
    @Around("@annotation(monitor)")
    public Object monitorAnnotated(ProceedingJoinPoint joinPoint, Monitor monitor) throws Throwable {
        long startTime = System.currentTimeMillis();
        boolean isError = false;
        String methodName = getMethodName(joinPoint);
        
        try {
            Object result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            isError = true;
            throw e;
        } finally {
            long responseTime = System.currentTimeMillis() - startTime;
            
            // 检查是否超过阈值
            if (monitor.threshold() > 0 && responseTime > monitor.threshold()) {
                log.warn("方法 {} 执行时间 {}ms 超过阈值 {}ms", 
                        methodName, responseTime, monitor.threshold());
            }
            
            // 记录性能日志
            if (monitor.logEnabled()) {
                logPerformance(methodName, responseTime, isError);
            }
        }
    }

    /**
     * 记录性能日志
     */
    private void logPerformance(String methodName, long responseTime, boolean isError) {
        String requestInfo = getRequestInfo();
        
        if (isError) {
            log.error("性能监控 - 方法: {}, 响应时间: {}ms, 状态: 异常{}", 
                    methodName, responseTime, requestInfo);
        } else if (responseTime > 1000) {
            log.warn("性能监控 - 方法: {}, 响应时间: {}ms, 状态: 慢查询{}", 
                    methodName, responseTime, requestInfo);
        } else {
            log.debug("性能监控 - 方法: {}, 响应时间: {}ms, 状态: 正常{}", 
                    methodName, responseTime, requestInfo);
        }
    }

    /**
     * 获取方法名
     */
    private String getMethodName(ProceedingJoinPoint joinPoint) {
        return joinPoint.getTarget().getClass().getSimpleName() + "." + joinPoint.getSignature().getName();
    }

    /**
     * 获取请求信息
     */
    private String getRequestInfo() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return String.format(", 请求: %s %s", request.getMethod(), request.getRequestURI());
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }
}
