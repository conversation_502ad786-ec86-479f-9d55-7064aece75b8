package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.menu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RemoteMenuEditDTO implements Serializable {

    public RemoteMenuEditDTO() {
        this.parentId = "0";
    }

    private String code;
    private String ext1;
    private String ext2;
    private String ext3;
    private String id;
    private String name;
    @JsonProperty("parent_id")
    private String parentId;
    private String remark;
    @JsonProperty("sort_no")
    private Integer sortNo;
    private String type;
    private String url;
    private String ico;
    private String path;
    @JsonProperty("open_method")
    private Integer openMethod;
    private Integer enable;
    @JsonProperty("link_flag")
    private Integer linkFlag;
}
