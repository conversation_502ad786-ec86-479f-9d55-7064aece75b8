package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.*;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.fallback.RemoteUserFallbackFactory;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthRequestInterceptor;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
/**
 * <AUTHOR>
 */
@FeignClient(name = "platformUserFeign", url = "EMPTY", contextId = "PlatformUserFeign", configuration = AuthRequestInterceptor.class, fallback = RemoteUserFallbackFactory.class)
public interface PlatformUserFeign {

    /**
     * 用户绑定应用
     *
     * @param platformUserBindDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/platform/user/bind")
    CommonRlt<JSONObject> platformUserBind(RemotePlatformUserBindDTO platformUserBindDTO);

    /**
     * 平台用户列表
     * @param searchUserListForm
     * @return CommonRlt<JSONObject>
     */
    @PostMapping("/public/pubqc/permission/platform/user/list")
    CommonRlt<JSONObject> list(@Validated @RequestBody SearchUserListForm searchUserListForm);

    /**
     * 创建用户
     *
     * @param userCreateDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/user/create")
    CommonRlt<JSONObject> userCreate(@RequestBody RemoteUserCreateDTO userCreateDTO);

    /**
     * 用户绑定多角色
     *
     * @param userBindRoleDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/role/bind/user")
    CommonRlt<JSONObject> userBindRole(@RequestBody RemoteUserBindRoleDTO userBindRoleDTO);


    /**
     * 组织用户树
     *
     * @param groupUserTreeDTO
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/user/groupUserTree")
    CommonRlt<JSONObject> groupUserTree(@SpringQueryMap RemoteGroupUserTreeDTO groupUserTreeDTO);

    /**
     * 获取用户关联的应用列表
     *
     * @param userPlatformListDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/user/platform")
    CommonRlt<JSONObject> userPlatformList(@RequestBody RemoteUserPlatformListDTO userPlatformListDTO);

    /**
     * 修改用户别名
     *
     * @param userAliasEditDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/user/alias/change")
    CommonRlt<JSONObject> userAliasEdit(@RequestBody RemoteUserAliasEditDTO userAliasEditDTO);

    /**
     * 上传用户头像
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/upload/avatar", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    CommonRlt<JSONObject> uploadAvatar(@RequestPart("file") MultipartFile file);

    /**
     * 组织用户树初始化
     *
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/user/groupUserTreeInit")
    CommonRlt<JSONArray> groupUserTreeInit();

    //根据应用id和用户code查询角色

    /**
     * 搜索用户列表
     *
     * @param searchUserListDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/user/containsGroupList")
    CommonRlt<JSONObject> searchUserList(@RequestBody RemoteSearchUserListDTO searchUserListDTO);

    /**
     * 应用用户解绑
     *
     * @param platformUserUnbindDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/platform/user/unbind")
    CommonRlt<JSONObject> platformUserUnbind(@RequestBody RemotePlatformUserUnbindDTO platformUserUnbindDTO);

    /**
     * 修改自定义用户密码
     *
     * @param userPasswordEditDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/user/password/change")
    CommonRlt<JSONObject> userPasswordEdit(@RequestBody RemoteUserPasswordEditDTO userPasswordEditDTO);

    /**
     * 查询平台用户列表
     *
     * @param platformUserListDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/platform/user/list")
    CommonRlt<JSONObject> platformUserList(RemotePlatformUserListDTO platformUserListDTO);

    /**
     * 重置自定义用户密码
     *
     * @param customUserResetPasswordDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/user/password/reset")
    CommonRlt<JSONObject> resetCustomUserPassword(RemoteCustomUserResetPasswordDTO customUserResetPasswordDTO);

    /**
     * 修改用户
     *
     * @param userEditDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/user/modify/userInfo")
    CommonRlt<JSONObject> userModify(@RequestBody RemoteUserEditDTO userEditDTO);

    /**
     * 查询用户列表
     *
     * @param userListDTO
     * @return
     */
    @PostMapping(value = "/public/pubqc/permission/user/list")
    CommonRlt<JSONObject> userList(@RequestBody RemoteUserListDTO userListDTO);

    /**
     * 查询用户详情
     *
     * @param userDetailDTO
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/user/get")
    CommonRlt<JSONObject> userDetail(@SpringQueryMap RemoteUserDetailDTO userDetailDTO);

    /**
     * 查询用户的角色列表
     *
     * @param userRoleListDTO
     * @return
     */
    @GetMapping(value = "/public/pubqc/permission/user/role/list")
    CommonRlt<JSONArray> userRoleList(@SpringQueryMap RemoteUserRoleListDTO userRoleListDTO);

}
