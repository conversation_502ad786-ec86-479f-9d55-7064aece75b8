package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
public class CommonRlt<T> implements Serializable {

    private static final long serialVersionUID = 892913062179185791L;
    /**
     * 返回码
     */
    private String code;
    private String key;
    /**
     * 返回码对应信息
     */
    private String message;
    /**
     * 返回数据
     */
    @JsonProperty("result")
    private T data;

    public CommonRlt(String code, String key, String message, T result) {
        this.code = code;
        this.key = key;
        this.message = message;
        this.data = result;
    }

    public String toJSONString() {
        return JSON.toJSONString(this, SerializerFeature.DisableCircularReferenceDetect);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.DisableCircularReferenceDetect);
    }

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isSuccess() {
        return key.equals(BaseRltCode.S_0000);
    }
}
