package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("角色绑定组织参数对象")
public class RoleBindGroupForm implements Serializable {

    @JsonIgnore
    private String account;

	@ApiModelProperty("平台码")
    @JsonProperty(value = "platform_code")
    private String platformCode;

    /**
     * 角色ID
     */
    @ApiModelProperty("角色ID")
    private String id;

    /**
     * 组织集合
     */
    @ApiModelProperty("组织集合")
    @JsonProperty(value = "group_ids")
    private List<Integer> groupIds;
}