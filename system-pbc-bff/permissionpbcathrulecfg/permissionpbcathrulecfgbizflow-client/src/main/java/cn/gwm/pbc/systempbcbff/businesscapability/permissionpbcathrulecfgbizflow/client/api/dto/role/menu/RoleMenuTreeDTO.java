package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.menu;

import com.alibaba.bizworks.core.specification.*;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "RoleMenuTreeDTO", desc = "")
@Data
public class RoleMenuTreeDTO implements Serializable {

    /**
     * id
     */
    @Field(name = "id", desc = "")
    private String id;

}
