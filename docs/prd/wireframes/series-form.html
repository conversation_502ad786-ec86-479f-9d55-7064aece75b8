<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车系新增/编辑页 - 线框图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .wireframe {
            background-color: white;
            border: 2px solid #333;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ccc;
            margin-bottom: 30px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #333;
            background: white;
            cursor: pointer;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .form-row {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        .form-label {
            width: 120px;
            font-weight: bold;
        }
        .required {
            color: red;
        }
        .form-control {
            padding: 8px;
            border: 1px solid #ccc;
            width: 300px;
        }
        .form-control.large {
            width: 400px;
        }
        .form-control.small {
            width: 100px;
        }
        .form-hint {
            color: #666;
            font-size: 12px;
            margin-left: 10px;
        }
        .textarea {
            height: 80px;
            resize: vertical;
        }
        .upload-area {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            background-color: #f9f9f9;
            width: 300px;
        }
        .preview-area {
            border: 1px solid #ccc;
            width: 200px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f0f0f0;
            margin-top: 10px;
        }
        .radio-group {
            display: flex;
            gap: 20px;
        }
        .radio-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .price-range {
            display: flex;
            align-items: center;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="wireframe">
        <div class="header">
            <div class="title">车系管理 > 新增车系</div>
            <div class="buttons">
                <button class="btn">保存</button>
                <button class="btn">取消</button>
            </div>
        </div>

        <div class="section">
            <div class="section-title">基础信息</div>
            <div class="form-row">
                <label class="form-label">车系名称<span class="required">*</span>:</label>
                <input type="text" class="form-control" placeholder="请输入车系名称">
                <span class="form-hint">(2-50字符)</span>
            </div>
            <div class="form-row">
                <label class="form-label">英文名称:</label>
                <input type="text" class="form-control" placeholder="请输入英文名称">
                <span class="form-hint">(可选)</span>
            </div>
            <div class="form-row">
                <label class="form-label">所属品牌<span class="required">*</span>:</label>
                <select class="form-control">
                    <option>请选择品牌</option>
                    <option>奔驰</option>
                    <option>宝马</option>
                    <option>奥迪</option>
                </select>
                <span class="form-hint">(必选，仅显示启用品牌)</span>
            </div>
            <div class="form-row">
                <label class="form-label">车系类型<span class="required">*</span>:</label>
                <select class="form-control">
                    <option>请选择类型</option>
                    <option>轿车</option>
                    <option>SUV</option>
                    <option>MPV</option>
                    <option>跑车</option>
                    <option>皮卡</option>
                </select>
                <span class="form-hint">(轿车/SUV/MPV等)</span>
            </div>
            <div class="form-row">
                <label class="form-label">车系级别<span class="required">*</span>:</label>
                <select class="form-control">
                    <option>请选择级别</option>
                    <option>A级</option>
                    <option>B级</option>
                    <option>C级</option>
                    <option>D级</option>
                </select>
                <span class="form-hint">(A/B/C/D级)</span>
            </div>
        </div>

        <div class="section">
            <div class="section-title">时间信息</div>
            <div class="form-row">
                <label class="form-label">上市时间<span class="required">*</span>:</label>
                <input type="date" class="form-control">
                <span class="form-hint">(必填)</span>
            </div>
            <div class="form-row">
                <label class="form-label">停产时间:</label>
                <input type="date" class="form-control">
                <span class="form-hint">(可选)</span>
            </div>
        </div>

        <div class="section">
            <div class="section-title">价格信息</div>
            <div class="form-row">
                <label class="form-label">指导价区间:</label>
                <div class="price-range">
                    <input type="number" class="form-control small" placeholder="最低价">
                    <span>万 - </span>
                    <input type="number" class="form-control small" placeholder="最高价">
                    <span>万</span>
                </div>
                <span class="form-hint">(可选，用于参考)</span>
            </div>
        </div>

        <div class="section">
            <div class="section-title">车系图片</div>
            <div class="form-row">
                <label class="form-label">代表图片:</label>
                <div>
                    <div class="upload-area">
                        <div>点击上传图片</div>
                        <div class="form-hint">支持jpg/png/gif，最大5MB</div>
                    </div>
                    <div class="preview-area">
                        预览图片区域
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">详细信息</div>
            <div class="form-row">
                <label class="form-label">车系简介:</label>
                <textarea class="form-control large textarea" placeholder="请输入车系简介"></textarea>
                <span class="form-hint">(最大2000字符)</span>
            </div>
        </div>

        <div class="section">
            <div class="section-title">其他设置</div>
            <div class="form-row">
                <label class="form-label">状态:</label>
                <div class="radio-group">
                    <div class="radio-item">
                        <input type="radio" name="status" value="coming" checked>
                        <label>即将上市</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" name="status" value="active">
                        <label>在售</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" name="status" value="discontinued">
                        <label>停售</label>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <label class="form-label">排序权重:</label>
                <input type="number" class="form-control small" value="100">
                <span class="form-hint">(数字)</span>
            </div>
        </div>
    </div>
</body>
</html>