<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GWM Scaffold 代码生成器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .form-row {
            display: flex;
            gap: 20px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .table-list {
            margin-top: 30px;
        }
        .table-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .table-item:hover {
            background: #e9ecef;
            border-color: #667eea;
        }
        .table-item.selected {
            background: #e3f2fd;
            border-color: #667eea;
        }
        .table-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .table-comment {
            color: #666;
            font-size: 14px;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GWM Scaffold 代码生成器</h1>
            <p>根据数据库表结构自动生成符合脚手架规范的代码</p>
        </div>
        
        <div class="content">
            <!-- 配置表单 -->
            <form id="generatorForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="tableName">表名 *</label>
                        <select id="tableName" name="tableName" required>
                            <option value="">请选择表</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="moduleName">模块名 *</label>
                        <input type="text" id="moduleName" name="moduleName" placeholder="如：user" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="packageName">包名</label>
                        <input type="text" id="packageName" name="packageName" value="com.gwm.scaffold.example">
                    </div>
                    <div class="form-group">
                        <label for="author">作者</label>
                        <input type="text" id="author" name="author" value="scaffold">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="businessName">业务名称</label>
                        <input type="text" id="businessName" name="businessName" placeholder="如：用户">
                    </div>
                    <div class="form-group">
                        <label for="functionName">功能描述</label>
                        <input type="text" id="functionName" name="functionName" placeholder="如：用户管理">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="outputPath">输出路径</label>
                    <input type="text" id="outputPath" name="outputPath" value="scaffold-example/src/main/java/com/gwm/scaffold/example">
                    <small class="form-text text-muted">路径相对于项目根目录，生成的代码将保存在scaffold-example模块下</small>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="overwrite" name="overwrite"> 覆盖已存在的文件
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="generatePermission" name="generatePermission" checked> 生成权限注解
                        </label>
                    </div>
                </div>
                
                <div style="margin-top: 30px;">
                    <button type="button" class="btn" onclick="loadTables()">🔄 刷新表列表</button>
                    <button type="button" class="btn btn-secondary" onclick="previewCode()">👁️ 预览代码</button>
                    <button type="submit" class="btn btn-success">🚀 生成代码</button>
                </div>
            </form>
            
            <!-- 表列表 -->
            <div class="table-list">
                <h3>数据库表列表</h3>
                <div id="tableList">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
            
            <!-- 结果显示 -->
            <div id="result" class="result"></div>
        </div>
    </div>

    <script>
        // 页面加载完成后自动加载表列表
        document.addEventListener('DOMContentLoaded', function() {
            loadTables();
        });

        // 加载数据库表列表
        function loadTables() {
            const tableList = document.getElementById('tableList');
            const tableSelect = document.getElementById('tableName');
            
            tableList.innerHTML = '<div class="loading"><div class="spinner"></div><p>加载中...</p></div>';
            
            fetch('/api/generator/tables')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTables(data.data);
                        populateTableSelect(data.data);
                    } else {
                        showResult('error', '加载表列表失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showResult('error', '加载表列表失败：' + error.message);
                });
        }

        // 显示表列表
        function displayTables(tables) {
            const tableList = document.getElementById('tableList');
            
            if (tables.length === 0) {
                tableList.innerHTML = '<p>没有找到数据库表</p>';
                return;
            }
            
            let html = '';
            tables.forEach(table => {
                html += `
                    <div class="table-item" onclick="selectTable('${table.tableName}')">
                        <div class="table-name">${table.tableName}</div>
                        <div class="table-comment">${table.tableComment || '无注释'}</div>
                    </div>
                `;
            });
            
            tableList.innerHTML = html;
        }

        // 填充表选择下拉框
        function populateTableSelect(tables) {
            const tableSelect = document.getElementById('tableName');
            
            // 清空现有选项
            tableSelect.innerHTML = '<option value="">请选择表</option>';
            
            // 添加表选项
            tables.forEach(table => {
                const option = document.createElement('option');
                option.value = table.tableName;
                option.textContent = `${table.tableName} - ${table.tableComment || '无注释'}`;
                tableSelect.appendChild(option);
            });
        }

        // 选择表
        function selectTable(tableName) {
            // 更新选择状态
            document.querySelectorAll('.table-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.closest('.table-item').classList.add('selected');
            
            // 设置表单值
            document.getElementById('tableName').value = tableName;
            
            // 自动填充模块名
            const moduleName = tableName.replace(/^(sys_|t_|tb_|tbl_)/, '').toLowerCase();
            document.getElementById('moduleName').value = moduleName;
            
            // 自动填充业务名称
            document.getElementById('businessName').value = moduleName;
            document.getElementById('functionName').value = moduleName + '管理';
        }

        // 预览代码
        function previewCode() {
            const formData = getFormData();
            if (!formData.tableName) {
                showResult('error', '请选择要生成代码的表');
                return;
            }
            
            showResult('', '正在预览代码...');
            
            fetch('/api/generator/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 这里可以打开一个新窗口显示代码预览
                    console.log('代码预览：', data.data);
                    showResult('success', '代码预览生成成功，请查看控制台');
                } else {
                    showResult('error', '代码预览失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showResult('error', '代码预览失败：' + error.message);
            });
        }

        // 表单提交
        document.getElementById('generatorForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = getFormData();
            if (!formData.tableName) {
                showResult('error', '请选择要生成代码的表');
                return;
            }
            
            showResult('', '正在生成代码...');
            
            fetch('/api/generator/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('success', '代码生成成功！');
                } else {
                    showResult('error', '代码生成失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showResult('error', '代码生成失败：' + error.message);
            });
        });

        // 获取表单数据
        function getFormData() {
            const form = document.getElementById('generatorForm');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                if (key === 'overwrite' || key === 'generatePermission') {
                    data[key] = form.querySelector(`[name="${key}"]`).checked;
                } else {
                    data[key] = value;
                }
            }
            
            return data;
        }

        // 显示结果
        function showResult(type, message) {
            const result = document.getElementById('result');
            result.className = 'result ' + type;
            result.textContent = message;
            result.style.display = 'block';
            
            if (type) {
                setTimeout(() => {
                    result.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>
