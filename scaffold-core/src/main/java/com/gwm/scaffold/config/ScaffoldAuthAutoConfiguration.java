package com.gwm.scaffold.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Scaffold Auth 模块自动配置
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "scaffold.modules.auth", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(name = "com.gwm.scaffold.auth.config.AuthAutoConfiguration")
@Import(com.gwm.scaffold.auth.config.AuthAutoConfiguration.class)
public class ScaffoldAuthAutoConfiguration {

    public ScaffoldAuthAutoConfiguration() {
        log.debug("GWM Scaffold Auth 模块已启用");
    }
}
