package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcAdminTeamSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcAdminTeamSvcI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 权限PBC-管理团队树服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@RestController("PermissionPBCAthAdminTeamCfgBizFlowSystemPbcRuleSvcCON")
public class SystemPbcAdminTeamSvcController implements SystemPbcAdminTeamSvcI {

    @Autowired
    private SystemPbcAdminTeamSvc systemPbcAdminTeamSvc;

    /**
     * 权限中心-根据员工工号查询完整团队树 v2-/manage/admin/team/getTeamTree
     *
     * @return Response 权限中心-根据员工工号查询完整团队树
     */
    @Override
    public Map<String, Object> getTeamTree() {
        return systemPbcAdminTeamSvc.getTeamTree();
    }
}
