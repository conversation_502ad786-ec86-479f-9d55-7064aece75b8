package com.gwm.scaffold.monitor.domain;

import lombok.Builder;
import lombok.Data;

/**
 * 数据库指标
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@Builder
public class DatabaseMetrics {

    /**
     * 数据库是否健康
     */
    private boolean isHealthy;

    /**
     * 连接池大小
     */
    private int connectionPoolSize;

    /**
     * 活跃连接数
     */
    private int activeConnections;

    /**
     * 连接使用率（百分比）
     */
    private double connectionUsagePercent;

    /**
     * 获取空闲连接数
     */
    public int getIdleConnections() {
        return connectionPoolSize - activeConnections;
    }

    /**
     * 获取健康状态文本
     */
    public String getHealthStatus() {
        return isHealthy ? "正常" : "异常";
    }

    /**
     * 获取健康状态样式类
     */
    public String getHealthStatusClass() {
        return isHealthy ? "status-healthy" : "status-unhealthy";
    }

    /**
     * 格式化连接使用情况
     */
    public String getFormattedConnectionUsage() {
        return String.format("%d / %d (%.1f%%)", 
                activeConnections, connectionPoolSize, connectionUsagePercent);
    }
}
