package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import cn.hutool.core.annotation.Alias;
import com.alibaba.bizworks.core.specification.*;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@StructureObject(name = "SearchUserListDTO", desc = "")
@Data
public class SearchUserListDTO implements Serializable {

    /**
     * 页码
     */
    @Field(name = "页码", desc = "页码")
    private Integer page;
    /**
     * 条数（默认返回10条）
     */
    @Field(name = "条数（默认返回10条）", desc = "条数（默认返回10条）")
    private Integer size;
    /**
     * 用户账号或别名
     */
    @Field(name = "用户账号或别名", desc = "用户账号或别名")
    @Alias("query_param")
    private String keyword;

}
