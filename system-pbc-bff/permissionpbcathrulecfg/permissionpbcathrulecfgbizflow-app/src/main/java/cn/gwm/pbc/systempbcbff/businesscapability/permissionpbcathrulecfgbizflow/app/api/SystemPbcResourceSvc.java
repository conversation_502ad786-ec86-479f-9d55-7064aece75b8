package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.*;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-资源服务")
public interface SystemPbcResourceSvc {

    /**
     * 权限中心-异步加载资源树
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-异步加载资源树")
    @Method(desc = "权限中心-异步加载资源树", name = "权限中心-异步加载资源树 v2-/admin/resource/asyncLoading/resourceTree")
    Map<String, Object> asyncLoadingResourceTree(@Parameter(name = "request", required = true) AsyncClientDTO request);
    /**
     * 权限中心-通过名称模糊查询资源树
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-通过名称模糊查询资源树")
    @Method(desc = "权限中心-通过名称模糊查询资源树", name = "权限中心-通过名称模糊查询资源树 v2-/admin/resource/searchResourceTree")
    Map<String, Object> searchResourceTree(@Parameter(name = "request", required = true) SearchClientDTO request);
    /**
     * 权限中心-查询菜单树形结构
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-查询菜单树形结构")
    @Method(desc = "权限中心-查询菜单树形结构", name = "权限中心-查询菜单树形结构 v2-/admin/resource/tree")
    Map<String, Object> tree(@Parameter(name = "request", required = true) TreeClientDTO request);
    /**
     * 权限中心-创建菜单
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-创建菜单")
    @Method(desc = "权限中心-创建菜单", name = "权限中心-创建菜单 v2-/admin/resource/create")
    Map<String, Object> create(@Parameter(name = "request", required = true) AddAdminResourceFormClientDTO request);
    /**
     * 权限中心-编辑菜单
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-编辑菜单")
    @Method(desc = "权限中心-编辑菜单", name = "权限中心-编辑菜单 v2-/admin/resource/edit")
    Map<String, Object> editResource(@Parameter(name = "request", required = true) EditAdminResourceFormClientDTO request);
    /**
     * 权限中心-删除菜单
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-删除菜单")
    @Method(desc = "权限中心-删除菜单", name = "权限中心-删除菜单 v2-/admin/resource/delete")
    Map<String, Object> delResource(@Parameter(name = "request", required = true) DelAdminResourceFormClientDTO request);
    /**
     * 权限中心-根据菜单ID获取详情
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-根据菜单ID获取详情")
    @Method(desc = "权限中心-根据菜单ID获取详情", name = "权限中心-根据菜单ID获取详情 v2-/admin/resource/get")
    Map<String, Object> searchResourceById(@Parameter(name = "request", required = true) DetailResourceFormClientDTO request);

}
