package com.gwm.scaffold.auth.aspect;

import com.gwm.scaffold.auth.annotation.RequirePermission;
import com.gwm.scaffold.auth.annotation.RequireRole;
import com.gwm.scaffold.auth.service.AuthenticateService;
import com.gwm.scaffold.auth.util.LoginUserUtil;
import com.gwm.scaffold.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 权限校验切面
 * 
 * 处理@RequirePermission和@RequireRole注解的权限校验
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Aspect
@Component
@Order(100)
public class AuthAspect {

    @Autowired
    private AuthenticateService authenticateService;

    /**
     * 权限校验
     */
    @Before("@annotation(requirePermission)")
    public void checkPermission(JoinPoint joinPoint, RequirePermission requirePermission) {
        String userCode = LoginUserUtil.getUserCode();
        if (userCode == null) {
            throw new ServiceException("用户未登录");
        }

        String[] permissions = requirePermission.permissions().length > 0 
                ? requirePermission.permissions() 
                : requirePermission.value();

        if (permissions.length == 0) {
            return;
        }

        boolean hasPermission = false;
        if (requirePermission.logical() == RequirePermission.Logical.AND) {
            // 所有权限都必须满足
            hasPermission = true;
            for (String permission : permissions) {
                if (!authenticateService.hasPermission(userCode, permission)) {
                    hasPermission = false;
                    break;
                }
            }
        } else {
            // 满足任一权限即可
            for (String permission : permissions) {
                if (authenticateService.hasPermission(userCode, permission)) {
                    hasPermission = true;
                    break;
                }
            }
        }

        if (!hasPermission) {
            log.warn("用户 {} 访问 {} 权限不足，需要权限：{}", userCode, getMethodName(joinPoint), String.join(",", permissions));
            throw new ServiceException("权限不足，无法访问该资源");
        }
    }

    /**
     * 角色校验
     */
    @Before("@annotation(requireRole)")
    public void checkRole(JoinPoint joinPoint, RequireRole requireRole) {
        String userCode = LoginUserUtil.getUserCode();
        if (userCode == null) {
            throw new ServiceException("用户未登录");
        }

        String[] roles = requireRole.roles().length > 0 
                ? requireRole.roles() 
                : requireRole.value();

        if (roles.length == 0) {
            return;
        }

        boolean hasRole = false;
        if (requireRole.logical() == RequireRole.Logical.AND) {
            // 所有角色都必须满足
            hasRole = true;
            for (String role : roles) {
                if (!authenticateService.hasRole(userCode, role)) {
                    hasRole = false;
                    break;
                }
            }
        } else {
            // 满足任一角色即可
            for (String role : roles) {
                if (authenticateService.hasRole(userCode, role)) {
                    hasRole = true;
                    break;
                }
            }
        }

        if (!hasRole) {
            log.warn("用户 {} 访问 {} 角色不足，需要角色：{}", userCode, getMethodName(joinPoint), String.join(",", roles));
            throw new ServiceException("角色不足，无法访问该资源");
        }
    }

    /**
     * 获取方法名
     */
    private String getMethodName(JoinPoint joinPoint) {
        return joinPoint.getTarget().getClass().getSimpleName() + "." + joinPoint.getSignature().getName();
    }
}
