package com.gwm.scaffold.tools.util;

import com.google.common.collect.Range;
import com.google.common.collect.RangeSet;
import com.google.common.collect.TreeRangeSet;
import com.gwm.scaffold.core.exception.ServiceException;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static java.time.ZoneId.systemDefault;
import static java.time.ZoneOffset.UTC;
import static java.util.Calendar.*;

/**
 * <AUTHOR>
 * @date 2025/2/26
 */
public final class ObjectUtil {

    private static final int ZERO = 0;
    private static final int ONE = 1;
    private static final String SUPPRESS_WARNINGS_VAL = "unchecked";

    /**
     * 获取目标时间
     *
     * @param amount 距离当前日期的天数
     * @return 目标时间
     */
    public static Date getTargetDate(Integer amount) {
        Date currentDate = new Date();
        if (null == amount) {
            return currentDate;
        }
        // 创建 Calendar 实例并设置时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 步骤 1：操作 指定天数
        calendar.add(DAY_OF_YEAR, amount);
        // 步骤 2：清除时分秒（设置为 0）
        calendar.set(HOUR_OF_DAY, ZERO);
        calendar.set(MINUTE, ZERO);
        calendar.set(SECOND, ZERO);
        calendar.set(MILLISECOND, ZERO);
        return calendar.getTime();
    }

    /**
     * 集合中区间是否存在冲突
     *
     * @param list 集合类型
     * @param t 集合元素
     * @param startFieldName 开始字段名称
     * @param endFieldName 结束字段名称
     * @return 是否存在冲突
     * @param <T> 元素类型
     */
    @SuppressWarnings(SUPPRESS_WARNINGS_VAL)
    public static <T extends Comparable<T>> boolean existConflict(List<T> list, T t, String startFieldName, String endFieldName) {
        // 获取对象的 Class 对象
        RangeSet<T> rangeSet;
        Field start;
        Field end;
        try {
            rangeSet = initRangeSet(list, startFieldName, endFieldName);
            start = t.getClass().getDeclaredField(startFieldName);
            start.setAccessible(true);
            end = t.getClass().getDeclaredField(endFieldName);
            end.setAccessible(true);
            return existConflict(rangeSet, Range.closed((T) formatRange(start.get(t)), (T) formatRange(end.get(t))));
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 集合中区间是否存在包含
     *
     * @param list 集合类型
     * @param t 集合元素
     * @param startFieldName 开始字段名称
     * @param endFieldName 结束字段名称
     * @return 是否存在冲突
     * @param <T> 元素类型
     */
    @SuppressWarnings(SUPPRESS_WARNINGS_VAL)
    public static <T extends Comparable<T>> boolean existCover(Collection<T> list, T t, String startFieldName, String endFieldName) {
        // 获取对象的 Class 对象
        RangeSet<T> rangeSet;
        Field start;
        Field end;
        try {
            rangeSet = initRangeSet(list, startFieldName, endFieldName);
            start = t.getClass().getDeclaredField(startFieldName);
            start.setAccessible(true);
            end = t.getClass().getDeclaredField(endFieldName);
            end.setAccessible(true);
            return existCover(rangeSet, Range.closed((T) formatRange(start.get(t)), (T) formatRange(end.get(t))));
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 初始化区间集合
     *
     * @param list 集合类型
     * @param startFieldName 开始字段名称
     * @param endFieldName 结束字段名称
     * @return 是否存在冲突
     * @param <T> 元素类型
     */
    @SuppressWarnings(SUPPRESS_WARNINGS_VAL)
    private static <T extends Comparable<T>> RangeSet<T> initRangeSet(Collection<T> list,String startFieldName, String endFieldName){
        RangeSet<T> rangeSet = TreeRangeSet.create();
        Field start;
        Field end;
        try {
            for (T item : list) {
                start = item.getClass().getDeclaredField(startFieldName);
                start.setAccessible(true);
                end = item.getClass().getDeclaredField(endFieldName);
                end.setAccessible(true);
                rangeSet.add(Range.closed((T) formatRange(start.get(item)), (T) formatRange(end.get(item))));
            }
            return rangeSet;
        }catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 返回区间包含的间隔
     *
     * @param rangeSet        区间集合
     * @param containingRange 区间
     * @param <C>             区间类型
     * @return 包含的区间间隔
     */
    public static <C extends Comparable<C>> List<Range<C>> getContainedRanges(RangeSet<C> rangeSet, Range<C> containingRange) {
        List<Range<C>> containedRanges = new ArrayList<>();
        // 遍历 RangeSet 中的所有区间
        for (Range<C> range : rangeSet.asRanges()) {
            // 判断当前区间是否被指定区间包含
            if (containingRange.encloses(range)) {
                // 如果包含，则将该区间添加到结果列表中
                containedRanges.add(range);
            }
        }
        return containedRanges;
    }

    /**
     * 区间是否存在覆盖关系
     *
     * @param rangeSet        区间集合
     * @param containingRange 区间范围
     * @param <C>             区间类型
     * @return 是否存在覆盖关系
     */
    public static <C extends Comparable<C>> boolean existCover(RangeSet<C> rangeSet, Range<C> containingRange) {
        // 遍历 RangeSet 中的所有区间
        for (Range<C> range : rangeSet.asRanges()) {
            // 判断当前区间是否被指定区间包含
            if (containingRange.encloses(range)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 区间范围 是否存在冲突
     *
     * @param rangeSet 区间集合
     * @param newRange 区间范围
     * @param <C>      区间类型
     * @return 是否存在冲突
     */
    public static <C extends Comparable<C>> boolean existConflict(RangeSet<C> rangeSet, Range<C> newRange) {
        // 遍历 RangeSet 中的每个已有区间
        for (Range<C> existingRange : rangeSet.asRanges()) {
            // 判断新区间与已有区间是否相连且交集不为空
            if (existingRange.isConnected(newRange) && !existingRange.intersection(newRange).isEmpty()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取集合最后一个元素
     *
     * @param list 集合
     * @param <T>  元素类型
     * @return 集合最后一个元素
     */
    public static <T> T getLastElement(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(list.size() - ONE);
    }

    /**
     * 将对象转换为map
     * 非基本类型不进行转换
     *
     * @param obj 待转换的对象
     * @return 转换后的集合
     * @throws IllegalAccessException 非法访问异常
     */
    public static Map<? extends String, ? extends String> objectToMap(Object obj) {
        if (null == obj) {
            throw new ServiceException("传入转换对象为空");
        }
        Map<String, String> map = new HashMap<>(16);
        Class<?> clazz = obj.getClass();
        Class<?> fieldType;
        for (Field field : clazz.getDeclaredFields()) {
            fieldType = field.getType();
            if (fieldType.isPrimitive() || fieldType == String.class ||
                    fieldType == Boolean.class || fieldType == Character.class ||
                    fieldType == Byte.class || fieldType == Short.class ||
                    fieldType == Integer.class || fieldType == Long.class ||
                    fieldType == Float.class || fieldType == Double.class ||
                    fieldType == BigDecimal.class || fieldType == Date.class) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(obj).toString());
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return map;
    }

    /**
     * 格式化区间数据
     *
     * @param t 区间数据对象
     * @return 返回格式化后的区间类型
     * @param <T> 区间数据类型
     */
    private static <T> Object formatRange(T t){
        if (t instanceof Date){
            return ((Date) t).getTime();
        }
        if (t instanceof LocalDate){
            return ((LocalDate) t).atStartOfDay(systemDefault()).toInstant().toEpochMilli();
        }
        if (t instanceof LocalDateTime){
            return ((LocalDateTime) t).toInstant(UTC).toEpochMilli();
        }
        return t;
    }

    private ObjectUtil() {
    }
}
