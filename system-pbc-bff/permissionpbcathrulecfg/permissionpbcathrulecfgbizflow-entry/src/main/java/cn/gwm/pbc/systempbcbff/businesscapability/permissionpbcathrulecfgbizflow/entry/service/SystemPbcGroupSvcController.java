package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcGroupSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcGroupSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.group.SearchGroupClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource.SearchAdminResourceOfRoleFormClientDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 权限PBC-组织管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@RestController("PermissionPBCAthGroupCfgBizFlowSystemPbcRuleSvcCON")
public class SystemPbcGroupSvcController implements SystemPbcGroupSvcI {

    @Autowired
    private SystemPbcGroupSvc systemPbcGroupSvc;

    /**
     * 权限中心-通过账号获取树形结构 v2-/manage/admin/group/tree
     *
     * @return Response 权限中心-通过账号获取树形结构
     */
    @Override
    public Map<String, Object> groupTree() {
        return systemPbcGroupSvc.groupTree();
    }

    /**
     * 权限中心-获取完整树形结构（管理组织） v2-/manage/admin/group/tree/all
     *
     * @return Response 权限中心-获取完整树形结构（管理组织）
     */
    @Override
    public Map<String, Object> groupTreeAll() {
        return systemPbcGroupSvc.groupTreeAll();
    }

    /**
     * 权限中心-获取完整树形结构（HR组织） v2-/manage/admin/group/tree/hr/all
     *
     * @return Response 权限中心-获取完整树形结构（HR组织）
     */
    @Override
    public Map<String, Object> hrGroupTreeAll() {
        return systemPbcGroupSvc.hrGroupTreeAll();
    }

    /**
     * 权限中心-查询组织管理员 v2-/manage/admin/group/info
     *
     * @param request
     * @return Response 权限中心-查询组织管理员
     */
    @Override
    public Map<String, Object> groupInfo(SearchGroupClientDTO request) {
        return systemPbcGroupSvc.groupInfo(request);
    }
}
