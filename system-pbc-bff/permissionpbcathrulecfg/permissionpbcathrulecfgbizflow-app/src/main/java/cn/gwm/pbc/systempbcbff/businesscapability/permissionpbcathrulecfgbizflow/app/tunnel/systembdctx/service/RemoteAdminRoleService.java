package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.AdminRemoveUsersOfRoleForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.RoleBindGroupForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.SearchAdminUsersOfRoleForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.interceptor.AuthCenterRequestInterceptor;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
/**
 * <AUTHOR>
 */
@FeignClient(name = "remoteAdminRoleService", url = "EMPTY", configuration = {AuthCenterRequestInterceptor.class})
public interface RemoteAdminRoleService {

    /**
     * v2 角色绑定组织
     * @param roleBindGroupForm
     * @return CommonRlt<JSONObject>
     */
    @PostMapping("/admin/role/bind/group")
    CommonRlt<JSONObject> roleBindGroup(@RequestBody RoleBindGroupForm roleBindGroupForm);

    /**
     * v2 查询角色用户和组织列表
     * @param searchAdminUsersOfRoleForm
     * @return CommonRlt<JSONObject>
     */
    @PostMapping("/admin/role/userAndGroup/list")
    CommonRlt<JSONObject> searchUserAndGroupOfRole(@RequestBody SearchAdminUsersOfRoleForm searchAdminUsersOfRoleForm);

    /**
     *  解除绑定用户
     * @param adminRemoveUsersOfRoleForm
     * @return CommonRlt
     */
    @PostMapping("/admin/role/user/unbind")
    CommonRlt removeUsersOfRole( @RequestBody AdminRemoveUsersOfRoleForm adminRemoveUsersOfRoleForm);

}
