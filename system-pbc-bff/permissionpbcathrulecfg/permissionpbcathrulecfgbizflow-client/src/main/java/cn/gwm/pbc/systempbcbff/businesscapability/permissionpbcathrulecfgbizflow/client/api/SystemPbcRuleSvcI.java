package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.department.TeamTreeDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule.*;
import com.alibaba.bizworks.core.runtime.common.Response;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * 权限PBC-规则服务api
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@FeignClient(value = "system-pbc-bff", contextId = "SystemPbcRuleSvcI-1702533187760")
@RequestMapping(path = "/PermissionPBCAthRuleCfgBizFlow")
public interface SystemPbcRuleSvcI {

    /**
     * 权限中心-查询规则列表 v2-manage/admin/role/rule/list
     *
     * @param request request
     * @return Response 权限中心-查询应用下用户组树形列表
     */
    @PostMapping(path = "/SystemPbcRuleSvc/ruleList/v1")
    Map<String, Object> ruleList(@RequestBody(required = true) RuleListFormClientDTO request);

    /**
     *  权限中心-规则列表(下拉列表) v2-manage/admin/role/rule/list/drop/down
     *
     * @param request
     * @return
     */
    @PostMapping(path = "/SystemPbcRuleSvc/listDropDown/v1")
    Map<String, Object> listDropDown(@RequestBody(required = true) RuleListDownClientDTO request);

    /**
     *  权限中心-规则创建 v2-manage/admin/role/rule/create
     *
     * @param request
     * @return
     */
    @PostMapping(path = "/SystemPbcRuleSvc/create/v1")
    Map<String, Object> create(@RequestBody(required = true) RuleCreateFormClientDTO request);

    /**
     * 权限中心-规则详情 v2-manage/admin/role/rule/detail
     *
     * @param request
     * @return
     */
    @PostMapping(path = "/SystemPbcRuleSvc/detail/v1")
    Map<String, Object> detail(@RequestBody(required = true) RuleDetailClientDTO request);

    /**
     *  权限中心-规则编辑 v2-manage/admin/rule/edit
     *
     * @param request
     * @return
     */
    @PostMapping(path = "/SystemPbcRuleSvc/edit/v1")
    Map<String, Object> edit(@RequestBody(required = true) RuleEditFormClientDTO request);

    /**
     *  权限中心-规则删除 v2-manage/admin/role/rule/delete
     *
     * @param request
     * @return
     */
    @PostMapping(path = "/SystemPbcRuleSvc/delete/v1")
    Map<String, Object> delete(@RequestBody(required = true) RuleDelFormClientDTO request);

    /**
     *  权限中心-规则状态修改 v2-manage/admin/role/rule/edit/enable
     *
     * @param request
     * @return
     */
    @PostMapping(path = "/SystemPbcRuleSvc/editEnable/v1")
    Map<String, Object> editEnable(@RequestBody(required = true) RuleDelFormClientDTO request);
}
