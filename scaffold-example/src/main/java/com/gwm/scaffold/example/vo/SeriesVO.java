package com.gwm.scaffold.example.vo;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 视图对象VO
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "信息")
public class SeriesVO {

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * name
     */
    @Schema(description = "name")
    private String name;

    /**
     * englishName
     */
    @Schema(description = "englishName")
    private String englishName;

    /**
     * brandId
     */
    @Schema(description = "brandId")
    private Long brandId;

    /**
     * type
     */
    @Schema(description = "type")
    private String type;

    /**
     * level
     */
    @Schema(description = "level")
    private String level;

    /**
     * launchTime
     */
    @Schema(description = "launchTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date launchTime;

    /**
     * discontinueTime
     */
    @Schema(description = "discontinueTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date discontinueTime;

    /**
     * minPrice
     */
    @Schema(description = "minPrice")
    private BigDecimal minPrice;

    /**
     * maxPrice
     */
    @Schema(description = "maxPrice")
    private BigDecimal maxPrice;

    /**
     * imageUrl
     */
    @Schema(description = "imageUrl")
    private String imageUrl;

    /**
     * description
     */
    @Schema(description = "description")
    private String description;

    /**
     * status
     */
    @Schema(description = "status")
    private String status;

    /**
     * sortWeight
     */
    @Schema(description = "sortWeight")
    private Integer sortWeight;

    /**
     * deletedTime
     */
    @Schema(description = "deletedTime")
    private LocalDateTime deletedTime;

    /**
     * createTime
     */
    @Schema(description = "createTime")
    private LocalDateTime createTime;

    /**
     * createUserCode
     */
    @Schema(description = "createUserCode")
    private String createUserCode;

    /**
     * createUserName
     */
    @Schema(description = "createUserName")
    private String createUserName;

    /**
     * updateTime
     */
    @Schema(description = "updateTime")
    private LocalDateTime updateTime;

    /**
     * updateUserCode
     */
    @Schema(description = "updateUserCode")
    private String updateUserCode;

    /**
     * updateUserName
     */
    @Schema(description = "updateUserName")
    private String updateUserName;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

}
