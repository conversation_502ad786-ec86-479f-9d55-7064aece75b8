package com.gwm.scaffold.example.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.monitor.annotation.Monitor;
import com.gwm.scaffold.example.dto.OrderCreateRequest;
import com.gwm.scaffold.example.dto.OrderQueryRequest;
import com.gwm.scaffold.example.dto.OrderUpdateRequest;
import com.gwm.scaffold.example.entity.Order;
import com.gwm.scaffold.example.mapper.OrderMapper;
import com.gwm.scaffold.example.service.OrderService;
import com.gwm.scaffold.example.vo.OrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单服务实现类
 * 
 * 提供订单相关的业务功能实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    private static final AtomicLong ORDER_SEQUENCE = new AtomicLong(1);

    @Override
    @Monitor(value = "订单分页查询", threshold = 1000)
    public IPage<OrderVO> getOrderPage(OrderQueryRequest request) {
        // 参数校验
//        ValidationUtil.validatePageParams(request.getCurrent(), request.getSize());

        Page<OrderVO> page = new Page<>(request.getCurrent(), request.getSize());
        return baseMapper.selectOrderPage(page, request);
    }

    @Override
    @Monitor(value = "获取订单详情")
    public OrderVO getOrderById(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("订单ID不能为空");
        }

        OrderVO orderVO = baseMapper.selectOrderById(id);
        if (orderVO == null) {
            throw new ServiceException("订单不存在");
        }

        return orderVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "创建订单")
    public OrderVO createOrder(OrderCreateRequest request) {
        // 参数校验
        validateCreateRequest(request);

        // 创建订单
        Order order = new Order();
        BeanUtils.copyProperties(request, order);
        
        // 生成订单号
        order.setOrderNo(generateOrderNo());
        
        // 计算金额
        calculateOrderAmount(order, request);
        
        // 设置默认状态
        order.setOrderStatus(Order.OrderStatus.PENDING_PAYMENT.getCode());
        order.setVersion(0);

        boolean success = save(order);
        if (!success) {
            throw new ServiceException("创建订单失败");
        }

        log.info("创建订单成功，订单号：{}", order.getOrderNo());
        return getOrderById(order.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新订单")
    public OrderVO updateOrder(OrderUpdateRequest request) {
        // 参数校验
        validateUpdateRequest(request);

        Order existingOrder = getById(request.getId());
        if (existingOrder == null) {
            throw new ServiceException("订单不存在");
        }

        // 检查版本号（乐观锁）
        if (!existingOrder.getVersion().equals(request.getVersion())) {
            throw new ServiceException("订单已被其他用户修改，请刷新后重试");
        }

        // 更新订单信息
        Order order = new Order();
        BeanUtils.copyProperties(request, order);
        
        // 重新计算金额（如果相关字段有变化）
        if (request.getQuantity() != null || request.getUnitPrice() != null || request.getDiscountAmount() != null) {
            calculateOrderAmountForUpdate(order, existingOrder, request);
        }

        boolean success = updateById(order);
        if (!success) {
            throw new ServiceException("更新订单失败");
        }

        log.info("更新订单成功，订单ID：{}", request.getId());
        return getOrderById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "删除订单")
    public boolean deleteOrder(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("订单ID不能为空");
        }

        Order order = getById(id);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        boolean success = removeById(id);
        if (success) {
            log.info("删除订单成功，订单ID：{}", id);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "批量删除订单")
    public int batchDeleteOrders(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("订单ID列表不能为空");
        }

        boolean success = removeByIds(ids);
        int count = success ? ids.size() : 0;
        
        if (success) {
            log.info("批量删除订单成功，删除数量：{}", count);
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新订单状态")
    public boolean updateOrderStatus(Long id, Integer status) {
        if (id == null || id <= 0) {
            throw new ServiceException("订单ID不能为空");
        }
        
        if (status == null || status < 1 || status > 5) {
            throw new ServiceException("订单状态值不正确");
        }

        Order order = getById(id);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        order.setOrderStatus(status);
        
        // 根据状态设置相应的时间
        LocalDateTime now = LocalDateTime.now();
        switch (status) {
            case 2: // 已支付
                order.setPaymentTime(now);
                break;
            case 3: // 已发货
                order.setShippingTime(now);
                break;
            case 4: // 已完成
                order.setDeliveryTime(now);
                break;
        }

        boolean success = updateById(order);
        if (success) {
            log.info("更新订单状态成功，订单ID：{}，状态：{}", id, status);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "批量更新订单状态")
    public int batchUpdateOrderStatus(List<Long> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("订单ID列表不能为空");
        }
        
        if (status == null || status < 1 || status > 5) {
            throw new ServiceException("订单状态值不正确");
        }

        int count = 0;
        for (Long id : ids) {
            if (updateOrderStatus(id, status)) {
                count++;
            }
        }
        
        log.info("批量更新订单状态成功，更新数量：{}", count);
        return count;
    }

    @Override
    @Monitor(value = "根据订单号查询订单")
    public OrderVO getOrderByOrderNo(String orderNo) {
        if (!StringUtils.hasText(orderNo)) {
            throw new ServiceException("订单号不能为空");
        }

        Order order = baseMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        return getOrderById(order.getId());
    }

    @Override
    @Monitor(value = "根据客户ID查询订单列表")
    public List<OrderVO> getOrdersByCustomerId(Long customerId) {
        if (customerId == null || customerId <= 0) {
            throw new ServiceException("客户ID不能为空");
        }

        return baseMapper.selectOrdersByCustomerId(customerId);
    }

    @Override
    public String generateOrderNo() {
        // 生成格式：ORD + yyyyMMdd + 6位序列号
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        long sequence = ORDER_SEQUENCE.getAndIncrement();
        return String.format("ORD%s%06d", dateStr, sequence % 1000000);
    }

    @Override
    @Monitor(value = "获取订单状态统计")
    public Map<String, Long> getOrderStatusStatistics() {
        List<OrderMapper.OrderStatusCount> statusCounts = baseMapper.selectOrderCountByStatus();
        
        Map<String, Long> statistics = new HashMap<>();
        for (OrderMapper.OrderStatusCount statusCount : statusCounts) {
            Order.OrderStatus status = Order.OrderStatus.fromCode(statusCount.getOrderStatus());
            String statusDesc = status != null ? status.getDescription() : "未知状态";
            statistics.put(statusDesc, statusCount.getCount());
        }
        
        return statistics;
    }

    /**
     * 校验创建请求参数
     */
    private void validateCreateRequest(OrderCreateRequest request) {
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }
        
//        ValidationUtil.validateNotBlank(request.getCustomerName(), "客户姓名");
//        ValidationUtil.validateNotBlank(request.getProductName(), "商品名称");
        
        if (request.getQuantity() == null || request.getQuantity() <= 0) {
            throw new ServiceException("商品数量必须大于0");
        }
        
        if (request.getUnitPrice() == null || request.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("商品单价必须大于0");
        }
    }

    /**
     * 校验更新请求参数
     */
    private void validateUpdateRequest(OrderUpdateRequest request) {
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }
        
        if (request.getId() == null || request.getId() <= 0) {
            throw new ServiceException("订单ID不能为空");
        }
        
        if (request.getVersion() == null) {
            throw new ServiceException("版本号不能为空");
        }
    }

    /**
     * 计算订单金额
     */
    private void calculateOrderAmount(Order order, OrderCreateRequest request) {
        BigDecimal unitPrice = request.getUnitPrice();
        Integer quantity = request.getQuantity();
        BigDecimal discountAmount = request.getDiscountAmount() != null ? request.getDiscountAmount() : BigDecimal.ZERO;
        
        // 计算总金额
        BigDecimal totalAmount = unitPrice.multiply(new BigDecimal(quantity));
        order.setTotalAmount(totalAmount);
        order.setDiscountAmount(discountAmount);
        
        // 计算实际支付金额
        BigDecimal actualAmount = totalAmount.subtract(discountAmount);
        if (actualAmount.compareTo(BigDecimal.ZERO) < 0) {
            actualAmount = BigDecimal.ZERO;
        }
        order.setActualAmount(actualAmount);
    }

    /**
     * 计算更新订单的金额
     */
    private void calculateOrderAmountForUpdate(Order order, Order existingOrder, OrderUpdateRequest request) {
        BigDecimal unitPrice = request.getUnitPrice() != null ? request.getUnitPrice() : existingOrder.getUnitPrice();
        Integer quantity = request.getQuantity() != null ? request.getQuantity() : existingOrder.getQuantity();
        BigDecimal discountAmount = request.getDiscountAmount() != null ? request.getDiscountAmount() : existingOrder.getDiscountAmount();
        
        // 计算总金额
        BigDecimal totalAmount = unitPrice.multiply(new BigDecimal(quantity));
        order.setTotalAmount(totalAmount);
        order.setDiscountAmount(discountAmount);
        
        // 计算实际支付金额
        BigDecimal actualAmount = totalAmount.subtract(discountAmount);
        if (actualAmount.compareTo(BigDecimal.ZERO) < 0) {
            actualAmount = BigDecimal.ZERO;
        }
        order.setActualAmount(actualAmount);
    }
}
