{"info": {"_postman_id": "brand-quick-test", "name": "品牌管理API快速测试", "description": "品牌管理模块的快速验证测试集合，包含核心功能的基本测试用例", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 创建测试品牌", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"创建品牌成功\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(jsonData.data).to.have.property('id');", "    pm.environment.set(\"createdBrandId\", jsonData.data.id);", "    console.log(\"品牌创建成功，ID: \" + jsonData.data.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"快速测试品牌\",\n  \"englishName\": \"Quick Test Brand\",\n  \"country\": \"中国\",\n  \"foundedYear\": 2020,\n  \"website\": \"https://www.quicktest.com\",\n  \"description\": \"用于快速测试的品牌\",\n  \"status\": \"ACTIVE\",\n  \"sortWeight\": 100\n}"}, "url": {"raw": "{{baseUrl}}/api/brands", "host": ["{{baseUrl}}"], "path": ["api", "brands"]}}, "response": []}, {"name": "2. 查询品牌详情", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"查询品牌详情成功\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(jsonData.data.name).to.eql('快速测试品牌');", "    pm.expect(jsonData.data.status).to.eql('ACTIVE');", "    console.log(\"品牌查询成功: \" + jsonData.data.name);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/brands/{{createdBrandId}}", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{createdBrandId}}"]}}, "response": []}, {"name": "3. 分页查询品牌列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"分页查询成功\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(jsonData.data).to.have.property('records');", "    pm.expect(jsonData.data).to.have.property('total');", "    console.log(\"查询到品牌数量: \" + jsonData.data.total);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/brands?current=1&size=10", "host": ["{{baseUrl}}"], "path": ["api", "brands"], "query": [{"key": "current", "value": "1"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "4. 更新品牌信息", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"更新品牌成功\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(jsonData.data.description).to.include('已更新');", "    console.log(\"品牌更新成功\");", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"已更新的品牌描述信息\",\n  \"sortWeight\": 200\n}"}, "url": {"raw": "{{baseUrl}}/api/brands/{{createdBrandId}}", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{createdBrandId}}"]}}, "response": []}, {"name": "5. 更新品牌状态", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"状态更新成功\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    console.log(\"品牌状态更新成功\");", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/brands/{{createdBrandId}}/status?status=INACTIVE", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{createdBrandId}}", "status"], "query": [{"key": "status", "value": "INACTIVE"}]}}, "response": []}, {"name": "6. 获取启用品牌列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取启用品牌列表成功\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(Array.isArray(jsonData.data)).to.be.true;", "    console.log(\"启用品牌数量: \" + jsonData.data.length);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/brands/active", "host": ["{{baseUrl}}"], "path": ["api", "brands", "active"]}}, "response": []}, {"name": "7. 删除测试品牌", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"删除品牌成功\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    console.log(\"品牌删除成功\");", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/brands/{{createdBrandId}}", "host": ["{{baseUrl}}"], "path": ["api", "brands", "{{createdBrandId}}"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 设置默认环境变量", "if (!pm.environment.get(\"baseUrl\")) {", "    pm.environment.set(\"baseUrl\", \"http://localhost:8080\");", "}", "", "// 设置请求时间戳", "pm.environment.set(\"timestamp\", new Date().getTime());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局测试脚本", "pm.test(\"响应时间小于2秒\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test(\"响应格式为JSON\", function () {", "    pm.response.to.be.json;", "});"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "createdBrandId", "value": "", "type": "string"}]}