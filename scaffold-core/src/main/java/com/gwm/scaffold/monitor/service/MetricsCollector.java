package com.gwm.scaffold.monitor.service;

import com.gwm.scaffold.monitor.domain.AppMetrics;
import com.gwm.scaffold.monitor.domain.BusinessMetrics;
import com.gwm.scaffold.monitor.domain.DatabaseMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.ThreadMXBean;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 指标收集服务
 * 
 * 收集应用运行时的各种指标数据
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Service
public class MetricsCollector {

    private final DataSource dataSource;
    private final MemoryMXBean memoryMXBean;
    private final OperatingSystemMXBean osMXBean;
    private final ThreadMXBean threadMXBean;
    
    // 业务指标计数器
    private final AtomicLong requestCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final AtomicLong totalResponseTime = new AtomicLong(0);
    
    // 应用启动时间
    private final long startTime = System.currentTimeMillis();

    public MetricsCollector(DataSource dataSource) {
        this.dataSource = dataSource;
        this.memoryMXBean = ManagementFactory.getMemoryMXBean();
        this.osMXBean = ManagementFactory.getOperatingSystemMXBean();
        this.threadMXBean = ManagementFactory.getThreadMXBean();
    }

    /**
     * 获取应用基础指标
     */
    public AppMetrics getAppMetrics() {
        long uptime = System.currentTimeMillis() - startTime;
        
        // 内存使用情况
        long usedMemory = memoryMXBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryMXBean.getHeapMemoryUsage().getMax();
        double memoryUsagePercent = maxMemory > 0 ? (double) usedMemory / maxMemory * 100 : 0;
        
        // CPU使用率（简化实现）
        double cpuUsage = 0;
        try {
            if (osMXBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsMXBean =
                    (com.sun.management.OperatingSystemMXBean) osMXBean;
                cpuUsage = sunOsMXBean.getProcessCpuLoad() * 100;
                if (cpuUsage < 0) {
                    cpuUsage = 0; // 某些系统可能返回负值
                }
            }
        } catch (Exception e) {
            log.warn("获取CPU使用率失败: {}", e.getMessage());
            cpuUsage = 0;
        }
        
        // 线程数
        int threadCount = threadMXBean.getThreadCount();
        
        return AppMetrics.builder()
                .uptime(uptime)
                .usedMemory(usedMemory)
                .maxMemory(maxMemory)
                .memoryUsagePercent(memoryUsagePercent)
                .cpuUsagePercent(cpuUsage)
                .threadCount(threadCount)
                .availableProcessors(Runtime.getRuntime().availableProcessors())
                .build();
    }

    /**
     * 获取业务指标
     */
    public BusinessMetrics getBusinessMetrics() {
        long totalRequests = requestCount.get();
        long totalErrors = errorCount.get();
        long avgResponseTime = totalRequests > 0 ? totalResponseTime.get() / totalRequests : 0;
        double errorRate = totalRequests > 0 ? (double) totalErrors / totalRequests * 100 : 0;
        
        // 获取当前活跃用户数（如果有认证模块）
        int activeUsers = getActiveUserCount();
        
        return BusinessMetrics.builder()
                .totalRequests(totalRequests)
                .totalErrors(totalErrors)
                .errorRate(errorRate)
                .avgResponseTime(avgResponseTime)
                .activeUsers(activeUsers)
                .qps(calculateQPS())
                .build();
    }

    /**
     * 获取数据库指标
     */
    public DatabaseMetrics getDatabaseMetrics() {
        boolean isHealthy = checkDatabaseHealth();
        int connectionPoolSize = getConnectionPoolSize();
        int activeConnections = getActiveConnections();
        
        return DatabaseMetrics.builder()
                .isHealthy(isHealthy)
                .connectionPoolSize(connectionPoolSize)
                .activeConnections(activeConnections)
                .connectionUsagePercent(connectionPoolSize > 0 ? 
                        (double) activeConnections / connectionPoolSize * 100 : 0)
                .build();
    }

    /**
     * 记录请求指标
     */
    public void recordRequest(long responseTime, boolean isError) {
        requestCount.incrementAndGet();
        totalResponseTime.addAndGet(responseTime);
        if (isError) {
            errorCount.incrementAndGet();
        }
    }

    /**
     * 检查数据库健康状态
     */
    private boolean checkDatabaseHealth() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5); // 5秒超时
        } catch (SQLException e) {
            log.warn("数据库健康检查失败", e);
            return false;
        }
    }

    /**
     * 获取连接池大小
     */
    private int getConnectionPoolSize() {
        try {
            // 尝试获取Druid连接池信息
            if (dataSource.getClass().getName().contains("DruidDataSource")) {
                Object maxActive = dataSource.getClass().getMethod("getMaxActive").invoke(dataSource);
                return (Integer) maxActive;
            }
        } catch (Exception e) {
            log.debug("无法获取连接池大小", e);
        }
        return 0;
    }

    /**
     * 获取活跃连接数
     */
    private int getActiveConnections() {
        try {
            // 尝试获取Druid连接池信息
            if (dataSource.getClass().getName().contains("DruidDataSource")) {
                Object activeCount = dataSource.getClass().getMethod("getActiveCount").invoke(dataSource);
                return (Integer) activeCount;
            }
        } catch (Exception e) {
            log.debug("无法获取活跃连接数", e);
        }
        return 0;
    }

    /**
     * 获取活跃用户数
     */
    private int getActiveUserCount() {
        try {
            // 尝试从认证模块获取活跃用户数
            // 这里可以根据实际情况实现
            return 0;
        } catch (Exception e) {
            log.debug("无法获取活跃用户数", e);
            return 0;
        }
    }

    /**
     * 计算QPS（每秒查询数）
     */
    private double calculateQPS() {
        long uptime = System.currentTimeMillis() - startTime;
        if (uptime <= 0) {
            return 0;
        }
        return (double) requestCount.get() / (uptime / 1000.0);
    }

    /**
     * 重置指标
     */
    public void resetMetrics() {
        requestCount.set(0);
        errorCount.set(0);
        totalResponseTime.set(0);
        log.info("监控指标已重置");
    }
}
