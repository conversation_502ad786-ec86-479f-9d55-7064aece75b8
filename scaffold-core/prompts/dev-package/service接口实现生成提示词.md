# 直接可用的Service代码生成提示词

## 完整提示词（可直接复制使用）

```
你是一个优秀的技术架构师和Java开发专家。请根据提供的产品需求文档(PRD)、数据库设计和接口规范，为指定的接口生成完整的Service层业务逻辑代码。

## 架构设计原则
请严格遵循以下原则：
- 遵循"第一性原理"进行业务逻辑设计
- 应用DRY、KISS、SOLID、YAGNI原则
- 单个方法不超过50行，单个类不超过500行
- 如果类或方法超过限制，请识别分解和分离

## 目标接口信息
**接口名称**: 竞品车型管理列表查询接口
**接口功能**: 根据筛选条件查询竞品车型信息，支持分页展示，包含品牌、车型、本品关系、创建更新信息等
**对应Controller**: CompetitorVehicleController.getCompetitorVehicles

## 参考文档和代码

### PRD业务规则要求
请严格按照PRD文档中"3.5.2.1 竞品车型管理列表页面"章节的完整业务需求实现，包括：
- 筛选查询功能的所有要求
- 数据列表展示的所有字段和格式要求
- 分页功能的所有要求
- 所有业务规则和边界情况处理

**参考文档**: `docs/prd/竞品车型管理模块PRD.md`

### 接口规范
请参考接口文档中的完整接口定义，包括：
- 准确的接口路径
- 完整的请求参数定义
- 完整的响应结果定义
- 所有接口规范要求

**参考文档**: `docs/接口文档/竞品车型管理功能接口文档.md`

### 数据库设计
请参考数据库脚本中的完整表结构设计，包括：
- 准确的表结构定义
- 完整的字段信息和约束
- 正确的表关系和索引
- 所有数据库设计细节

**参考文档**: `docs/db/竞品车型管理模块/scripts/02_create_tables.sql`

### 现有代码参考
请查看项目中已有的Mapper接口实现，优先使用现有方法：

**主要Mapper接口**:
- `CompetitorVehicleMapper` - 竞品车型数据访问接口
- `BrandMapper` - 品牌数据访问接口  
- `CompetitorRelationMapper` - 竞品关系数据访问接口
- `UserService` - 用户信息服务接口

**代码位置**: 请在项目源码中查找对应的Mapper接口定义

## 技术规范要求

### Service层设计规范（必须严格遵循）
1. 使用`@Service`注解标注服务类
2. 类名使用`CompetitorVehicleQueryService`/`CompetitorVehicleQueryServiceImpl`格式
3. 使用构造函数注入依赖的Mapper
4. 使用`@Slf4j`进行日志记录
5. 参数校验在Service层进行
6. 返回结果使用统一的分页响应格式

### 数据访问规范（关键要求）
1. **优先使用已有的Mapper接口和BaseMapper通用方法**
2. **避免在Service中进行多表JOIN查询**
3. **如需复杂查询，在对应Mapper中定义新的接口方法**
4. 使用MyBatis-Plus的分页插件处理分页查询
5. 单表操作优先使用BaseMapper的selectPage等方法

### 业务逻辑规范
1. 严格按照PRD中的业务规则实现
2. 数据校验要完整（必填字段、格式校验、业务规则校验）
3. 状态管理要准确（启用/停用、删除状态等）
4. 审计信息正确处理（创建人、创建时间、更新人、更新时间）
5. 用户信息格式化按PRD要求实现
6. 时间格式化按PRD要求实现

## 输出要求

请生成以下完整代码：

### 1. Service接口定义
```java
public interface CompetitorVehicleQueryService {
    /**
     * 分页查询竞品车型列表
     * @param queryDTO 查询条件（请根据接口文档定义具体参数）
     * @return 分页结果（请根据接口文档定义具体返回格式）
     */
    [具体返回类型] queryPage([具体参数类型] queryDTO);
}
```

### 2. Service实现类
要求包含：
- 完整的`@Service`注解和依赖注入
- 完整的`queryPage`方法实现
- 参数校验私有方法
- 查询条件构建私有方法  
- 数据转换私有方法
- 完整的JavaDoc注释
- 完整的日志记录
- 完整的异常处理

### 3. 如需要新增Mapper方法
如果现有BaseMapper无法满足查询需求，请提供新的Mapper方法定义。

### 4. 相关DTO和VO类
如果接口文档中的DTO和VO类不存在，请根据接口规范生成对应的类。

## 代码质量要求
- 代码必须可以直接编译运行
- 遵循阿里巴巴Java编码规范
- 完整的错误处理和边界情况处理
- 符合所有架构设计原则
- 中文注释，英文方法名和变量名
- 严格按照PRD业务规则实现，不遗漏任何功能点

## 实现步骤建议
1. 首先仔细阅读PRD文档中的业务需求
2. 查看接口文档了解准确的接口定义
3. 分析数据库表结构理解数据关系
4. 查看现有Mapper接口确定可用方法
5. 设计Service层的业务逻辑结构
6. 实现完整的Service代码
7. 确保所有业务规则都正确实现

请严格按照以上要求，基于准确的参考文档生成高质量的Service层代码。
```

## 使用说明

### 使用前准备
1. 确保已准备好以下参考文档：
   - PRD文档：`docs/prd/竞品车型管理模块PRD.md`
   - 接口文档：`docs/接口文档/竞品车型管理功能接口文档.md`
   - 数据库脚本：`docs/db/竞品车型管理模块/scripts/02_create_tables.sql`
   - 项目源码中的现有Mapper接口

### 使用步骤
1. 将上述完整提示词复制给大模型
2. 同时提供所有相关的参考文档内容
3. 根据具体接口需求调整"目标接口信息"部分
4. 大模型将基于准确的文档生成完整的Service代码

### 优化特点
- **准确性**: 直接引用原始文档，避免信息失真
- **完整性**: 要求大模型查看完整的文档内容
- **灵活性**: 可以根据不同接口调整目标信息
- **规范性**: 明确的技术规范和代码质量要求

## 预期输出质量

使用此优化后的提示词，大模型将：
- 基于准确的文档信息生成代码
- 严格遵循PRD中的所有业务规则
- 使用正确的接口定义和数据库结构
- 优先使用现有的Mapper方法
- 生成可直接编译运行的高质量代码 