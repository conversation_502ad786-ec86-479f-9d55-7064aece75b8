-- H2数据库兼容的测试数据
-- 汽车品牌、车系、车型测试数据

-- 清空现有数据（可选）
-- DELETE FROM t_model WHERE id > 0;
-- DELETE FROM t_series WHERE id > 0;
-- DELETE FROM t_brand WHERE id > 0;

-- 插入品牌数据
INSERT INTO t_brand (name, english_name, country, founded_year, website, logo_url, description, status, sort_weight, create_user_code, create_user_name) VALUES
('奔驰', 'Mercedes-Benz', '德国', 1926, 'https://www.mercedes-benz.com', 'https://example.com/logo/mercedes.png', '德国豪华汽车品牌，以高品质、高性能和高科技著称', 'ACTIVE', 100, 'admin', '系统管理员'),
('宝马', 'BMW', '德国', 1916, 'https://www.bmw.com', 'https://example.com/logo/bmw.png', '德国豪华汽车制造商，专注于高性能豪华汽车', 'ACTIVE', 95, 'admin', '系统管理员'),
('奥迪', 'Audi', '德国', 1909, 'https://www.audi.com', 'https://example.com/logo/audi.png', '德国豪华汽车品牌，以quattro四驱技术闻名', 'ACTIVE', 90, 'admin', '系统管理员'),
('丰田', 'Toyota', '日本', 1937, 'https://www.toyota.com', 'https://example.com/logo/toyota.png', '日本汽车制造商，以可靠性和燃油经济性著称', 'ACTIVE', 85, 'admin', '系统管理员'),
('本田', 'Honda', '日本', 1948, 'https://www.honda.com', 'https://example.com/logo/honda.png', '日本汽车制造商，以发动机技术和可靠性闻名', 'ACTIVE', 80, 'admin', '系统管理员'),
('比亚迪', 'BYD', '中国', 1995, 'https://www.byd.com', 'https://example.com/logo/byd.png', '中国新能源汽车领导品牌，专注于电动汽车技术', 'ACTIVE', 75, 'admin', '系统管理员'),
('特斯拉', 'Tesla', '美国', 2003, 'https://www.tesla.com', 'https://example.com/logo/tesla.png', '美国电动汽车制造商，引领电动汽车革命', 'ACTIVE', 70, 'admin', '系统管理员');

-- 插入车系数据
INSERT INTO t_series (name, english_name, brand_id, type, level, launch_time, min_price, max_price, image_url, description, status, sort_weight, create_user_code, create_user_name) VALUES
-- 奔驰车系
('C级', 'C-Class', 1, 'SEDAN', 'C', '2014-01-15', 32.52, 47.48, 'https://example.com/series/benz-c.jpg', '奔驰入门级豪华轿车，兼顾舒适性和运动性', 'ON_SALE', 100, 'admin', '系统管理员'),
('E级', 'E-Class', 1, 'SEDAN', 'D', '2016-07-25', 44.28, 65.68, 'https://example.com/series/benz-e.jpg', '奔驰中大型豪华轿车，商务与舒适并重', 'ON_SALE', 95, 'admin', '系统管理员'),
('GLC', 'GLC-Class', 1, 'SUV', 'C', '2015-11-19', 40.25, 58.78, 'https://example.com/series/benz-glc.jpg', '奔驰中型豪华SUV，城市与越野兼顾', 'ON_SALE', 90, 'admin', '系统管理员'),

-- 宝马车系
('3系', '3 Series', 2, 'SEDAN', 'C', '2019-06-22', 29.39, 40.99, 'https://example.com/series/bmw-3.jpg', '宝马运动型豪华轿车，操控性能出色', 'ON_SALE', 100, 'admin', '系统管理员'),
('5系', '5 Series', 2, 'SEDAN', 'D', '2017-06-23', 42.69, 65.99, 'https://example.com/series/bmw-5.jpg', '宝马中大型豪华轿车，商务舒适首选', 'ON_SALE', 95, 'admin', '系统管理员'),
('X3', 'X3', 2, 'SUV', 'C', '2018-07-09', 38.98, 47.98, 'https://example.com/series/bmw-x3.jpg', '宝马中型豪华SUV，运动与实用并重', 'ON_SALE', 90, 'admin', '系统管理员'),

-- 奥迪车系
('A4L', 'A4L', 3, 'SEDAN', 'C', '2020-04-10', 30.58, 39.68, 'https://example.com/series/audi-a4l.jpg', '奥迪中型豪华轿车，科技配置丰富', 'ON_SALE', 100, 'admin', '系统管理员'),
('A6L', 'A6L', 3, 'SEDAN', 'D', '2018-10-19', 41.98, 65.38, 'https://example.com/series/audi-a6l.jpg', '奥迪中大型豪华轿车，商务精英之选', 'ON_SALE', 95, 'admin', '系统管理员'),
('Q5L', 'Q5L', 3, 'SUV', 'C', '2018-07-06', 39.68, 51.70, 'https://example.com/series/audi-q5l.jpg', '奥迪中型豪华SUV，quattro四驱系统', 'ON_SALE', 90, 'admin', '系统管理员'),

-- 丰田车系
('凯美瑞', 'Camry', 4, 'SEDAN', 'B', '2017-12-20', 17.98, 26.98, 'https://example.com/series/toyota-camry.jpg', '丰田中型轿车，可靠性和燃油经济性出色', 'ON_SALE', 100, 'admin', '系统管理员'),
('汉兰达', 'Highlander', 4, 'SUV', 'C', '2018-03-23', 25.88, 34.88, 'https://example.com/series/toyota-highlander.jpg', '丰田中大型SUV，7座家用首选', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 本田车系
('雅阁', 'Accord', 5, 'SEDAN', 'B', '2018-04-16', 17.98, 25.98, 'https://example.com/series/honda-accord.jpg', '本田中型轿车，运动与舒适平衡', 'ON_SALE', 100, 'admin', '系统管理员'),
('CR-V', 'CR-V', 5, 'SUV', 'B', '2017-07-09', 16.98, 27.68, 'https://example.com/series/honda-crv.jpg', '本田紧凑型SUV，城市代步首选', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 比亚迪车系
('汉', 'Han', 6, 'SEDAN', 'C', '2020-07-12', 21.98, 32.98, 'https://example.com/series/byd-han.jpg', '比亚迪旗舰轿车，刀片电池技术', 'ON_SALE', 100, 'admin', '系统管理员'),
('唐', 'Tang', 6, 'SUV', 'C', '2018-06-26', 23.99, 35.99, 'https://example.com/series/byd-tang.jpg', '比亚迪中大型SUV，插电混动技术', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 特斯拉车系
('Model 3', 'Model 3', 7, 'SEDAN', 'C', '2019-02-22', 26.59, 34.99, 'https://example.com/series/tesla-model3.jpg', '特斯拉入门级电动轿车，智能驾驶技术', 'ON_SALE', 100, 'admin', '系统管理员'),
('Model Y', 'Model Y', 7, 'SUV', 'C', '2021-01-01', 30.99, 39.99, 'https://example.com/series/tesla-modely.jpg', '特斯拉紧凑型电动SUV，超级充电网络', 'ON_SALE', 95, 'admin', '系统管理员');

-- 插入车型数据
INSERT INTO t_model (name, series_id, year_model, displacement, max_power, max_torque, transmission, gear_count, drive_type, fuel_type, body_structure, seat_count, length, width, height, wheelbase, curb_weight, tank_capacity, fuel_consumption, guide_price, launch_time, image_urls, config_details, status, sort_weight, create_user_code, create_user_name) VALUES
-- 奔驰C级车型
('C 200 L 运动版', 1, 2023, 1.5, 150, 280, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 4784, 1810, 1457, 2920, 1555, 66, 6.9, 32.52, '2023-03-15', '["https://example.com/model/benz-c200l-1.jpg", "https://example.com/model/benz-c200l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "盲点监测"], "舒适配置": ["座椅加热", "自动空调", "无线充电"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('C 260 L 豪华版', 1, 2023, 1.5, 150, 280, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 4784, 1810, 1457, 2920, 1575, 66, 7.1, 37.38, '2023-03-15', '["https://example.com/model/benz-c260l-1.jpg", "https://example.com/model/benz-c260l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "盲点监测"], "舒适配置": ["座椅加热通风", "自动空调", "无线充电", "柏林之声音响"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 奔驰E级车型
('E 300 L 豪华型', 2, 2023, 2.0, 190, 370, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 5078, 1860, 1484, 3079, 1730, 80, 7.8, 44.28, '2023-01-10', '["https://example.com/model/benz-e300l-1.jpg", "https://example.com/model/benz-e300l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["座椅按摩", "四区空调", "柏林之声音响"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('E 350 L 运动型', 2, 2023, 2.0, 220, 400, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 5078, 1860, 1484, 3079, 1750, 80, 8.2, 52.88, '2023-01-10', '["https://example.com/model/benz-e350l-1.jpg", "https://example.com/model/benz-e350l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["座椅按摩", "四区空调", "柏林之声音响", "运动套件"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 奔驰GLC车型
('GLC 260 L 4MATIC', 3, 2023, 1.5, 150, 280, '9G-TRONIC', 9, 'AWD', 'GASOLINE', 'SUV', 5, 4764, 1898, 1648, 2873, 1770, 66, 7.8, 40.25, '2023-05-20', '["https://example.com/model/benz-glc260l-1.jpg", "https://example.com/model/benz-glc260l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["座椅加热", "全景天窗", "无线充电"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('GLC 300 L 4MATIC 豪华型', 3, 2023, 2.0, 190, 370, '9G-TRONIC', 9, 'AWD', 'GASOLINE', 'SUV', 5, 4764, 1898, 1648, 2873, 1820, 66, 8.3, 48.38, '2023-05-20', '["https://example.com/model/benz-glc300l-1.jpg", "https://example.com/model/benz-glc300l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["座椅加热通风", "全景天窗", "无线充电", "柏林之声音响"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 宝马3系车型
('325Li M运动套装', 4, 2023, 2.0, 135, 300, '8挡手自一体', 8, 'RWD', 'GASOLINE', 'SEDAN', 5, 4829, 1827, 1463, 2961, 1555, 59, 6.9, 29.39, '2023-02-15', '["https://example.com/model/bmw-325li-1.jpg", "https://example.com/model/bmw-325li-2.jpg"]', '{"安全配置": ["主动刹车", "车道偏离预警"], "舒适配置": ["运动座椅", "自动空调", "哈曼卡顿音响"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('330Li xDrive M运动套装', 4, 2023, 2.0, 185, 300, '8挡手自一体', 8, 'AWD', 'GASOLINE', 'SEDAN', 5, 4829, 1827, 1463, 2961, 1590, 59, 7.1, 36.19, '2023-02-15', '["https://example.com/model/bmw-330li-1.jpg", "https://example.com/model/bmw-330li-2.jpg"]', '{"安全配置": ["主动刹车", "车道偏离预警", "自适应巡航"], "舒适配置": ["运动座椅", "自动空调", "哈曼卡顿音响", "全景天窗"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 宝马5系车型
('525Li 豪华套装', 5, 2023, 2.0, 135, 290, '8挡手自一体', 8, 'RWD', 'GASOLINE', 'SEDAN', 5, 5106, 1868, 1500, 3105, 1680, 68, 7.2, 42.69, '2023-04-10', '["https://example.com/model/bmw-525li-1.jpg", "https://example.com/model/bmw-525li-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "四区空调", "哈曼卡顿音响"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('530Li xDrive 豪华套装', 5, 2023, 2.0, 185, 350, '8挡手自一体', 8, 'AWD', 'GASOLINE', 'SEDAN', 5, 5106, 1868, 1500, 3105, 1720, 68, 7.8, 50.39, '2023-04-10', '["https://example.com/model/bmw-530li-1.jpg", "https://example.com/model/bmw-530li-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "四区空调", "宝华韦健音响", "全景天窗"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 宝马X3车型
('xDrive25i M运动套装', 6, 2023, 2.0, 135, 290, '8挡手自一体', 8, 'AWD', 'GASOLINE', 'SUV', 5, 4717, 1891, 1689, 2864, 1770, 65, 7.6, 38.98, '2023-03-20', '["https://example.com/model/bmw-x3-25i-1.jpg", "https://example.com/model/bmw-x3-25i-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持"], "舒适配置": ["运动座椅", "全景天窗", "哈曼卡顿音响"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('xDrive30i M运动套装', 6, 2023, 2.0, 185, 350, '8挡手自一体', 8, 'AWD', 'GASOLINE', 'SUV', 5, 4717, 1891, 1689, 2864, 1810, 65, 8.1, 47.98, '2023-03-20', '["https://example.com/model/bmw-x3-30i-1.jpg", "https://example.com/model/bmw-x3-30i-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["运动座椅", "全景天窗", "宝华韦健音响"]}', 'ON_SALE', 95, 'admin', '系统管理员');
