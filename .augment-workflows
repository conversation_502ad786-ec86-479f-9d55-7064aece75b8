# AI 开发工作流程规范

## 🎯 核心原则
1. **严格按需求执行** - 禁止猜测和发散，严格按照用户需求文档执行
2. **分步骤实施** - 将复杂任务分解为明确的步骤
3. **先分析后编码** - 充分理解需求后再开始编码
4. **持续确认** - 关键决策点需要用户确认

## 📋 标准开发流程

### 第一步：需求分析
- 仔细阅读用户需求描述
- 识别核心功能点
- 确认技术实现方案
- **禁止**：添加需求中未提及的功能

### 第二步：代码架构设计
- 分析代码调用流程和依赖关系
- 确定需要修改或新增的代码层次
- 规划API接口的调用链路
- 确认使用的脚手架工具类和现有组件
- **注意**：技术方案和数据模型通常已在前期设计完成

### 第三步：脚手架生成与代码实现
#### 3.1 脚手架代码生成（推荐优先使用）
- **优先使用脚手架生成器**生成基础CRUD代码
- 脚手架可生成：Entity、Mapper、Service、Controller
- 生成的代码更准确，符合项目规范
- 减少手工编码错误

#### 3.2 手工代码实现顺序（仅在脚手架无法满足时使用）
1. **Entity** - 实体类（继承BaseSuperEntity）
2. **DTO** - 请求和响应对象
3. **VO** - 视图对象
4. **Mapper** - 数据访问接口
5. **Service** - 业务逻辑接口和实现
6. **Controller** - 控制器

#### 3.3 业务逻辑实现
- 在生成的基础代码上添加具体业务逻辑
- 实现复杂查询和业务规则
- 添加数据校验和异常处理

### 第四步：质量检查
- 检查是否使用了脚手架提供的工具类
- 验证异常处理是否使用ServiceException
- 确认命名规范是否正确
- 检查是否遵循baomidou原始接口

## 🔧 具体执行规则

### 代码生成规则
```
当用户要求生成代码时：
1. 首先确认具体需求范围
2. **优先询问**：是否可以使用脚手架生成器
3. 如果使用脚手架：
   - 确认生成参数（表名、实体名、包路径等）
   - 指导用户使用脚手架生成基础代码
   - 在生成的基础上添加业务逻辑
4. 如果手工编码：
   - 列出将要生成的文件清单
   - 按照标准顺序逐个生成
   - 每个文件都要符合脚手架规范
5. 生成完成后提供使用说明
```

### 问题处理规则
```
当遇到不明确的需求时：
1. 不要猜测用户意图
2. 明确询问具体要求
3. 提供多个选项供用户选择
4. 等待用户确认后再继续
```

### 工具类使用规则
```
在编写代码时必须：
1. 优先检查脚手架是否有相关工具类
2. 使用完整的包路径引用工具类
3. 不要重复造轮子
4. 如需新功能，先询问是否有现有工具
```

## 📝 常用工作模式

### 模式一：CRUD模块生成（推荐使用脚手架）
```
用户请求：创建XXX管理模块
执行步骤：
1. 确认实体字段和业务规则
2. **优先方案**：使用脚手架生成器生成基础CRUD代码
   - 脚手架生成Entity、Mapper、Service、Controller
   - 确保生成的代码符合项目规范
3. **补充方案**：手工生成脚手架无法覆盖的部分
   - 复杂业务逻辑
   - 特殊查询方法
   - 自定义校验规则
4. 在生成的基础上添加具体业务实现
5. 提供测试建议
```

### 模式二：脚手架生成后的业务实现
```
用户请求：在脚手架生成的基础上实现具体业务
执行步骤：
1. 分析脚手架生成的代码结构
2. 确认需要添加的业务逻辑
3. 在Service层添加业务方法
4. 在Mapper中添加复杂查询（如需要）
5. 在Controller中添加业务接口
6. 添加必要的DTO和VO
7. 确保不破坏脚手架生成的基础功能
```

### 模式三：功能增强
```
用户请求：为现有模块添加XXX功能
执行步骤：
1. 分析现有代码结构
2. 确认新功能的影响范围
3. 列出需要修改的文件
4. 逐个文件进行修改
5. 确保不破坏现有功能
```

### 模式四：问题诊断
```
用户请求：解决XXX问题
执行步骤：
1. 分析问题现象
2. 检查相关代码
3. 定位问题根因
4. 提供解决方案
5. 给出预防措施
```

## ⚠️ 禁止行为

1. **禁止猜测** - 不明确时必须询问
2. **禁止发散** - 严格按需求范围执行
3. **禁止创新** - 使用已有的技术栈和工具
4. **禁止跳步** - 必须按流程顺序执行
5. **禁止假设** - 所有决策都要有明确依据

## 🎯 成功标准

代码生成成功的标准：
- ✅ **优先使用脚手架生成器**生成基础代码
- ✅ 完全符合脚手架技术规范
- ✅ 使用了合适的脚手架工具类
- ✅ 遵循了正确的命名规范
- ✅ 实现了用户要求的功能
- ✅ 没有添加额外的功能
- ✅ 代码可以直接运行
- ✅ 在脚手架生成的基础上正确添加了业务逻辑
