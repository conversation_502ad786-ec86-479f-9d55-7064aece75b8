package com.gwm.scaffold.example.controller;

import com.gwm.scaffold.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * Hello 控制器 - 简单示例
 * 
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@Tag(name = "Hello示例", description = "简单的Hello World示例接口")
@RestController
@RequestMapping("/api/hello")
public class HelloController {

    /**
     * Hello World
     */
    @Operation(summary = "Hello World", description = "返回Hello World消息")
    @GetMapping
    public Result<String> hello() {
        log.info("Hello World 接口被调用");
        return Result.success("Hello World from GWM Scaffold!");
    }

    /**
     * Hello with name
     */
    @Operation(summary = "Hello with name", description = "返回个性化的Hello消息")
    @GetMapping("/{name}")
    public Result<String> helloWithName(@Parameter(description = "姓名") @PathVariable String name) {
        log.info("Hello with name 接口被调用，name: {}", name);
        return Result.success("Hello " + name + " from GWM Scaffold!");
    }

    /**
     * Hello with query parameter
     */
    @Operation(summary = "Hello with query", description = "通过查询参数返回Hello消息")
    @GetMapping("/query")
    public Result<String> helloWithQuery(@Parameter(description = "姓名") @RequestParam(defaultValue = "World") String name) {
        log.info("Hello with query 接口被调用，name: {}", name);
        return Result.success("Hello " + name + " from GWM Scaffold Query!");
    }

    /**
     * Hello POST
     */
    @Operation(summary = "Hello POST", description = "通过POST方法返回Hello消息")
    @PostMapping
    public Result<String> helloPost(@Parameter(description = "消息内容") @RequestBody String message) {
        log.info("Hello POST 接口被调用，message: {}", message);
        return Result.success("Received: " + message + " from GWM Scaffold!");
    }
}
