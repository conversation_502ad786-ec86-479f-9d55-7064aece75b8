package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.AdminRemoveUsersOfRoleFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.RoleBindGroupFormClientDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.SearchAdminUsersOfRoleFormClientDTO;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.pbc.BusinessCapabilityService;

import java.util.Map;
/**
 * <AUTHOR>
 */
@BusinessCapabilityService(name = "权限PBC-admin角色服务")
public interface SystemPbcAdminRoleSvc {

    /**
     * 权限中心-角色绑定组织
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-角色绑定组织")
    @Method(desc = "权限中心-角色绑定组织", name = "权限中心-角色绑定组织 v2-/manage/admin/role/bind/group")
    Map<String, Object> roleBindGroup(@Parameter(name = "request", required = true) RoleBindGroupFormClientDTO request);

    /**
     * 权限中心-查询角色用户和组织列表
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-查询角色用户和组织列表")
    @Method(desc = "权限中心-查询角色用户和组织列表", name = "权限中心-查询角色用户和组织列表 v2-/manage/admin/role/userAndGroup/list")
    Map<String, Object> searchUserAndGroupOfRole(@Parameter(name = "request", required = true) SearchAdminUsersOfRoleFormClientDTO request);

    /**
     * 权限中心-解除绑定用户
     * @param request
     * @return Map<String, Object>
     */
    @ReturnValue(desc = "权限中心-解除绑定用户")
    @Method(desc = "权限中心-解除绑定用户", name = "权限中心-解除绑定用户 v2-/manage/admin/role/user/unbind")
    Map<String, Object> removeUsersOfRole(@Parameter(name = "request", required = true) AdminRemoveUsersOfRoleFormClientDTO request);

}
