package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcGroupSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteGroupService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.group.SearchGroupClientDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component("systemPbcGroupSvcImpl")
public class SystemPbcGroupSvcImpl implements SystemPbcGroupSvc {
    
    @Autowired
    private RemoteGroupService groupService;
    
    @Override
    public Map<String, Object> groupTree() {
        CommonRlt<JSONObject> jsonObjectCommonRlt = groupService.groupTree();
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> groupTreeAll() {
        CommonRlt<JSONObject> jsonObjectCommonRlt = groupService.groupTreeAll();
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> hrGroupTreeAll() {
        CommonRlt<JSONObject> jsonObjectCommonRlt = groupService.hrGroupTreeAll();
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> groupInfo(SearchGroupClientDTO request) {
        CommonRlt<JSONObject> jsonObjectCommonRlt = groupService.groupInfo(request.getGroupId());
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }
}
