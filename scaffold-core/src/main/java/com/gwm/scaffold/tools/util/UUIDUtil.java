package com.gwm.scaffold.tools.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * UUID工具类
 * 
 * 提供UUID生成和唯一编号生成功能
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
public final class UUIDUtil {

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyyMMddHHmmss");
    private static final String EMPTY_STR = "";
    private static final int ZERO = 0;

    /**
     * 私有构造方法，防止实例化
     */
    private UUIDUtil() {
    }

    /**
     * 生成32位UUID（去除横线）
     *
     * @return 32位UUID字符串
     */
    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-", EMPTY_STR);
    }

    /**
     * 生成36位UUID（包含横线）
     *
     * @return 36位UUID字符串
     */
    public static String getUUIDWithHyphen() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成唯一编号
     * 格式：yyyyMMddHHmmss + 4位随机数
     *
     * @return 唯一编号
     */
    public static String getUniqueNo() {
        return SDF.format(new Date()) + String.format("%04d", ThreadLocalRandom.current().nextInt(ZERO, 10000));
    }

    /**
     * 生成指定长度的随机字符串
     *
     * @param length 长度
     * @return 随机字符串
     */
    public static String getRandomString(int length) {
        if (length <= 0) {
            return EMPTY_STR;
        }
        String uuid = getUUID();
        if (length >= uuid.length()) {
            return uuid;
        }
        return uuid.substring(0, length);
    }

    /**
     * 生成带前缀的唯一编号
     *
     * @param prefix 前缀
     * @return 带前缀的唯一编号
     */
    public static String getUniqueNoWithPrefix(String prefix) {
        return (prefix != null ? prefix : EMPTY_STR) + getUniqueNo();
    }
}
