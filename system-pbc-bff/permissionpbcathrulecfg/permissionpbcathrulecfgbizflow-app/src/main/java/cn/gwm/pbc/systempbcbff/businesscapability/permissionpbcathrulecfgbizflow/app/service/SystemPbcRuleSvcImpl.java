package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRuleSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule.RuleCreateForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule.RuleDelForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule.RuleEditForm;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.rule.RuleListFormDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteAdminRuleService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule.*;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
/**
 * <AUTHOR>
 */
@Component("systemPbcRuleSvcImpl")
public class SystemPbcRuleSvcImpl implements SystemPbcRuleSvc {

    @Value("${gwm.sso.platform-code}")
    private String platformCode;

    @Resource
    private RemoteAdminRuleService remoteAdminRuleService;

    @Override
    public Map<String, Object> list(RuleListFormClientDTO request) {
        RuleListFormDTO ruleListFormDTO = new RuleListFormDTO();
        BeanUtil.copyProperties(request, ruleListFormDTO);

        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteAdminRuleService.ruleList(ruleListFormDTO);
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> listDropDown(RuleListDownClientDTO request) {
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteAdminRuleService.listDropDown(request.getRoleSetId());
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> create(RuleCreateFormClientDTO request) {
        RuleCreateForm remote = new RuleCreateForm();
        BeanUtil.copyProperties(request, remote);
        remote.setPlatformCode(platformCode);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteAdminRuleService.create(remote);
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> detail(RuleDetailClientDTO request) {
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteAdminRuleService.detail(request.getRuleId());
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> edit(RuleEditFormClientDTO request) {
        RuleEditForm ruleEditForm = new RuleEditForm();
        BeanUtil.copyProperties(request, ruleEditForm);

        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteAdminRuleService.edit(ruleEditForm);
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> delete(RuleDelFormClientDTO request) {
        RuleDelForm ruleDelForm = new RuleDelForm();
        BeanUtil.copyProperties(request, ruleDelForm);

        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteAdminRuleService.delete(ruleDelForm);
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }

    @Override
    public Map<String, Object> editEnable(RuleDelFormClientDTO request) {
        RuleDelForm ruleDelForm = new RuleDelForm();
        BeanUtil.copyProperties(request, ruleDelForm);

        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteAdminRuleService.editEnable(ruleDelForm);
        return BeanUtil.beanToMap(jsonObjectCommonRlt);
    }
}
