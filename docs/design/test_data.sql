-- 汽车品牌、车系、车型测试数据
-- 执行前请确保已创建相关表结构

-- 清空现有数据（可选）
-- DELETE FROM t_model WHERE id > 0;
-- DELETE FROM t_series WHERE id > 0;
-- DELETE FROM t_brand WHERE id > 0;

-- 插入品牌数据
INSERT INTO t_brand (name, english_name, country, founded_year, website, logo_url, description, status, sort_weight, create_user_code, create_user_name) VALUES
('奔驰', 'Mercedes-Benz', '德国', 1926, 'https://www.mercedes-benz.com', 'https://example.com/logo/mercedes.png', '德国豪华汽车品牌，以高品质、高性能和高科技著称', 'ACTIVE', 100, 'admin', '系统管理员'),
('宝马', 'BMW', '德国', 1916, 'https://www.bmw.com', 'https://example.com/logo/bmw.png', '德国豪华汽车制造商，专注于高性能豪华汽车', 'ACTIVE', 95, 'admin', '系统管理员'),
('奥迪', 'Audi', '德国', 1909, 'https://www.audi.com', 'https://example.com/logo/audi.png', '德国豪华汽车品牌，以quattro四驱技术闻名', 'ACTIVE', 90, 'admin', '系统管理员'),
('丰田', 'Toyota', '日本', 1937, 'https://www.toyota.com', 'https://example.com/logo/toyota.png', '日本汽车制造商，以可靠性和燃油经济性著称', 'ACTIVE', 85, 'admin', '系统管理员'),
('本田', 'Honda', '日本', 1948, 'https://www.honda.com', 'https://example.com/logo/honda.png', '日本汽车制造商，以发动机技术和可靠性闻名', 'ACTIVE', 80, 'admin', '系统管理员'),
('比亚迪', 'BYD', '中国', 1995, 'https://www.byd.com', 'https://example.com/logo/byd.png', '中国新能源汽车领导品牌，专注于电动汽车技术', 'ACTIVE', 75, 'admin', '系统管理员'),
('特斯拉', 'Tesla', '美国', 2003, 'https://www.tesla.com', 'https://example.com/logo/tesla.png', '美国电动汽车制造商，引领电动汽车革命', 'ACTIVE', 70, 'admin', '系统管理员');

-- 插入车系数据
INSERT INTO t_series (name, english_name, brand_id, type, level, launch_time, min_price, max_price, image_url, description, status, sort_weight, create_user_code, create_user_name) VALUES
-- 奔驰车系
('C级', 'C-Class', 1, 'SEDAN', 'C', '2014-01-15', 32.52, 47.48, 'https://example.com/series/benz-c.jpg', '奔驰入门级豪华轿车，兼顾舒适性和运动性', 'ON_SALE', 100, 'admin', '系统管理员'),
('E级', 'E-Class', 1, 'SEDAN', 'D', '2016-07-25', 44.28, 65.68, 'https://example.com/series/benz-e.jpg', '奔驰中大型豪华轿车，商务与舒适并重', 'ON_SALE', 95, 'admin', '系统管理员'),
('GLC', 'GLC-Class', 1, 'SUV', 'C', '2015-11-19', 40.25, 58.78, 'https://example.com/series/benz-glc.jpg', '奔驰中型豪华SUV，城市与越野兼顾', 'ON_SALE', 90, 'admin', '系统管理员'),

-- 宝马车系
('3系', '3 Series', 2, 'SEDAN', 'C', '2019-06-22', 29.39, 40.99, 'https://example.com/series/bmw-3.jpg', '宝马运动型豪华轿车，操控性能出色', 'ON_SALE', 100, 'admin', '系统管理员'),
('5系', '5 Series', 2, 'SEDAN', 'D', '2017-06-23', 42.69, 65.99, 'https://example.com/series/bmw-5.jpg', '宝马中大型豪华轿车，商务舒适首选', 'ON_SALE', 95, 'admin', '系统管理员'),
('X3', 'X3', 2, 'SUV', 'C', '2018-07-09', 38.98, 47.98, 'https://example.com/series/bmw-x3.jpg', '宝马中型豪华SUV，运动与实用并重', 'ON_SALE', 90, 'admin', '系统管理员'),

-- 奥迪车系
('A4L', 'A4L', 3, 'SEDAN', 'C', '2020-04-10', 30.58, 39.68, 'https://example.com/series/audi-a4l.jpg', '奥迪中型豪华轿车，科技配置丰富', 'ON_SALE', 100, 'admin', '系统管理员'),
('A6L', 'A6L', 3, 'SEDAN', 'D', '2018-10-19', 41.98, 65.38, 'https://example.com/series/audi-a6l.jpg', '奥迪中大型豪华轿车，商务精英之选', 'ON_SALE', 95, 'admin', '系统管理员'),
('Q5L', 'Q5L', 3, 'SUV', 'C', '2018-07-06', 39.68, 51.70, 'https://example.com/series/audi-q5l.jpg', '奥迪中型豪华SUV，quattro四驱系统', 'ON_SALE', 90, 'admin', '系统管理员'),

-- 丰田车系
('凯美瑞', 'Camry', 4, 'SEDAN', 'B', '2017-12-20', 17.98, 26.98, 'https://example.com/series/toyota-camry.jpg', '丰田中型轿车，可靠性和燃油经济性出色', 'ON_SALE', 100, 'admin', '系统管理员'),
('汉兰达', 'Highlander', 4, 'SUV', 'C', '2018-03-23', 25.88, 34.88, 'https://example.com/series/toyota-highlander.jpg', '丰田中大型SUV，7座家用首选', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 本田车系
('雅阁', 'Accord', 5, 'SEDAN', 'B', '2018-04-16', 17.98, 25.98, 'https://example.com/series/honda-accord.jpg', '本田中型轿车，运动与舒适平衡', 'ON_SALE', 100, 'admin', '系统管理员'),
('CR-V', 'CR-V', 5, 'SUV', 'B', '2017-07-09', 16.98, 27.68, 'https://example.com/series/honda-crv.jpg', '本田紧凑型SUV，城市代步首选', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 比亚迪车系
('汉', 'Han', 6, 'SEDAN', 'C', '2020-07-12', 21.98, 32.98, 'https://example.com/series/byd-han.jpg', '比亚迪旗舰轿车，刀片电池技术', 'ON_SALE', 100, 'admin', '系统管理员'),
('唐', 'Tang', 6, 'SUV', 'C', '2018-06-26', 23.99, 35.99, 'https://example.com/series/byd-tang.jpg', '比亚迪中大型SUV，插电混动技术', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 特斯拉车系
('Model 3', 'Model 3', 7, 'SEDAN', 'C', '2019-02-22', 26.59, 34.99, 'https://example.com/series/tesla-model3.jpg', '特斯拉入门级电动轿车，智能驾驶技术', 'ON_SALE', 100, 'admin', '系统管理员'),
('Model Y', 'Model Y', 7, 'SUV', 'C', '2021-01-01', 30.99, 39.99, 'https://example.com/series/tesla-modely.jpg', '特斯拉紧凑型电动SUV，超级充电网络', 'ON_SALE', 95, 'admin', '系统管理员');

-- 插入车型数据
INSERT INTO t_model (name, series_id, year_model, displacement, max_power, max_torque, transmission, gear_count, drive_type, fuel_type, body_structure, seat_count, length, width, height, wheelbase, curb_weight, tank_capacity, fuel_consumption, guide_price, launch_time, image_urls, config_details, status, sort_weight, create_user_code, create_user_name) VALUES
-- 奔驰C级车型
('C 200 L 运动版', 1, 2023, 1.5, 150, 280, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 4784, 1810, 1457, 2920, 1555, 66, 6.9, 32.52, '2023-03-15', '["https://example.com/model/benz-c200l-1.jpg", "https://example.com/model/benz-c200l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "盲点监测"], "舒适配置": ["座椅加热", "自动空调", "无线充电"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('C 260 L 豪华版', 1, 2023, 1.5, 150, 280, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 4784, 1810, 1457, 2920, 1575, 66, 7.1, 37.38, '2023-03-15', '["https://example.com/model/benz-c260l-1.jpg", "https://example.com/model/benz-c260l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "盲点监测"], "舒适配置": ["座椅加热通风", "自动空调", "无线充电", "柏林之声音响"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 奔驰E级车型
('E 300 L 豪华型', 2, 2023, 2.0, 190, 370, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 5078, 1860, 1484, 3079, 1730, 80, 7.8, 44.28, '2023-01-10', '["https://example.com/model/benz-e300l-1.jpg", "https://example.com/model/benz-e300l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["座椅按摩", "四区空调", "柏林之声音响"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('E 350 L 运动型', 2, 2023, 2.0, 220, 400, '9G-TRONIC', 9, 'RWD', 'GASOLINE', 'SEDAN', 5, 5078, 1860, 1484, 3079, 1750, 80, 8.2, 52.88, '2023-01-10', '["https://example.com/model/benz-e350l-1.jpg", "https://example.com/model/benz-e350l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["座椅按摩", "四区空调", "柏林之声音响", "运动套件"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 奔驰GLC车型
('GLC 260 L 4MATIC', 3, 2023, 1.5, 150, 280, '9G-TRONIC', 9, 'AWD', 'GASOLINE', 'SUV', 5, 4764, 1898, 1648, 2873, 1770, 66, 7.8, 40.25, '2023-05-20', '["https://example.com/model/benz-glc260l-1.jpg", "https://example.com/model/benz-glc260l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["座椅加热", "全景天窗", "无线充电"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('GLC 300 L 4MATIC 豪华型', 3, 2023, 2.0, 190, 370, '9G-TRONIC', 9, 'AWD', 'GASOLINE', 'SUV', 5, 4764, 1898, 1648, 2873, 1820, 66, 8.3, 48.38, '2023-05-20', '["https://example.com/model/benz-glc300l-1.jpg", "https://example.com/model/benz-glc300l-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["座椅加热通风", "全景天窗", "无线充电", "柏林之声音响"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 宝马3系车型
('325Li M运动套装', 4, 2023, 2.0, 135, 300, '8挡手自一体', 8, 'RWD', 'GASOLINE', 'SEDAN', 5, 4829, 1827, 1463, 2961, 1555, 59, 6.9, 29.39, '2023-02-15', '["https://example.com/model/bmw-325li-1.jpg", "https://example.com/model/bmw-325li-2.jpg"]', '{"安全配置": ["主动刹车", "车道偏离预警"], "舒适配置": ["运动座椅", "自动空调", "哈曼卡顿音响"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('330Li xDrive M运动套装', 4, 2023, 2.0, 185, 300, '8挡手自一体', 8, 'AWD', 'GASOLINE', 'SEDAN', 5, 4829, 1827, 1463, 2961, 1590, 59, 7.1, 36.19, '2023-02-15', '["https://example.com/model/bmw-330li-1.jpg", "https://example.com/model/bmw-330li-2.jpg"]', '{"安全配置": ["主动刹车", "车道偏离预警", "自适应巡航"], "舒适配置": ["运动座椅", "自动空调", "哈曼卡顿音响", "全景天窗"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 宝马5系车型
('525Li 豪华套装', 5, 2023, 2.0, 135, 290, '8挡手自一体', 8, 'RWD', 'GASOLINE', 'SEDAN', 5, 5106, 1868, 1500, 3105, 1680, 68, 7.2, 42.69, '2023-04-10', '["https://example.com/model/bmw-525li-1.jpg", "https://example.com/model/bmw-525li-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "四区空调", "哈曼卡顿音响"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('530Li xDrive 豪华套装', 5, 2023, 2.0, 185, 350, '8挡手自一体', 8, 'AWD', 'GASOLINE', 'SEDAN', 5, 5106, 1868, 1500, 3105, 1720, 68, 7.8, 50.39, '2023-04-10', '["https://example.com/model/bmw-530li-1.jpg", "https://example.com/model/bmw-530li-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "四区空调", "宝华韦健音响", "全景天窗"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 宝马X3车型
('xDrive25i M运动套装', 6, 2023, 2.0, 135, 290, '8挡手自一体', 8, 'AWD', 'GASOLINE', 'SUV', 5, 4717, 1891, 1689, 2864, 1770, 65, 7.6, 38.98, '2023-03-20', '["https://example.com/model/bmw-x3-25i-1.jpg", "https://example.com/model/bmw-x3-25i-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持"], "舒适配置": ["运动座椅", "全景天窗", "哈曼卡顿音响"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('xDrive30i M运动套装', 6, 2023, 2.0, 185, 350, '8挡手自一体', 8, 'AWD', 'GASOLINE', 'SUV', 5, 4717, 1891, 1689, 2864, 1810, 65, 8.1, 47.98, '2023-03-20', '["https://example.com/model/bmw-x3-30i-1.jpg", "https://example.com/model/bmw-x3-30i-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["运动座椅", "全景天窗", "宝华韦健音响"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 奥迪A4L车型
('35 TFSI 进取型', 7, 2023, 2.0, 110, 250, '7挡双离合', 7, 'FWD', 'GASOLINE', 'SEDAN', 5, 4858, 1847, 1439, 2908, 1575, 54, 6.8, 30.58, '2023-01-25', '["https://example.com/model/audi-a4l-35-1.jpg", "https://example.com/model/audi-a4l-35-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持"], "舒适配置": ["真皮座椅", "自动空调", "虚拟座舱"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('40 TFSI 时尚型', 7, 2023, 2.0, 140, 320, '7挡双离合', 7, 'FWD', 'GASOLINE', 'SEDAN', 5, 4858, 1847, 1439, 2908, 1595, 54, 7.2, 34.68, '2023-01-25', '["https://example.com/model/audi-a4l-40-1.jpg", "https://example.com/model/audi-a4l-40-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "自动空调", "虚拟座舱", "B&O音响"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 奥迪A6L车型
('40 TFSI 豪华致雅型', 8, 2023, 2.0, 140, 320, '7挡双离合', 7, 'FWD', 'GASOLINE', 'SEDAN', 5, 5050, 1886, 1475, 3024, 1735, 73, 7.5, 41.98, '2023-02-10', '["https://example.com/model/audi-a6l-40-1.jpg", "https://example.com/model/audi-a6l-40-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "四区空调", "虚拟座舱", "B&O音响"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('45 TFSI quattro 臻选型', 8, 2023, 2.0, 185, 370, '7挡双离合', 7, 'AWD', 'GASOLINE', 'SEDAN', 5, 5050, 1886, 1475, 3024, 1780, 73, 8.0, 50.98, '2023-02-10', '["https://example.com/model/audi-a6l-45-1.jpg", "https://example.com/model/audi-a6l-45-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "四区空调", "虚拟座舱", "B&O音响", "quattro四驱"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 奥迪Q5L车型
('40 TFSI 荣享进取型', 9, 2023, 2.0, 140, 320, '7挡双离合', 7, 'AWD', 'GASOLINE', 'SUV', 5, 4770, 1893, 1667, 2907, 1770, 70, 7.8, 39.68, '2023-04-05', '["https://example.com/model/audi-q5l-40-1.jpg", "https://example.com/model/audi-q5l-40-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持"], "舒适配置": ["真皮座椅", "全景天窗", "虚拟座舱"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('45 TFSI quattro 尊享豪华型', 9, 2023, 2.0, 185, 370, '7挡双离合', 7, 'AWD', 'GASOLINE', 'SUV', 5, 4770, 1893, 1667, 2907, 1820, 70, 8.3, 47.98, '2023-04-05', '["https://example.com/model/audi-q5l-45-1.jpg", "https://example.com/model/audi-q5l-45-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "全景天窗", "虚拟座舱", "B&O音响", "quattro四驱"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 丰田凯美瑞车型
('2.0E 精英版', 10, 2023, 2.0, 131, 210, 'CVT无级变速', NULL, 'FWD', 'GASOLINE', 'SEDAN', 5, 4885, 1840, 1455, 2825, 1530, 60, 5.7, 17.98, '2023-03-01', '["https://example.com/model/toyota-camry-20e-1.jpg", "https://example.com/model/toyota-camry-20e-2.jpg"]', '{"安全配置": ["主动刹车", "车道偏离预警"], "舒适配置": ["织物座椅", "自动空调", "多媒体系统"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('2.5HG 豪华版', 10, 2023, 2.5, 160, 221, 'E-CVT电子无级变速', NULL, 'FWD', 'HYBRID', 'SEDAN', 5, 4885, 1840, 1455, 2825, 1590, 50, 4.1, 23.98, '2023-03-01', '["https://example.com/model/toyota-camry-25hg-1.jpg", "https://example.com/model/toyota-camry-25hg-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "自动空调", "JBL音响", "混合动力"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 丰田汉兰达车型
('2.0T 两驱精英版 7座', 11, 2023, 2.0, 162, 350, '6挡手自一体', 6, 'FWD', 'GASOLINE', 'SUV', 7, 4965, 1930, 1750, 2850, 1925, 65, 8.2, 25.88, '2023-05-15', '["https://example.com/model/toyota-highlander-20t-1.jpg", "https://example.com/model/toyota-highlander-20t-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持"], "舒适配置": ["织物座椅", "三区空调", "7座布局"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('2.0T 四驱豪华版 7座', 11, 2023, 2.0, 162, 350, '6挡手自一体', 6, 'AWD', 'GASOLINE', 'SUV', 7, 4965, 1930, 1750, 2850, 1985, 65, 8.8, 32.58, '2023-05-15', '["https://example.com/model/toyota-highlander-20t-4wd-1.jpg", "https://example.com/model/toyota-highlander-20t-4wd-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["真皮座椅", "三区空调", "7座布局", "四驱系统"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 本田雅阁车型
('260TURBO 精享版', 12, 2023, 1.5, 143, 260, 'CVT无级变速', NULL, 'FWD', 'GASOLINE', 'SEDAN', 5, 4893, 1862, 1449, 2830, 1495, 56, 6.0, 17.98, '2023-02-20', '["https://example.com/model/honda-accord-260t-1.jpg", "https://example.com/model/honda-accord-260t-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持"], "舒适配置": ["织物座椅", "自动空调", "Honda SENSING"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('260TURBO 旗舰版', 12, 2023, 1.5, 143, 260, 'CVT无级变速', NULL, 'FWD', 'GASOLINE', 'SEDAN', 5, 4893, 1862, 1449, 2830, 1515, 56, 6.2, 22.98, '2023-02-20', '["https://example.com/model/honda-accord-260t-flagship-1.jpg", "https://example.com/model/honda-accord-260t-flagship-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "自适应巡航"], "舒适配置": ["真皮座椅", "自动空调", "Honda SENSING", "BOSE音响"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 本田CR-V车型
('240TURBO CVT两驱舒适版', 13, 2023, 1.5, 142, 243, 'CVT无级变速', NULL, 'FWD', 'GASOLINE', 'SUV', 5, 4703, 1866, 1680, 2701, 1576, 57, 6.4, 16.98, '2023-04-12', '["https://example.com/model/honda-crv-240t-2wd-1.jpg", "https://example.com/model/honda-crv-240t-2wd-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持"], "舒适配置": ["织物座椅", "自动空调", "Honda SENSING"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('240TURBO CVT四驱尊贵版', 13, 2023, 1.5, 142, 243, 'CVT无级变速', NULL, 'AWD', 'GASOLINE', 'SUV', 5, 4703, 1866, 1680, 2701, 1619, 57, 6.9, 24.98, '2023-04-12', '["https://example.com/model/honda-crv-240t-4wd-1.jpg", "https://example.com/model/honda-crv-240t-4wd-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["真皮座椅", "自动空调", "Honda SENSING", "四驱系统"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 比亚迪汉车型
('DM-i 121KM 尊贵型', 14, 2023, 1.5, 139, 231, 'E-CVT电子无级变速', NULL, 'FWD', 'PLUG_IN_HYBRID', 'SEDAN', 5, 4995, 1910, 1495, 2920, 1985, 70, 4.2, 21.98, '2023-01-08', '["https://example.com/model/byd-han-dmi-1.jpg", "https://example.com/model/byd-han-dmi-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["真皮座椅", "自动空调", "DiLink智能网联", "插电混动"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('EV 创世版 715KM', 14, 2023, NULL, 380, 700, '电动车单速变速箱', 1, 'RWD', 'ELECTRIC', 'SEDAN', 5, 4995, 1910, 1495, 2920, 2020, NULL, NULL, 29.98, '2023-01-08', '["https://example.com/model/byd-han-ev-1.jpg", "https://example.com/model/byd-han-ev-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["真皮座椅", "自动空调", "DiLink智能网联", "刀片电池", "715km续航"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 比亚迪唐车型
('DM-i 112KM 尊贵型', 15, 2023, 1.5, 139, 231, 'E-CVT电子无级变速', NULL, 'AWD', 'PLUG_IN_HYBRID', 'SUV', 7, 4870, 1950, 1725, 2820, 2295, 70, 5.3, 23.99, '2023-03-18', '["https://example.com/model/byd-tang-dmi-1.jpg", "https://example.com/model/byd-tang-dmi-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["真皮座椅", "三区空调", "DiLink智能网联", "7座布局", "插电混动"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('EV 创世版 635KM', 15, 2023, NULL, 380, 700, '电动车单速变速箱', 1, 'AWD', 'ELECTRIC', 'SUV', 7, 4870, 1950, 1725, 2820, 2420, NULL, NULL, 32.99, '2023-03-18', '["https://example.com/model/byd-tang-ev-1.jpg", "https://example.com/model/byd-tang-ev-2.jpg"]', '{"安全配置": ["主动刹车", "车道保持", "全景影像"], "舒适配置": ["真皮座椅", "三区空调", "DiLink智能网联", "7座布局", "刀片电池", "635km续航"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 特斯拉Model 3车型
('后轮驱动版', 16, 2023, NULL, 194, 340, '电动车单速变速箱', 1, 'RWD', 'ELECTRIC', 'SEDAN', 5, 4694, 1850, 1443, 2875, 1745, NULL, NULL, 26.59, '2023-01-06', '["https://example.com/model/tesla-model3-rwd-1.jpg", "https://example.com/model/tesla-model3-rwd-2.jpg"]', '{"安全配置": ["Autopilot自动辅助驾驶", "主动安全功能"], "舒适配置": ["极简内饰", "15英寸触摸屏", "超级充电网络", "468km续航"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('长续航全轮驱动版', 16, 2023, NULL, 324, 493, '电动车单速变速箱', 1, 'AWD', 'ELECTRIC', 'SEDAN', 5, 4694, 1850, 1443, 2875, 1844, NULL, NULL, 34.99, '2023-01-06', '["https://example.com/model/tesla-model3-awd-1.jpg", "https://example.com/model/tesla-model3-awd-2.jpg"]', '{"安全配置": ["Autopilot自动辅助驾驶", "主动安全功能"], "舒适配置": ["极简内饰", "15英寸触摸屏", "超级充电网络", "全轮驱动", "713km续航"]}', 'ON_SALE', 95, 'admin', '系统管理员'),

-- 特斯拉Model Y车型
('后轮驱动版', 17, 2023, NULL, 194, 340, '电动车单速变速箱', 1, 'RWD', 'ELECTRIC', 'SUV', 5, 4750, 1921, 1624, 2890, 1909, NULL, NULL, 30.99, '2023-01-06', '["https://example.com/model/tesla-modely-rwd-1.jpg", "https://example.com/model/tesla-modely-rwd-2.jpg"]', '{"安全配置": ["Autopilot自动辅助驾驶", "主动安全功能"], "舒适配置": ["极简内饰", "15英寸触摸屏", "超级充电网络", "545km续航"]}', 'ON_SALE', 100, 'admin', '系统管理员'),
('长续航全轮驱动版', 17, 2023, NULL, 324, 493, '电动车单速变速箱', 1, 'AWD', 'ELECTRIC', 'SUV', 5, 4750, 1921, 1624, 2890, 2003, NULL, NULL, 39.99, '2023-01-06', '["https://example.com/model/tesla-modely-awd-1.jpg", "https://example.com/model/tesla-modely-awd-2.jpg"]', '{"安全配置": ["Autopilot自动辅助驾驶", "主动安全功能"], "舒适配置": ["极简内饰", "15英寸触摸屏", "超级充电网络", "全轮驱动", "660km续航"]}', 'ON_SALE', 95, 'admin', '系统管理员');

-- 查询验证数据
-- SELECT COUNT(*) as brand_count FROM t_brand WHERE deleted = 0;
-- SELECT COUNT(*) as series_count FROM t_series WHERE deleted = 0;
-- SELECT COUNT(*) as model_count FROM t_model WHERE deleted = 0;

-- 查询品牌及其车系数量
-- SELECT b.name as brand_name, COUNT(s.id) as series_count
-- FROM t_brand b
-- LEFT JOIN t_series s ON b.id = s.brand_id AND s.deleted = 0
-- WHERE b.deleted = 0
-- GROUP BY b.id, b.name
-- ORDER BY b.sort_weight DESC;

-- 查询车系及其车型数量
-- SELECT s.name as series_name, b.name as brand_name, COUNT(m.id) as model_count
-- FROM t_series s
-- LEFT JOIN t_brand b ON s.brand_id = b.id
-- LEFT JOIN t_model m ON s.id = m.series_id AND m.deleted = 0
-- WHERE s.deleted = 0 AND b.deleted = 0
-- GROUP BY s.id, s.name, b.name
-- ORDER BY b.sort_weight DESC, s.sort_weight DESC;
