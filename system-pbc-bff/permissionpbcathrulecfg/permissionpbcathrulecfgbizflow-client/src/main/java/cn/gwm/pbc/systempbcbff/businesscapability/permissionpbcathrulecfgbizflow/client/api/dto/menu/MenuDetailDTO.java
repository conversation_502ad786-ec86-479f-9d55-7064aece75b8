package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.menu;

import com.alibaba.bizworks.core.specification.*;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@StructureObject(name = "MenuDetailDTO", desc = "")
@Data
public class MenuDetailDTO implements Serializable {

    /**
     * 菜单ID
     */
    @Field(name = "菜单ID", desc = "菜单ID")
    private String id;

}
