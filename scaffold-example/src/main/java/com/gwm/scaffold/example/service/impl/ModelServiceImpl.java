package com.gwm.scaffold.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwm.scaffold.core.exception.ServiceException;
import com.gwm.scaffold.monitor.annotation.Monitor;
import com.gwm.scaffold.example.entity.Model;
import com.gwm.scaffold.example.mapper.ModelMapper;
import com.gwm.scaffold.example.service.ModelService;
import com.gwm.scaffold.example.dto.ModelCreateRequest;
import com.gwm.scaffold.example.dto.ModelUpdateRequest;
import com.gwm.scaffold.example.dto.ModelQueryRequest;
import com.gwm.scaffold.example.vo.ModelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Service
public class ModelServiceImpl extends ServiceImpl<ModelMapper, Model> implements ModelService {

    @Override
    @Monitor(value = "分页查询", threshold = 1000)
    public IPage<ModelVO> getModelPage(ModelQueryRequest request) {
        Page<Model> page = new Page<>(request.getCurrent(), request.getSize());
        IPage<Model> entityPage = baseMapper.selectModelPage(page, request);

        // 转换为VO
        Page<ModelVO> voPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        List<ModelVO> voList = entityPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    @Monitor(value = "获取详情")
    public ModelVO getModelById(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("ID不能为空");
        }

        Model model = getById(id);
        if (model == null) {
            throw new ServiceException("不存在");
        }

        return convertToVO(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "创建")
    public ModelVO createModel(ModelCreateRequest request) {
        if (request == null) {
            throw new ServiceException("创建请求不能为空");
        }

        // 创建
        Model model = new Model();
        BeanUtils.copyProperties(request, model);

        boolean success = save(model);
        if (!success) {
            throw new ServiceException("创建失败");
        }

        log.info("创建成功，ID：{}", model.getId());
        return getModelById(model.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "更新")
    public ModelVO updateModel(ModelUpdateRequest request) {
        if (request == null) {
            throw new ServiceException("更新请求不能为空");
        }
        if (request.getId() == null || request.getId() <= 0) {
            throw new ServiceException("ID不能为空");
        }

        // 检查是否存在
        Model existingModel = getById(request.getId());
        if (existingModel == null) {
            throw new ServiceException("不存在");
        }

        // 更新
        BeanUtils.copyProperties(request, existingModel);

        boolean success = updateById(existingModel);
        if (!success) {
            throw new ServiceException("更新失败");
        }

        log.info("更新成功，ID：{}", request.getId());
        return getModelById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "删除")
    public boolean deleteModel(Long id) {
        if (id == null || id <= 0) {
            throw new ServiceException("ID不能为空");
        }

        Model model = getById(id);
        if (model == null) {
            throw new ServiceException("不存在");
        }

        boolean success = removeById(id);
        if (success) {
            log.info("删除成功，ID：{}", id);
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Monitor(value = "批量删除")
    public int batchDeleteModels(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("ID列表不能为空");
        }

        boolean success = removeByIds(ids);
        int count = success ? ids.size() : 0;

        log.info("批量删除完成，删除数量：{}", count);
        return count;
    }

    /**
     * 将实体转换为VO
     */
    private ModelVO convertToVO(Model entity) {
        if (entity == null) {
            return null;
        }

        ModelVO vo = new ModelVO();
        BeanUtils.copyProperties(entity, vo);

        // 可以在这里添加额外的转换逻辑，例如状态名称等

        return vo;
    }
}
