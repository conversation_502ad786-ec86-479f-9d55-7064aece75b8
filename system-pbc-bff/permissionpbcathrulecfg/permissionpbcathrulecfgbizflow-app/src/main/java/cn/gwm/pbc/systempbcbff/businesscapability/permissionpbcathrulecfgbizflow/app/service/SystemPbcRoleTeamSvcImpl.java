package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRoleTeamSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.service.RemoteRoleService;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.user.CommonRlt;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.RemoteRoleBindUserDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.RemoteRoleBindUsersDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.role.RemoteRoleUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.vo.user.RoleUserInfoItemVO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserBindDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserListDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.role.user.RoleUserUnbindDTO;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.gwm.framework.core.exception.BusinessException;
import java.lang.Override;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
/**
 * <AUTHOR>
 */
@Component("SystemPbcRoleTeamSvcImpl")
public class SystemPbcRoleTeamSvcImpl implements SystemPbcRoleTeamSvc {

    @Resource
    private RemoteRoleService remoteRoleService;

    @Override
    public Map<String, Object> list(RoleUserListDTO request) {
    	RemoteRoleUserListDTO remoteRoleUserListDTO = new RemoteRoleUserListDTO();
        BeanUtil.copyProperties(request, remoteRoleUserListDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteRoleService.roleUserList(remoteRoleUserListDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
        JSONObject result = jsonObjectCommonRlt.getData();
        if (result != null) {
            JSONArray rows = result.getJSONArray("rows");
            List<RoleUserInfoItemVO> roleInfoItemVOS = rows.toJavaList(RoleUserInfoItemVO.class);
            result.put("rows", roleInfoItemVOS);
        }
        return BeanUtil.beanToMap(result);
	}

    @Override
    public void bind(RoleUserBindDTO request) {
    	RemoteRoleBindUserDTO remoteRoleDeleteDTO = new RemoteRoleBindUserDTO();
        BeanUtil.copyProperties(request, remoteRoleDeleteDTO);
        CommonRlt<JSONArray> jsonObjectCommonRlt = remoteRoleService.roleBindUser(remoteRoleDeleteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}

    @Override
    public void unbind(RoleUserUnbindDTO request) {
    	RemoteRoleBindUsersDTO remoteDTO = new RemoteRoleBindUsersDTO();
        BeanUtil.copyProperties(request, remoteDTO);
        CommonRlt<JSONObject> jsonObjectCommonRlt = remoteRoleService.roleBindUsers(remoteDTO);
        if (!jsonObjectCommonRlt.isSuccess()) {
            throw new BusinessException(jsonObjectCommonRlt.getMessage());
        }
	}
}
