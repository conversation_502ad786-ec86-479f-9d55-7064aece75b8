{"properties": [{"name": "scaffold.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用GWM Scaffold"}, {"name": "scaffold.application.name", "type": "java.lang.String", "defaultValue": "GWM Scaffold Application", "description": "应用名称"}, {"name": "scaffold.application.version", "type": "java.lang.String", "defaultValue": "1.0.0", "description": "应用版本"}, {"name": "scaffold.application.description", "type": "java.lang.String", "defaultValue": "基于GWM Scaffold构建的应用", "description": "应用描述"}, {"name": "scaffold.modules.auth", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用认证模块"}, {"name": "scaffold.modules.web", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用Web增强模块"}, {"name": "scaffold.modules.data", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用数据访问模块"}, {"name": "scaffold.modules.tools", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用工具模块"}, {"name": "scaffold.modules.monitor", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用监控模块"}, {"name": "scaffold.modules.docs", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用文档模块"}, {"name": "scaffold.generator.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用代码生成器"}, {"name": "scaffold.generator.web.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": true, "description": "是否启用代码生成器Web界面"}]}