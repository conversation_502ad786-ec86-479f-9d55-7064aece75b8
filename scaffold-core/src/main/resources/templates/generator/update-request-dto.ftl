package ${packageName}.dto;

<#list tableInfo.imports as import>
import ${import};
</#list>
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * ${tableInfo.tableComment!entityName}更新请求DTO
 * 
 * <AUTHOR>
 * @date ${date}
 */
@Data
@Schema(description = "${tableInfo.tableComment!entityName}更新请求")
public class ${updateRequestName} {

    /**
     * ${tableInfo.primaryKey.columnComment!tableInfo.primaryKey.javaField}
     */
    @Schema(description = "${tableInfo.primaryKey.columnComment!tableInfo.primaryKey.javaField}", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "${tableInfo.primaryKey.columnComment!tableInfo.primaryKey.javaField}不能为空")
    @Min(value = 1, message = "${tableInfo.primaryKey.columnComment!tableInfo.primaryKey.javaField}必须大于0")
    private ${tableInfo.primaryKey.javaType} id;

<#list tableInfo.columns as column>
    <#if column.formField && !column.primaryKey>
    /**
     * ${column.columnComment!column.javaField}
     */
    @Schema(description = "${column.columnComment!column.javaField}")
    <#if column.javaType == "String" && column.columnSize??>
    @Size(max = ${column.columnSize?c}, message = "${column.columnComment!column.javaField}长度不能超过${column.columnSize?c}")
    </#if>
    <#if column.javaType == "String" && column.javaField?contains("email")>
    @Email(message = "邮箱格式不正确")
    </#if>
    <#if column.javaType == "String" && column.javaField?contains("phone")>
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    </#if>
    <#if (column.javaType == "Integer" || column.javaType == "Long") && column.javaField != "status">
    @Min(value = 1, message = "${column.columnComment!column.javaField}必须大于0")
    </#if>
    private ${column.javaType} ${column.javaField};

    </#if>
</#list>
<#if tableInfo.hasVersion>
    /**
     * 版本号
     */
    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    @Min(value = 0, message = "版本号不能为负数")
    private Integer version;

</#if>
}
