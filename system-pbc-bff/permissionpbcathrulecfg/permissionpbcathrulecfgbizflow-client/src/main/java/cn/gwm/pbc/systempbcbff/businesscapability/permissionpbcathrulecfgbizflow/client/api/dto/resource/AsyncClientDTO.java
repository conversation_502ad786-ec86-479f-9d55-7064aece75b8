package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.resource;

import com.alibaba.bizworks.core.specification.*;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * <AUTHOR>
 */
@StructureObject(name = "AsyncClientDTO", desc = "")
@Data
public class AsyncClientDTO {

    /**
     * admin_platform_code
     * 接入平台码
     */
//    private String adminPlatformCode;

    /**
     *  类别（0-菜单管理，1-数据管理）
     *  category
     */
    @Field(name = "类别（0-菜单管理，1-数据管理）", desc = "类别（0-菜单管理，1-数据管理）category")
    private Integer category;

    /**
     *  资源ID
     */
    @Field(name = "资源ID", desc = "资源ID")
    private String id;

}
