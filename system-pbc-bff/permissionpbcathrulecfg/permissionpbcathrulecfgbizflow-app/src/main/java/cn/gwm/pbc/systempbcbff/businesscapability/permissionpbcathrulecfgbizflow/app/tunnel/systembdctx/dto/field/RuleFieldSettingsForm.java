package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.tunnel.systembdctx.dto.field;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("规则字段设置请求参数对象")
public class RuleFieldSettingsForm implements Serializable {

    /**
     * 操作人账号
     */
    @JsonIgnore
    @ApiModelProperty("操作人账号")
    private String account;

    /**
     * 角色ID（虚拟角色）
     */
    @ApiModelProperty("角色ID")
    @JsonProperty(value = "role_id")
    private Long roleSetId;

    /**
     * 规则字段数据
     */
    @ApiModelProperty("规则字段数据")
    @JsonProperty(value = "rule_field_data")
    List<RuleFieldDataForm> ruleFieldDataList;
}