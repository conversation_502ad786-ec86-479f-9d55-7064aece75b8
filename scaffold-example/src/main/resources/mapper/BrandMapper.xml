<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwm.scaffold.example.mapper.BrandMapper">

    <!-- 结果映射 -->
    <resultMap id="BrandResultMap" type="com.gwm.scaffold.example.entity.Brand">
        <id column="ID" property="id"/>
        <result column="NAME" property="name"/>
        <result column="ENGLISH_NAME" property="englishName"/>
        <result column="COUNTRY" property="country"/>
        <result column="FOUNDED_YEAR" property="foundedYear"/>
        <result column="WEBSITE" property="website"/>
        <result column="LOGO_URL" property="logoUrl"/>
        <result column="DESCRIPTION" property="description"/>
        <result column="STATUS" property="status"/>
        <result column="SORT_WEIGHT" property="sortWeight"/>
        <result column="DELETED" property="deleted"/>
        <result column="DELETED_TIME" property="deletedTime"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_USER_CODE" property="createUserCode"/>
        <result column="CREATE_USER_NAME" property="createUserName"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="UPDATE_USER_CODE" property="updateUserCode"/>
        <result column="UPDATE_USER_NAME" property="updateUserName"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        ID, NAME, ENGLISH_NAME, COUNTRY, FOUNDED_YEAR, WEBSITE, LOGO_URL, DESCRIPTION, STATUS, SORT_WEIGHT, DELETED, DELETED_TIME, CREATE_TIME, CREATE_USER_CODE, CREATE_USER_NAME, UPDATE_TIME, UPDATE_USER_CODE, UPDATE_USER_NAME
    </sql>

    <!-- 分页查询列表 -->
    <select id="selectBrandPage" resultMap="BrandResultMap">
        SELECT
        <include refid="BaseColumns"/>
        FROM T_BRAND
        <where>
            deleted = 0
            <if test="request.id != null">
                AND ID = #{request.id}
            </if>
            <if test="request.name != null and request.name != ''">
                AND NAME LIKE CONCAT('%', #{request.name}, '%')
            </if>
            <if test="request.englishName != null and request.englishName != ''">
                AND ENGLISH_NAME LIKE CONCAT('%', #{request.englishName}, '%')
            </if>
            <if test="request.country != null and request.country != ''">
                AND COUNTRY LIKE CONCAT('%', #{request.country}, '%')
            </if>
            <if test="request.foundedYear != null">
                AND FOUNDED_YEAR = #{request.foundedYear}
            </if>
            <if test="request.website != null and request.website != ''">
                AND WEBSITE LIKE CONCAT('%', #{request.website}, '%')
            </if>
            <if test="request.logoUrl != null and request.logoUrl != ''">
                AND LOGO_URL LIKE CONCAT('%', #{request.logoUrl}, '%')
            </if>
            <if test="request.description != null and request.description != ''">
                AND DESCRIPTION LIKE CONCAT('%', #{request.description}, '%')
            </if>
            <if test="request.status != null and request.status != ''">
                AND STATUS LIKE CONCAT('%', #{request.status}, '%')
            </if>
            <if test="request.sortWeight != null">
                AND SORT_WEIGHT = #{request.sortWeight}
            </if>
            <if test="request.deletedTimeStart != null">
                AND DELETED_TIME >= #{request.deletedTimeStart}
            </if>
            <if test="request.deletedTimeEnd != null">
                AND DELETED_TIME &lt;= #{request.deletedTimeEnd}
            </if>
        </where>
        ORDER BY SORT_WEIGHT ASC, CREATE_TIME DESC
    </select>

    <!-- 根据品牌名称模糊查询 -->
    <select id="selectByBrandNameLike" resultMap="BrandResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM T_BRAND
        WHERE DELETED = 0
        AND NAME LIKE CONCAT('%', #{brandName}, '%')
        ORDER BY SORT_WEIGHT ASC, NAME ASC
    </select>

    <!-- 根据国家查询品牌 -->
    <select id="selectByCountry" resultMap="BrandResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM T_BRAND
        WHERE DELETED = 0
        AND COUNTRY = #{country}
        ORDER BY SORT_WEIGHT ASC, NAME ASC
    </select>

    <!-- 统计各国家的品牌数量 -->
    <select id="countByCountry" resultType="com.gwm.scaffold.example.vo.BrandVO">
        SELECT
            COUNTRY as country,
            COUNT(*) as brandCount
        FROM T_BRAND
        WHERE DELETED = 0
        GROUP BY COUNTRY
        ORDER BY brandCount DESC, COUNTRY ASC
    </select>

    <!-- 检查品牌名称是否存在（排除指定ID） -->
    <select id="checkBrandNameExists" resultType="int">
        SELECT COUNT(*)
        FROM T_BRAND
        WHERE DELETED = 0
        AND NAME = #{name}
        <if test="excludeId != null">
            AND ID != #{excludeId}
        </if>
    </select>

</mapper>
