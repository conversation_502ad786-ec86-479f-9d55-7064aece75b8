package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.entry.service;

import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcRuleSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.app.api.SystemPbcTeamSvc;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcRuleSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.SystemPbcTeamSvcI;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.department.TeamTreeDTO;
import cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.rule.*;
import com.alibaba.bizworks.core.runtime.common.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 权限PBC-规则管理服务controller
 *
 * <AUTHOR> 管理员
 * @date 2023.12.14
 */
@RestController("PermissionPBCAthRuleCfgBizFlowSystemPbcRuleSvcCON")
public class SystemPbcRuleSvcController implements SystemPbcRuleSvcI {

    @Autowired
    private SystemPbcRuleSvc systemPbcRuleSvc;

    @Override
    public Map<String, Object> ruleList(@RequestBody(required = true) RuleListFormClientDTO request) {
        return systemPbcRuleSvc.list(request);
	}

    @Override
    public Map<String, Object> listDropDown(RuleListDownClientDTO request) {
        return systemPbcRuleSvc.listDropDown(request);
    }

    @Override
    public Map<String, Object> create(RuleCreateFormClientDTO request) {
        return systemPbcRuleSvc.create(request);
    }

    @Override
    public Map<String, Object> detail(RuleDetailClientDTO request) {
        return systemPbcRuleSvc.detail(request);
    }

    @Override
    public Map<String, Object> edit(RuleEditFormClientDTO request) {
        return systemPbcRuleSvc.edit(request);
    }

    @Override
    public Map<String, Object> delete(RuleDelFormClientDTO request) {
        return systemPbcRuleSvc.delete(request);
    }

    @Override
    public Map<String, Object> editEnable(RuleDelFormClientDTO request) {
        return systemPbcRuleSvc.editEnable(request);
    }

}
