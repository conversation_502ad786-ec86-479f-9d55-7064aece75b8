package cn.gwm.pbc.systempbcbff.businesscapability.permissionpbcathrulecfgbizflow.client.api.dto.user;

import com.alibaba.bizworks.core.specification.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
/**
 * <AUTHOR>
 */
@StructureObject(name = "PlatformUserDTO", desc = "")
@Data
@ApiModel("用户平台关联列表")
public class PlatformUserDTO {

    /**
     * 平台用户ID
     */
    @Field(name = "平台用户ID", desc = "平台用户ID")
    @ApiModelProperty("平台用户ID")
    @JsonProperty(value = "platform_user_id")
    private Long platformUserId;
    
    @Field(name = "userId", desc = "")
    @ApiModelProperty("用户ID")
    @JsonProperty(value = "user_id")
    private Long userId;

    /**
     * 用户别名
     */
    @Field(name = "用户别名", desc = "用户别名")
    @ApiModelProperty("用户别名")
    private String alias;

    /**
     * 账号
     */
    @Field(name = "账号", desc = "账号")
    @ApiModelProperty("账号")
    @JsonProperty(value = "user_code")
    private String userCode;

    /**
     * 启用/禁用
     */
    @Field(name = "启用/禁用", desc = "启用/禁用")
    @ApiModelProperty("启用/禁用")
    private Integer enable;

    /**
     * 用户类型
     */
    @Field(name = "用户类型", desc = "用户类型")
    @ApiModelProperty("用户类型")
    @JsonProperty(value = "user_type")
    private Integer userType;

    /**
     * 创建人编号
     */
    @Field(name = "创建人编号", desc = "创建人编号")
    @ApiModelProperty("创建人编号")
    @JsonProperty(value = "creator_code")
    private String creatorCode;

    /**
     * 创建时间
     */
    @Field(name = "创建时间", desc = "创建时间")
    @ApiModelProperty("创建时间")
    @JsonProperty(value = "create_time")
    private Date createTime;
}
