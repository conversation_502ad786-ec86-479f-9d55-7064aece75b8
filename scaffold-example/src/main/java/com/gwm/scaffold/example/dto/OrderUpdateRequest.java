package com.gwm.scaffold.example.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 订单更新请求DTO
 * 
 * 用于接收更新订单的请求参数
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@ApiModel(description = "订单更新请求")
public class OrderUpdateRequest {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    @ApiModelProperty(value = "订单ID", required = true, example = "1")
    private Long id;

    /**
     * 客户姓名
     */
    @Size(max = 100, message = "客户姓名长度不能超过100个字符")
    @ApiModelProperty(value = "客户姓名", example = "张三")
    private String customerName;

    /**
     * 客户电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "客户电话格式不正确")
    @ApiModelProperty(value = "客户电话", example = "13800138001")
    private String customerPhone;

    /**
     * 客户邮箱
     */
    @Email(message = "客户邮箱格式不正确")
    @Size(max = 100, message = "客户邮箱长度不能超过100个字符")
    @ApiModelProperty(value = "客户邮箱", example = "<EMAIL>")
    private String customerEmail;

    /**
     * 商品名称
     */
    @Size(max = 200, message = "商品名称长度不能超过200个字符")
    @ApiModelProperty(value = "商品名称", example = "iPhone 15 Pro Max 256GB")
    private String productName;

    /**
     * 商品SKU
     */
    @Size(max = 100, message = "商品SKU长度不能超过100个字符")
    @ApiModelProperty(value = "商品SKU", example = "IPHONE15PM256GB-BLACK")
    private String productSku;

    /**
     * 商品数量
     */
    @Min(value = 1, message = "商品数量不能小于1")
    @Max(value = 999, message = "商品数量不能大于999")
    @ApiModelProperty(value = "商品数量", example = "1")
    private Integer quantity;

    /**
     * 商品单价
     */
    @DecimalMin(value = "0.01", message = "商品单价不能小于0.01")
    @DecimalMax(value = "999999.99", message = "商品单价不能大于999999.99")
    @Digits(integer = 8, fraction = 2, message = "商品单价格式不正确")
    @ApiModelProperty(value = "商品单价", example = "9999.00")
    private BigDecimal unitPrice;

    /**
     * 优惠金额
     */
    @DecimalMin(value = "0.00", message = "优惠金额不能小于0")
    @DecimalMax(value = "999999.99", message = "优惠金额不能大于999999.99")
    @Digits(integer = 8, fraction = 2, message = "优惠金额格式不正确")
    @ApiModelProperty(value = "优惠金额", example = "500.00")
    private BigDecimal discountAmount;

    /**
     * 订单状态 (1:待支付 2:已支付 3:已发货 4:已完成 5:已取消)
     */
    @Min(value = 1, message = "订单状态值不正确")
    @Max(value = 5, message = "订单状态值不正确")
    @ApiModelProperty(value = "订单状态", example = "1", notes = "1:待支付 2:已支付 3:已发货 4:已完成 5:已取消")
    private Integer orderStatus;

    /**
     * 支付方式 (1:支付宝 2:微信 3:银行卡 4:现金)
     */
    @Min(value = 1, message = "支付方式值不正确")
    @Max(value = 4, message = "支付方式值不正确")
    @ApiModelProperty(value = "支付方式", example = "1", notes = "1:支付宝 2:微信 3:银行卡 4:现金")
    private Integer paymentMethod;

    /**
     * 收货地址
     */
    @Size(max = 500, message = "收货地址长度不能超过500个字符")
    @ApiModelProperty(value = "收货地址", example = "北京市朝阳区建国门外大街1号")
    private String shippingAddress;

    /**
     * 收货电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "收货电话格式不正确")
    @ApiModelProperty(value = "收货电话", example = "13800138001")
    private String shippingPhone;

    /**
     * 收货人姓名
     */
    @Size(max = 100, message = "收货人姓名长度不能超过100个字符")
    @ApiModelProperty(value = "收货人姓名", example = "张三")
    private String shippingName;

    /**
     * 订单备注
     */
    @Size(max = 500, message = "订单备注长度不能超过500个字符")
    @ApiModelProperty(value = "订单备注", example = "请小心轻放")
    private String remark;
    
    /**
     * 版本号（乐观锁）
     */
    @NotNull(message = "版本号不能为空")
    @ApiModelProperty(value = "版本号", required = true, example = "0")
    private Integer version;
}
