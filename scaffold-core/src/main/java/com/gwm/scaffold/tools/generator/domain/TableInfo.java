package com.gwm.scaffold.tools.generator.domain;

import lombok.Data;

import java.util.List;

/**
 * 表信息
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Data
public class TableInfo {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 表注释
     */
    private String tableComment;

    /**
     * 实体类名
     */
    private String entityName;

    /**
     * 实体类名（首字母小写）
     */
    private String entityNameLower;

    /**
     * 字段列表
     */
    private List<ColumnInfo> columns;

    /**
     * 主键字段
     */
    private ColumnInfo primaryKey;

    /**
     * 是否有逻辑删除字段
     */
    private boolean hasLogicDelete;

    /**
     * 是否有版本字段
     */
    private boolean hasVersion;

    /**
     * 是否有创建时间字段
     */
    private boolean hasCreateTime;

    /**
     * 是否有更新时间字段
     */
    private boolean hasUpdateTime;

    /**
     * 导入的包列表
     */
    private List<String> imports;
}
